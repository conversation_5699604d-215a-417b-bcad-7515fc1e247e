import { defineMockPms } from "./base";

export default defineMockPms([
    {
        url: "purchaser/getPerchasePersonnelList",
        method: ["GET"],
        body: {
            code: 0,
            data: [
                {
                    userId: '1',
                    userName: "采购员1",
                },
                {
                    userId: '2',
                    userName: "采购员2",
                },
                {
                    userId: '3',
                    userName: "采购员3",
                },
            ],
            msg: "一切ok",
        },
    },

  {
    url: "purchaser/user/select",
    method: ["POST"],
    body: {
      code: 0,
      data: [
        {
          id: 2,
          name: "系统管理员",
        },
        {
          id: 4,
          name: "系统管理员1",
        },
        {
          id: 5,
          name: "系统管理员2",
        },
      ],
      msg: "一切ok",
    },
  },

  {
    url: "purchaser/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
          {
            id: '1',
            purchaserName: "采购员1",
            purchaserAccount: 'we',
            roleNames: "采购员描述1",
            deptName: "部门1",
            purchaserMobile: "***********",
            createTime: "2021-03-25 12:39:54",
          },
          {
              id: '2',
              purchaserName: "采购员2",
              purchaserAccount: 'we',
              roleNames: "采购员描述1",
              deptName: "部门2",
              purchaserMobile: "***********",
              createTime: "2021-03-25 12:39:54",
          },
        ],
        total: 11,
      },
      msg: "一切ok",
    },
  },

  // 新增采购员
  {
    url: "purchaser/save",
    method: ["POST"],
    body: {
        code: 0,
        data: null,
        msg: "新增采购员成功",
    },
  },

  // 删除采购员
  {
    url: "purchaser/delete",
    method: ["POST"],
    body:{
        code: 0,
        data: null,
        msg: "删除采购员成功",
    },
  },

]);
