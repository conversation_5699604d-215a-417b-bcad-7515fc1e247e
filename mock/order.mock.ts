import { defineMockOms } from "./base";
import {ProductAllPageVO} from "@/modules/pms/api/purchaseOrder";

export default defineMockOms([

  // 查询客户列表
  {
    url: "customer/queryList",
    method: ["GET"],
    body: {
      code: 0,
      data: [
          {
              customerCode:'001',
              customerName:'客户1'
          },
          {
              customerCode:'002',
              customerName:'客户2'
          }
        ],
      },
      msg: "一切ok",
  },

    // 获取订单分页数据
    {
    url: "order/queryPageList",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
          {
            id: '1',
            orderCode: "wqewreeeee11111111111111",
            customerName: '客户名称',
            allProductName: '商品,ww',
            totalCount: 5,
            currencyCode:'CNY',
            totalAmount:'1367.8',
            contactPerson: "收**",
            contactAreaCode:'+86',
            contactMobile: "135****8888",
            countryId: '1',
            countryName: '中国',
            provinceId: '01',
            provinceName: '陕西省',
            cityId: '001',
            cityName: '西安市',
            districtId: '0001',
            districtName: '长安区',
            address: 'xsjaionc***mioxzcehsi',
            expectedReceivedTimeEnd: 1743156000000,
            expectedReceivedTimeStar:1743152400000,
            orderCreateTime:1743152400000,
            orderSource: 2,
            orderStatus: 0,
            remark:'xiddddddddddddddaohua',
          },
          {
            id: '2',
            orderCode: "wqewreeeee11111111111111",
            customerName: '客户名称',
            allProductName: '商品,ww',
            totalCount: 5,
            currencyCode:'CNY',
            totalAmount:'1367.8',
            contactPerson: "收**",
            contactAreaCode:'+86',
            contactMobile: "135****8888",
            countryId: '1',
            countryName: '中国',
            provinceId: '01',
            provinceName: '陕西省',
            cityId: '001',
            cityName: '西安市',
            districtId: '0001',
            districtName: '长安区',
            address: 'xsjaionc***mioxzcehsi',
            expectedReceivedTimeEnd: 1743156000000,
            expectedReceivedTimeStar:1743152400000,
            orderCreateTime:1743152400000,
            orderSource: 2,
            orderStatus: 1,
            remark:'xiddddddddddddddaohua',
          },
          {
            id: '3',
            orderCode: "wqewreeeee11111111111111",
            customerName: '客户名称',
            allProductName: '商品,ww',
            totalCount: 5,
            currencyCode:'CNY',
            totalAmount:'1367.8',
            contactPerson: "收**",
            contactAreaCode:'+86',
            contactMobile: "135****8888",
            countryId: '1',
            countryName: '中国',
            provinceId: '01',
            provinceName: '陕西省',
            cityId: '001',
            cityName: '西安市',
            districtId: '0001',
            districtName: '长安区',
            address: 'xsjaionc***mioxzcehsi',
            expectedReceivedTimeEnd: 1743156000000,
            expectedReceivedTimeStar:1743152400000,
            orderCreateTime:1743152400000,
            orderSource: 2,
            orderStatus: 2,
            remark:'xiddddddddddddddaohua',
          },
          {
            id: '4',
            orderCode: "wqewreeeee11111111111111",
            customerName: '客户名称',
            allProductName: '商品,ww',
            totalCount: 5,
            currencyCode:'CNY',
            totalAmount:'1367.8',
            contactPerson: "收**",
            contactAreaCode:'+86',
            contactMobile: "135****8888",
            countryId: '1',
            countryName: '中国',
            provinceId: '01',
            provinceName: '陕西省',
            cityId: '001',
            cityName: '西安市',
            districtId: '0001',
            districtName: '长安区',
            address: 'xsjaionc***mioxzcehsi',
            expectedReceivedTimeEnd: 1743156000000,
            expectedReceivedTimeStar:1743152400000,
            orderCreateTime:1743152400000,
            orderSource: 2,
            orderStatus: 3,
            remark:'xiddddddddddddddaohua',
          },
          {
            id: '5',
            orderCode: "wqewreeeee11111111111111",
            customerName: '客户名称',
            allProductName: '商品,ww',
            totalCount: 5,
            currencyCode:'CNY',
            totalAmount:'1367.8',
            contactPerson: "收**",
            contactAreaCode:'+86',
            contactMobile: "135****8888",
            countryId: '1',
            countryName: '中国',
            provinceId: '01',
            provinceName: '陕西省',
            cityId: '001',
            cityName: '西安市',
            districtId: '0001',
            districtName: '长安区',
            address: 'xsjaionc***mioxzcehsi',
            expectedReceivedTimeEnd: 1743156000000,
            expectedReceivedTimeStar:1743152400000,
            orderCreateTime:1743152400000,
            orderSource: 2,
            orderStatus: 4,
            remark:'xiddddddddddddddaohua',
          },
          {
            id: '6',
            orderCode: "wqewreeeee11111111111111",
            customerName: '客户名称',
            allProductName: '商品,ww',
            totalCount: 5,
            currencyCode:'CNY',
            totalAmount:'1367.8',
            contactPerson: "收**",
            contactAreaCode:'+86',
            contactMobile: "135****8888",
            countryId: '1',
            countryName: '中国',
            provinceId: '01',
            provinceName: '陕西省',
            cityId: '001',
            cityName: '西安市',
            districtId: '0001',
            districtName: '长安区',
            address: 'xsjaionc***mioxzcehsi',
            expectedReceivedTimeEnd: 1743156000000,
            expectedReceivedTimeStar:1743152400000,
            orderCreateTime:1743152400000,
            orderSource: 2,
            orderStatus: 5,
            remark:'xiddddddddddddddaohua',
          },
        ],
        total: '6',
      },
      msg: "一切ok",
    },
  },

  //删除订单
  {
      url: "order/delete",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "删除成功",
      },
  },

  //  根据商品id查询采购单编辑详情
  {
      url: "order/queryDetailForEdit",
      method: ["POST"],
      body: {
          code: 0,
          data:{
                id: '1',
                orderCode: "wqewreeeee11111111111111",
                customerName: '客户名称',
                allProductName: '商品,ww',
                totalCount: 5,
                currencyCode:'CNY',
                totalAmount:'1367.8',
                contactPerson: "收**",
                contactAreaCode:'+86',
                contactMobile: "135****8888",
                countryId: '1',
                countryName: '中国',
                provinceId: '01',
                provinceName: '陕西省',
                cityId: '001',
                cityName: '西安市',
                districtId: '0001',
                districtName: '长安区',
                address: 'xsjaionc***mioxzcehsi',
                expectedReceivedTimeEnd: 1743156000000,
                expectedReceivedTimeStar:1743152400000,
                orderCreateTime:1743152400000,
                orderSource: 2,
                orderStatus: 0,
                remark:'xiddddddddddddddaohua',
                orderAttachmentFiles:'[{\\"bucket\\":\\"yt-oxms-read-uat\\",\\"fileName\\":\\"https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.com/yt-oxms-read-uat/image/编组 <EMAIL>\\",\\"originalFileName\\":\\"编组 <EMAIL>\\"}]',

                  externalOrderCode:'12333333',
                  orderDate:'2021-03-25 12:12:12',
                  deliverDate:'2021-03-25 12:12:12',
                  finashDate:'2021-03-25 12:12:12',
                  closeDate:'2021-03-25 12:12:12',
                  warehouseId:'001',
                  warehouseName:'仓库1',

                  orderDetailList:[
                      {
                          id: '001',
                          imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                          productCode: '001',
                          allProductName: '西瓜1',
                          productSpecName: '1斤',
                          saleCount: 5,
                          salePrice: 10.00,
                          saleMoney: 50,
                      },
                      {
                          id: '001',
                          imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                          productCode: '001',
                          allProductName: '西瓜1',
                          productSpecName: '1斤',
                          saleCount: 10,
                          salePrice: 9.99,
                          saleMoney: 99.90,
                      },
                  ],
                  deliveryPlanList:[
                      {
                          id:'1',
                          show:true,
                          orderStatus:1,
                          deliveryPlanTime: "2021-03-26 12:12:12",
                          deliveryBatch: "JF202502230001",
                          estimatedDeliveryTime: "2021-03-25 12:12:12",
                          generationTime: "2021-03-25 12:12:12",
                          deliverDateTime: "2021-03-25 12:12:12",
                          deliveryPlanDetailList:[
                              {
                                  id: '001',
                                  productCode: '001',
                                  allProductName: '西瓜1',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                              {
                                  id: '002',
                                  productCode: '001',
                                  allProductName: '西瓜2',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                          ]
                      },
                      {
                          id:'2',
                          show:true,
                          orderStatus:2,
                          deliveryPlanTime: "2021-03-27 12:12:12",
                          deliveryBatch: "JF202502230001",
                          estimatedDeliveryTime: "2021-03-25 12:12:12",
                          generationTime: "2021-03-25 12:12:12",
                          deliverDateTime: "2021-03-25 12:12:12",
                          deliveryPlanDetailList:[
                              {
                                  id: '001',
                                  productCode: '001',
                                  allProductName: '西瓜1',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                              {
                                  id: '002',
                                  productCode: '001',
                                  allProductName: '西瓜2',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                          ]
                      },
                  ],
                  operationLogList:[
                      {
                          id:'1',
                          operationTime: "2021-03-25 12:12:12",
                          operationType: "操作类型",
                          operationUserName: "操作人",
                      },
                      {
                          id:'2',
                          operationTime: "2021-03-25 12:12:12",
                          operationType: "操作类型",
                          operationUserName: "操作人",
                      },
                  ]
          },
          msg: "一切ok",
      },
  },
  {
      url: "order/queryDetail",
      method: ["POST"],
      body: {
          code: 0,
          data:{
                id: '1',
                orderCode: "wqewreeeee11111111111111",
                customerName: '客户名称',
                allProductName: '商品,ww',
                totalCount: 5,
                currencyCode:'CNY',
                totalAmount:'1367.8',
                contactPerson: "收**",
                contactAreaCode:'+86',
                contactMobile: "135****8888",
                countryId: '1',
                countryName: '中国',
                provinceId: '01',
                provinceName: '陕西省',
                cityId: '001',
                cityName: '西安市',
                districtId: '0001',
                districtName: '长安区',
                address: 'xsjaionc***mioxzcehsi',
                expectedReceivedTimeEnd: 1743156000000,
                expectedReceivedTimeStar:1743152400000,
                orderCreateTime:1743152400000,
                orderSource: 2,
                orderStatus: 0,
                remark:'xiddddddddddddddaohua',
                orderAttachmentFiles:'[{\\"bucket\\":\\"yt-oxms-read-uat\\",\\"fileName\\":\\"https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.com/yt-oxms-read-uat/image/编组 <EMAIL>\\",\\"originalFileName\\":\\"编组 <EMAIL>\\"}]',

                  externalOrderCode:'12333333',
                  orderDate:'2021-03-25 12:12:12',
                  deliverDate:'2021-03-25 12:12:12',
                  finashDate:'2021-03-25 12:12:12',
                  closeDate:'2021-03-25 12:12:12',
                  warehouseId:'001',
                  warehouseName:'仓库1',

                  orderDetailList:[
                      {
                          id: '001',
                          imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                          productCode: '001',
                          allProductName: '西瓜1',
                          productSpecName: '1斤',
                          saleCount: 5,
                          salePrice: 10.00,
                          saleMoney: 50,
                      },
                      {
                          id: '001',
                          imagesUrls: "https://img0.baidu.com/it/u=2191392668,814349101&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1399",
                          productCode: '001',
                          allProductName: '西瓜1',
                          productSpecName: '1斤',
                          saleCount: 10,
                          salePrice: 9.99,
                          saleMoney: 99.90,
                      },
                  ],
                  deliveryPlanList:[
                      {
                          id:'1',
                          show:true,
                          orderStatus:1,
                          deliveryPlanTime: "2021-03-26 12:12:12",
                          deliveryBatch: "JF202502230001",
                          estimatedDeliveryTime: "2021-03-25 12:12:12",
                          generationTime: "2021-03-25 12:12:12",
                          deliverDateTime: "2021-03-25 12:12:12",
                          deliveryPlanDetailList:[
                              {
                                  id: '001',
                                  productCode: '001',
                                  allProductName: '西瓜1',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                              {
                                  id: '002',
                                  productCode: '001',
                                  allProductName: '西瓜2',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                          ]
                      },
                      {
                          id:'2',
                          show:true,
                          orderStatus:2,
                          deliveryPlanTime: "2021-03-27 12:12:12",
                          deliveryBatch: "JF202502230001",
                          estimatedDeliveryTime: "2021-03-25 12:12:12",
                          generationTime: "2021-03-25 12:12:12",
                          deliverDateTime: "2021-03-25 12:12:12",
                          deliveryPlanDetailList:[
                              {
                                  id: '001',
                                  productCode: '001',
                                  allProductName: '西瓜1',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                              {
                                  id: '002',
                                  productCode: '001',
                                  allProductName: '西瓜2',
                                  productSpecName: '1斤',
                                  arrivalQuantity: 5,
                                  salePrice: 10.00,
                                  saleMoney: 50,
                              },
                          ]
                      },
                  ],
                  operationLogList:[
                      {
                          id:'1',
                          operationTime: "2021-03-25 12:12:12",
                          operationType: "操作类型",
                          operationUserName: "操作人",
                      },
                      {
                          id:'2',
                          operationTime: "2021-03-25 12:12:12",
                          operationType: "操作类型",
                          operationUserName: "操作人",
                      },
                  ]
          },
          msg: "一切ok",
      },
  },

    // 获取订单仓库列表
    {
        url: "order/deliveryWarehouseList",
        method: ["GET"],
        body: {
            code: 0,
            data: [
                    {
                        value:'001',
                        name:'仓库1',
                    },
                    {
                        value:'002',
                        name:'仓库2',
                    }
            ],
            msg: "一切ok",
        },
    },


    //  添加采购单
    {
        url: "order/save",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

    // 编辑采购单
    {
        url: "order/edit",
        method: ["POST"],
        body: {
            code: 0,
            data:null,
            msg: "一切ok",
        },
    },

]);
