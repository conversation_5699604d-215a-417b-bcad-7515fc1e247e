import { defineMockWms } from "./base";
import request from "@/core/utils/request";
import {ReportLossOrderFrom} from "@/modules/wms/api/reportLossOrder";

export default defineMockWms([
  {
    url: "lossOrder/page",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
             {
                 id: '1',
                 lossOrderCode: '报损单号',
                 orderType: 1,
                 sourceOrderType: 12,
                 sourceOrderCode: '拆装单号',
                 reporterName: '报损人',
                 reportTime: 1740473528000,
                 createUser:'ccxc',
                 createTime: 1740473528000,
                 remark: "卑职呃呃",
                 orderStatus:0,
             },
             {
                 id: '2',
                 lossOrderCode: '报损单号',
                 orderType: 1,
                 reporterName: '报损人',
                 reportTime: 1740473528000,
                 createUser:'ccxc',
                 createTime: 1740473528000,
                 remark: "卑职呃呃",
                 orderStatus: 1,
             },
        ],
        total: '2',
      },
      msg: "一切ok",
    },
  },

  {
    url: "lossOrder/detail",
    method: ["POST"],
    body: {
      code: 0,
      data: {
            id: '1',
            lossOrderCode: '报损单号1',
            orderType: 1,
            sourceOrderType: 12,
            sourceOrderCode: '拆装单号',
            reportLossOrderDetailList:[
                {
                    id: '1',
                    productCode: '001',
                    productName: 'productName',
                    productSpec: '商品规格',
                    productUnitName: '斤',
                    warehouseAreaId: '001',
                    warehouseAreaName: 'warehouseAreaName',
                    disassemblyInQty: 5,
                    lossQty:2,
                    lossStatus: 1,
                },
            ],
            reporterName: '报损人',
            reportTime: 1740473528000,
            createUser: 'ccxc',
            createTime: 1740473528000,
            remark: "卑职呃呃",
            orderStatus: 0,
      },
      msg: "一切ok",
    },
  },


  // 删除报损单
  {
      url: "lossOrder/delete",
      method: ["POST"],
      body:{
          code: 0,
          data: null,
          msg: "删除成功",
      },
  },

    // 暂存报损单
    {
        url: "lossOrder/save",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "暂存成功",
        },
    },

    // 提交报损单
    {
        url: "lossOrder/submit",
        method: ["POST"],
        body:{
            code: 0,
            data: null,
            msg: "提交成功",
        },
    },

]);
