import { defineMockWms } from "./base";

export default defineMockWms([
  {
    url: "inventoryLossInfo/list",
    method: ["POST"],
    body: {
      code: 0,
      data: {
         records: [
             {
               id: '1',
               lossOrderCode: "损益单号",
               lossType:1,
               createUserName:'刘莹',
               createTime:1740473528000,
               handlerUserName:'刘莹',
               handlerTime:1740473528000,
               lossStatus:1,
               remark:"备注"
             },
             {
               id: '2',
               lossOrderCode: "损益单号",
               lossType:1,
               createUserName:'刘莹',
               createTime:1740473528000,
               handlerUserName:'刘莹',
               handlerTime:1740473528000,
               lossStatus:0,
               remark:"备注"
             },
             {
               id: '3',
               lossOrderCode: "损益单号",
               lossType:1,
               createUserName:'刘莹',
               createTime:1740473528000,
               handlerUserName:'刘莹',
               handlerTime:1740473528000,
               lossStatus:1,
               remark:"备注"
             },
             {
               id: '4',
               lossOrderCode: "损益单号",
               lossType:1,
               createUserName:'刘莹',
               createTime:1740473528000,
               handlerUserName:'刘莹',
               handlerTime:1740473528000,
               lossStatus:0,
               remark:"备注"
             },
        ],
        total: '4',
      },
      msg: "一切ok",
    },
  },

  //  根据id查询损益详情
  {
      url: "inventoryLossInfo/detail/1",
      method: ["GET"],
      body: {
          code: 0,
          data:{
            id:1,
            lossOrderCode:"损益编号",
            createUserName:"创建人",
            createTime:1740473528000,
            lossType:1,
            remark:"备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注备注",
            inventoryLossInfoDetailList:[
              {
                productCode: "商品编码",
                productName: "商品名称",
                productSpec: "规格",
                warehouseAreaCode: "库区",
                warehouseAreaName: "库区",
                beforeInventoryQty: 400,
                afterInventoryQty: 200,
                lossQty: 200,
                productUnit: "斤",
              },
              {
                productCode: "商品编码",
                productName: "商品名称",
                productSpec: "规格",
                warehouseAreaCode: "库区",
                warehouseAreaName: "库区",
                beforeInventoryQty: 200,
                afterInventoryQty: 400,
                lossQty: -200,
                productUnit: "斤",
              }
            ]
          },
          msg: "一切ok",
      },
  },

  //删除损益
  {
      url: "inventoryLossInfo/del/1",
      method: ["POST"],
      body: {
          code: 0,
          data:null,
          msg: "一切ok",
      },
  },
  //损益管理保存
  {
      url: "inventoryLossInfo/save",
      method: ["POST"],
      body: {
          code: 0,
          data:null,
          msg: "一切ok",
      },
  },
  //获取库区商品库存数量
  {
    url: "productStock/queryAreaProductStockQty",
    method: ["POST"],
    body: {
      code: 0,
      data:{
        totalStockQty:200,
        warehouseAreaName:"库区名称",
      },
      msg: "一切ok",
    },
  }

]);
