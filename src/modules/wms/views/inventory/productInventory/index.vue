<template>
  <div class="app-container">
    <div class="productInventory">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
            <el-form-item prop="productName" :label="$t('productInventory.label.product')">
              <el-input class="!w-[256px]" v-model="queryParams.productName"
                :placeholder="$t('productInventory.placeholder.keywords')" clearable />
            </el-form-item>
            <el-form-item :label="$t('productInventory.label.productCategory')" prop="productCategory">
              <el-cascader v-model="queryParams.productCategory" :options="categoryList" :props="propsCategory"
                @change="handleChange" ref="cascaderRef" filterable collapse-tags collapse-tags-tooltip
                class="!w-[256px]" :placeholder="$t('common.placeholder.selectTips')" clearable />
            </el-form-item>
            <el-form-item :label="$t('productInventory.label.warehouseAreaCode')" prop="warehouseAreaIds">
              <el-select v-model="queryParams.warehouseAreaIds" :placeholder="$t('common.placeholder.selectTips')"
                clearable filterable multiple collapse-tags collapse-tags-tooltip class="!w-[256px]">
                <el-option v-for="item in areaList" :key="item.id" :label="item.warehouseArea"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-hasPerm="['wms:productStock:search']" type="primary" @click="handleQuery">
                {{ $t('common.search') }}
              </el-button>
              <el-button v-hasPerm="['wms:productStock:reset']" @click="handleResetQuery">
                {{ $t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <el-table v-loading="loading" :data="productInventoryList" highlight-current-row stripe>
          <template #empty>
            <Empty />
          </template>
          <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" />
          <el-table-column :label="$t('productInventory.label.productCode')" prop="productCode"
            show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('productInventory.label.productName')" prop="productName"
            show-overflow-tooltip></el-table-column>
          <!-- 库区 -->
          <el-table-column :label="$t('productInventory.label.warehouseAreaName')" prop="warehouseAreaName"
            show-overflow-tooltip></el-table-column>
          <!-- 库区标识 -->
          <el-table-column :label="$t('productInventory.label.warehouseAreaIdentification')" min-width="120"
            prop="warehouseAreaIdentification" show-overflow-tooltip>
            <template #default="scope">
              <div v-if="scope.row.warehouseAreaIdentification == 1">
                {{ $t('productInventory.warehouseAreaIdentificationList.physicsWarehouseArea') }}</div>
              <div v-if="scope.row.warehouseAreaIdentification == 2">
                {{ $t('productInventory.warehouseAreaIdentificationList.virtualWarehouseArea') }}</div>
            </template>
          </el-table-column>
          <!-- 总库存 -->
          <el-table-column :label="$t('productInventory.label.totalStockQty')" prop="totalStockQty" min-width="150"
            show-overflow-tooltip>
            <template #default="scope">
              <div>{{ $t('productInventory.label.totalQty') }}({{ scope.row.productUnitName }})：<span
                  style="color: #90979E ">{{ scope.row.totalStockQty }}</span></div>
              <div>{{ $t('productInventory.label.totalWeight') }}({{ scope.row.conversionRelSecondUnitName }})：<span
                  style="color: #90979E ">{{ scope.row.totalStockWeight }}kg</span></div>
            </template>
          </el-table-column>
          <!-- 可用库存 -->
          <el-table-column :label="$t('productInventory.label.availableStockQty')" prop="availableStockQty"
            min-width="150" show-overflow-tooltip>
            <template #default="scope">
              <div>{{ $t('productInventory.label.availableQty') }}：<span
                  style="color: #90979E ">{{ scope.row.availableStockQty }}</span></div>
              <div>{{ $t('productInventory.label.availableWeight') }}：<span
                  style="color: #90979E ">{{ scope.row.availableStockWeight }}</span></div>
            </template>
          </el-table-column>
          <!-- 锁定数量 -->
          <el-table-column :label="$t('productInventory.label.lockedStockQty')" prop="lockedStockQty" min-width="150"
            show-overflow-tooltip>
            <template #default="scope">
              <div>{{ $t('productInventory.label.lockedAmount') }}：<span
                  style="color: #90979E ">{{ scope.row.lockedStockQty }}</span></div>
              <div>{{ $t('productInventory.label.lockedConvertAmount') }}：<span
                  style="color: #90979E ">{{ scope.row.lockedStockWeight }}kg</span></div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('productInventory.label.productUnitName')" prop="productUnitName"
            show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('productInventory.label.productSpec')" prop="productSpec"
            show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('productInventory.label.productCategory')" prop="fullCategoryName"
            show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('common.handle')" width="120">
            <template #default="scope">
              <el-button v-hasPerm="['wms:productStock:log']" type="primary" link
                @click="openLogList(scope.row)">{{ $t('productInventory.button.operationLog') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.page"
            v-model:limit="queryParams.limit" @pagination="handleQuery" />
        </div>
      </el-card>
      <el-dialog width="90%" style="max-height: 87%;overflow: hidden;" v-model="showDialog"
        :title="$t('productInventory.title.operationLogTitle')" :close-on-click-modal="false" @close="onCloseHandler">
        <el-table v-loading="logLoading" :data="logDataList" highlight-current-row stripe :max-height="625"
          style="overflow:auto;">
          <template #empty>
            <Empty />
          </template>
          <!-- Index Column -->
          <el-table-column type="index" :label="$t('common.sort')" width="60" />
          <!-- Operation Type - 操作环节 -->
          <el-table-column :label="$t('productInventory.label.operationType')" width="120" prop="operationTypeName"
            show-overflow-tooltip>
            <template #default="scope">
              <div class="purchase">
                <span class="purchase-status purchase-status-color4">{{ scope.row.operationTypeName }}</span>
              </div>
            </template>
          </el-table-column>
          <!-- Source Order Code - 对应单据号 -->
          <el-table-column :label="$t('productInventory.label.sourceOrderCode')" width="180" prop="sourceOrderCode">
            <template #default="scope">
              <el-button type="primary" link @click="toDetail(scope.row)">{{ scope.row.sourceOrderCode }}</el-button>
            </template>
          </el-table-column>
          <!-- Operation Quantity/Weight - 操作数量/操作重量 -->
          <el-table-column v-if="showOriginalColumn" width="150"
            :label="$t('productInventory.label.operationQty') + '/' + $t('productInventory.label.operationWeight')"
            show-overflow-tooltip align="right">
            <template #default="scope">
              <span>{{ scope.row.operationQty }}{{ scope.row.productUnitName }}/{{ scope.row.operationWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
            </template>
          </el-table-column>
          <!-- 操作量/操作转换量【new】 -->
          <!-- Change Type - 变动方式 -->
          <el-table-column width="90" :label="$t('productInventory.label.changeType')" prop="changeTypeName"
            show-overflow-tooltip class-name="my-custom-class">
            <template #default="scope">
              <div class="flex-center-start">
                <div v-if="scope.row.changeType == 1 || scope.row.changeType == 5" class="solid-circle"
                  style="background-color: #29B610"></div>
                <div v-if="scope.row.changeType == 2" class="solid-circle" style="background-color: #FF2338"></div>
                <div v-if="scope.row.changeType == 3 || scope.row.changeType == 4" class="solid-circle"
                  style="background-color: #FF9C00"></div>
                <div>{{ scope.row.changeTypeName }}</div>
              </div>
            </template>
          </el-table-column>
          <!-- Total Quantity/Weight - 总数量/总重量 -->
          <el-table-column v-if="showOriginalColumn" width="400"
            :label="$t('productInventory.label.totalQty') + '/' + $t('productInventory.label.totalWeight') + '：' + totalStockQty + productUnitName + '/' + totalStockWeight + 'Kg'"
            align="center" class-name="my-custom-class">
            <!-- Original Total - 原始总数量/总重量 -->
            <el-table-column width="200" :label="$t('productInventory.label.primeval')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.originalTotalStockQty }}{{ scope.row.productUnitName }}/{{ scope.row.originalTotalStockWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
            <!-- After Change Total - 变动后总数量/总重量 -->
            <el-table-column width="200" :label="$t('productInventory.label.afterChange')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.totalStockQty }}{{ scope.row.productUnitName }}/{{ scope.row.totalStockWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <!-- 总量/总转换量【new】 -->
          <!-- Available Quantity/Weight - 可用数量/可用重量 -->
          <el-table-column v-if="showOriginalColumn" width="420px"
            :label="$t('productInventory.label.availableQty') + '/' + $t('productInventory.label.availableWeight') + '：' + availableStockQty + productUnitName + '/' + availableStockWeight + 'kg'"
            align="center" class-name="my-custom-class">
            <!-- Original Available - 原始可用数量/可用重量 -->
            <el-table-column width="140" :label="$t('productInventory.label.primeval')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.originalAvailableStockQty }}{{ scope.row.productUnitName }}/{{ scope.row.originalAvailableStockWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
            <!-- After Change Available - 变动后可用数量/可用重量 -->
            <el-table-column width="140" :label="$t('productInventory.label.afterChange')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.availableStockQty }}{{ scope.row.productUnitName }}/{{ scope.row.availableStockWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
            <!-- Available Change Amount - 可用数量/可用重量变动量 -->
            <el-table-column width="140" :label="$t('productInventory.label.changeNUm')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.availableStockChangeQty }}{{ scope.row.productUnitName }}/{{ scope.row.availableStockChangeWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <!-- 可用重量/可用转换量【new】 -->
          <!-- Locked Quantity/Weight - 锁定数量/锁定重量 -->
          <el-table-column v-if="showOriginalColumn" width="420"
            :label="$t('productInventory.label.lockedStockQty') + '/' + $t('productInventory.label.lockedStockWeight') + '：' + lockedStockQty + productUnitName + '/' + lockedStockWeight + 'Kg'"
            align="center" class-name="my-custom-class">
            <!-- Original Locked - 原始锁定数量/锁定重量 -->
            <el-table-column width="140" :label="$t('productInventory.label.primeval')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.originalLockedStockQty }}{{ scope.row.productUnitName }}/{{ scope.row.originalLockedStockWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
            <!-- After Change Locked - 变动后锁定数量/锁定重量 -->
            <el-table-column width="140" :label="$t('productInventory.label.afterChange')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.lockedStockQty }}{{ scope.row.productUnitName }}/{{ scope.row.lockedStockWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
            <!-- Locked Change Amount - 锁定数量/锁定重量变动量 -->
            <el-table-column width="140" :label="$t('productInventory.label.changeNUm')" show-overflow-tooltip
              align="center" class-name="my-custom-class">
              <template #default="scope">
                <span>{{ scope.row.lockedStockChangeQty }}{{ scope.row.productUnitName }}/{{ scope.row.lockedStockChangeWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <!-- 锁定量/锁定转换量【new】 -->
          <!-- Warehouse Area - 库区 -->
          <el-table-column :label="$t('productInventory.label.warehouseAreaCode')" prop="warehouseAreaName" width="120"
            show-overflow-tooltip></el-table-column>
          <!-- Operator Name - 操作人 -->
          <el-table-column :label="$t('productInventory.label.operatorName')" prop="operatorName" width="120"
            show-overflow-tooltip></el-table-column>
          <!-- Operation Time - 操作时间 -->
          <el-table-column :label="$t('productInventory.label.operationTime')" prop="operationTime" width="170"
            show-overflow-tooltip>
            <template #default="scope">
              <span>{{ parseDateTime(scope.row.operationTime, "dateTime") }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="logTotal > 0" v-model:total="logTotal" v-model:page="queryLogParams.page"
          v-model:limit="queryLogParams.limit" @pagination="handleQueryLog" />
        <template #footer>
          <div class="dialog-footer" style="padding: 12px 0px;margin-right: 30px">
            <el-button @click="onCloseHandler">{{ $t("productInventory.button.closeBtn") }}</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CascaderProps } from "element-plus";
import { emitter } from "@/core/utils/eventBus";

defineOptions({
  name: "ProductInventory",
  inheritAttrs: false,
});
import { useRouter } from "vue-router";
import productInventoryApi, { ProductInventoryPageQuery, ProductInventoryPageVO, ProductStockLogPageQuery, ProductStockLogPageV0 } from "@/modules/wms/api/productInventory";
import { parseDateTime } from "@/core/utils/index.js";
import CommonAPI from "@/modules/wms/api/common";

const router = useRouter();
const { t } = useI18n();
const queryFormRef = ref(null);
const loading = ref(false);
const productInventoryList = ref<ProductInventoryPageVO[]>();
const total = ref(0);
const categoryList = ref([]);
const propsCategory: CascaderProps = {
  multiple: true,
  checkStrictly: true,
  value: 'id',
  label: 'categoryName',
  children: 'children',
}
const areaList = ref([]);
const logTotal = ref(0);
const showDialog = ref(false);
const logLoading = ref(false);
const logDataList = ref<ProductStockLogPageV0>();
const queryParams = reactive<ProductInventoryPageQuery>({
  page: 1,
  limit: 20,
});
const queryLogParams = reactive<ProductStockLogPageQuery>({
  page: 1,
  limit: 20,
});
const showOriginalColumn = ref(true); // 隐藏原始数据列，方便后续项目使用
const lockedStockQty = ref('');
const availableStockQty = ref('');
const totalStockQty = ref('');
const totalStockWeight = ref('');
const availableStockWeight = ref('');
const lockedStockWeight = ref('');
const productUnitName = ref('');
//操作日志单号跳转详情页面
function toDetail(row) {
  switch (row.operationTypeName) {
    case '损益': {
      //损益单详情
      router.push({
        path: "/wms/inventory/addProfitAndLoss",
        query: { id: row.sourceOrderId, type: 'detail', title: t('profitAndLossManagement.button.profitAndLossDetail') }
      });
      onCloseHandler()
      break;
    }
    case '拆装': {
      //商品拆装单详情
      router.push({
        path: '/wms/insideWarehouseManagement/addProductDisassemblyAssembleOrder',
        query: { id: row.sourceOrderId, type: 'detail', title: t('common.detailBtn') }
      });
      onCloseHandler()
      break;
    }
    case '库存转移': {
      //库存转移单详情
      router.push({
        path: "/wms/insideWarehouseManagement/addInventoryTransfer",
        query: { id: row.sourceOrderId, type: 'detail', title: t('common.detailBtn') }
      });
      onCloseHandler()
      break;
    }
    case '拣货出库': {
      //拣货单详情
      router.push({
        path: "/wms/pickingOrder/detail",
        query: { pickingListCode: row.sourceOrderCode, id: row.sourceOrderId },
      });
      onCloseHandler()
      break;
    }
    case '入库': {
      //入库单详情
      router.push({
        path: "/wms/storeManagement/warehousingEntryOrderDetail",
        query: { id: row.sourceOrderId, entryOrderCode: row.sourceOrderCode },
      });
      onCloseHandler()
      break;
    }
    case '收运': {
      //收运单详情
      router.push({
        path: "/wms/storeManagement/receivingOrderDetail",
        query: { id: row.sourceOrderId, receivingOrderCode: row.sourceOrderCode },
      });
      onCloseHandler()
      break;
    }
    case '分拣': {
      //分拣单详情
      router.push({
        path: "/wms/storeManagement/editPickOrder",
        query: {
          sortingCode: row.sourceOrderCode,
          type: 'detail'
        },
      });
      onCloseHandler()
      break;
    }
    case '出库审核': {
      //出库通知单详情
      router.push({
        path: "/wms/outboundManagement/detailOutboundNotice",
        query: { id: row.sourceOrderId },
      });
      onCloseHandler()
      break;
    }
    case '出库反审': {
      //出库通知单详情
      router.push({
        path: "/wms/outboundManagement/detailOutboundNotice",
        query: { id: row.sourceOrderId },
      });
      onCloseHandler()
      break;
    }
    default: {
      break
    }
  }
}
/** 查询商品分类列表 */
function queryManagerCategoryList(id?: any) {
  CommonAPI.queryCategoryTreeList({}).then((data: any) => {
    categoryList.value = data;
  })
}
function handleChange() {
  let valueArr = queryParams.productCategory
  let firstCategoryIds = [];
  let secondCategoryIds = [];
  let thirdCategoryIds = [];
  if (valueArr && valueArr.length > 0) {
    valueArr.forEach(item => {
      if (item[0]) {
        firstCategoryIds.push(item[0]);
      }
      if (item[1]) {
        secondCategoryIds.push(item[1]);
      }
      if (item[2]) {
        thirdCategoryIds.push(item[2]);
      }
    })
  }
  queryParams.firstCategoryIds = Array.from(new Set(firstCategoryIds));
  queryParams.secondCategoryIds = Array.from(new Set(secondCategoryIds));
  queryParams.thirdCategoryIds = Array.from(new Set(thirdCategoryIds));
}
/** 查询当前仓库的库区下拉数据源 */
function getOutWarehouseAreaList() {
  CommonAPI.getOutWarehouseAreaList().then((data: any) => {
    areaList.value = data;
    if (areaList.value && areaList.value.length > 0) {
      areaList.value.map((item) => {
        item.warehouseArea = item.areaName + '|' + item.areaCode
        return item
      });
    }
  })
}
/** 查询商品库存列表 */
function handleQuery() {
  loading.value = true;
  let params = {
    ...queryParams,
  }
  delete params.productCategory
  productInventoryApi.getProductInventoryPage(params)
    .then((data) => {
      productInventoryList.value = data.records;
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.dateType = 1;
  queryParams.productCategory = [];
  queryParams.firstCategoryIds = [];
  queryParams.secondCategoryIds = [];
  queryParams.thirdCategoryIds = [];
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}
/** 操作日志按钮点击 */
function openLogList(row) {
  queryLogParams.productCode = row.productCode
  queryLogParams.warehouseAreaId = row.warehouseAreaId
  productUnitName.value = row.productUnitName
  handleQueryLog();
  showDialog.value = true
}
/** 操作日志弹框关闭 */
function onCloseHandler() {
  showDialog.value = false
}
/** 查询商品库存操作日志列表 */
function handleQueryLog() {
  logLoading.value = true
  let params = {
    ...queryLogParams
  }
  productInventoryApi.getProductStockLogPage(params)
    .then((data) => {
      logDataList.value = data.records;
      logTotal.value = parseInt(data.total);
      totalStockQty.value = data.totalStockQty ? data.totalStockQty : '-';
      availableStockQty.value = data.availableStockQty ? data.availableStockQty : '-';
      lockedStockQty.value = data.lockedStockQty ? data.lockedStockQty : '-';
      totalStockWeight.value = data.totalStockWeight ? data.totalStockWeight : '-';
      availableStockWeight.value = data.availableStockWeight ? data.availableStockWeight : '-';
      lockedStockWeight.value = data.lockedStockWeight ? data.lockedStockWeight : '-';
    })
    .finally(() => {
      logLoading.value = false
    });
}
onActivated(() => {
  queryManagerCategoryList();
  getOutWarehouseAreaList();
  handleQuery();
});
emitter.on("reloadListByWarehouseId", (e) => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
:deep(.el-button--primary.el-button--default.is-link) {
  color: #762adb;
}

:deep(.el-button--danger.el-button--default.is-link) {
  color: #c00c1d;
}

.productInventory {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    flex-shrink: 0;
  }

  .content-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
<style lang="scss">
.productInventory {
  .el-dialog {
    padding: 0px;

    .el-dialog__header {
      padding: 16px 30px;
      border-bottom: 1px solid #E5E7F3;
      margin-bottom: 45px;
    }

    .el-dialog__body {
      padding: 0px 30px;
    }

    .el-dialog__footer {
      border-top: 1px solid #E5E7F3;
      margin-top: 16px;
    }

    .el-dialog__headerbtn {
      width: 58px;
      height: 58px;

      .el-icon {
        width: 22px;
        height: 22px;
      }

      .el-icon svg {
        width: 22px;
        height: 22px;
      }
    }
  }

  .el-dialog {
    .el-table--border .el-table__cell {
      border-right: none;
    }
  }

  .el-table__header .my-custom-class {
    border-right: var(--el-table-border) !important;
  }

  .solid-circle {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 4px;
  }
}
</style>
