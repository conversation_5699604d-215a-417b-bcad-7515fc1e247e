<template>
  <div class="app-container">
    <div class="inventory-order">
      <el-card shadow="never" class="mt-4">
        <!-- 基本信息 -->
        <div class="basic-info">
          <div class="section-title">{{ $t('inventoryCount.label.basicInformation') }}</div>
          <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('inventoryCount.label.warehouseAreaName') + '*'" prop="warehouseAreaCode">
                  <el-select
                    v-model="formData.warehouseAreaCode"
                    :placeholder="$t('inventoryCount.placeholder.warehouseArea')"
                    filterable
                    @change="handleAreaChange"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="item in warehouseAreaList" 
                      :key="item.areaCode" 
                      :label="item.warehouseArea" 
                      :value="item.areaCode"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
             <!--  <el-col :span="8">
                <el-form-item :label="$t('inventoryCount.label.countType') + '*'" prop="countType">
                  <el-select
                    v-model="formData.countType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    style="width: 100%"
                  >
                    <el-option :label="$t('inventoryCount.countType.visible')" :value="1"></el-option>
                    <el-option :label="$t('inventoryCount.countType.blind')" :value="2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->
            </el-row>
            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item :label="$t('inventoryCount.label.remark')" prop="remark">
                  <el-input
                    v-model="formData.remark"
                    type="textarea"
                    :rows="3"
                    :placeholder="$t('common.placeholder.inputTips')"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 盘点明细 -->
        <div class="inventory-detail mt-4">
          <div class="section-header flex justify-between items-center">
            <div class="section-title">{{ $t('inventoryCount.label.inventoryDetail') }}</div>
            <el-button type="primary" @click="openProductSelector">
              {{ $t('common.add') }}
            </el-button>
          </div>

          <el-table
            v-loading="tableLoading"
            :data="formData.detailList"
            highlight-current-row
            stripe
            class="mt-2"
          >
            <template #empty>
              <Empty />
            </template>
            <el-table-column type="index" :label="$t('common.sort')" width="60" />
            <el-table-column :label="$t('inventoryCount.label.productInfo')" show-overflow-tooltip>
              <template #default="scope">
                <div>{{ scope.row.productCode }}</div>
                <div>{{ scope.row.productName }}</div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('inventoryCount.label.productSpec')" prop="productSpec" show-overflow-tooltip />
            <el-table-column :label="$t('inventoryCount.label.productUnit')" prop="productUnit" show-overflow-tooltip />
            <el-table-column :label="$t('inventoryCount.label.warehouseStockQty')" prop="systemQty" show-overflow-tooltip />
            <el-table-column :label="$t('inventoryCount.label.countQty')" width="180">
              <template #default="scope">
                <el-input-number 
                  v-model="scope.row.countQty"
                  :min="0"
                  :precision="3"
                  @change="calculateDiff(scope.$index)"
                  style="width: 150px"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column :label="$t('inventoryCount.label.diffQty')" show-overflow-tooltip>
              <template #default="scope">
                <span :class="{ 'text-red-500': scope.row.diffQty < 0, 'text-green-500': scope.row.diffQty > 0 }">
                  {{ scope.row.diffQty || 0 }}
                </span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('inventoryCount.label.operation')" width="100">
              <template #default="scope">
                <el-button link type="primary" @click="handleDeleteDetail(scope.$index)">
                  {{ $t('common.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 按钮组 -->
        <div class="button-group mt-6 flex justify-center">
          <el-button @click="goBack">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit">{{ $t('common.save') }}</el-button>
        </div>
      </el-card>
    </div>

    <!-- 商品选择抽屉 -->
    <el-drawer
      v-model="productDialogVisible"
      :title="$t('inventoryCount.title.selectProduct')"
      size="50%"
      destroy-on-close
      direction="rtl"
    >
      <div class="product-selector">
        <div class="search-container px-4 mb-4">
          <el-form :model="productQueryParams" :inline="true">
            <el-form-item :label="$t('inventoryCount.label.productName')">
              <el-input 
                v-model="productQueryParams.productKeyword" 
                :placeholder="$t('inventoryCount.placeholder.productKeyword')"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchProducts">{{ $t('common.search') }}</el-button>
              <el-button @click="resetProductSearch">{{ $t('common.reset') }}</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="px-4">
          <el-table
            v-loading="productLoading"
            :data="productList"
            @selection-change="handleProductSelectionChange"
            height="calc(100vh - 250px)"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" :label="$t('common.sort')" width="60" />
            <el-table-column :label="$t('inventoryCount.label.productCode')" prop="productCode" show-overflow-tooltip />
            <el-table-column :label="$t('inventoryCount.label.productName')" prop="productName" show-overflow-tooltip />
            <el-table-column :label="$t('inventoryCount.label.productSpec')" prop="productSpec" show-overflow-tooltip />
            <el-table-column :label="$t('inventoryCount.label.productUnit')" prop="productUnit" show-overflow-tooltip />
            <el-table-column :label="$t('inventoryCount.label.warehouseStockQty')" prop="stockQty" show-overflow-tooltip />
          </el-table>

          <pagination
            v-model:total="productTotal"
            v-model:page="productQueryParams.pageNum"
            v-model:limit="productQueryParams.pageSize"
            @pagination="searchProducts"
          />
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="productDialogVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmSelectProducts">{{ $t('common.confirm') }}</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
// import PageHeader from '@/components/PageHeader/index.vue';
import InventoryCountApi, { InventoryCountForm, InventoryCountDetailForm } from '@/modules/wms/api/inventoryCount';
import CommonAPI from '@/modules/wms/api/common';
import productInventoryApi from '@/modules/wms/api/productInventory';

defineOptions({
  name: 'InventoryOrderAdd',
  inheritAttrs: false,
});

const router = useRouter();
const { t } = useI18n();
const formRef = ref(null);
const tableLoading = ref(false);
const warehouseAreaList = ref([]);
const productDialogVisible = ref(false);
const productLoading = ref(false);
const productList = ref([]);
const productTotal = ref(0);
const selectedProducts = ref([]);

// 表单数据
const formData = reactive<InventoryCountForm>({
  countType: 1, // 默认明盘
  warehouseCode: '',
  warehouseAreaCode: '',
  countBy: '',
  countTime: new Date().toISOString(),
  remark: '',
  detailList: [],
});

// 表单校验规则
const rules = {
  warehouseAreaCode: [
    { required: true, message: t('common.required'), trigger: 'change' },
  ],
  countType: [
    { required: true, message: t('common.required'), trigger: 'change' },
  ],
};

// 商品查询参数
const productQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  productKeyword: '',
  warehouseAreaCode: '',
});

/** 返回上一页 */
function goBack() {
  router.back();
}

/** 查询库区列表 */
function getWarehouseAreaList() {
  CommonAPI.getOutWarehouseAreaList().then((data: any) => {
    warehouseAreaList.value = data;
    if (warehouseAreaList.value && warehouseAreaList.value.length > 0) {
      warehouseAreaList.value.map((item) => {
        item.warehouseArea = item.areaName + '|' + item.areaCode;
        return item;
      });
    }
  });
}

/** 处理库区变更 */
function handleAreaChange(val) {
  if (val) {
    const area = warehouseAreaList.value.find(item => item.areaCode === val);
    if (area) {
      formData.warehouseCode = area.warehouseCode || '';
      // 变更库区时清空明细
      if (formData.detailList.length > 0) {
        ElMessageBox.confirm(
          t('inventoryCount.message.changeAreaClearDetail'),
          t('common.warning'),
          {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: 'warning',
          }
        ).then(() => {
          formData.detailList = [];
          productQueryParams.warehouseAreaCode = val;
        }).catch(() => {
          // 取消选择，还原之前的库区
          formData.warehouseAreaCode = formData.detailList[0]?.warehouseAreaCode || '';
        });
      } else {
        productQueryParams.warehouseAreaCode = val;
      }
    }
  }
}

/** 打开商品选择器 */
function openProductSelector() {
 /*  if (!formData.warehouseAreaCode) {
    ElMessage.warning(t('inventoryCount.message.selectAreaFirst'));
    return;
  } */
  productDialogVisible.value = true;
  searchProducts();
}

/** 搜索商品 */
function searchProducts() {
  if (!productQueryParams.warehouseAreaCode) {
    productQueryParams.warehouseAreaCode = formData.warehouseAreaCode;
  }
  
  productLoading.value = true;
  productInventoryApi.getProductInventoryPage(productQueryParams)
    .then((res) => {
      productList.value = res.records || [];
      productTotal.value = res.total || 0;
    })
    .finally(() => {
      productLoading.value = false;
    });
}

/** 重置商品搜索 */
function resetProductSearch() {
  productQueryParams.productKeyword = '';
  productQueryParams.pageNum = 1;
  searchProducts();
}

/** 处理商品选择变更 */
function handleProductSelectionChange(selection) {
  selectedProducts.value = selection;
}

/** 确认选择商品 */
function confirmSelectProducts() {
  if (!selectedProducts.value.length) {
    ElMessage.warning(t('inventoryCount.message.selectProductFirst'));
    return;
  }
  
  // 检查是否有重复商品
  const existingProductCodes = formData.detailList.map(item => item.productCode);
  const newDetails = [];
  
  selectedProducts.value.forEach(product => {
    if (!existingProductCodes.includes(product.productCode)) {
      const detail: InventoryCountDetailForm = {
        productCode: product.productCode,
        productName: product.productName,
        productSpec: product.productSpec,
        productUnit: product.productUnit,
        locationCode: '',
        systemQty: product.stockQty || 0,
        countQty: product.stockQty || 0, // 默认与系统数量相同
        diffQty: 0,
        batchNo: product.batchNo || '',
        productionDate: product.productionDate || '',
        expiryDate: product.expiryDate || '',
        remark: '',
      };
      newDetails.push(detail);
      existingProductCodes.push(product.productCode);
    }
  });
  
  formData.detailList = [...formData.detailList, ...newDetails];
  // 计算差异数量
  formData.detailList.forEach((item, index) => {
    calculateDiff(index);
  });
  
  productDialogVisible.value = false;
  selectedProducts.value = [];
}

/** 计算差异数量 */
function calculateDiff(index) {
  const detail = formData.detailList[index];
  if (detail) {
    detail.diffQty = (detail.countQty || 0) - (detail.systemQty || 0);
  }
}

/** 删除明细 */
function handleDeleteDetail(index) {
  formData.detailList.splice(index, 1);
}

/** 提交表单 */
function handleSubmit() {
  formRef.value.validate((valid) => {
    if (!valid) {
      return;
    }
    
    if (!formData.detailList.length) {
      ElMessage.warning(t('inventoryCount.message.emptyDetail'));
      return;
    }
    
    const submitData = JSON.parse(JSON.stringify(formData));
    
    tableLoading.value = true;
    InventoryCountApi.addInventoryCount(submitData)
      .then(() => {
        ElMessage.success(t('common.saveSuccess'));
        goBack();
      })
      .finally(() => {
        tableLoading.value = false;
      });
  });
}

onMounted(() => {
  getWarehouseAreaList();
});
</script>

<style lang="scss" scoped>
.inventory-order {
  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    position: relative;
    padding-left: 12px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #722ed1;
      border-radius: 2px;
    }
  }
  
  .section-header {
    margin-bottom: 16px;
  }
}
</style>
