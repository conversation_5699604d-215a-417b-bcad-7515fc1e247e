<template>
  <div class="app-container">
    <div class="inventory-count-detail">
      <el-card v-loading="loading">
        <!-- 顶部导航 -->
        <div class="card-header mb-24px">
          <img
            src="@/core/assets/images/arrow-left.png"
            alt=""
            class="back-btn"
            @click="handleBack"
          />
          <span class="code" @click="handleBack">
            {{ $t('inventoryCount.label.countNo') }}：{{ basicInfo.checkCode || "--" }}
          </span>
          <!-- 盘点状态 -->
          <!--<span
            class="contract status ml-10px"
            :class="getStatusClass(basicInfo.checkStatus)"
          >
            {{ getStatusText(basicInfo.checkStatus) }}
          </span>-->
        </div>

        <!-- 基本信息 -->
        <div class="section">
          <div class="card-title mb-20px">
            <span>{{ $t('inventoryCount.title.basicInfo') }}</span>
          </div>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  {{ $t('inventoryCount.label.countNo') }}
                </span>
                <span class="form-text">
                  {{ basicInfo.checkCode || "--" }}
                </span>
              </div>
            </el-col>
            <!-- 盘点库区 -->
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点库区
                </span>
                <span class="form-text">
                  {{ basicInfo.warehouseAreaName || "--" }}
                </span>
              </div>
            </el-col>
             <!-- 创建人？ -->
             <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  创建人
                </span>
                <span class="form-text">
                  {{ basicInfo.createUserName || "--" }}
                </span>
              </div>
            </el-col>
             
             <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  创建时间
                </span>
                <span class="form-text">
                  {{ parseDateTime(basicInfo.createTime, "dateTime") || "--" }}
                </span>
              </div>
            </el-col>
            <!-- 状态 -->
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  状态
                </span>
                <span class="form-text">
                  {{ getStatusText(basicInfo.checkStatus) }}
                </span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  是否领单
                </span>
                <span class="form-text">
                  {{ basicInfo.receiptStatus === 0 ? '否' : basicInfo.receiptStatus === 1 ? '是' : '--' }}
                </span>
              </div>
            </el-col>
            <!-- 领单人-->
            <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  领单人
                </span>
                <span class="form-text">
                  {{ basicInfo.handleUserName || "--" }}
                </span>
              </div>
            </el-col>
             <!-- 领单时间 -->
             <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  领单时间
                </span>
                <span class="form-text">
                  {{ parseDateTime(basicInfo.handleTime, "dateTime") || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 库区库存总个数 -->
               <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  库区库存总个数
                </span>
                <span class="form-text">
                  {{ basicInfo.beforeCheckQty || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 库区库存总数量 -->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  库区库存总数量
                </span>
                <span class="form-text">
                  {{ basicInfo.beforeCheckTotalQty || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 盘点后商品个数 -->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点后商品个数
                </span>
                <span class="form-text">
                  {{ basicInfo.afterCheckQty || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 盘点后商品数量 -->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点后商品数量
                </span>
                <span class="form-text">
                  {{ basicInfo.afterCheckTotalQty || "--" }}
                </span>
              </div>
            </el-col>
              <!--处理人  -->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理人
                </span>
                <span class="form-text">
                  {{ basicInfo.operationUser || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 处理时间 -->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理时间
                </span>
                <span class="form-text">
                  {{ parseDateTime(basicInfo.operationTime, "dateTime") || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 处理方式 operationType 1=损益 2=移库 3=关闭-->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理方式
                </span>
                <span class="form-text">
                  {{ getOperationTypeText(basicInfo.operationType) || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 处理意见-->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理意见
                </span>
                <span class="form-text">
                  {{ basicInfo.operationRemark || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 处理单号 -->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理单号
                </span>
                <span class="form-text">
                  {{ basicInfo.operationCode || "--" }}
                </span>
              </div>
            </el-col>
              <!-- 备注-->
              <el-col :span="6">
              <div class="info-item">
                <span class="form-label mr-8px">
                  备注
                </span>
                <span class="form-text">
                  {{ basicInfo.remark || "--" }}
                </span>
              </div>
            </el-col>
            
          <!--   <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  {{ $t('inventoryCount.label.warehouseAreaName') }}
                </span>
                <span class="form-text">
                  {{ basicInfo.warehouseAreaName || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  {{ $t('inventoryCount.label.warehouseAreaLocationName') }}
                </span>
                <span class="form-text">
                  {{ basicInfo.warehouseAreaLocationName || "--" }}
                </span>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理人
                </span>
                <span class="form-text">
                  {{ basicInfo.handleUserName || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理时间
                </span>
                <span class="form-text">
                  {{ parseDateTime(basicInfo.handleTime, "dateTime") || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点状态
                </span>
                <span class="form-text">
                  {{ getStatusText(basicInfo.checkStatus) }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点前商品个数
                </span>
                <span class="form-text">
                  {{ basicInfo.beforeCheckQty || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点后商品个数
                </span>
                <span class="form-text">
                  {{ basicInfo.afterCheckQty || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点前商品总数量
                </span>
                <span class="form-text">
                  {{ basicInfo.beforeCheckTotalQty || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点后商品总数量
                </span>
                <span class="form-text">
                  {{ basicInfo.afterCheckTotalQty || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  处理类型
                </span>
                <span class="form-text">
                  {{ getHandleTypeText(basicInfo.handleType) }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  盘点时间
                </span>
                <span class="form-text">
                  {{ parseDateTime(basicInfo.checkTime, "dateTime") || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  操作方式
                </span>
                <span class="form-text">
                  {{ getOperationTypeText(basicInfo.operationType) }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  操作人
                </span>
                <span class="form-text">
                  {{ basicInfo.operationUserName || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  操作时间
                </span>
                <span class="form-text">
                  {{ parseDateTime(basicInfo.operationTime, "dateTime") || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  操作单号
                </span>
                <span class="form-text">
                  {{ basicInfo.operationCode || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  操作备注
                </span>
                <span class="form-text">
                  {{ basicInfo.operationRemark || "--" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="form-label mr-8px">
                  备注
                </span>
                <span class="form-text">
                  {{ basicInfo.remark || "--" }}
                </span>
              </div>
            </el-col> -->
           
          </el-row>
        </div>

        <!-- 盘点明细 -->
        <div class="section mt-4">
          <div class="card-title mb-20px">
            <span>{{ $t('inventoryCount.title.detailList') }}</span>
          </div>
          <el-table
            :data="detailList"
            border
            highlight-current-row
            stripe
            max-height="400"
          >
            <template #empty>
              <Empty />
            </template>
            <el-table-column type="index" :label="$t('common.sort')" width="60" />
           <!--  <el-table-column
              :label="$t('inventoryCount.label.productCode')"
              prop="productCode"
              show-overflow-tooltip
              min-width="120"
            /> -->
            <el-table-column
              label="商品信息"
              prop="productCode"
              show-overflow-tooltip
              min-width="120"
            >
              <template #default="scope">
                <div>
                  <div>商品编码：{{ scope.row.productCode || '--' }}</div>
                  <div>商品名称：{{ scope.row.productName || '--' }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="规格"
              prop="productSpec"
              show-overflow-tooltip
              min-width="120"
            >
              <template #default="scope">
                <div>
                  <div>{{ scope.row.productSpec }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="单位"
              prop="productUnit"
              show-overflow-tooltip
              min-width="120"
            />
            <el-table-column
              label="库区库存数量"
              prop="beforeCheckTotalQty"
              show-overflow-tooltip
              min-width="120"
            />
            <el-table-column
              label="盘点后数量"
              prop="afterCheckTotalQty"
              show-overflow-tooltip
              min-width="120"
            />
            <el-table-column
              label="数量差值"
              prop="beforeCheckTotalQty"
              show-overflow-tooltip
              min-width="120"
            >
              <template #default="scope">
                <span>
                  {{ scope.row.afterCheckTotalQty - scope.row.beforeCheckTotalQty}}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="'盘点YSN'"
              width="120"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  @click="handleViewYsnDetail(scope.row)"
                  v-hasPerm="['wms:inventory:inventoryOrder:detail:ysn']"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- YSN明细弹窗 -->
      <el-dialog
        v-model="ysnDialogVisible"
        :title="`盘点商品YSN - ${currentDetailRow.productCode} | ${currentDetailRow.productName}`"
        width="600px"
        :before-close="handleCloseYsnDialog"
      >
        <el-table
          v-loading="ysnLoading"
          :data="currentYsnList"
          border
          max-height="400"
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column type="index" :label="$t('common.sort')" width="60" />
          <el-table-column
            label="变动方式"
            prop="ysnCheckStatus"
            show-overflow-tooltip
            min-width="100"
          >
            <template #default="scope">
              <span :style="{ color: getYsnCheckStatusColor(scope.row.ysnCheckStatus) }">
                {{ getYsnCheckStatusSymbol(scope.row.ysnCheckStatus) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="YSN码"
            prop="ysnCode"
            show-overflow-tooltip
            min-width="200"
          />
          <el-table-column
            label="重量(kg)"
            prop="weight"
            show-overflow-tooltip
            min-width="100"
          />
        </el-table>
        
        <!-- 分页 -->
        <div class="mt-4 flex justify-end">
          <el-pagination
            v-model:current-page="ysnQueryParams.page"
            v-model:page-size="ysnQueryParams.limit"
            :total="ysnTotal"
            :page-sizes="[10, 20, 50, 100]"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleYsnPageChange"
            @size-change="handleYsnSizeChange"
          />
        </div>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleCloseYsnDialog">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { parseDateTime } from "@/core/utils/index.js";
import InventoryCountApi, { 
  InventoryCountDetailResponseVO, 
  AppInventoryCheckDetailVO, 
  InventoryCheckDetailYsnVO,
  YsnListPageQuery
} from "@/modules/wms/api/inventoryCount";
import { ElMessage } from "element-plus";

defineOptions({
  name: "InventoryCountDetail",
  inheritAttrs: false,
});

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const basicInfo = ref<any>({});
const detailList = ref<any[]>([]);
const ysnDialogVisible = ref(false);
const currentYsnList = ref<any[]>([]);
const ysnLoading = ref(false);
const ysnTotal = ref(0);
const currentDetailRow = ref<any>({});

// YSN查询参数
const ysnQueryParams = reactive<YsnListPageQuery>({
  page: 1,
  limit: 20,
  checkDetailId: undefined,
  checkCode: '',
  productCode: ''
});

// 获取详情数据
const fetchDetailData = async () => {
  const id = route.query.id as string;
  if (!id) {
    ElMessage.error('缺少盘点单ID');
    return;
  }

  loading.value = true;
  try {
    const response = await InventoryCountApi.getInventoryCountDetail(id);
    const data = response.data || response; // 处理不同的响应格式
    basicInfo.value = data;
    detailList.value = data.detailList || [];
  } catch (error) {
    console.error('获取盘点单详情失败:', error);
    ElMessage.error('获取盘点单详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回上一页
const handleBack = () => {
  router.go(-1);
};

// 获取状态样式类
const getStatusClass = (status: number) => {
  switch (status) {
    case 0:
      return 'unwokring'; // 草稿
    case 1:
      return 'executing'; // 盘点中
    case 2:
      return 'executing'; // 完成
    default:
      return '';
  }
};

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '草稿';
    case 1:
      return '盘点中';
    case 2:
      return '完成';
    default:
      return '--';
  }
};

// 获取处理类型文本
const getHandleTypeText = (handleType: string) => {
  switch (handleType) {
    case '00':
      return '未处理';
    case '10':
      return '损益处理';
    default:
      return '--';
  }
};

// 获取操作方式文本
const getOperationTypeText = (operationType: number) => {
  switch (operationType) {
    case 1:
      return '损益';
    case 2:
      return '移库';
    case 3:
      return '关闭';
    default:
      return '--';
  }
};

// 获取商品分类名称
const getCategoryName = (row: any) => {
  const parts = [];
  if (row.firstCategoryName) parts.push(row.firstCategoryName);
  if (row.secondCategoryName) parts.push(row.secondCategoryName);
  if (row.thirdCategoryName) parts.push(row.thirdCategoryName);
  return parts.length > 0 ? parts.join(' > ') : '--';
};

// 查看YSN明细
const handleViewYsnDetail = async (row: any) => {
  currentDetailRow.value = row;
  ysnQueryParams.checkDetailId = row.id;
  ysnQueryParams.checkCode = row.checkCode || basicInfo.value.checkCode;
  ysnQueryParams.productCode = row.productCode;
  ysnQueryParams.page = 1;
  
  ysnDialogVisible.value = true;
  await fetchYsnList();
};

// 获取YSN列表数据
const fetchYsnList = async () => {
  ysnLoading.value = true;
  try {
    const response = await InventoryCountApi.getYsnList(ysnQueryParams);
    const data = response.data || response;
    currentYsnList.value = data.records || [];
    ysnTotal.value = data.total || 0;
  } catch (error) {
    console.error('获取YSN明细失败:', error);
    ElMessage.error('获取YSN明细失败');
  } finally {
    ysnLoading.value = false;
  }
};

// YSN分页变化
const handleYsnPageChange = (page: number) => {
  ysnQueryParams.page = page;
  fetchYsnList();
};

// YSN每页条数变化  
const handleYsnSizeChange = (size: number) => {
  ysnQueryParams.limit = size;
  ysnQueryParams.page = 1;
  fetchYsnList();
};

// 关闭YSN明细弹窗
const handleCloseYsnDialog = () => {
  ysnDialogVisible.value = false;
  currentYsnList.value = [];
  currentDetailRow.value = {};
  ysnTotal.value = 0;
  ysnQueryParams.page = 1;
  ysnQueryParams.checkDetailId = undefined;
  ysnQueryParams.checkCode = '';
  ysnQueryParams.productCode = '';
};

// 获取YSN盘点状态符号
const getYsnCheckStatusSymbol = (status: number) => {
  switch (status) {
    case 0:
      return '待盘';
    case 1:
      return '盘亏';
    case 2:
      return '正常';
    case 3:
      return '盘盈';
    default:
      return '--';
  }
};

// 获取YSN盘点状态颜色
const getYsnCheckStatusColor = (status: number) => {
  switch (status) {
    case 0:
      return '#52c41a';
    case 1:
      return '#f5222d';
    case 2:
      return '#333';
    case 3:
      return '#fa8c16';
    default:
      return '#333';
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDetailData();
});
</script>

<style lang="scss" scoped>
.inventory-count-detail {
 /*  .card-header {
    display: flex;
    align-items: center;
    
    .back-btn {
      cursor: pointer;
      margin-right: 12px;
      width: 20px;
      height: 20px;
    }
    
    .code {
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      color: #333;
    }
    
    .status {
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      
      &.unwokring {
        background-color: #fff2e8;
        color: #fa8c16;
        border: 1px solid #ffd591;
      }
      
      &.executing {
        background-color: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }
    }
  } */
  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;

    .code {
      font-family: "PingFangSC", "PingFang SC";
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }

    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .status {
      font-family: "PingFangSC", "PingFang SC";
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }
  .section {
    margin-bottom: 24px;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: #722ed1;
        border-radius: 2px;
      }
    }
    
    .info-item {
      margin-bottom: 16px;
      
      .form-label {
        color: #666;
        font-weight: 500;
        min-width: 120px;
        display: inline-block;
      }
      
      .form-text {
        color: #333;
        word-break: break-all;
      }
    }
  }
}
</style>
