<template>
  <div class="app-container">
    <div class="profitAndLossManagement">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
            <el-form-item prop="profitLossCode" :label="$t('profitAndLossManagement.label.profitLossCode')">
              <el-input v-model="queryParams.profitLossCode"
                        :placeholder="$t('profitAndLossManagement.placeholder.codeValideTips')" clearable class="!w-[256px]" />
            </el-form-item>
            <el-form-item prop="profitLossType" :label="$t('profitAndLossManagement.label.profitLossType')">
              <el-select v-model="queryParams.profitLossTypeList" :placeholder="$t('common.placeholder.selectTips')"
                         clearable multiple collapse-tags collapse-tags-tooltip class="!w-[256px]">
                <el-option v-for="item in profitAndLossTypeList" :key="item.typeId" :label="item.typeName"
                           :value="item.typeId"></el-option>
              </el-select>
            </el-form-item>
           <!--  <el-form-item :label="$t('profitAndLossManagement.label.receiptStatus')" prop="receiptStatus">
              <el-select v-model="queryParams.receiptStatus" :placeholder="$t('common.placeholder.selectTips')" clearable
                         class="!w-[256px]">
                <el-option v-for="item in claimTypeList" :key="item.key" :label="item.value"
                           :value="item.key"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item :label="$t('profitAndLossManagement.label.status')" prop="status">
              <el-select v-model="queryParams.statusList" :placeholder="$t('common.placeholder.selectTips')" clearable
                         multiple collapse-tags collapse-tags-tooltip class="!w-[256px]">
                <el-option v-for="item in statusList" :key="item.statusId" :label="item.statusName"
                           :value="item.statusId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="dateRange">
              <el-select v-model="queryParams.dateType" :placeholder="$t('common.placeholder.selectTips')"
                         class="!w-[200px] ml5px">
                <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
              <el-date-picker :editable="false" class="!w-[370px]" v-model="queryParams.dateRange" type="datetimerange"
                              range-separator="~" :start-placeholder="$t('profitAndLossManagement.label.startTime')"
                              :end-placeholder="$t('profitAndLossManagement.label.endTime')" value-format="YYYY-MM-DD HH:mm:ss"
                              :default-time="defaultTime" :placeholder="$t('common.placeholder.selectTips')" />
              <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)"
                    @click="handleChangeDateRange(1)">{{ $t('profitAndLossManagement.label.today') }}</span>
              <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)"
                    @click="handleChangeDateRange(2)">{{ $t('profitAndLossManagement.label.yesterday') }}</span>
              <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)"
                    @click="handleChangeDateRange(3)">{{ $t('profitAndLossManagement.label.weekday') }}</span>
            </el-form-item>
            <el-form-item>
              <el-button v-hasPerm="['wms:inventoryLossInfo:search']" type="primary" @click="handleQuery">
                {{ $t('common.search') }}
              </el-button>
              <el-button v-hasPerm="['wms:inventoryLossInfo:reset']" @click="handleResetQuery">
                {{ $t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <div class="action-bar">
          <el-button v-hasPerm="['wms:inventoryLossInfo:add']" type="primary" @click="addProfitAndLoss(null, 'add')">
            {{ $t('profitAndLossManagement.button.addMaterial') }}
          </el-button>
        </div>

        <el-table v-loading="loading" :data="profitAndLossList" highlight-current-row stripe>
          <template #empty>
            <Empty />
          </template>
          <el-table-column type="index" :label="$t('common.sort')" width="60" fixed="left" />
          <el-table-column :label="$t('profitAndLossManagement.label.profitLossCode')" prop="profitLossCode"
                           min-width="120px" show-overflow-tooltip fixed="left"></el-table-column>
          <el-table-column :label="$t('profitAndLossManagement.label.profitLossType')" prop="profitLossType"
                           min-width="150px" show-overflow-tooltip>
            <template #default="scope">
                <span
                  v-if="scope.row.profitLossType == 0">{{ $t('profitAndLossManagement.profitAndLossTypeList.inventoryCheck') }}</span>
              <span
                v-if="scope.row.profitLossType == 1">{{ $t('profitAndLossManagement.profitAndLossTypeList.singleProduct') }}</span>
              <span
                v-if="scope.row.profitLossType == 2">{{ $t('profitAndLossManagement.profitAndLossTypeList.ysnCode') }}</span>
              <span
                v-if="scope.row.profitLossType == 3">{{ $t('profitAndLossManagement.profitAndLossTypeList.fastProfitAndLoss') }}</span>
            </template>
          </el-table-column>
          <!--          <el-table-column :label="$t('profitAndLossManagement.label.afterProfitLossQty')" prop="afterProfitLossQty" align="right" show-overflow-tooltip></el-table-column>-->
          <el-table-column :label="$t('profitAndLossManagement.label.profitLossTotalQty')" prop="profitLossTotalQty" min-width="140px"
                           align="left" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('profitAndLossManagement.label.totalProfitAndLossConversion')"
                           prop="profitLossTotalWeight" min-width="140px" align="left" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.profitLossTotalWeight != null && scope.row.profitLossTotalWeight != undefined && scope.row.profitLossTotalWeight !== ''">{{ scope.row.profitLossTotalWeight > 0 ? '+' : '' }}{{ scope.row.profitLossTotalWeight }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('profitAndLossManagement.label.warehouseAreaName')" prop="warehouseAreaName" min-width="120px" show-overflow-tooltip></el-table-column>
         <!--  <el-table-column :label="$t('profitAndLossManagement.label.useMessage')" min-width="220px"
                           show-overflow-tooltip>
            <template #default="scope">
              <div>{{ $t('profitAndLossManagement.label.handleUserName') }}：<span
                style="color: #90979E ">{{ scope.row.handleUserName || '-' }}</span></div>
              <div>{{ $t('profitAndLossManagement.label.handleTime') }}：<span style="color: #90979E ">{{
                  parseDateTime(scope.row.handleTime, "dateTime") }}</span></div>
            </template>
          </el-table-column> -->
          <el-table-column :label="$t('profitAndLossManagement.label.remark')" prop="remark" min-width="100px"
                           show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('profitAndLossManagement.label.createUserName')" prop="createUserName"
                           min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('profitAndLossManagement.label.createTime')" prop="createTime" min-width="160px"
                           show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('profitAndLossManagement.label.handlerUserName')" prop="handleUserName"
                           min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('profitAndLossManagement.label.handlerTime')" min-width="160px"
                           show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.handleTime !== undefined">{{ parseDateTime(scope.row.handleTime, "dateTime") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('profitAndLossManagement.label.status')" prop="status"
                           show-overflow-tooltip min-width="120">
            <template #default="scope">
              <div class="purchase">
                  <span class="purchase-status purchase-status-color0"
                        v-if="scope.row.status == 0">{{ $t('profitAndLossManagement.statusList.draft') }}</span>
                <span class="purchase-status purchase-status-color2"
                      v-if="scope.row.status == 1">{{ $t('profitAndLossManagement.statusList.dealWidth') }}</span>
                <span class="purchase-status purchase-status-color3"
                      v-if="scope.row.status == 2">{{ $t('profitAndLossManagement.statusList.finish') }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('common.handle')" width="160">
            <template #default="scope">
              <!-- <el-button v-hasPerm="['wms:inventoryLossInfo:claim']"
                         v-if="scope.row.receiptStatus == 0 && scope.row.status == 0 && (scope.row.profitLossType == 0 || scope.row.profitLossType == 3)"
                         type="primary" link @click="receiveProfitAndLoss(scope.row.id)">
                {{ $t('profitAndLossManagement.button.claim') }}
              </el-button>
              <el-button v-hasPerm="['wms:inventoryLossInfo:cancelClaim']"
                         v-if="scope.row.receiptStatus == 1 && scope.row.status == 0 && (scope.row.profitLossType == 0 || scope.row.profitLossType == 3)"
                         type="primary" link @click="cancelProfitAndLoss(scope.row.id)">
                {{ $t('profitAndLossManagement.button.cancelClaim') }}
              </el-button> -->
             <!--  <el-button v-hasPerm="['wms:inventoryLossInfo:edit']"
                         v-if="scope.row.status == 0 && scope.row.receiptStatus == 1 && userStore.user.userId == scope.row.handleUser && (scope.row.profitLossType == 0 || scope.row.profitLossType == 3)"
                         type="primary" link @click="addProfitAndLoss(scope.row.id, 'edit')">
                {{ $t('profitAndLossManagement.button.inventoryLoss') }}
              </el-button>
              <el-button v-hasPerm="['wms:inventoryLossInfo:detail']" type="primary" v-if="scope.row.status !== 0" link
                         @click="addProfitAndLoss(scope.row.id, 'detail')">
                {{ $t('common.detailBtn') }}
              </el-button>
              <el-button v-hasPerm="['wms:inventoryLossInfo:delete']"
                         v-if="scope.row.receiptStatus == 0 && scope.row.status == 0" type="danger" link
                         @click="handleDelete(scope.row.id)">
                {{ $t('common.delete') }}
              </el-button> -->
              <!-- 去损益 -->
              <el-button v-hasPerm="['wms:inventoryLossInfo:edit']"
                         v-if="scope.row.status == 0"
                         type="primary" link @click="addProfitAndLoss(scope.row.id, 'edit')">
                {{ $t('profitAndLossManagement.button.inventoryLoss') }}
              </el-button>
              <el-button v-hasPerm="['wms:inventoryLossInfo:detail']" type="primary" v-if="scope.row.status === 2" link
                         @click="detailProfitAndLoss(scope.row.id, 'detail')">
                {{ $t('common.detailBtn') }}
              </el-button>
              <el-button v-hasPerm="['wms:inventoryLossInfo:delete']"
                         v-if="scope.row.status == 0" type="danger" link
                         @click="handleDelete(scope.row.id)">
                {{ $t('common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.page"
                      v-model:limit="queryParams.limit" @pagination="handleQuery" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { changeDateRange, parseDateTime, convertToTimestamp } from "@/core/utils/index.js";
import profitAndLossManagementApi, { ProfitAddLossPageQuery, ProfitAddLossPageV0 } from "@/modules/wms/api/profitAndLossManagement";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";
import { useUserStore } from "@/core/store";
import QualityInspectionOrderAPI from "@/modules/wms/api/qualityInspectionOrder";

defineOptions({
  name: "ProfitAndLossManagement",
  inheritAttrs: false,
});

const userStore = useUserStore();
const router = useRouter();
const { t } = useI18n();
const queryFormRef = ref(null);
const loading = ref(false);
const total = ref(0);
const profitAndLossList = ref<ProfitAddLossPageV0[]>();
const queryParams = reactive<ProfitAddLossPageQuery>({
  dateType: 1,
  dateRange: [moment().subtract('days', 29).format('YYYY-MM-DD') + ' 00:00:00', moment(new Date()).format('YYYY-MM-DD') + ' 23:59:59'],
  page: 1,
  limit: 20,
  profitLossTypeList:[],
  statusList: []
});
const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];
const dateTypeList = ref([
  {
    key: 1,
    value: t('profitAndLossManagement.dateTypeList.createDate')
  },
  {
    key: 2,
    value: t('profitAndLossManagement.dateTypeList.handlerDate')
  }
]);
const statusList = ref([
  {
    statusId: 0,
    statusName: t('profitAndLossManagement.statusList.draft')
  },
 /*  {
    statusId: 1,
    statusName: t('profitAndLossManagement.statusList.dealWidth')
  }, */
  {
    statusId: 2,
    statusName: t('profitAndLossManagement.statusList.finish')
  }
]);
const profitAndLossTypeList = ref([
  /* {
    typeId: 0,
    typeName: t('profitAndLossManagement.profitAndLossTypeList.inventoryCheck')
  },
  {
    typeId: 1,
    typeName: t('profitAndLossManagement.profitAndLossTypeList.singleProduct')
  },
  {
    typeId: 2,
    typeName: t('profitAndLossManagement.profitAndLossTypeList.ysnCode')
  }, */
  {
    typeId: 3,
    typeName: t('profitAndLossManagement.profitAndLossTypeList.fastProfitAndLoss')
  },
]);
const claimTypeList = ref([
  {
    key: 0,
    value: t('profitAndLossManagement.claimTypeList.no')
  },
  {
    key: 1,
    value: t('profitAndLossManagement.claimTypeList.yes')
  },
])
/** 时间转换 */
function handleChangeDateRange(val: any) {
  queryParams.dateRange = changeDateRange(val);
}
/** 领用损益单 */
function receiveProfitAndLoss(id?: string) {
  loading.value = true;
  let data = {
    profitLossId: id,
  }
  profitAndLossManagementApi.receiveProfitAndLoss(data)
    .then(() => {
      ElMessage.success(t('profitAndLossManagement.message.receiveOrCancelSucess'));
      handleResetQuery();
    })
    .finally(() => (loading.value = false));
}
/** 取消领用损益单 */
function cancelProfitAndLoss(id?: string) {
  ElMessageBox.confirm(t('profitAndLossManagement.message.receiveOrCancelTips'), t('profitAndLossManagement.message.receiveOrCancelTipTitle'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      let data = {
        profitLossId: id,
      }
      profitAndLossManagementApi.cancelProfitAndLoss(data)
        .then(() => {
          ElMessage.success(t('profitAndLossManagement.message.receiveOrCancelSucess'));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t('profitAndLossManagement.message.receiveOrCancelConcel'));
    }
  );
}

/** 查询损益管理列表 */
function handleQuery() {
  loading.value = true;
  if (queryParams.profitLossCode && queryParams.profitLossCode.length < 4) {
    loading.value = false;
    return ElMessage.error(t("profitAndLossManagement.message.codeValideTips"));
  }
  let params = {
    ...queryParams,
  }
  params.queryTimeType = queryParams.dateType
  if (queryParams.dateRange && queryParams.dateRange.length > 0) {
    params.startTime = convertToTimestamp(queryParams.dateRange[0])
    params.endTime = convertToTimestamp(queryParams.dateRange[1])
  }
  delete params.dateType
  delete params.dateRange
  profitAndLossManagementApi.getProfitAndLossPage(params)
    .then((data) => {
      profitAndLossList.value = data.records;
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.dateType = 1
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.profitLossTypeList = []
  queryParams.statusList = []
  handleQuery();
}
/** 删除 */
function handleDelete(id?: string) {
  ElMessageBox.confirm(t('profitAndLossManagement.message.deleteTips'), t('common.tipTitle'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      let data = {
        profitLossId: id
      }
      profitAndLossManagementApi.deleteInventoryLoss(data)
        .then(() => {
          ElMessage.success(t('profitAndLossManagement.message.deleteSuccess'));
          handleResetQuery();
        })
        .finally(() => { loading.value = false });
    },
    () => {
      ElMessage.info(t('profitAndLossManagement.message.deleteCancel'));
    }
  );
}
/** 新增/编辑*/
function addProfitAndLoss(id?: string, type?: string) {
  router.push({
    path: "/wms/inventory/addProfitAndLoss",
    query: { id: id, type: type, title: type == 'add' ? t('profitAndLossManagement.button.addProfitAndLoss') : type == 'edit' ? t('profitAndLossManagement.button.editProfitAndLoss') : t('profitAndLossManagement.button.profitAndLossDetail') }
  });
}
/** 详情 */
function detailProfitAndLoss(id?: string, type?: string) {
  router.push({
    path: "/wms/inventory/detailProfitAndLoss",
    query: { id: id, type: type, title: t('profitAndLossManagement.button.profitAndLossDetail') }
  });
}
onActivated(() => {
  handleQuery();
});
emitter.on("reloadListByWarehouseId", (e) => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .profitAndLossManagement {
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }

    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .action-bar {
        margin-bottom: 12px;
        flex-shrink: 0;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
</style>
