<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="850px" @close="close" class="add-product">
        <div>
            <el-table
                    v-loading="loading"
                    :data="lossInfoList"
                    highlight-current-row
                    stripe
            >
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column :label="$t('profitAndLossManagement.label.changeType')">
                    <template #default="scope">
                      <div class="purchase">
                        <span class="purchase-status purchase-status-color5"
                              v-if="scope.row.ysnCheckStatus==1">-</span>
                        <span class="purchase-status purchase-status-color1"
                              v-if="scope.row.ysnCheckStatus==2">=</span>
                        <span class="purchase-status purchase-status-color3"
                              v-if="scope.row.ysnCheckStatus==3">+</span>
                      </div>
                        <!--<span v-if="scope.row.ysnCheckStatus==1">-</span>
                        <span v-if="scope.row.ysnCheckStatus==2">=</span>
                        <span v-if="scope.row.ysnCheckStatus==3">+</span>-->
                    </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.product')" show-overflow-tooltip min-width="150px">
                    <template #default="scope">
                        <div>{{ lossInfoFrom.productCode}}</div>
                        <div>{{ lossInfoFrom.productName}}</div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('profitAndLossManagement.label.ysnCode')" prop="ysnCode" show-overflow-tooltip min-width="200px"/>
                <el-table-column :label="$t('profitAndLossManagement.label.weight')" prop="weight" align="right" show-overflow-tooltip/>
            </el-table>
            <pagination
                    v-if="lossInfoTotal > 0"
                    v-model:total="lossInfoTotal"
                    v-model:page="lossInfoFrom.page"
                    v-model:limit="lossInfoFrom.limit"
                    @pagination="getLossInfoPage"
            />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup lang="ts">
    import profitAndLossManagementApi, {LossInfoPageVO,LossInfoPageQuery} from "@/modules/wms/api/profitAndLossManagement";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();
    const loading = ref(false);
    const lossInfoTotal = ref(0);
    const lossInfoList = ref<LossInfoPageVO[]>()
    let lossInfoFrom = reactive<LossInfoPageQuery>({
        page: 1,
        limit: 20,
    });

    function close() {
        emit("update:visible", false);
        lossInfoFrom.page = 1;
        lossInfoFrom.limit = 20;
    }

    function getLossInfoPage(){
        loading.value = true;
        let data = {
            ...lossInfoFrom,
        }
        profitAndLossManagementApi.getLossInfoPage(data)
            .then((data) => {
                lossInfoList.value = data.records;
                lossInfoTotal.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    function setFormData(data) {
        lossInfoFrom.checkDetailId = data.queryParams.checkDetailId;
        lossInfoFrom.checkCode = data.queryParams.checkCode;
        lossInfoFrom.productCode = data.queryParams.productCode;
        lossInfoFrom.productName = data.queryParams.productName;
        getLossInfoPage()
    }

    defineExpose({
        setFormData
    });
</script>

<style scoped lang="scss">
</style>
<style lang="scss">
</style>
