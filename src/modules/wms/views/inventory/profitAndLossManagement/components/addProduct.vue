<template>
  <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="850px" @close="close"
    class="add-product">
    <div>
      <el-form ref="productFromRef" :model="productFrom" :inline="true">
        <el-form-item prop="productCategory" :label="$t('profitAndLossManagement.label.productCategory')">
          <el-cascader v-model="productFrom.productCategory" :options="categoryList" :props="propsCategory"
            @change="handleChange" ref="cascaderRef" filterable class="!w-[256px]" collapse-tags collapse-tags-tooltip
            :placeholder="$t('profitAndLossManagement.placeholder.productCategory')" clearable />
        </el-form-item>
        <el-form-item prop="productName" :label="$t('profitAndLossManagement.label.product')">
          <el-input v-model="productFrom.productName" :placeholder="$t('profitAndLossManagement.placeholder.product')"
            clearable class="!w-[256px]" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryProductAll">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="reset">
            {{ $t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
      <div class="select-div">{{ $t("profitAndLossManagement.message.selectNumTips1") }}<span class="select-num">{{
        multipleSelectionProduct.length }}</span>{{
            $t("profitAndLossManagement.message.selectNumTips2") }}</div>
      <el-table v-loading="loading" :data="productTable" highlight-current-row stripe
        @selection-change="handleSelectionProductChange">
        <el-table-column type="selection" width="60" align="center" />
        <el-table-column :label="$t('profitAndLossManagement.label.productInformation')" min-width="150"
          show-overflow-tooltip>
          <template #default="scope">
            <div class="product-div">
              <div class="product">
                <div class="product-code">
                  <span class="product-key">{{ $t('profitAndLossManagement.label.productCode') }}：</span>
                  <span class="product-value">{{ scope.row.productCode }}</span>
                </div>
                <div class="product-name">{{ scope.row.productName }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('profitAndLossManagement.label.productCategory')" prop="fullCategoryName"
          show-overflow-tooltip />
        <el-table-column :label="$t('profitAndLossManagement.label.productSpec')" prop="productSpec"
          show-overflow-tooltip align="right" />
      </el-table>
      <pagination v-if="productTotal > 0" v-model:total="productTotal" v-model:page="productFrom.page"
        v-model:limit="productFrom.limit" @pagination="queryProductAll" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm"
          :disabled="multipleSelectionProduct.length == 0">{{ $t("common.confirm") }}</el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { CascaderProps } from 'element-plus';
import CommonAPI, { ProductAllPageQuery, ProductAllPageVO } from "@/modules/wms/api/common";
import ProductMgAPI from "@/modules/wms/api/productManagement"
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();
const ids = ref([])
const submitLoading = ref(false)
const loading = ref(false);
const productTotal = ref(0);
const productFromRef = ref();
const multipleSelectionProduct = ref([]);
const productTable = ref<ProductAllPageVO[]>()
const cascaderRef = ref();
const categoryList = ref([])
const propsCategory: CascaderProps = {
  multiple: true,
  checkStrictly: true,
  value: 'id',
  label: 'categoryName',
  children: 'children',
}
let productFrom = reactive<ProductAllPageQuery>({
  page: 1,
  limit: 20,
  isSku: 1, // 是否sku->1:是;0:否
});

function close() {
  emit("update:visible", false);
  productFrom.productName = '';
  productFrom.productCategory = [];
  productFrom.firstCategoryIds = [];
  productFrom.secondCategoryIds = [];
  productFrom.thirdCategoryIds = [];
  productFrom.page = 1;
  productFrom.limit = 20;
}

function reset() {
  productFrom.productName = '';
  productFrom.productCategory = [];
  productFrom.firstCategoryIds = [];
  productFrom.secondCategoryIds = [];
  productFrom.thirdCategoryIds = [];
  productFrom.page = 1;
  productFrom.limit = 20;
  queryProductAll()
}

function handleChange() {
  let valueArr = productFrom.productCategory
  let firstCategoryIds = [];
  let secondCategoryIds = [];
  let thirdCategoryIds = [];
  if (valueArr && valueArr.length > 0) {
    valueArr.forEach(item => {
      if (item[0]) {
        firstCategoryIds.push(item[0]);
      }
      if (item[1]) {
        secondCategoryIds.push(item[1]);
      }
      if (item[2]) {
        thirdCategoryIds.push(item[2]);
      }
    })
  }
  productFrom.firstCategoryIds = Array.from(new Set(firstCategoryIds));
  productFrom.secondCategoryIds = Array.from(new Set(secondCategoryIds));
  productFrom.thirdCategoryIds = Array.from(new Set(thirdCategoryIds));
  console.log('firstCategoryIds' + productFrom.firstCategoryIds)
  console.log('secondCategoryIds' + productFrom.secondCategoryIds)
  console.log('thirdCategoryIds' + productFrom.thirdCategoryIds)
  /* if(cascaderRef.value.getCheckedNodes()){
       let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues
       productFrom.firstCategoryId = valueArr[0];
       productFrom.secondCategoryId = valueArr[1];
       productFrom.thirdCategoryId = valueArr[2];
       productFrom.productCategory = valueArr;
   }*/
}

function handleSelectionProductChange(val) {
  multipleSelectionProduct.value = val;
}

function submitForm() {
  submitLoading.value = true;
  const collection = multipleSelectionProduct.value
  close();
  submitLoading.value = false;
  emit("onSubmit", collection);
}

function queryProductAll() {
  loading.value = true;
  submitLoading.value = true
  let data = {
    ...productFrom
  }
  delete data.productCategory
  ProductMgAPI.getPageList(data)
    .then((data) => {
      productTable.value = []
      if (data.records && data.records.length > 0) {
        data.records.forEach(data => {
          let obj = {
            productName: data.productName,
            productCode: data.productCode,
            productSpec: data.productSpec,
            fullCategoryName: data.fullCategoryName,
            productUnit: data.productUnitName,
          }
          productTable.value.push(obj)
        })
      }
      productTotal.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
      submitLoading.value = false
    });
}

function setFormData(data) {
  productTable.value = data.productAllList;
  productTotal.value = data.productTotal;
  productFrom.warehouseAreaId = data.queryParams.warehouseAreaId;
  productFrom.productName = '';
  productFrom.productCategory = [];
  productFrom.firstCategoryId = '';
  productFrom.secondCategoryId = '';
  productFrom.thirdCategoryId = '';
}


/** 查询商品分类列表 */
function queryManagerCategoryList(id?: any) {
  CommonAPI.queryCategoryTreeList({}).then((data: any) => {
    categoryList.value = data;
  })
}

defineExpose({
  setFormData,
  queryManagerCategoryList,
});
</script>

<style scoped lang="scss">
.add-product {
  .supplier-div {
    width: calc(100% - 170px);
  }

  .product-div {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .picture {
      margin-right: 16px;

      img {
        width: 80px;
        height: 80px;
      }
    }

    .product {
      font-family: PingFangSC, PingFang SC;
      font-style: normal;

      .product-code {
        font-weight: 400;
        font-size: 14px;
        color: #90979E;
      }

      .product-name {
        font-weight: 500;
        font-size: 14px;
        color: #52585F;
      }
    }
  }

  .select-div {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #52585F;
    font-style: normal;
    margin-bottom: 16px;

    .select-num {
      color: var(--el-color-primary)
    }
  }
}
</style>
<style lang="scss"></style>
