<template>
    <div class="app-container">
        <div class="qualityInspectionOrder">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="98px">
                    <el-form-item prop="inspectionCode" :label="$t('qualityInspectionOrder.label.inspectionCode')">
                        <el-input
                                v-model="queryParams.inspectionCode"
                                :placeholder="$t('qualityInspectionOrder.placeholder.inspectionCode')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="inspectionTypeList" :label="$t('qualityInspectionOrder.label.inspectionType')">
                        <el-select
                                v-model="queryParams.inspectionTypeList"
                                :placeholder="$t('common.placeholder.selectTips')"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in inspectionTypeList" :key="item.inspectionTypeId" :label="item.inspectionTypeName" :value="item.inspectionTypeId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('qualityInspectionOrder.label.status')" prop="statusList">
                        <el-select
                                v-model="queryParams.statusList"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in statusList" :key="item.statusId" :label="item.statusName" :value="item.statusId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('qualityInspectionOrder.label.receivingStatus')" prop="receivingStatus">
                        <el-select
                                v-model="queryParams.receivingStatus"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in claimTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('qualityInspectionOrder.label.inspectionResultType')" prop="inspectionResultType">
                        <el-select
                                v-model="queryParams.inspectionResultType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in inspectionResultTypeList" :key="item.statusId" :label="item.statusName" :value="item.statusId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="dateRange">
                        <el-select
                                v-model="queryParams.dateType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[200px] ml5px"
                        >
                            <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker
                                :editable="false"
                                class="!w-[370px]"
                                v-model="queryParams.dateRange"
                                type="datetimerange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                :default-time="defaultTime"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                        <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(1)">{{$t('qualityInspectionOrder.label.today')}}</span>
                        <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(2)">{{$t('qualityInspectionOrder.label.yesterday')}}</span>
                        <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(3)">{{$t('qualityInspectionOrder.label.weekday')}}</span>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:reset']" @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card shadow="never" class="table-container">
                <template #header>
                    <el-button  v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:add']" type="primary" @click="addQualityInspectionOrder(null,'add')">
                        {{$t('qualityInspectionOrder.button.addQualityInspectionOrder')}}
                    </el-button>
                </template>

                <el-table
                        v-loading="loading"
                        :data="QualityInspectionOrderList"
                        @selection-change="handleSelectionChange"
                        highlight-current-row
                        stripe
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="index" :label="$t('common.sort')" width="60" />
                    <el-table-column :label="$t('qualityInspectionOrder.label.inspectionCode')" prop="inspectionCode" show-overflow-tooltip min-width="180px"></el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.inspectionType')" prop="inspectionType" show-overflow-tooltip min-width="100px">
                        <template #default="scope">
                            <span v-if="scope.row.inspectionType==0">{{$t('qualityInspectionOrder.inspectionTypeList.collectionTransportationQualityInspection')}}</span>
                            <span v-if="scope.row.inspectionType==1">{{$t('qualityInspectionOrder.inspectionTypeList.inWarehouseQualityInspection')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.receivingOrderCode')" prop="receivingOrderCode" show-overflow-tooltip min-width="190px"></el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.warehouseArea')" show-overflow-tooltip min-width="150px">
                        <template #default="scope">
                            <span v-if="scope.row.warehouseAreaCode">{{scope.row.warehouseAreaName}} | {{scope.row.warehouseAreaCode}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.inspectionResultType')" show-overflow-tooltip min-width="100px">
                        <template #default="scope">
                            <div v-if="scope.row.inspectionResultType==1" class="circle-div">
                                <div class="circle circle-color0"></div>
                                <div>{{t('qualityInspectionOrder.inspectionResultTypeList.abnormal')}}</div>
                            </div>
                            <div v-if="scope.row.inspectionResultType==0" class="circle-div">
                                <div class="circle circle-color1"></div>
                                <div>{{t('qualityInspectionOrder.inspectionResultTypeList.NoExceptions')}}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.qualityInspectionInfo')" show-overflow-tooltip min-width="200px">
                        <template #default="scope">
                                <div>{{t('qualityInspectionOrder.label.productCount')}}:{{scope.row.productCount}}</div>
                                <div>{{t('qualityInspectionOrder.label.productWeight')}}:{{scope.row.productWeight}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.receivingInfo')" prop="inspector" show-overflow-tooltip min-width="240px">
                        <template #default="scope">
                            <div>{{t('qualityInspectionOrder.label.receivingUserName')}}:{{scope.row.receivingUserName}}</div>
                            <div>{{t('qualityInspectionOrder.label.receivingTime')}}:{{ parseDateTime(scope.row.receivingTime, "dateTime") }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.inspectionTime')" prop="inspectionTime" show-overflow-tooltip min-width="180px">
                        <template #default="scope">
                            <span v-if="scope.row.inspectionTime">{{ parseDateTime(scope.row.inspectionTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.inspector')" prop="inspector" show-overflow-tooltip min-width="100px"></el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.remark')" prop="remark" show-overflow-tooltip min-width="150px"></el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.createType')" show-overflow-tooltip min-width="100px">
                        <template #default="scope">
                                <span v-if="scope.row.createType==1">{{$t('qualityInspectionOrder.createTypeList.createManually')}}</span>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column :label="$t('qualityInspectionOrder.label.workOrderNum')" prop="workOrderNum" show-overflow-tooltip min-width="150px"></el-table-column> -->
                    <el-table-column :label="$t('qualityInspectionOrder.label.createUserName')" prop="createUserName" show-overflow-tooltip min-width="100px"/>
                    <el-table-column :label="$t('qualityInspectionOrder.label.createTime')" prop="createTime" show-overflow-tooltip min-width="180px">
                        <template #default="scope">
                            <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('qualityInspectionOrder.label.status')" prop="status" show-overflow-tooltip min-width="100px">
                        <template #default="scope">
                            <div class="purchase">
                                <span class="purchase-status purchase-status-color0" v-if="scope.row.status==1">{{$t('qualityInspectionOrder.statusList.draft')}}</span>
                                <span class="purchase-status purchase-status-color3" v-if="scope.row.status==2">{{$t('qualityInspectionOrder.statusList.QualityInspectionFinish')}}</span>
                                <span class="purchase-status purchase-status-color2" v-if="scope.row.status==3">{{$t('qualityInspectionOrder.statusList.qualityInspectioning')}}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="180">
                        <template #default="scope">
                            <el-button
                                    v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:claim']"
                                    v-if="scope.row.receivingStatus==0 && scope.row.status==1"
                                    type="primary"
                                    link
                                    @click="receiveOrCancelQualityInspectionOrder(scope.row.id,1)"
                            >
                                {{$t('qualityInspectionOrder.button.claim')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:cancelClaim']"
                                    v-if="scope.row.receivingStatus==1 && scope.row.status==1"
                                    type="primary"
                                    link
                                    @click="receiveOrCancelQualityInspectionOrder(scope.row.id,0)"
                            >
                                {{$t('qualityInspectionOrder.button.cancelClaim')}}
                            </el-button>
                            <!-- 质检 -->
                            <el-button
                                    v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:edit']"
                                    v-if="scope.row.status==1 && scope.row.receivingStatus==1 && userStore.user.userId==scope.row.receivingUserId"
                                    type="primary"
                                    link
                                    @click="addQualityInspectionOrder(scope.row.id,'edit', scope.row.status)"
                            >
                                {{$t('qualityInspectionOrder.button.qualityInspection')}}
                            </el-button>
                            <!-- 暂时放开质检按钮权限控制 -->
                           <!--  <el-button
                                
                                    type="primary"
                                    link
                                    @click="addQualityInspectionOrder(scope.row.id,'edit')"
                            >
                                {{$t('qualityInspectionOrder.button.qualityInspection')}}
                            </el-button> -->

                            <el-button
                                    v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:detail']"
                                    v-if="scope.row.status!==1"
                                    type="primary"
                                    link
                                    @click="addQualityInspectionOrder(scope.row.id,'detail')"
                            >
                                {{$t('common.detailBtn')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['wms:insideWarehouseManagement:qualityInspectionOrder:delete']"
                                    v-if="scope.row.receivingStatus==0 && scope.row.status==1"
                                    type="danger"
                                    link
                                    @click="handleDelete(scope.row.id)"
                            >
                                {{$t('common.delete')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "QualityInspectionOrder",
        inheritAttrs: false,
    });

    import {changeDateRange, convertToTimestamp, parseDateTime} from "@/core/utils/index.js";
    import QualityInspectionOrderAPI, { QualityInspectionOrderPageVO, QualityInspectionOrderPageQuery} from "@/modules/wms/api/qualityInspectionOrder";
    import {IObject} from "@/core/components/CURD/types";
    import {useRouter} from "vue-router";
    import moment from "moment";
    import { emitter } from "@/core/utils/eventBus";
    import {useUserStore} from "@/core/store";
import { Row } from "element-plus/es/components/table-v2/src/components";

    const userStore = useUserStore();
    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const loading = ref(false);
    const total = ref(0);
    const inspectionTypeList = ref([
        {
            inspectionTypeId: 0,
            inspectionTypeName: t('qualityInspectionOrder.inspectionTypeList.collectionTransportationQualityInspection')
        },
        {
            inspectionTypeId: 1,
            inspectionTypeName:t('qualityInspectionOrder.inspectionTypeList.inWarehouseQualityInspection')
        },
    ])
    const statusList = ref([
        {
            statusId: 1,
            statusName: t('qualityInspectionOrder.statusList.draft')
        },
        {
            statusId: 3,
            statusName:t('qualityInspectionOrder.statusList.qualityInspectioning')
        },
        {
            statusId: 2,
            statusName:t('qualityInspectionOrder.statusList.QualityInspectionFinish')
        },
    ])
    const inspectionResultTypeList = ref([
        {
            statusId: 1,
            statusName: t('qualityInspectionOrder.inspectionResultTypeList.abnormal')
        },
        {
            statusId: 0,
            statusName:t('qualityInspectionOrder.inspectionResultTypeList.NoExceptions')
        },
    ])
    const createTypeList = ref([
        {
            statusId: 1,
            statusName: t('qualityInspectionOrder.createTypeList.createManually')
        },
    ])
    const dateTypeList = ref([
        {
            key: 1,
            value: t('qualityInspectionOrder.dateTypeList.createDate')
        },
        {
            key: 3,
            value:t('qualityInspectionOrder.dateTypeList.claimDate')
        },
        {
            key: 2,
            value:t('qualityInspectionOrder.dateTypeList.qualityInspectionDate')
        },
    ])

    const claimTypeList = ref([
        {
            key: 0,
            value:t('qualityInspectionOrder.claimTypeList.no')
        },
        {
            key: 1,
            value: t('qualityInspectionOrder.claimTypeList.yes')
        },
    ])
    const queryParams = reactive<QualityInspectionOrderPageQuery>({
        dateType:1,
        dateRange: [moment().subtract('days', 29).startOf("days").format('YYYY-MM-DD HH:mm:ss'), moment().endOf("days").format("YYYY-MM-DD HH:mm:ss")],
        page: 1,
        limit: 20,
    });
    const defaultTime: [Date, Date] = [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
    ]
    const QualityInspectionOrderList = ref<QualityInspectionOrderPageVO[]>();

    /** 时间转换 */
    function handleChangeDateRange(val: any) {
        queryParams.dateRange = changeDateRange(val);
    }

    /** 查询 */
    function handleQuery() {
        if (queryParams.inspectionCode && queryParams.inspectionCode.length < 4) {
            return ElMessage.error(t("qualityInspectionOrder.message.codeValideTips"));
        }
        loading.value = true;
        let params = {
            queryStartTime: undefined,
            queryEndTime: undefined,
            queryType:queryParams.dateType,
            ...queryParams,
        }
        if(queryParams.dateRange && queryParams.dateRange.length>0){
            params.queryStartTime=convertToTimestamp(queryParams.dateRange[0]+ ' 00:00:00')
            params.queryEndTime=convertToTimestamp(queryParams.dateRange[1] + ' 23:59:59')
        }
        delete params.dateType
        delete params.dateRange
        QualityInspectionOrderAPI.getQualityInspectionOrderPage(params)
            .then((data) => {
                QualityInspectionOrderList.value = data.records;
                total.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery(clearForm: boolean = true) {
        if(clearForm){ // 默认清空表单
            queryFormRef.value?.resetFields();
            queryParams.dateType=1
        }
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    /** 删除 */
    function handleDelete(id?: string) {
        ElMessageBox.confirm(t('qualityInspectionOrder.message.deleteTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                loading.value = true;
                let data = {
                    id:id
                }
                QualityInspectionOrderAPI.deleteQualityInspectionOrder(data)
                    .then(() => {
                        ElMessage.success(t('qualityInspectionOrder.message.deleteSucess'));
                        handleResetQuery(false);
                    })
                    .finally(() => (loading.value = false));
            },
            () => {
                ElMessage.info(t('qualityInspectionOrder.message.deleteConcel'));
            }
        );
    }

    /** 领用或取消领用质检单 */
    function receiveOrCancelQualityInspectionOrder(id?: string,receivingStatus?:number) {
        if(receivingStatus==0){
            ElMessageBox.confirm(t('qualityInspectionOrder.message.receiveOrCancelTips'), t('qualityInspectionOrder.message.receiveOrCancelTipTitle'), {
                confirmButtonText: t('common.confirm'),
                cancelButtonText: t('common.cancel'),
                type: "warning",
            }).then(
                () => {
                    receiveOrCancelQualityInspectionOrderDo(id,receivingStatus)
                },
                () => {
                    ElMessage.info(t('qualityInspectionOrder.message.receiveOrCancelConcel'));
                }
            );
        }else{
            receiveOrCancelQualityInspectionOrderDo(id,receivingStatus)
        }
    }

    function receiveOrCancelQualityInspectionOrderDo(id,receivingStatus) {
        loading.value = true;
        let data = {
            id:id,
            receivingStatus:receivingStatus
        }
        QualityInspectionOrderAPI.receiveOrCancelQualityInspectionOrder(data)
            .then(() => {
                ElMessage.success(receivingStatus==0?t('qualityInspectionOrder.message.receiveOrCancelSucess'):t('qualityInspectionOrder.message.receiveSucess'));
                handleResetQuery(false);
            })
            .finally(() => (loading.value = false));
    }

    /** 新增/编辑/详情*/
    function addQualityInspectionOrder(id?:string,type?:string,status?:number){
        router.push({
            path: "/wms/insideWarehouseManagement/addQualityInspectionOrder",
            query: {id:id,type:type,title:type=='add'?t('qualityInspectionOrder.button.addQualityInspectionOrder'):type=='edit'?t('qualityInspectionOrder.button.editQualityInspectionOrder'):t('common.detailBtn'),status:status}
        });
    }

    onActivated(() => {
        handleQuery();
    });

    emitter.on("reloadListByWarehouseId", (e) => {
        nextTick(() => {
            handleQuery();
        });
    });

</script>

<style lang="scss" scoped>
    .qualityInspectionOrder{
        .circle-div{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .circle{
                width: 6px;
                height: 6px;
                border-radius: 3px;
                margin-right: 7px;
            }
            .circle-color1{
                background: #29B610;
            }
            .circle-color0{
                background: #FF9A00 ;
            }
        }
    }
</style>
