<template>
  <div class="app-container">
    <div class="addReportLossOrder" v-loading="loading">
      <div>
        <div class="page-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon>
              <Back />
            </el-icon>
          </div>
          <div>
            <span>
              {{ $t("reportLossOrder.button.addReportLossOrder") }}
            </span>
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="fromRef"
          label-width="98px"
          label-position="right"
        >
          <div class="title-lable">
            <div class="title-content">
              {{ $t("reportLossOrder.label.basicInformation") }}
            </div>
          </div>
          <!--新增、编辑-->
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item
                  :label="$t('reportLossOrder.label.orderType')"
                  prop="orderType"
                >
                  <el-select
                    v-model="form.orderType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    filterable
                    class="!w-[256px]"
                    @change="handleChangeOrderType"
                  >
                    <el-option
                      v-for="item in reportLossOrderTypeList"
                      :key="item.reportLossOrderTypeId"
                      :label="item.reportLossOrderTypeName"
                      :value="item.reportLossOrderTypeId"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 拆装单 -->
                <el-form-item
                  v-if="form.orderType === 1"
                  :label="$t('reportLossOrder.label.sourceOrderCode')"
                  prop="sourceOrderCode"
                  :rules="[{required: true, message: '请选择拆装单号', trigger: ['blur', 'change']}]"
                >
                  <el-select
                    v-model="form.sourceOrderCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    filterable
                    @change="changeSourceOrderCode"
                    :disabled="type == 'edit'"
                    class="!w-[256px]"
                  >
                    <el-option
                      v-for="item in disassemblyAssembleOrderList"
                      :key="item.code"
                      :label="item.code"
                      :value="item.code"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <!-- 分拣单 -->
                <el-form-item
                  v-if="form.orderType == 2"
                  :label="$t('reportLossOrder.label.pickingOrderCode')"
                  prop="sourceOrderCode"
                  :rules="[{required: true, message: '请选择分拣单号', trigger: ['blur', 'change']}]"
                >
                  <el-select
                    v-model="form.sourceOrderCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    filterable
                    @change="changeSourceOrderCode"
                    :disabled="type == 'edit'"
                    class="!w-[256px]"
                  >
                    <el-option
                      v-for="item in disassemblyAssembleOrderList"
                      :key="item.code"
                      :label="item.code"
                      :value="item.code"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item
                  :label="$t('reportLossOrder.label.remark')"
                  prop="remark"
                >
                  <el-input
                    :rows="4"
                    type="textarea"
                    show-word-limit
                    v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="200"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="line"></div>
          <div class="title-lable" style="justify-content: space-between">
            <div class="title-content">
              {{ $t("reportLossOrder.label.reportLossInformation") }}
            </div>
          </div>
          <!--     拆装报损明细     -->
          <div v-if="form.orderType == 1">
            <el-table :data="form.productList" highlight-current-row stripe>
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
              />
              <!--商品信息-->
              <el-table-column
                :label="$t('reportLossOrder.label.productInformation')"
                show-overflow-tooltip
                min-width="150px"
              >
                <template #default="scope">
                  <div class="product-div">
                    <div class="product">
                      <div class="product-name">
                        {{ scope.row.productName }}
                      </div>
                      <div>
                        <span class="product-key">
                          {{ $t("reportLossOrder.label.productCode") }}：
                        </span>
                        <span class="product-value">
                          {{ scope.row.productCode }}
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!--规格-->
              <el-table-column
                :label="$t('reportLossOrder.label.productSpec')"
                prop="productSpec"
                show-overflow-tooltip
                min-width="100px"
              />
              <el-table-column
                :label="'单位'"
                prop="productUnitName"
                show-overflow-tooltip
                width="100px"
              />
            
              <!--出库库区-->
              <el-table-column
                :label="$t('reportLossOrder.label.warehouseArea')"
                show-overflow-tooltip
                min-width="150px"
                v-if="form.orderType === 1"
              >
                <template #default="scope">
                  <span>{{ scope.row.warehouseAreaName }}</span>
                  <span>|</span>
                  <span>{{ scope.row.warehouseAreaId }}</span>
                </template>
              </el-table-column>
              <!--出库数量-->
              <el-table-column
                :label="$t('reportLossOrder.label.disassemblyOutQty')"
                prop="disassemblyOutQty"
                show-overflow-tooltip
                min-width="100px"
              />
              <el-table-column
                :label="'出库重量(Kg)'"
                show-overflow-tooltip
                min-width="100px"
                prop="disassemblyOutWeight"
              ></el-table-column>
              <!-- 新增\编辑 -->
              <el-table-column
                :label="'报损重量(Kg)'"
                show-overflow-tooltip
                min-width="100px"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.lossQty"
                    placeholder="请输入报损重量"
                    clearable
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('common.handle')"
                show-overflow-tooltip
                min-width="100px"
              >
                <template #default="scope">
                  <el-switch
                    :active-text="$t('reportLossOrder.label.activeBtn')"
                    :inactive-text="$t('reportLossOrder.label.inactiveBtn')"
                    inline-prompt
                    v-model="scope.row.lossStatus"
                    :active-value="1"
                    :inactive-value="0"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column
                v-if="
                  form.productList &&
                  form.productList.some((perm) => perm.lossStatus == 1)
                "
                :label="$t('reportLossOrder.label.lossQty')"
                prop="lossQty"
                show-overflow-tooltip
                min-width="200px"
              >
                <template #default="scope">
                  <el-form-item
                    v-if="scope.row.lossStatus == 1"
                    class="mt15px"
                    label-width="0px"
                    :prop="'productList.' + scope.$index + '.lossQty'"
                    :rules="[
                      {
                        required: true,
                        message: t('reportLossOrder.rules.lossQty'),
                        trigger: ['blur', 'change'],
                      },
                      {
                        required: true,
                        validator: validatorLossQty,
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                        message: t('reportLossOrder.rules.lossQtyFormat'),
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.lossQty"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                      :disabled="type == 'detail'"
                    >
                      <template #append>
                        {{ scope.row.productUnitName }}
                      </template>
                    </el-input>
                  </el-form-item>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!--     分拣报损明细     -->
          <div v-if="form.orderType == 2">
            <el-table :data="form.productList" highlight-current-row stripe>
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
              />
              <!--商品信息-->
              <el-table-column
                :label="$t('reportLossOrder.label.productInformation')"
                show-overflow-tooltip
                min-width="150px"
              >
                <template #default="scope">
                  <div class="product-div">
                    <div class="product">
                      <div class="product-name">
                        {{ scope.row.productName }}
                      </div>
                      <div>
                        <span class="product-key">
                          {{ $t("reportLossOrder.label.productCode") }}：
                        </span>
                        <span class="product-value">
                          {{ scope.row.productCode }}
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              
              <!--规格-->
              <el-table-column
                :label="$t('reportLossOrder.label.productSpec')"
                prop="productSpec"
                show-overflow-tooltip
                min-width="100px"
              />
              <el-table-column
                :label="'单位'"
                prop="productUnitName"
                show-overflow-tooltip
                width="100px"
              />
              <el-table-column
                :label="'收运数量'"
                prop="receivedQty"
                show-overflow-tooltip
                width="100px"
              />
              <el-table-column
                :label="'收运重量(Kg)'"
                prop="receivedWeight"
                show-overflow-tooltip
                min-width="100px"
              >
              </el-table-column>
              <!-- 新增\编辑 -->
              <el-table-column
                :label="'报损重量(Kg)'"
                show-overflow-tooltip
                min-width="100px"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.lossWeight"
                    placeholder="请输入报损重量"
                    clearable
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('common.handle')"
                show-overflow-tooltip
                min-width="100px"
              >
                <template #default="scope">
                  <el-switch
                    :active-text="$t('reportLossOrder.label.activeBtn')"
                    :inactive-text="$t('reportLossOrder.label.inactiveBtn')"
                    inline-prompt
                    v-model="scope.row.lossStatus"
                    :active-value="1"
                    :inactive-value="0"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column
                v-if="
                  form.productList &&
                  form.productList.some((perm) => perm.lossStatus === 1)
                "
                :label="$t('reportLossOrder.label.lossQty')"
                prop="lossQty"
                show-overflow-tooltip
                min-width="200px"
              >
                <template #default="scope">
                  <el-form-item
                    v-if="scope.row.lossStatus == 1"
                    class="mt15px"
                    label-width="0px"
                    :prop="'productList.' + scope.$index + '.lossQty'"
                    :rules="[
                      {
                        required: true,
                        message: t('reportLossOrder.rules.lossQty'),
                        trigger: ['blur', 'change'],
                      },
                      {
                        required: true,
                        validator: validatorLossQty,
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                        message: t('reportLossOrder.rules.lossQtyFormat'),
                        trigger: ['blur', 'change'],
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.lossQty"
                      :placeholder="$t('common.placeholder.inputTips')"
                      clearable
                    >
                      <template #append>
                        {{ scope.row.productUnitName }}
                      </template>
                    </el-input>
                  </el-form-item>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          plain
          @click="handleSubmit(0)"
          :loading="submitLoading"
        >
          {{ $t("reportLossOrder.button.saveDraft") }}
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit(1)"
          :loading="submitLoading"
        >
          {{ $t("reportLossOrder.button.confirm") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddReportLossOrder",
  inheritAttrs: false,
});

import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import ReportLossOrderAPI, {
  ReportLossOrderFrom,
} from "@/modules/wms/api/reportLossOrder";
import ProductDisassemblyAssembleOrderAPI from "@/modules/wms/api/productDisassemblyAssembleOrder";
import CommonAPI, {
  ProductAllPageQuery,
} from "@/modules/wms/api/common";

// 扩展产品对象类型以支持分拣报损
interface ExtendedProductVO {
  id?: string;
  productCode?: string;
  productName?: string;
  productSpec?: string;
  productUnitName?: string;
  productUnit?: string;
  warehouseAreaId?: string;
  warehouseAreaCode?: string;
  warehouseAreaName?: string;
  // 拆装报损字段
  disassemblyOutQty?: number;
  disassemblyOutWeight?: number;
  sourceWarehouseAreaCode?: string;
  // 分拣报损字段
  receivedQty?: number;
  receivedWeight?: number;
  sortingBeforeQty?: number;
  sortingBeforeWeight?: number;
  lossWeight?: number;
  // 通用字段
  lossQty?: number;
  lossStatus?: number;
  outQty?: number;
}

// 扩展报损单表单类型
interface ExtendedReportLossOrderFrom extends Omit<ReportLossOrderFrom, 'productList'> {
  productList: ExtendedProductVO[];
}

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const outWarehouseAreaList = ref<any[]>([]);
const disassemblyAssembleOrderList = ref<any[]>([]);
const fromRef = ref();
const submitLoading = ref(false);
const loading = ref(false);
const id = route.query.id;
const type = route.query.type;
const reportLossOrderTypeList = ref([
  {
    reportLossOrderTypeId: 1,
    reportLossOrderTypeName: "拆装报损",
  },

  {
    reportLossOrderTypeId: 2,
    reportLossOrderTypeName: "分拣报损",
  },
]);
const handleChangeOrderType = () => {
  disassemblyAssembleOrderList.value = [];
  form.sourceOrderCode = undefined;
  if (form?.orderType === 1) { // 拆装单报损
    getDisassemblyAssembleOrderList();
  } else if (form.orderType === 2) { // 分拣单报损
    getCompletedOrderListFetch();
  }
};

const getCompletedOrderListFetch = () => {
  // 分拣状态：0=初始 1=分拣中 2=完结
  ReportLossOrderAPI.getCompletedOrderList({}).then(
    (data) => {
      disassemblyAssembleOrderList.value = data;
    }
  );
};
const form = reactive<ExtendedReportLossOrderFrom>({
  orderType: 1,
  sourceOrderType: 12,
  productList: [],
});
const queryParams = reactive<ProductAllPageQuery>({
  page: 1,
  limit: 20,
});
const rules = reactive({
  orderType: [
    {
      required: true,
      message: t("reportLossOrder.rules.orderType"),
      trigger: ["blur", "change"],
    },
  ]
});

function validatorLossQty(rule: any, value: any, callback: any) {
  let index = rule.field?.split(".")[1];
  let maxQty = 0;
  
  if (form.productList[index]) {
    if (form.orderType === 1) {
      // 拆装报损：检查是否超过拆装出库数量
      maxQty = form.productList[index].disassemblyOutQty || 0;
    } else if (form.orderType === 2) {
      // 分拣报损：检查是否超过收运数量
      maxQty = form.productList[index].receivedQty || 0;
    }
  }
  
  if (value && maxQty) {
    if (value > parseFloat(maxQty.toString())) {
      callback(new Error(t("reportLossOrder.message.lossQtyTips")));
    } else {
      callback();
    }
  } else {
    callback();
  }
}

/** 查询出库库区列表 */
function getOutWarehouseAreaList() {
  CommonAPI.getOutWarehouseAreaList().then((data: any) => {
    outWarehouseAreaList.value = data;
    if (outWarehouseAreaList.value && outWarehouseAreaList.value.length > 0) {
      outWarehouseAreaList.value.map((item: any) => {
        item.warehouseArea = item.areaName + "|" + item.areaCode;
        return item;
      });
    }
  });
}

/** 获取拆装单号下拉数据 (报损单创建页面) */
function getDisassemblyAssembleOrderList() {
  ProductDisassemblyAssembleOrderAPI.getDisassemblyAssembleOrderList().then(
    (data: any) => {
      disassemblyAssembleOrderList.value = data;
    }
  );
}

// 根据单号查询商品明细
function changeSourceOrderCode() {
  form.productList = [];
  if (form.sourceOrderCode) {
    if(form.orderType === 1){ // 查询拆装单下商品
      queryLossProductListByOrderId();
    }
    else if(form.orderType === 2){ // 查询分拣单下商品
      queryPickOrderProcuctsById();
    }
  }
}

function queryPickOrderProcuctsById(){
  if (form.sourceOrderCode) {
    ProductDisassemblyAssembleOrderAPI.queryProductOfPickOrders(form.sourceOrderCode)
      .then((data: any) => {
        form.productList = data.detailVOList.map((item: any) => {
          return {
            ...item,
            receivedQty: item.sortingBeforeQty,
            receivedWeight: item.sortingBeforeWeight,
            productUnitName: item.productUnit,
            lossStatus: 0
          }
        })
      })
      .finally(() => {
        // 处理完成
      })
  }
}


async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}

/** 暂存/提交 */
function handleSubmit(val: number) {
  fromRef.value.validate((valid: boolean) => {
    if (!valid) return;
    if (form.productList && form.productList.length == 0) {
      return ElMessage.error(t("reportLossOrder.message.reportLossOrderTips"));
    }

    if (form.productList && form.productList.length > 0) {
      const flag = form.productList.some((item) => item.lossStatus == 1);
      if (!flag) {
        return ElMessage.error(
          t("reportLossOrder.message.reportLossOrderLessTips")
        );
      }
    }
    submitLoading.value = true;
    let productList: any[] = [];
    if (form.productList && form.productList.length > 0) {
      form.productList.forEach((item) => {
        let obj;
        if (form.orderType === 1) {
          // 拆装报损逻辑
          let sourceWarehouseArea = outWarehouseAreaList.value.filter(
            (out) => item.warehouseAreaId == out.areaCode
          );
          obj = {
            id: item.id,
            productCode: item.productCode,
            productName: item.productName,
            productSpec: item.productSpec,
            productUnitName: item.productUnitName,
            warehouseAreaId: sourceWarehouseArea[0]?.id,
            warehouseAreaName: item.warehouseAreaName,
            outQty: item.disassemblyOutQty,
            lossQty: item.lossQty,
            lossStatus: item.lossStatus,
          };
        } else if (form.orderType === 2) {
          let sourceWarehouseArea = outWarehouseAreaList.value.filter(
            (out) => item.warehouseCode == out.warehouseCode
          );
          // 分拣报损逻辑
          obj = {
            id: item.id,
            productCode: item.productCode,
            productName: item.productName,
            productSpec: item.productSpec,
            productUnitName: item.productUnitName,
            warehouseAreaId: sourceWarehouseArea[0]?.id,
            outQty: item.receivedQty,
            outWeight: item.receivedWeight,
            lossQty: item.lossQty,
            lossWeight: item.lossWeight,
            lossStatus: item.lossStatus,
          };
        }
        productList.push(obj);
      });
    }

    let params: any = {
      orderType: form.orderType,
      sourceOrderCode: form.sourceOrderCode,
      sourceOrderType: form.sourceOrderType,
      remark: form.remark,
      productList: productList,
    };
    if (type !== "add") {
      params.id = form.id;
      params.lossOrderCode = form.lossOrderCode;
    }
    if (val == 0) {
      ReportLossOrderAPI.saveReportLossOrder(params as ReportLossOrderFrom)
        .then((data) => {
          ElMessage.success(t("reportLossOrder.message.saveSucess"));
          handleClose();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    } else {
      ElMessageBox.confirm(
        t("reportLossOrder.message.submitTips"),
        t("common.tipTitle"),
        {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning",
        }
      ).then(
        () => {
          ReportLossOrderAPI.submitReportLossOrder(params as ReportLossOrderFrom)
            .then((data) => {
              ElMessage.success(t("reportLossOrder.message.submitSucess"));
              handleClose();
            })
            .finally(() => {
              submitLoading.value = false;
            });
        },
        () => {
          submitLoading.value = false;
          ElMessage.info(t("reportLossOrder.message.submitConcel"));
        }
      );
    }
  });
}

/** 查询报损详情 */
function getReportLossOrderDetail() {
  loading.value = true;
  let params = {
    id: id,
  };
  ReportLossOrderAPI.getReportLossOrderDetail(params)
    .then((data) => {
      Object.assign(form, data);
      if (form.productList && form.productList.length > 0) {
        form.productList.forEach((item) => {
          (item.warehouseAreaId = item.warehouseAreaCode),
            (item.disassemblyOutQty = item.outQty);
        });
      }
      if (type == "detail") {
        const reportLossOrderType = reportLossOrderTypeList.value.filter(
          (item) => form.orderType == item.reportLossOrderTypeId
        );
        form.orderType = reportLossOrderType[0].reportLossOrderTypeName;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 根据拆装单号查询报损商品列表(报损单创建页面) */
function queryLossProductListByOrderId() {
  fromRef.value.validate((valid) => {
    if (!valid) return;
    loading.value = true;
    let params = {
      disassemblyOrderCode: form.sourceOrderCode,
    };
    ProductDisassemblyAssembleOrderAPI.queryLossProductListByOrderId(params)
      .then((data) => {
        form.productList = data;
        if (form.productList && form.productList.length > 0) {
          form.productList.forEach((item) => {
            let sourceWarehouseArea = outWarehouseAreaList.value.filter(
              (out) => item.sourceWarehouseAreaCode == out.areaCode
            );
            item.warehouseAreaId = item.sourceWarehouseAreaCode;
            item.warehouseAreaName = sourceWarehouseArea[0].areaName;
            item.lossStatus = 0;
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

onMounted(() => {
  getOutWarehouseAreaList();
  getDisassemblyAssembleOrderList(); // 拆装报损单列表
});
</script>
<style scoped lang="scss">
.addReportLossOrder {
  background: #ffffff;
  border-radius: 4px;

  .page-content {
    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }

    .table-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      background: #f4f6fa;
      box-shadow:
        inset 1px 1px 0px 0px #e5e7f3,
        inset -1px -1px 0px 0px #e5e7f3;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
      font-style: normal;
      padding: 15px 12px;
    }
  }
}
</style>
<style lang="scss">
.addReportLossOrder {
  .page-content {
    .el-switch .el-switch__inner {
      width: 60px !important;
    }
  }
}
</style>
