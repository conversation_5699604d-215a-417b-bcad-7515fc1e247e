<template>
  <div class="app-container">
    <div class="inventoryTransfer">
      <div class="search-container">
        <el-form
          ref="queryFormRef"
          :model="queryParams"
          :inline="true"
          label-width="98px"
        >
          <el-form-item
            prop="transferOrderCode"
            :label="$t('inventoryTransfer.label.transferOrderCode')"
          >
            <el-input
              v-model="queryParams.transferOrderCode"
              :placeholder="
                $t('inventoryTransfer.placeholder.transferOrderCode')
              "
              clearable
              class="!w-[256px]"
            />
          </el-form-item>
          <el-form-item
            :label="$t('inventoryTransfer.label.transferStatus')"
            prop="transferStatus"
          >
            <el-select
              v-model="queryParams.transferStatus"
              :placeholder="$t('common.placeholder.selectTips')"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              class="!w-[256px]"
            >
              <el-option
                v-for="item in statusList"
                :key="item.statusId"
                :label="item.statusName"
                :value="item.statusId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('inventoryTransfer.label.receiptStatus')"
            prop="receiptStatus"
          >
            <el-select
              v-model="queryParams.receiptStatus"
              :placeholder="$t('common.placeholder.selectTips')"
              clearable
              class="!w-[256px]"
            >
              <el-option
                v-for="(item, index) in receiveOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="dateRange">
            <el-select
              v-model="queryParams.dateType"
              :placeholder="$t('common.placeholder.selectTips')"
              class="!w-[200px] ml5px"
            >
              <el-option
                v-for="item in dateTypeList"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
            <el-date-picker
              :editable="false"
              class="!w-[370px]"
              v-model="queryParams.dateRange"
              type="datetimerange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="截止时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="defaultTime"
              :placeholder="$t('common.placeholder.selectTips')"
            />
            <span
              class="ml16px mr14px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(1)"
            >
              {{ $t("inventoryTransfer.label.today") }}
            </span>
            <span
              class="mr14px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(2)"
            >
              {{ $t("inventoryTransfer.label.yesterday") }}
            </span>
            <span
              class="mr16px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(3)"
            >
              {{ $t("inventoryTransfer.label.weekday") }}
            </span>
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPerm="[
                'wms:insideWarehouseManagement:inventoryTransfer:search',
              ]"
              type="primary"
              @click="handleQuery"
            >
              {{ $t("common.search") }}
            </el-button>
            <el-button
              v-hasPerm="[
                'wms:insideWarehouseManagement:inventoryTransfer:reset',
              ]"
              @click="handleResetQuery"
            >
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-card shadow="never" class="table-container">
        <template #header>
          <el-button
            v-hasPerm="['wms:insideWarehouseManagement:inventoryTransfer:add']"
            type="primary"
            @click="addInventoryTransfer(null, 'add')"
          >
            {{ $t("inventoryTransfer.button.addInventoryTransfer") }}
          </el-button>
        </template>

        <el-table
          v-loading="loading"
          :data="inventoryTransferList"
          highlight-current-row
          stripe
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column type="index" :label="$t('common.sort')" width="60" />
          <el-table-column
            :label="$t('inventoryTransfer.label.transferOrderCode')"
            prop="transferOrderCode"
            show-overflow-tooltip
            min-width="150px"
          ></el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.transferType')"
            prop="transferType"
            show-overflow-tooltip
            min-width="100px"
          >
            <template #default="scope">
              <span v-if="scope.row.transferType == 1">
                {{
                  $t(
                    "inventoryTransfer.inventoryTransferTypeList.normalTransfer"
                  )
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.sourceWarehouseArea')"
            show-overflow-tooltip
            min-width="150px"
          >
            <template #default="scope">
              <span v-if="scope.row.sourceWarehouseAreaCode">
                {{ scope.row.sourceWarehouseAreaName }} |
                {{ scope.row.sourceWarehouseAreaCode }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.transferReasonName')"
            prop="transferReasonName"
            show-overflow-tooltip
            min-width="150px"
          ></el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.transferAmount')"
            prop="transferTotalQty"
            show-overflow-tooltip
            min-width="150px"
          >
            <template #default="scope">
              <div class="item">
                {{ $t("inventoryTransfer.label.quantity") }}:{{
                  scope.row.transferTotalQty ? scope.row.transferTotalQty : "-"
                }}
              </div>
              <div class="item">
                {{ $t("inventoryTransfer.label.weight") }}(kg):{{
                  scope.row.transferTotalWeight
                    ? scope.row.transferTotalWeight
                    : "-"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.receiveInfo')"
            prop="transferUserName"
            show-overflow-tooltip
            min-width="150px"
          >
            <template #default="scope">
              <div class="item">
                {{ $t("inventoryTransfer.label.receiveName") }}:{{
                  scope.row.transferUserName ? scope.row.transferUserName : "-"
                }}
              </div>
              <div class="item">
                {{ $t("inventoryTransfer.label.receiveTime") }}:{{
                  scope.row.transferTime
                    ? parseDateTime(scope.row.transferTime, "dateTime")
                    : "-"
                }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('inventoryTransfer.label.remark')"
            prop="remark"
            show-overflow-tooltip
            min-width="150px"
          ></el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.createUserName')"
            prop="createUserName"
            show-overflow-tooltip
            min-width="150px"
          ></el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.createTime')"
            prop="createTime"
            show-overflow-tooltip
            min-width="180px"
          >
            <template #default="scope">
              <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.transferUserName')"
            prop="transferUserName"
            show-overflow-tooltip
            min-width="150px"
          ></el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.transferTime')"
            prop="transferTime"
            show-overflow-tooltip
            min-width="180px"
          >
            <template #default="scope">
              <span v-if="scope.row.transferTime">
                {{ parseDateTime(scope.row.transferTime, "dateTime") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('inventoryTransfer.label.transferStatus')"
            prop="transferStatus"
            show-overflow-tooltip
            min-width="100px"
            fixed="right"
          >
            <template #default="scope">
              <div class="purchase">
                <span
                  class="purchase-status purchase-status-color0"
                  v-if="scope.row.transferStatus == 0"
                >
                  {{ $t("inventoryTransfer.statusList.draft") }}
                </span>
                <span
                  class="purchase-status purchase-status-color2"
                  v-if="scope.row.transferStatus == 1"
                >
                  {{ $t("inventoryTransfer.statusList.movement") }}
                </span>
                <span
                  class="purchase-status purchase-status-color3"
                  v-if="scope.row.transferStatus == 2"
                >
                  {{ $t("inventoryTransfer.statusList.finish") }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            :label="$t('common.handle')"
            width="160"
          >
            <template #default="scope">
              <template v-if="scope.row.transferStatus === 0">
                <template v-if="scope.row.receiptStatus === 0">
                  <el-button
                    v-hasPerm="[
                      'wms:insideWarehouseManagement:inventoryTransfer:receive',
                    ]"
                    type="primary"
                    link
                    @click="handleReceive(scope.row)"
                  >
                    {{ $t("inventoryTransfer.button.actionReceive") }}
                  </el-button>
                  <el-button
                    v-hasPerm="[
                      'wms:insideWarehouseManagement:inventoryTransfer:delete',
                    ]"
                    type="danger"
                    link
                    @click="handleDelete(scope.row.id)"
                  >
                    {{ $t("common.delete") }}
                  </el-button>
                </template>
                <template v-else>
                    <el-button

                      v-hasPerm="[
                          'wms:insideWarehouseManagement:inventoryTransfer:release',
                        ]"
                      type="primary"
                      link
                      @click="handleCancelReceive(scope.row)"
                    >
                    {{ $t("inventoryTransfer.button.cancelTransfer") }}
                </el-button>
                  <el-button
                    v-hasPerm="[
                      'wms:insideWarehouseManagement:inventoryTransfer:edit',
                    ]"
                    v-if="userStore.user.userId==scope.row.transferUser"
                    type="primary"
                    link
                    @click="addInventoryTransfer(scope.row.id, 'edit')"
                  >
                    {{ $t("common.edit") }}
                  </el-button>

                </template>
              </template>
              <template v-else>
                <el-button
                  v-hasPerm="[
                    'wms:insideWarehouseManagement:inventoryTransfer:detail',
                  ]"
                  type="primary"
                  link
                  @click="addInventoryTransfer(scope.row.id, 'detail')"
                >
                  {{ $t("common.detailBtn") }}
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "InventoryTransfer",
  inheritAttrs: false,
});

import {
  changeDateRange,
  convertToTimestamp,
  parseDateTime,
} from "@/core/utils/index.js";
import InventoryTransferAPI, {
  InventoryTransferPageVO,
  InventoryTransferPageQuery,
} from "@/modules/wms/api/inventoryTransfer";
import { useRouter } from "vue-router";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";
import { useUserStore } from "@/core/store";
const userStore = useUserStore();

const router = useRouter();
const { t } = useI18n();
const queryFormRef = ref(null);
const loading = ref(false);
const total = ref(0);
const inventoryTransferTypeList = ref([
  {
    inventoryTransferTypeId: 1,
    inventoryTransferTypeName: t(
      "inventoryTransfer.inventoryTransferTypeList.normalTransfer"
    ),
  },
]);
const statusList = ref([
  {
    statusId: 0,
    statusName: t("inventoryTransfer.statusList.draft"),
  },
  {
    statusId: 1,
    statusName: t("inventoryTransfer.statusList.movement"),
  },
  {
    statusId: 2,
    statusName: t("inventoryTransfer.statusList.finish"),
  },
]);
const receiveOption = ref([
  {
    value: 0,
    label: t("inventoryTransfer.whetherOption.no"),
  },
  {
    value: 1,
    label: t("inventoryTransfer.whetherOption.yes"),
  },
]);
const dateTypeList = ref([
  {
    key: 1,
    value: t("inventoryTransfer.dateTypeList.createDate"),
  },
  {
    key: 3,
    value: t("inventoryTransfer.dateTypeList.receiveDate"),
  },
  {
    key: 2,
    value: t("inventoryTransfer.dateTypeList.inventoryTransferDate"),
  },
]);
const queryParams = reactive<InventoryTransferPageQuery>({
  dateType: 1,
  dateRange: [
    moment().subtract("days", 29).startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
  ],
  page: 1,
  limit: 20,
});
const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];
const inventoryTransferList = ref<InventoryTransferPageVO[]>();

/** 时间转换 */
function handleChangeDateRange(val: any) {
  queryParams.dateRange = changeDateRange(val);
}

/** 查询 */
function handleQuery() {
  if (
    queryParams.transferOrderCode &&
    queryParams.transferOrderCode.length < 4
  ) {
    return ElMessage.error(t("inventoryTransfer.message.codeValideTips"));
  }
  loading.value = true;
  let params = {
    ...queryParams,
  };
  params.queryTimeType = queryParams.dateType;
  if (queryParams.dateRange && queryParams.dateRange.length > 0) {
    params.startTime = convertToTimestamp(queryParams.dateRange[0]);
    params.endTime = convertToTimestamp(queryParams.dateRange[1]);
  }
  delete params.dateType;
  delete params.dateRange;
  InventoryTransferAPI.getInventoryTransferPage(params)
    .then((data) => {
      inventoryTransferList.value = data.records;
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.dateType = 1;
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}

/** 删除 */
function handleDelete(id?: string) {
  ElMessageBox.confirm(
    t("inventoryTransfer.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let data = {
        id: id,
      };
      InventoryTransferAPI.deleteInventoryTransfer(data)
        .then(() => {
          ElMessage.success(t("inventoryTransfer.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("inventoryTransfer.message.deleteConcel"));
    }
  );
}

/** 新增/编辑/详情*/
function addInventoryTransfer(id?: string, type?: string) {
  router.push({
    path: "/wms/insideWarehouseManagement/addInventoryTransfer",
    query: {
      id: id,
      type: type,
      title:
        type == "add"
          ? t("inventoryTransfer.button.addInventoryTransfer")
          : type == "edit"
            ? t("inventoryTransfer.button.editInventoryTransfer")
            : t("common.detailBtn"),
    },
  });
}

/**
 * 领单
 * @param item
 */
function handleReceive(item: any) {
    let transferOrderCode = item.transferOrderCode;
    InventoryTransferAPI.pickOrder(transferOrderCode).then(() => {
        ElMessage.success(t("inventoryTransfer.message.actionSucess"));
        handleQuery();
    });
}

/**
 * 取消领单
 * @param item
 */
function handleCancelReceive(item: any) {
    ElMessageBox.confirm(
      t("inventoryTransfer.message.receiveTips"),
      t("common.tipTitle"),
      {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning",
      }
    ).then(() => {
        let transferOrderCode = item.transferOrderCode;
        InventoryTransferAPI.releaseOrder(transferOrderCode).then(() => {
            ElMessage.success(t("inventoryTransfer.message.actionSucess"));
            handleQuery();
        });
    });
}


onActivated(() => {
  handleQuery();
});

emitter.on("reloadListByWarehouseId", (e) => {
  nextTick(() => {
    handleQuery();
  });
});
</script>

<style lang="scss" scoped>
.inventoryTransfer {
}
</style>
