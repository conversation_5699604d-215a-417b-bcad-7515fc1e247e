<template>
    <div class="app-container">
        <div class="disassemblyAssembleTemplate"  v-loading="loading">
            <div >
                <div class="page-title">
                    <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                    <div>
                        <span v-if="type=='add'">{{ $t("disassemblyAssembleTemplate.button.addDisassemblyAssembleTemplate") }}</span>
                        <span v-else> {{t('disassemblyAssembleTemplate.button.editDisassemblyAssembleTemplate')}}：{{form.disassemblyTemplateCode}}</span>
                    </div>
                </div>
            </div>
            <div class="page-content">
                <el-form
                    :model="form"
                    :rules="rules"
                    ref="fromRef"
                    label-width="108px"
                    label-position="right"
                >
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("disassemblyAssembleTemplate.label.basicInformation") }}
                        </div>
                    </div>
                    <div>
                        <el-row>
                          <!-- 模板编码 -->
                          <el-col :span="8">
                            <el-form-item
                              :label="$t('disassemblyAssembleTemplate.label.templateCode')"
                              prop="disassemblyTemplateCode"
                            >
                              <el-input
                                v-model="form.disassemblyTemplateCode"
                                class="!w-[256px]"
                                :placeholder="$t('disassemblyAssembleTemplate.placeholder.systemGenerated')"
                                disabled
                              />
                            </el-form-item>
                          </el-col>
                         <!--  是否审核  -->
                          <el-col :span="8">
                              <el-form-item :label="$t('disassemblyAssembleTemplate.label.isApprove')" prop="isApprove">
                                  <el-select
                                          v-model="form.isApprove"
                                          :placeholder="$t('common.placeholder.selectTips')"
                                          clearable
                                          class="!w-[256px]"
                                  >
                                      <el-option v-for="item in whetherOptionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                  </el-select>
                              </el-form-item>
                          </el-col>
                          <el-col :span="8">
                            <!--  审核人  -->
                            <el-form-item :label="$t('disassemblyAssembleTemplate.label.approveUser')" prop="approveUser">
                              <el-select
                                v-model="form.approveUser"
                                :placeholder="$t('common.placeholder.selectTips')"
                                filterable
                                clearable
                                class="!w-[256px]"
                              >
                                <el-option v-for="item in personList" :key="item.userId" :value="item.userId" :label="item.nickName"></el-option>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                              <!--  模板名称  -->
                              <el-form-item :label="$t('disassemblyAssembleTemplate.label.templateName')" prop="disassemblyTemplateName">
                                <el-input
                                  class="!w-[256px]"
                                  v-model="form.disassemblyTemplateName"
                                  :placeholder="$t('disassemblyAssembleTemplate.placeholder.templateName')"
                                  maxlength="100"
                                  clearable
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="16">
                              <!--  备注  -->
                                <el-form-item :label="$t('disassemblyAssembleTemplate.label.remark')" prop="remark">
                                    <el-input
                                            :rows="4"
                                            type="textarea"
                                            show-word-limit
                                            v-model="form.remark"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            maxlength="200"
                                            clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("disassemblyAssembleTemplate.label.templateDetail") }}
                        </div>
                    </div>
                    <div class="flex-center-space-start">
                       <div style="width: calc(50% - 5px)">
                         <div class="flex-center-but">
                           <div class="trapezoid trapezoid-color1">
                             {{$t('disassemblyAssembleTemplate.label.sourceProduct')}}
                           </div>
                           <div class="button-add cursor-pointer" @click="addProduct(SourceDetailDataEnum.SOURCE)">
                             {{ $t("disassemblyAssembleTemplate.button.addProduct") }}
                           </div>
                         </div>
                           <el-table
                                   v-loading="loadSource"
                                   :data="form.sourceList"
                                   highlight-current-row
                                   stripe
                           >
                               <el-table-column type="index" :label="$t('common.sort')" width="60" />
                               <el-table-column :label="$t('disassemblyAssembleTemplate.label.productInformation')" show-overflow-tooltip min-width="220px">
                                   <template #default="scope">
                                       <div class="product-div">
                                           <div class="product">
                                               <div class="product-name">{{scope.row.productName}}</div>
                                               <div>
                                                   <span class="product-key">{{$t('disassemblyAssembleTemplate.label.productCode')}}：</span>
                                                   <span class="product-value">{{scope.row.productCode}}</span>
                                               </div>
                                           </div>
                                       </div>
                                   </template>
                               </el-table-column>
                               <el-table-column :label="$t('disassemblyAssembleTemplate.label.productSpec')" prop="productSpec" show-overflow-tooltip min-width="100px"/>
                             <el-table-column :label="$t('disassemblyAssembleTemplate.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" min-width="120px">
                               <template #default="scope">
                                 {{scope.row.isDiscreteUnit === 1 ? $t('disassemblyAssembleTemplate.whetherOption.yes') : $t('disassemblyAssembleTemplate.whetherOption.no')}}
                               </template>
                             </el-table-column>
                             <el-table-column :label="$t('disassemblyAssembleTemplate.label.productQty')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item class="mt15px" label-width="0px" :prop="'sourceList.'+scope.$index+ '.qty'"
                                               :rules="[{required:true,message:t('disassemblyAssembleTemplate.rules.productQty'),trigger:['blur','change']},
                                 {pattern: patternRegExp,
                                 message:t('disassemblyAssembleTemplate.rules.disassemblyQtyFormat'), trigger: ['blur','change']}]">
                                   <div>
                                     <el-input
                                       v-model="scope.row.qty"
                                       :placeholder="$t('common.placeholder.inputTips')"
                                       @change="(val) => firstLevelQtyChange(val, scope.row, 'convertedQty')"
                                       clearable
                                     >
                                       <template #append>{{ scope.row?.qtyUnitName}}</template>
                                     </el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <el-table-column :label="$t('disassemblyAssembleTemplate.label.productTransformQty')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item class="mt15px" label-width="0px"
                                               :prop="'sourceList.'+scope.$index+ '.convertedQty'"
                                               :rules="[{required:true,message:t('disassemblyAssembleTemplate.rules.productTransformQty'),trigger:['blur','change']},
                                               {pattern: patternRegExp,
                                               message:t('disassemblyAssembleTemplate.rules.disassemblyConvertedQtyFormat'), trigger: ['blur','change']}]">
                                     <div>
                                         <el-input
                                           v-model="scope.row.convertedQty"
                                           @change="(val: number) => secondLevelQtyChange(val, scope.row, 'qty')"
                                           :placeholder="$t('common.placeholder.inputTips')"
                                           clearable
                                         >
                                           <template #append>{{ scope.row?.convertedQtyUnitName}}</template>
                                         </el-input>
                                     </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                               <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                                   <template #default="scope">
                                       <el-button
                                               type="danger"
                                               size="small"
                                               link
                                               @click="handleDelete(scope.$index,SourceDetailDataEnum.SOURCE)"
                                       >
                                           {{$t('common.delete')}}
                                       </el-button>
                                   </template>
                               </el-table-column>
                           </el-table>
                       </div>
                        <div class="mt60px pl7px pr7px"><el-icon color="var(--el-color-primary)"><Right /></el-icon></div>
                        <div  style="width: calc(50% - 5px)">
                            <div class="flex-center-but">
                                <div class="trapezoid trapezoid-color1">
                                  {{$t('disassemblyAssembleTemplate.label.targetProduct')}}
                                </div>
                                <div class="button-add cursor-pointer" @click="addProduct(SourceDetailDataEnum.TARGET)">
                                    {{$t('disassemblyAssembleTemplate.button.addProduct')}}
                                </div>
                            </div>
                            <el-table
                              :data="form.targetList"
                              highlight-current-row
                              stripe
                            >
                                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productInformation')" show-overflow-tooltip min-width="220px">
                                    <template #default="scope">
                                        <div class="product-div">
                                            <div class="product">
                                                <div class="product-name">{{scope.row.productName}}</div>
                                                <div>
                                                    <span class="product-key">{{$t('disassemblyAssembleTemplate.label.productCode')}}：</span>
                                                    <span class="product-value">{{scope.row.productCode}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productSpec')" prop="productSpec" show-overflow-tooltip min-width="100px"/>
                              <el-table-column :label="$t('disassemblyAssembleTemplate.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                                <template #default="scope">
                                  {{scope.row.isDiscreteUnit === 1 ? $t('disassemblyAssembleTemplate.whetherOption.yes') : $t('disassemblyAssembleTemplate.whetherOption.no')}}
                                </template>
                              </el-table-column>
                                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productQty')" min-width="260px">
                                    <template #default="scope">
                                        <el-form-item class="mt15px" label-width="0px" :prop="'targetList.'+scope.$index+ '.qty'"
                                                      :rules="[{required:true,message:t('disassemblyAssembleTemplate.rules.productQty'),trigger:['blur','change']},
                                                       {pattern: patternRegExp,
                                                       message:t('disassemblyAssembleTemplate.rules.productTransformQtyFormat'),
                                                       trigger: ['blur','change']}]">
                                            <div>
                                                <el-input
                                                  v-model="scope.row.qty"
                                                  @change="(val: number) => firstLevelQtyChange(val, scope.row, 'convertedQty')"
                                                  :placeholder="$t('common.placeholder.inputTips')"
                                                  clearable
                                                >
                                                  <template #append>{{ scope.row?.qtyUnitName}}</template>
                                                </el-input>
                                            </div>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productTransformQty')" min-width="260px">
                                  <template #default="scope">
                                    <el-form-item class="mt15px" label-width="0px"
                                                  :prop="'targetList.'+scope.$index+ '.convertedQty'"
                                                  :rules="[{required:true,message:t('disassemblyAssembleTemplate.rules.productTransformQty'),trigger:['blur','change']},
                                                  {pattern: patternRegExp,
                                                  message:t('disassemblyAssembleTemplate.rules.productTransformQtyFormat'),
                                                   trigger: ['blur','change']}]">
                                      <div>
                                        <el-input
                                          v-model="scope.row.convertedQty"
                                          @change="(val: number) => secondLevelQtyChange(val, scope.row, 'qty')"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          clearable
                                        >
                                          <template #append>{{ scope.row?.convertedQtyUnitName}}</template>
                                        </el-input>
                                      </div>
                                    </el-form-item>
                                  </template>
                                </el-table-column>
                                <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                                    <template #default="scope">
                                        <el-button
                                          type="danger"
                                          link
                                          @click="handleDelete(scope.$index,SourceDetailDataEnum.TARGET)"
                                        >
                                            {{$t('common.delete')}}
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-form>
            </div>
            <div class="page-footer">
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
                <el-button type="primary" plain @click="handleSubmit('draft')" :loading="submitLoading">{{ $t("disassemblyAssembleTemplate.button.draft") }}</el-button>
                <el-button type="primary" @click="handleSubmit('submit')" :loading="submitLoading">{{ $t("disassemblyAssembleTemplate.button.submit") }}</el-button>
            </div>
            <AddProduct
                    ref="addProductRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    :outWarehouseAreaShow="false"
                    :availableStockQtyShow="false"
                    :outWarehouseAreaFromMultipShow="false"
                    @onSubmit="onSubmit"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
    defineOptions({
        name: "AddDisassemblyAssembleTemplate",
        inheritAttrs: false,
    });
    import {querySalesPersonUser} from "@/modules/oms/api/contract";
    import AddProduct from "../../../components/addProduct.vue";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import DisassemblyAssembleTemplateAPI, {
      convertUnitParamsInterface,
      DisassemblyAssembleTemplateFrom,
    } from "@/modules/wms/api/disassemblyAssembleTemplate";
    import CommonAPI, { ProductAllPageQuery}  from "@/modules/wms/api/common";
    import { isEmpty } from "@/core/utils";

    const enum SourceDetailDataEnum {
      SOURCE = 1,
      TARGET = 2,
    }
    const enum ConvertUnitTypeEnum {
      /** 一级转二级 */
      FIRST_TO_SECOND = "FIRST_TO_SECOND",
      /** 二级转一级 */
      SECOND_TO_FIRST = "SECOND_TO_FIRST",
    }

    const patternRegExp = /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/;

    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();

    const fromRef = ref()
    const submitLoading = ref(false)

    const loadSource = ref(false);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;
    const addProductRef = ref();
    const currentDialogRef = ref();

    const whetherOptionList = ref([
      {
        value: 0,
        label: t('disassemblyAssembleTemplate.whetherOption.no')
      },
      {
        value: 1,
        label: t('disassemblyAssembleTemplate.whetherOption.yes')
      },
    ])

    const personList = ref([]);
    function queryPersonList() {
      querySalesPersonUser().then((res)=>{
        personList.value = res || []
        console.log(personList.value)
      }).finally(()=>{})
    }

    const firstLevelQtyChange = (val: number, record : Record<string, any>, convertKey: string) => {
      /*一级单位增减为是 支持一级单位转二级单位*/
      if(patternRegExp.test(val) && ((record.isDiscreteUnit == 1) || (record.isDiscreteUnit == 0 && isEmpty(record[convertKey])))){
        submitLoading.value = true;
        const params: convertUnitParamsInterface = {
          productCode: record.productCode,
          originalValue: Number(val),
          convertUnitTypeEnum: ConvertUnitTypeEnum.FIRST_TO_SECOND,
        }
        CommonAPI.convertProductUnit(params)
        .then((data) => {
          record[convertKey] = data?.convertedValue
        }).finally(() => {
          submitLoading.value = false;
        });
      }
    }
    const secondLevelQtyChange = (val, record, convertKey) => {
      /*一级单位增减为否 支持二级单位转一级单位*/
      if(patternRegExp.test(val) && record.isDiscreteUnit == 0 && isEmpty(record[convertKey])){
        submitLoading.value = true;
        const params: convertUnitParamsInterface = {
          productCode: record.productCode,
          originalValue: Number(val),
          convertUnitTypeEnum: ConvertUnitTypeEnum.SECOND_TO_FIRST,
        }
        CommonAPI.convertProductUnit(params)
          .then((data) => {
            record[convertKey] = data?.convertedValue
          }).finally(() => {
           submitLoading.value = false;
          });
      }
    }

    const dialog = reactive({
        title: "",
        visible: false,
    });

    const form = reactive<DisassemblyAssembleTemplateFrom>({
        disassemblyTemplateName: '',
        isApprove: 1,
        remark: '',
        sourceList:[],
        targetList:[],
    });


    const rules = reactive({
      isApprove: [{ required: true, message: t("disassemblyAssembleTemplate.rules.isApprove"), trigger: ["blur","change"] }],
    });

    const uniqueByStringify = (array, key1, key2) => {
        const set = new Set();
        return array.filter(item => {
            const key = JSON.stringify({ [key1]: item[key1], [key2]: item[key2] }); // 创建唯一键字符串
            if (!set.has(key)) {
                set.add(key);
                return true; // 添加到结果数组中
            }
            return false; // 跳过重复项
        });
    };


    function onSubmit( data ) {
      if(currentDialogRef.value == SourceDetailDataEnum.SOURCE && data?.collection?.length>0){
        data.collection.forEach(item => {
          item.sourceWarehouseAreaId=item.warehouseAreaCode
          item.sourceWarehouseAreaName=item.warehouseAreaName
          item.convertedQtyUnitName = item.conversionRelSecondUnitName
          item.qtyUnitName = item.productUnitName
        })
        form.sourceList = uniqueByStringify([...form.sourceList,...data.collection], 'productCode', 'sourceWarehouseAreaId');
      }
      if(currentDialogRef.value == SourceDetailDataEnum.TARGET  && data?.collection?.length>0){
        data.collection.forEach(item => {
          item.targetWarehouseAreaId=item.warehouseAreaCode
          item.targetWarehouseAreaName=item.warehouseAreaName
          item.convertedQtyUnitName = item.conversionRelSecondUnitName
          item.qtyUnitName = item.productUnitName
        })
        form.targetList= uniqueByStringify([...form.targetList,...data.collection], 'productCode', 'sourceWarehouseAreaId');;
        // console.log("===targetList==="+form.targetList);
      }
    }

    /** 假删除*/
    function handleDelete(index?: number,val?:number) {
        if(val==1){
            form.sourceList?.splice(index, 1);
        }else{
            form.targetList?.splice(index, 1);
        }
        ElMessage.success(t('disassemblyAssembleTemplate.message.deleteSucess'));
    }

    async function handleClose() {
        await tagsViewStore.delView(route);
        router.go(-1);
    }

    /** 添加/编辑库存转移 */
    function handleSubmit(handleType: string){
        fromRef.value.validate((valid) => {
            if (!valid) return;
            if(form?.isApprove == 1 && !form?.approveUser){
              return  ElMessage.error(t('disassemblyAssembleTemplate.message.approveUser'));
            }
            if(handleType === 'submit' && (!form.sourceList?.length || !form.targetList?.length)){
              return  ElMessage.error(t('disassemblyAssembleTemplate.message.addOrEditDisassemblyAssembleTemplateTips'));
            }
            submitLoading.value=true

            let sourceList = form.sourceList?.map(item => {
              delete item.id
              return {
                ...item,
                convertedQtyUnitName: item.conversionRelSecondUnitName || item.convertedQtyUnitName,
                qtyUnitName: item.productUnitName || item.qtyUnitName
              }
            })
            let targetList = form.targetList?.map(item => {
              delete item.id
              return {
                ...item,
                convertedQtyUnitName: item.conversionRelSecondUnitName || item.convertedQtyUnitName,
                qtyUnitName: item.productUnitName || item.qtyUnitName
              }
            })
            let params = {
                disassemblyTemplateName: form.disassemblyTemplateName,
                isApprove:  form.isApprove as number,
                remark: form.remark as string,
                saveType: handleType == 'draft' ? 1 : 2,
                approveUser: form.approveUser as number,
                approveUserName: '',
                ...form,
                disassemblyTemplateProductSourceList: sourceList,
                disassemblyTemplateProductTargetList: targetList,
            }
            delete params.sourceList
            delete params.targetList
            if(form?.approveUser){
              params.approveUserName = personList.value.find((item:any)=>item.userId == form.approveUser)?.nickName
            }
            if(type === 'edit'){
               params.id = form.id
            }
            console.log(params,'=====params======')
            templateAddShowTipHandle(handleType,params)
        })
    }

    function templateAddShowTipHandle(handleType?: string, params:  any = {}) {
      if(handleType === 'submit'){
        ElMessageBox.confirm(t('disassemblyAssembleTemplate.message.submitTip'), t('common.tipTitle'), {
          confirmButtonText: t('common.confirm'),
          cancelButtonText: t('common.cancel'),
          type: "warning",
        }).then(
          () => {
            if(type === 'add'){
              addTemplateFetch(params, handleType)
            }else{
              editTemplateFetch(params,handleType)
            }
          },
          () => {
            ElMessage.info(t('disassemblyAssembleTemplate.message.submitCancel'));
          }
        );
      }else{
        if(type === 'add'){
          addTemplateFetch(params, handleType)
        }else{
          editTemplateFetch(params, handleType)
        }
      }
    }
    function addTemplateFetch(params:  DisassemblyAssembleTemplateFrom, handleType: string) {
      DisassemblyAssembleTemplateAPI.addDisassemblyAssembleTemplate(params)
        .then((data) => {
          if(handleType === 'submit'){
            ElMessage.success(t('disassemblyAssembleTemplate.message.submitSucess'));
          }else{
            ElMessage.success(t('disassemblyAssembleTemplate.message.saveDraftSucess'));
          }
          handleClose()
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
    function editTemplateFetch(params: DisassemblyAssembleTemplateFrom, handleType: string) {
      DisassemblyAssembleTemplateAPI.submitDisassemblyAssembleTemplate(params)
        .then((data) => {
          if(handleType === 'submit'){
            ElMessage.success(t('disassemblyAssembleTemplate.message.submitSucess'));
          }else{
            ElMessage.success(t('disassemblyAssembleTemplate.message.saveDraftSucess'));
          }
          handleClose()
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }

    /** 添加商品 */
   function addProduct( val: number) {
        dialog.title = val === SourceDetailDataEnum.SOURCE ?
          t('disassemblyAssembleTemplate.title.addSourceProduct')  : val === SourceDetailDataEnum.TARGET ?
            t('disassemblyAssembleTemplate.title.addTargetProduct') : t('disassemblyAssembleTemplate.title.addProduct');
        let data = {
            typeList: 2,
            status:  '1',
            hasAvailableStockQty: '',
            isExcludeVirtualArea: '',
            enableExcludeVirtualArea: '',
        }
        currentDialogRef.value = val;
        addProductRef.value.setFormData({queryParams:data});
        addProductRef.value.getOutWarehouseAreaList();
        addProductRef.value.queryManagerCategoryList();
        dialog.visible = true;
    }

    function getTemplateDetail() {
      if(type !== 'edit') return false
      submitLoading.value = true;
      DisassemblyAssembleTemplateAPI.getDisassemblyAssembleTemplateDetail({ id })
        .then((data) => {
          Object.assign(form, data)
          form.sourceList = data?.disassemblyTemplateProductSourceList || []
          form.targetList = data?.disassemblyTemplateProductTargetList || []
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }

    onMounted(async () => {
     /* 获取审核人*/
      queryPersonList()
      /*编辑获取详情*/
      getTemplateDetail()
    });
</script>
<style scoped lang="scss">
    .disassemblyAssembleTemplate {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .trapezoid {
                max-width:80%;
                //width:250px;
                padding: 9px 50px 9px 26px;
                clip-path: polygon(0 0, 90% 0, 100% 100%, 0% 100%);
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 16px;
                color: #FFFFFF;
                text-align: left;
                font-style: normal;
            }
            right{
            }
            .fw-400{
                font-weight: 400;
                margin-right: 20px;
            }
            .trapezoid-color1 {
                background-color: var(--el-color-primary);
            }
            .trapezoid-color2 {
                background-color: #008E7C;
            }
            .button-add{
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;
            }
        }
        .link{
            color: var(--el-color-primary);
            cursor: pointer;
        }
    }
</style>
