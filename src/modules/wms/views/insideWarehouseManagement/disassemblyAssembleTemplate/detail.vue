<template>
  <div class="app-container">
    <div class="disassemblyAssembleTemplate" v-loading="loading">
      <div >
        <div class="page-title">
          <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
          <div>
            <span>{{t('disassemblyAssembleTemplate.button.disassemblyAssembleTemplateCode')}}：{{form.disassemblyTemplateCode}}</span>
          </div>
        </div>
      </div>
      <div class="page-content">

          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("disassemblyAssembleTemplate.label.basicInformation") }}
            </div>
          </div>
          <div>
            <el-descriptions :column="4" style="width: 100%">
              <!--模板编码-->
              <el-descriptions-item :label="$t('disassemblyAssembleTemplate.label.templateCode')">
                {{form?.disassemblyTemplateCode ? form?.disassemblyTemplateCode : '-'}}
              </el-descriptions-item>
              <!--模板名称-->
              <el-descriptions-item :label="$t('disassemblyAssembleTemplate.label.templateName')">
                {{ form?.disassemblyTemplateName ? form.disassemblyTemplateName : '-'}}
              </el-descriptions-item>
              <!--申请人-->
              <el-descriptions-item :label="$t('disassemblyAssembleTemplate.label.applyName')">
                {{form?.applyUserName ? form?.applyUserName : '-'}}
              </el-descriptions-item>
              <!--申请时间-->
              <el-descriptions-item :label="$t('disassemblyAssembleTemplate.label.applyTime')">
                {{ form?.applyTime ? parseDateTime(form?.applyTime, "dateTime"):'-' }}
              </el-descriptions-item>
              <!--是否审核-->
              <el-descriptions-item :label="$t('disassemblyAssembleTemplate.label.isApprove')">
                {{ form?.isApprove == 1 ? $t('disassemblyAssembleTemplate.whetherOption.yes') : $t('disassemblyAssembleTemplate.whetherOption.no')}}
              </el-descriptions-item>
              <!--审核人-->
              <el-descriptions-item v-if="isDetailFlag" :label="$t('disassemblyAssembleTemplate.label.approveUser')">
                {{form?.approveUser ? form?.approveUserName : '-'}}
              </el-descriptions-item>
              <!--审核时间-->
              <el-descriptions-item v-if="isDetailFlag" :label="$t('disassemblyAssembleTemplate.label.approveTime')">
                {{ form?.approveTime ? parseDateTime(form?.approveTime, "dateTime"):'-' }}
              </el-descriptions-item>
              <!--审核结果-->
              <el-descriptions-item v-if="isDetailFlag" :label="$t('disassemblyAssembleTemplate.label.approveResult')">
                <span v-if="form?.approveStatus">{{ $t(`disassemblyAssembleTemplate.approveStatus[${form?.approveStatus}]`) }}</span>
                <span v-else>-</span>
              </el-descriptions-item>
              <!--审核意见-->
              <el-descriptions-item v-if="isDetailFlag" :label="$t('disassemblyAssembleTemplate.label.approveDesc')">
                {{ form?.approveRemark ? form.approveRemark : '-'}}
              </el-descriptions-item>
              <!--备注-->
              <el-descriptions-item :label="$t('disassemblyAssembleTemplate.label.remark')">
                <span v-if="form.remark" style="word-break:break-all;">{{form.remark}}</span>
                <span v-else>-</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="line"></div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("disassemblyAssembleTemplate.label.templateDetail") }}
            </div>
          </div>
          <div class="flex-center-space-start">
            <div style="width: calc(50% - 5px)">
              <div class="flex-center-but">
                <div class="trapezoid trapezoid-color1">
                  {{$t('disassemblyAssembleTemplate.label.sourceProduct')}}
                </div>
              </div>
              <el-table
                v-loading="loadSource"
                :data="form.sourceList"
                highlight-current-row
                stripe
              >
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productInformation')" show-overflow-tooltip min-width="220px">
                  <template #default="scope">
                    <div class="product-div">
                      <div class="product">
                        <div class="product-name">{{scope.row.productName}}</div>
                        <div>
                          <span class="product-key">{{$t('disassemblyAssembleTemplate.label.productCode')}}：</span>
                          <span class="product-value">{{scope.row.productCode}}</span>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productSpec')" prop="productSpec" show-overflow-tooltip min-width="100px"/>
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                  <template #default="scope">
                    {{scope.row.isDiscreteUnit === 1 ? $t('disassemblyAssembleTemplate.whetherOption.yes') : $t('disassemblyAssembleTemplate.whetherOption.no')}}
                  </template>
                </el-table-column>
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productQty')" show-overflow-tooltip min-width="200px">
                  <template #default="scope">
                    <div>
                      <span v-if="scope.row?.qty || scope.row?.qty == 0">{{ scope.row?.qty }} {{ scope.row?.qtyUnitName }}</span>
                      <span v-else>-</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productTransformQty')" show-overflow-tooltip min-width="200px">
                  <template #default="scope">
                    <div>
                      <span v-if="scope.row?.convertedQty || scope.row?.convertedQty == 0">{{ scope.row?.convertedQty }} {{ scope.row?.convertedQtyUnitName }}</span>
                      <span v-else>-</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="mt36px pl7px pr7px"><el-icon color="var(--el-color-primary)"><Right /></el-icon></div>
            <div  style="width: calc(50% - 5px)">
              <div class="flex-center-but">
                <div class="trapezoid trapezoid-color1">
                  {{$t('disassemblyAssembleTemplate.label.targetProduct')}}
                </div>
              </div>
              <el-table
                :data="form.targetList"
                highlight-current-row
                stripe
              >
                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productInformation')" show-overflow-tooltip min-width="220px">
                  <template #default="scope">
                    <div class="product-div">
                      <div class="product">
                        <div class="product-name">{{scope.row.productName}}</div>
                        <div>
                          <span class="product-key">{{$t('disassemblyAssembleTemplate.label.productCode')}}：</span>
                          <span class="product-value">{{scope.row.productCode}}</span>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productSpec')" prop="productSpec" show-overflow-tooltip min-width="100px"/>
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                  <template #default="scope">
                    {{scope.row.isDiscreteUnit === 1 ? $t('disassemblyAssembleTemplate.whetherOption.yes') : $t('disassemblyAssembleTemplate.whetherOption.no')}}
                  </template>
                </el-table-column>
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productQty')" show-overflow-tooltip min-width="200px">
                  <template #default="scope">
                    <div>
                      <span v-if="scope.row?.qty || scope.row?.qty == 0">{{ scope.row?.qty }} {{ scope.row?.qtyUnitName }}</span>
                      <span v-else>-</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('disassemblyAssembleTemplate.label.productTransformQty')" show-overflow-tooltip min-width="200px">
                  <template #default="scope">
                    <div>
                      <span v-if="scope.row?.convertedQty || scope.row?.convertedQty == 0">{{ scope.row?.convertedQty }} {{ scope.row?.convertedQtyUnitName }}</span>
                      <span v-else>-</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        <div class="mt-24px"  v-if="!isDetailFlag">
          <el-form
            :model="form"
            ref="fromRef"
            label-width="108px"
            label-position="left"
          >
            <el-row>
              <el-col :span="24">
                <!--  审核结果  -->
                <el-form-item :label="$t('disassemblyAssembleTemplate.label.approveResult')" prop="approveStatus">
                  <el-radio-group v-model="form.approveStatus" @change="approveStatusChange">
                    <el-radio :value="2">{{ $t("disassemblyAssembleTemplate.label.pass") }}</el-radio>
                    <el-radio :value="3">{{ $t("disassemblyAssembleTemplate.label.reject") }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!--  审核意见  -->
                <el-form-item :label="$t('disassemblyAssembleTemplate.label.approveDesc')" prop="approveRemark">
                  <el-input
                    :rows="4"
                    type="textarea"
                    show-word-limit
                    v-model="form.approveRemark"
                    :placeholder="$t('disassemblyAssembleTemplate.placeholder.approveDesc')"
                    maxlength="200"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("disassemblyAssembleTemplate.button.back") }}</el-button>
        <el-button v-if="!isDetailFlag" type="primary" @click="handleSubmit()" :loading="submitLoading">{{ $t("disassemblyAssembleTemplate.button.submit") }}</el-button>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "disassemblyAssembleTemplateDetail",
  inheritAttrs: false,
});

import { parseDateTime } from "@/core/utils";
import {useRoute,useRouter} from "vue-router";
import {useTagsViewStore} from "@/core/store";
import DisassemblyAssembleTemplateAPI, {
  DisassemblyAssembleTemplateFrom,
} from "@/modules/wms/api/disassemblyAssembleTemplate";


const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore()
const { t } = useI18n();

const fromRef = ref()
const submitLoading = ref(false)

const loadSource = ref(false);
const loading = ref(false);
const id = route.query.id;
const type = route.query.type;

const isDetailFlag = computed(() => {
  return type === 'detail'
})

const form = reactive({
  approveStatus: null,//审核结果
  approveRemark: '', //审核意见
  sourceList:[],
  targetList:[],
});


function handleSubmit() {
  submitLoading.value = true;
  let params = {
    ...form,
  }
  DisassemblyAssembleTemplateAPI.approveDisassemblyAssembleTemplateDetail(params)
    .then((data) => {
      ElMessage.success(t('disassemblyAssembleTemplate.message.approveSuccess'));
      handleClose()
    })
    .finally(() => {
      submitLoading.value = false;
    });
}

function approveStatusChange(value: number) {
  form.approveRemark = value === 2 ? t('disassemblyAssembleTemplate.label.pass') : ''
}


async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}


function getTemplateDetail() {
  submitLoading.value = true;
  DisassemblyAssembleTemplateAPI.getDisassemblyAssembleTemplateDetail({ id })
    .then((data) => {
      Object.assign(form, data)
      form.sourceList = data?.disassemblyTemplateProductSourceList || []
      form.targetList = data?.disassemblyTemplateProductTargetList || []
      console.log(form,'============9999999999999')
    })
    .finally(() => {
      submitLoading.value = false;
    });
}

onMounted(async () => {
  /*编辑获取详情*/
  getTemplateDetail()
});
</script>
<style scoped lang="scss">
.disassemblyAssembleTemplate {
  background: #FFFFFF;
  border-radius: 4px;
  .page-content{
    .trapezoid {
      max-width:80%;
      //width:250px;
      padding: 9px 50px 9px 26px;
      clip-path: polygon(0 0, 90% 0, 100% 100%, 0% 100%);
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
    }
    right{
    }
    .fw-400{
      font-weight: 400;
      margin-right: 20px;
    }
    .trapezoid-color1 {
      background-color: var(--el-color-primary);
    }
    .trapezoid-color2 {
      background-color: #008E7C;
    }
    .button-add{
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary)
    }
    .table-title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      background: #F4F6FA;
      box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585F;
      font-style: normal;
      padding: 15px 12px;
    }
  }
  .link{
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
</style>
