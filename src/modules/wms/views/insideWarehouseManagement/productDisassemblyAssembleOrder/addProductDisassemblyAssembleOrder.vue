<template>
    <div class="app-container">
        <div class="addProductDisassemblyAssembleOrder"  v-loading="loading">
            <div >
                <div class="page-title">
                    <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                    <div>
                        <span v-if="type=='add'">{{ $t("productDisassemblyAssembleOrder.button.addProductDisassemblyAssembleOrder") }}</span>
                        <span v-else> {{t('productDisassemblyAssembleOrder.label.disassemblyOrderCode')}}：{{form.disassemblyOrderCode}}</span>
                    </div>
                </div>
            </div>
            <div class="grad-row" style="position: absolute; top: 15px;right: 30px"  v-if="type=='edit'">
                <span class="el-form-item__label">{{ $t("productDisassemblyAssembleOrder.label.createUserName") }}：<span  class="el-form-item__content">{{form.createUserName}}</span></span>
                <span class="el-form-item__label"> {{t('productDisassemblyAssembleOrder.label.createTime')}}： <span class="el-form-item__content">{{parseDateTime(form.createTime, "dateTime")}}</span></span>
            </div>
            <div class="page-content">
                <el-form
                        :model="form"
                        :rules="rules"
                        ref="fromRef"
                        label-width="108px"
                        label-position="right"
                >
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("productDisassemblyAssembleOrder.label.basicInformation") }}
                        </div>
                    </div>
                    <div v-if="type=='detail'">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.createUserName')">
                                    {{form.createUserName}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.createTime')">
                                    {{parseDateTime(form.createTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.disassemblerName')">
                                    <span>{{form.disassemblerName}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.disassemblyTime')">
                                    {{parseDateTime(form.disassemblyTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.orderType')">
                                    <span>{{form.orderType}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.receiveStatus')">
                                    <span  v-if="!isEmpty(form.receivingStatus)" style="word-break:break-all;">{{filterReceiveStatus(form.receivingStatus)}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.receiveName')">
                                    <span  v-if="form.receivingUserName" style="word-break:break-all;">{{form.receivingUserName}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.receiveTime')">
                                    <span  v-if="form.receivingTime" style="word-break:break-all;"> {{parseDateTime(form.receivingTime, "dateTime")}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.disassemblyOrderStatus')">
                                    <span  v-if="!isEmpty(form.orderStatus)" style="word-break:break-all;">{{filterStatus(form.orderStatus)}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
<!--                            <el-col :span="8">-->
<!--                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.receiptNoticeCode')">-->
<!--                                    <span>{{form.receiptNoticeCode}}</span>-->
<!--                                </el-form-item>-->
<!--                            </el-col>-->
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.remark')">
                                    <span v-if="form.remark" style="word-break:break-all;">{{form.remark}}</span>
                                    <span v-else>-</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div v-if="type!=='detail'">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.orderType')" prop="orderType">
                                    <el-select
                                            v-model="form.orderType"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            :disabled="type=='edit'"
                                            @change="changeOrderType"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in orderTypeList" :key="item.orderTypeId" :label="item.orderTypeName" :value="item.orderTypeId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.template')" prop="disassemblyTemplateId">
                                    <el-select
                                            v-model="form.disassemblyTemplateId"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            filterable
                                            @change="changeTemplate"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in templateOptionList" :key="item.id" :label="item.disassemblyTemplateName" :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>

                          <el-col :span="8"  class="flex-center-start">
                            <el-form-item v-if="form.orderType && form.templateCode" :label="getCalculateLabel" prop="calculateNum">
                              <el-input-number
                                  v-model="form.calculateNum"
                                  :placeholder="$t('common.placeholder.inputTips')"
                                  :precision="0"
                                  :controls="false"
                                  class="!w-[256px]"
                              />
                              <el-button type="primary" @click="calculateHandle()" :loading="submitLoading" class="ml20px">
                                {{ $t("productDisassemblyAssembleOrder.button.calculate") }}
                              </el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item :label="$t('productDisassemblyAssembleOrder.label.remark')" prop="remark">
                                    <el-input
                                            :rows="4"
                                            type="textarea"
                                            show-word-limit
                                            v-model="form.remark"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            maxlength="200"
                                            clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("productDisassemblyAssembleOrder.label.productDisassemblyAssembleOrderInformation") }}
                        </div>
                    </div>

                    <div class="flex-center-space-start">
                       <div style="width: calc(50% - 5px)">
                           <div class="flex-center-but">
                               <div class="trapezoid trapezoid-color1">
                                   {{$t('productDisassemblyAssembleOrder.label.sourceProduct')}}
                                   <span class="fw-400">（{{$t('productDisassemblyAssembleOrder.label.totalCountProduct')}}:{{sourceTotalNum}}</span>
                                   <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalWeight')}}:
                                       <template v-if="type!=='detail'">  {{sourceTotalWeight}}</template>
                                        <template v-else> {{form.sourceProductTotalWeight}}</template>
                                   </span>
                                   <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalRowCount')}}:{{ sourceSkuProductNum }}）</span>
                               </div>
                               <div class="button-add cursor-pointer" @click="addProduct(SourceDetailDataEnum.SOURCE)" v-if="type!=='detail'">
                                   {{$t('productDisassemblyAssembleOrder.button.addProduct')}}
                               </div>
                           </div>
                           <el-table v-loading="loadSource" :data="form.sourceList" highlight-current-row stripe>
                               <el-table-column type="index" :label="$t('common.sort')" width="60" />
                               <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" show-overflow-tooltip min-width="220px">
                                   <template #default="scope">
                                       <div class="product-div">
                                           <div class="product">
                                               <div class="product-name">{{scope.row.productName}}</div>
                                               <div>
                                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                                                   <span class="product-value">{{scope.row.productCode}}</span>
                                               </div>
                                               <div  v-if="type!=='detail'">
                                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productSpec')}}：</span>
                                                   <span class="product-value">{{scope.row.productSpec}}</span>
                                               </div>
                                           </div>
                                       </div>
                                   </template>
                               </el-table-column>
                               <!--出库库区-->
                               <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.sourceWarehouseArea')" show-overflow-tooltip min-width="200px">
                                   <template #default="scope">
                                       <el-form-item class="mt15px" label-width="0px" :prop="'sourceList.'+scope.$index+'.targetWarehouseAreaId'" :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.sourceWarehouseAreaId'),trigger:['blur','change']}]">
                                           <el-select
                                               v-model="scope.row.targetWarehouseAreaId"
                                               :placeholder="$t('common.placeholder.selectTips')"
                                               filterable
                                               @change="(val: string) => sourceWarehouseChange(val, scope.row)"
                                               clearable
                                           >
                                               <el-option v-for="item in outWarehouseAreaList" :key="item.id" :label="item.warehouseArea" :value="item.id"></el-option>
                                           </el-select>
                                       </el-form-item>
                                   </template>
                               </el-table-column>
                              <!--单价-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.price')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item class="mt15px" label-width="0px" :prop="'sourceList.'+scope.$index+'.unitPrice'" :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.unitPrice'),trigger:['blur','change']}]">
                                   <el-input-number
                                        v-model="scope.row.unitPrice"
                                        :placeholder="$t('common.placeholder.inputTips')"
                                        :precision="4" :min="0"
                                        @change="(val: number) => unitPriceChange(val, scope.row, SourceDetailDataEnum.SOURCE)"
                                        controls-position="right"/>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--出库量-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.outWarehouseNum')" min-width="260px">
                               <template #default="scope">
                                 <el-form-item class="mt15px" label-width="0px" :prop="'sourceList.'+scope.$index+ '.disassemblyOutQty'"
                                               :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},]">
                                   <div>
                                     <el-input
                                         v-model="scope.row.disassemblyOutQty"
                                         @change="(val: number) => firstLevelQtyChange(val, scope.row, 'disassemblyOutWeight')"
                                         :placeholder="$t('common.placeholder.inputTips')"
                                         clearable
                                         :precision="3" :min="0"
                                     >
                                       <template #append>{{ scope.row?.productUnitName}}</template>
                                     </el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--一级单位增减-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.allUnitIncreaseAndDecrease')" prop="isDiscreteUnit" show-overflow-tooltip min-width="120px">
                               <template #default="scope">
                                 {{scope.row.isDiscreteUnit === 1 ? $t('productDisassemblyAssembleOrder.whetherOption.yes') : $t('productDisassemblyAssembleOrder.whetherOption.no')}}
                               </template>
                             </el-table-column>
                             <!--出库转换量-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.outWarehouseTransferNum')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <el-form-item class="mt15px" label-width="0px" :prop="'sourceList.'+scope.$index+ '.disassemblyOutWeight'"
                                               :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},]">
                                   <div>
                                     <el-input
                                         v-model="scope.row.disassemblyOutWeight"
                                         @change="(val: number) => secondLevelQtyChange(val, scope.row, 'disassemblyOutQty')"
                                         :placeholder="$t('common.placeholder.inputTips')"
                                         :precision="3" :min="0"
                                         clearable
                                     >
                                       <template #append>{{ scope.row?.conversionRelSecondUnitName}}</template>
                                     </el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--金额-->
                             <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.amount')" prop="amount" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <el-form-item class="mt15px" label-width="0px" :prop="'sourceList.'+scope.$index+ '.amount'"
                                               :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.amount'),trigger:['blur','change']},]">
                                   <div>
                                     <el-input-number v-model="scope.row.amount"
                                                      :placeholder="$t('common.placeholder.inputTips')"
                                                      @change="(val: number) => amountChange(val, scope.row, SourceDetailDataEnum.SOURCE)"
                                                      :precision="2" :min="0"
                                                      controls-position="right"/>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--包装-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.packaging')" prop="productPackaging" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <el-form-item class="mt15px" label-width="0px" :prop="'sourceList.'+scope.$index+ '.productPackaging'"
                                               :rules="[{required:false,message:t('productDisassemblyAssembleOrder.rules.productQty'),trigger:['blur','change']},]">
                                   <div>
                                     <el-input
                                         v-model="scope.row.productPackaging"
                                         :placeholder="$t('common.placeholder.inputTips')"
                                         clearable
                                     >
                                     </el-input>
                                   </div>
                                 </el-form-item>
                               </template>
                             </el-table-column>
                             <!--可用库存-->
                             <el-table-column :label="$t('productDisassemblyAssembleOrder.label.availableWarehouse')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                               <template #default="scope">
                                 <div>
                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.availableWarehouseNum')}}：</span>
                                   <span class="product-value">{{scope.row.availableStockQty || '-'}}</span>
                                 </div>
                                 <div>
                                   <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.availableWarehouseTransferNum')}}：</span>
                                   <span class="product-value">{{scope.row.availableStockWeight || '-'}}</span>
                                 </div>
                               </template>
                             </el-table-column>
                               <el-table-column fixed="right" :label="$t('common.handle')" width="80" v-if="type!=='detail'">
                                 <template #default="scope">
                                   <el-button type="primary" link size="small" @click="handleAddRow(scope.$index, scope.row, SourceDetailDataEnum.SOURCE)"><el-icon><Plus /></el-icon></el-button>
                                   <el-button type="danger" link size="small" @click="handleDeleteRow(scope.$index, SourceDetailDataEnum.SOURCE)"><el-icon><Minus /></el-icon></el-button>
                                 </template>
                               </el-table-column>
                           </el-table>
                       </div>
                        <div class="mt60px pl7px pr7px"><el-icon color="var(--el-color-primary)"><Right /></el-icon></div>
                        <div  style="width: calc(50% - 5px)">
                            <div class="flex-center-but">
                                <div class="trapezoid trapezoid-color2">
                                    {{$t('productDisassemblyAssembleOrder.label.targetProduct')}}
                                    <span class="fw-400">（{{$t('productDisassemblyAssembleOrder.label.totalCountProduct')}}:{{targetTotalNum}}</span>
                                    <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalWeight')}}:
                                        <template v-if="type!=='detail'">  {{targetTotalWeight}}</template>
                                        <template v-else> {{form.targetProductTotalWeight}}</template>
                                    </span>
                                    <span class="fw-400">{{$t('productDisassemblyAssembleOrder.label.totalRowCount')}}:{{ targetSkuProductNum }}）</span>
                                </div>
                                <div class="button-add cursor-pointer" @click="addProduct(SourceDetailDataEnum.TARGET)" v-if="type!=='detail'">
                                    {{$t('productDisassemblyAssembleOrder.button.addProduct')}}
                                </div>
                            </div>
                            <el-table :data="form.targetList" highlight-current-row stripe>
                                <el-table-column type="index" :label="$t('common.sort')" width="60" />
                                <el-table-column :label="$t('productDisassemblyAssembleOrder.label.productInformation')" show-overflow-tooltip min-width="220px">
                                    <template #default="scope">
                                        <div class="product-div">
                                            <div class="product">
                                                <div class="product-name">{{scope.row.productName}}</div>
                                                <div>
                                                    <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productCode')}}：</span>
                                                    <span class="product-value">{{scope.row.productCode}}</span>
                                                </div>
                                                <div v-if="type!=='detail'">
                                                    <span class="product-key">{{$t('productDisassemblyAssembleOrder.label.productSpec')}}：</span>
                                                    <span class="product-value">{{scope.row.productSpec}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                              <!--入库库区-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inSourceWarehouseArea')" show-overflow-tooltip min-width="200px">
                                <template #default="scope">
                                  <el-form-item class="mt15px" label-width="0px" :prop="'targetList.'+scope.$index+'.sourceWarehouseAreaId'" :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.sourceWarehouseAreaId'),trigger:['blur','change']}]">
                                    <el-select
                                        v-model="scope.row.sourceWarehouseAreaId"
                                        :placeholder="$t('common.placeholder.selectTips')"
                                        filterable
                                        @change="(val: string) => sourceWarehouseChange(val, scope.row)"
                                        clearable
                                    >
                                      <el-option v-for="item in outWarehouseAreaList" :key="item.id" :label="item.warehouseArea" :value="item.id"></el-option>
                                    </el-select>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--单价-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.price')" min-width="260px">
                                <template #default="scope">
                                  <el-form-item class="mt15px" label-width="0px" :prop="'targetList.'+scope.$index+'.unitPrice'" :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.unitPrice'),trigger:['blur','change']}]">
                                    <el-input-number v-model="scope.row.unitPrice"
                                                     :placeholder="$t('common.placeholder.inputTips')"
                                                     :precision="4" :min="0"
                                                     @change="(val: number) => unitPriceChange(val, scope.row, SourceDetailDataEnum.TARGET)"
                                                     controls-position="right"/>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--入库量-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inWarehouseNum')" min-width="260px">
                                <template #default="scope">
                                  <el-form-item class="mt15px" label-width="0px" :prop="'targetList.'+scope.$index+ '.disassemblyInQty'"
                                                :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},]">
                                    <div>
                                      <el-input
                                          v-model="scope.row.disassemblyInQty"
                                          @change="(val: number) => firstLevelQtyChange(val, scope.row, 'disassemblyInWeight')"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          :precision="3" :min="0"
                                          clearable
                                      >
                                        <template #append>{{ scope.row?.productUnitName}}</template>
                                      </el-input>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--入库转换量-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.inWarehouseTransferNum')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item class="mt15px" label-width="0px" :prop="'targetList.'+scope.$index+ '.disassemblyInWeight'"
                                                :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.disassemblyOutQty'),trigger:['blur','change']},]">
                                    <div>
                                      <el-input
                                          v-model="scope.row.disassemblyInWeight"
                                          @change="(val: number) => secondLevelQtyChange(val, scope.row, 'disassemblyInQty')"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          :precision="3" :min="0"
                                          clearable
                                      >
                                        <template #append>{{ scope.row?.conversionRelSecondUnitName}}</template>
                                      </el-input>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--金额-->
                              <el-table-column :label="'*'+$t('productDisassemblyAssembleOrder.label.amount')" prop="amount" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item class="mt15px" label-width="0px" :prop="'targetList.'+scope.$index+ '.amount'"
                                                :rules="[{required:true,message:t('productDisassemblyAssembleOrder.rules.amount'),trigger:['blur','change']},]">
                                    <div>
                                      <el-input-number v-model="scope.row.amount"
                                                       :placeholder="$t('common.placeholder.inputTips')"
                                                       @change="(val: number) => amountChange(val, scope.row, SourceDetailDataEnum.SOURCE)"
                                                       :precision="2" :min="0"
                                                       controls-position="right"/>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                              <!--包装-->
                              <el-table-column :label="$t('productDisassemblyAssembleOrder.label.packaging')" prop="isDiscreteUnit" show-overflow-tooltip min-width="260px">
                                <template #default="scope">
                                  <el-form-item class="mt15px" label-width="0px" :prop="'targetList.'+scope.$index+ '.qty'"
                                                :rules="[{required:false,message:t('productDisassemblyAssembleOrder.rules.productQty'),trigger:['blur','change']},]">
                                    <div>
                                      <el-input
                                          v-model="scope.row.productPackaging"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          clearable
                                      >
                                      </el-input>
                                    </div>
                                  </el-form-item>
                                </template>
                              </el-table-column>
                                <el-table-column fixed="right" :label="$t('common.handle')" width="80" v-if="type!=='detail'">
                                  <template #default="scope">
                                    <el-button type="primary" link size="small" @click="handleAddRow(scope.$index, scope.row, SourceDetailDataEnum.TARGET)"><el-icon><Plus /></el-icon></el-button>
                                    <el-button type="danger" link size="small" @click="handleDeleteRow(scope.$index, SourceDetailDataEnum.TARGET)"><el-icon><Minus /></el-icon></el-button>
                                  </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>

                </el-form>
            </div>
            <div class="page-footer"  v-if="type!=='detail'">
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
                <el-button type="primary" @click="handleSubmit()" :loading="submitLoading">{{ $t("productDisassemblyAssembleOrder.button.confirm") }}</el-button>
            </div>
            <AddProduct
                    ref="addProductRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    :outWarehouseAreaShow="outWarehouseAreaShow"
                    :availableStockQtyShow="availableStockQtyShow"
                    :outWarehouseAreaFromMultipShow="outWarehouseAreaFromMultipShow"
                    @onSubmit="onSubmit"
            />
            <DetailList  ref="detailListRef"
                         v-model:dialog-visible="detailDialog.visible"
                         :title="detailDialog.title" />
        </div>
    </div>
</template>

<script setup lang="ts">

    import {convertUnitParamsInterface} from "@/modules/wms/api/disassemblyAssembleTemplate";

    defineOptions({
        name: "AddProductDisassemblyAssembleOrder",
        inheritAttrs: false,
    });

    import AddProduct from "../../../components/addProduct.vue";
    import DetailList from "./components/detailList.vue";

    import { parseDateTime,isEmpty } from "@/core/utils/index.js";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import warehouseEntryAPI from "@/modules/wms/api/warehouseEntry";
    import ProductDisassemblyAssembleOrderAPI,{ProductDisassemblyAssembleOrderFrom} from "@/modules/wms/api/productDisassemblyAssembleOrder";
    import CommonAPI, { ProductAllPageQuery,ProductAllPageVO}  from "@/modules/wms/api/common";
    import {Minus, Plus} from "@element-plus/icons-vue";


    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const outWarehouseAreaList = ref([])
    const outWarehouseAreaEnableList = ref([])

    const fromRef = ref()
    const submitLoading = ref(false)
    const queryFormRef = ref(ElForm);
    const loadSource = ref(false);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;
    const addProductRef = ref();
    const outWarehouseAreaShow = ref(false);
    const availableStockQtyShow = ref(false);
    const outWarehouseAreaFromMultipShow = ref(false);
    const supplierList = ref([])
    const formUpdateRef = ref(null);
    const orderTypeList = ref([
        {
            orderTypeId: 1,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productPortfolio')
        },
        {
            orderTypeId: 2,
            orderTypeName: t('productDisassemblyAssembleOrder.orderTypeList.productSplit')
        },
    ])
    const orderStatusList = ref([
        {
            orderStatusId: 0,
            orderStatusName: t('productDisassemblyAssembleOrder.orderStatusList.draft')
        },
        {
            orderStatusId: 2,
            orderStatusName: t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssemble')
        },
        {
            orderStatusId: 1,
            orderStatusName:t('productDisassemblyAssembleOrder.orderStatusList.disassemblyAssembleFinish')
        },
    ])
    const receiveOption=ref([
        {
            value: 0,
            label: t('productDisassemblyAssembleOrder.whetherOption.no')
        },
        {
            value: 1,
            label: t('productDisassemblyAssembleOrder.whetherOption.yes')
        },

    ])

    const enum SourceDetailDataEnum {
      SOURCE = 1,
      TARGET = 2,
    }
    const enum ConvertUnitTypeEnum {
      /** 一级转二级 */
      FIRST_TO_SECOND = "FIRST_TO_SECOND",
      /** 二级转一级 */
      SECOND_TO_FIRST = "SECOND_TO_FIRST",
    }

    const dialog = reactive({
        title: "",
        visible: false,
    });
    const detailDialog = reactive({
        title: "",
        visible: false,
    });
    const detailListRef=ref()
    const num = ref(0)
    const form = reactive<ProductDisassemblyAssembleOrderFrom>({
       orderType :1,

        sourceList:[],
        targetList:[],
    });
    const queryParams = reactive<ProductAllPageQuery>({
        page: 1,
        limit: 20,
    });
    const productTotal = ref(0);
    const receiptNoticeCodeOld = ref();
    const productAllList = ref<ProductAllPageVO[]>();
    const rules = reactive({
        orderType: [{ required: true, message: t("productDisassemblyAssembleOrder.rules.orderType"), trigger: ["blur","change"] }],
    });


    watch(() =>form.receiptNoticeCode, (newValue, oldValue) => {
       receiptNoticeCodeOld.value = oldValue; // 使用 watch 的第二个参数直接获取旧值（Vue 3.2+）
    });

    //计算源 总量: 出库量求和
    const sourceTotalNum = computed(() => {
        let calc = form.sourceList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyOutQty || 0);
        }, 0)
        return calc
    })

    //计算源 总重量 出库转换量求和
    const sourceTotalWeight = computed(() => {
        let calc = form.sourceList?.reduce((total, item) => {
            return Number(total) +  Number(item.disassemblyOutWeight || 0);
        }, 0).toFixed(3)
        return calc
    })

    //计算源 商品个数 SKU的商品个数
    const sourceSkuProductNum = computed(() => {
      return form.sourceList?.filter(item => item.isSku)?.length || '0'
    })

    //计算目标 总量: 入库量求和
    const targetTotalNum = computed(() => {
        let calc = form.targetList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyInQty || 0);
        }, 0)
        return calc
    })

    //计算目标 总重量:入库转换量求和
    const targetTotalWeight = computed(() => {
        let calc = form.targetList?.reduce((total, item) => {
            return Number(total) + Number(item.disassemblyInWeight || 0);
        }, 0).toFixed(3)
        return calc
    })

    //计算目标 商品个数 SKU的商品个数
    const targetSkuProductNum = computed(() => {
      return form.targetList?.filter(item => item.isSku)?.length || '0'
    })

    function validatorDisassemblyOutQty(rule, value, callback) {
        let index = rule.field?.split('.')[1]
        if(form.sourceList[index].sourceType==1){
            let productInventoryQty = form.sourceList[index].productInventoryQty?form.sourceList[index].productInventoryQty:0
            if(value && productInventoryQty){
                if (value > parseFloat(productInventoryQty)) {
                    callback(new Error(t('productDisassemblyAssembleOrder.message.disassemblyOutQtyTips1')));
                } else {
                    callback();
                }
            }
        }else{
            let availableStockQty = form.sourceList[index].availableStockQty?form.sourceList[index].availableStockQty:0
            if(value && availableStockQty){
                if (value > parseFloat(availableStockQty)) {
                    callback(new Error(t('productDisassemblyAssembleOrder.message.disassemblyOutQtyTips2')));
                } else {
                    callback();
                }
            }
        }

    }
    function validatorDisassemblyOutWeight(rule:any, value:any, callback:any) {
        let index = rule.field?.split('.')[1]

            let availableStockWeight = form.sourceList[index].availableStockWeight?form.sourceList[index].availableStockWeight:0
            if(value && availableStockWeight){
                if (value > parseFloat(availableStockWeight)) {
                    callback(new Error(t('productDisassemblyAssembleOrder.message.disassemblyOutWeightTips')));
                } else {
                    callback();
                }
            }


    }
    function validatorDisassemblyInQty(rule:any, value:any, callback:any) {
        if(targetTotalNum.value >5000){
                callback(new Error(t('productDisassemblyAssembleOrder.rules.targetTotalNum')));
            } else {
                callback();
        }
    }


    /** 查询出库库区列表（全部） */
    function getOutWarehouseAreaList() {
        CommonAPI.getOutWarehouseAreaList()
            .then((data) => {
                outWarehouseAreaList.value = data;
                if(outWarehouseAreaList.value && outWarehouseAreaList.value.length>0){
                    outWarehouseAreaList.value.map((item)=>{
                        item.warehouseArea = item.areaName + '|' + item.areaCode
                        return item
                    });
                }
            })
    }

    /** 查询出库库区列表(启用) */
    function getOutWarehouseAreaEnableList(params) {
        return new Promise((resolve, reject) => {
            CommonAPI.getOutWarehouseAreaList(params)
                .then((data) => {
                    outWarehouseAreaEnableList.value = data;
                    if(outWarehouseAreaEnableList.value && outWarehouseAreaEnableList.value.length>0){
                        outWarehouseAreaEnableList.value.map((item)=>{
                            item.warehouseArea =item.areaName + '|' + item.areaCode
                            return item
                        });
                    }
                    resolve();
                })
                .catch((error) => {
                    loading.value = false;
                    reject(error);
                })
        });
    }

    const uniqueByStringify = (array, key1, key2) => {
        const set = new Set();
        return array.filter(item => {
            const key = JSON.stringify({ [key1]: item[key1], [key2]: item[key2] }); // 创建唯一键字符串
            if (!set.has(key)) {
                set.add(key);
                return true; // 添加到结果数组中
            }
            return false; // 跳过重复项
        });
    };

    function changeOrderType () {
      // if(!form.orderType){
      //   form.sourceList = [];
      //   form.targetList = [];
      // }
      if(form.orderType && form.disassemblyTemplateId){
        queryDisassemblyProductDetailList()
      }else{
        form.sourceList = [];
        form.targetList = [];
      }
    }
    function changeTemplate() {
      // if(!form.disassemblyTemplateId){
      //   form.sourceList = [];
      //   form.targetList = [];
      // }
      if(form.orderType && form.disassemblyTemplateId){
        queryDisassemblyProductDetailList()
      }else{
        form.sourceList = [];
        form.targetList = [];
      }
    }
    function queryDisassemblyProductDetailList() {
      const params = {
        orderType: form.orderType,
        disassemblyTemplateId: form.disassemblyTemplateId
      }
      ProductDisassemblyAssembleOrderAPI.queryDisassemblyProductDetailList(params).then((data) => {
        form.sourceList = data?.sourceList.map((item: any) => {
          return {
            isOriginal: true,
            isSku: true,
            ...item
          }
        }) || []
        form.targetList = data?.targetList.map((item: any) => {
          return {
            isOriginal: true,
            isSku: true,
            ...item
          }
        }) || []
      })
    }

    const getCalculateLabel = computed(() => {
      if(form.orderType == 1){
        return t('productDisassemblyAssembleOrder.label.groupNum')
      }else if(form.orderType == 2){
        return t('productDisassemblyAssembleOrder.label.splitNum')
      }
    })

    function unitPriceChange(val, row, sourceType) {
      console.log(row,'-----------------------------')
      let params = {
        productCode: row.productCode,
        unitPrice: row.unitPrice,
      }
      if(sourceType == SourceDetailDataEnum.SOURCE){
        params.convertedQty = row.disassemblyOutWeight
        params.qty = row.disassemblyOutQty
      }
      if(sourceType == SourceDetailDataEnum.TARGET){
        params.convertedQty = row.disassemblyInWeight
        params.qty = row.disassemblyInQty
      }
      calculateAmount(row,params)
    }

    // 计算入库金额
    const calculateAmount = (row, params: any) => {
      debugger
      CommonAPI.calculateAmount(params).then((res) => {
        row.amount = res.amount;
      });
    };

    function amountChange(val, record, sourceType){

    }

    const apiLoading = ref(false)
    const patternRegExp = /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/;
    const firstLevelQtyChange = (val: number, record : Record<string, any>, convertKey: string) => {
      /*一级单位增减为是 支持一级单位转二级单位*/
      if(patternRegExp.test(val) && ((record.isDiscreteUnit == 1) || (record.isDiscreteUnit == 0 && isEmpty(record[convertKey])))){
        apiLoading.value = true;
        const params: convertUnitParamsInterface = {
          productCode: record.productCode,
          originalValue: Number(val),
          convertUnitTypeEnum: ConvertUnitTypeEnum.FIRST_TO_SECOND,
        }
        CommonAPI.convertProductUnit(params)
            .then((data) => {
              record[convertKey] = data?.convertedValue
            }).finally(() => {
          apiLoading.value = false;
        });
      }
    }
    const secondLevelQtyChange = (val, record, convertKey) => {
      /*一级单位增减为否 支持二级单位转一级单位*/
      if(patternRegExp.test(val) && record.isDiscreteUnit == 0 && isEmpty(record[convertKey])){
        apiLoading.value = true;
        const params: convertUnitParamsInterface = {
          productCode: record.productCode,
          originalValue: Number(val),
          convertUnitTypeEnum: ConvertUnitTypeEnum.SECOND_TO_FIRST,
        }
        CommonAPI.convertProductUnit(params)
            .then((data) => {
              record[convertKey] = data?.convertedValue
            }).finally(() => {
          apiLoading.value = false;
        });
      }
    }

    function sourceWarehouseChange(val, record) {
      const params = {
        productCode: record.productCode,
        warehouseAreaId: record?.targetWarehouseAreaId
      }
      ProductDisassemblyAssembleOrderAPI.queryProductWarehouseInfo( params)
          .then((data) => {
            record.availableStockQty = data.availableStockQty
            record.availableStockWeight = data.availableStockWeight
          })
      // 计算加权平均单价
      CommonAPI.calculateWeightedAveragePrice({
        productCode: record.productCode,
        warehouseAreaCode: record.warehouseAreaCode,
        warehouseLocationCode: record.warehouseLocationCode,
        isDiscreteUnit: record.isDiscreteUnit
      }).then((data) => {
        record.unitPrice = data;
      });
    }


    /*计算*/
    function calculateHandle () {

    }

    const templateOptionList = ref([])
    function queryTemplateList() {
      ProductDisassemblyAssembleOrderAPI.queryTemplateList().then(data => {
        templateOptionList.value = data?.map(item => {
          return { ...item }
        });
      })
    }


    function onSubmit(data) {
        if(data){
            if(data.typeList==1){
                if(data.collection && data.collection.length>0){
                    data.collection.forEach(item=>{
                        item.sourceType=2
                        item.sourceWarehouseAreaId=item.warehouseAreaCode
                        item.sourceWarehouseAreaName=item.warehouseAreaName
                    })
                }
                form.sourceList = uniqueByStringify([...form.sourceList,...data.collection], 'productCode', 'sourceWarehouseAreaId');
                console.log("===sourceList==="+form.sourceList);
            }else{
                if(data && data.length>0){
                    data.forEach(item=>{
                        item.targetWarehouseAreaId=item.warehouseAreaCode
                        item.targetWarehouseAreaName=item.warehouseAreaName
                    })
                }
                let arr = data.collection.concat(form.targetList);
                form.targetList=arr;
                console.log("===targetList==="+form.targetList);
            }
        }
    }

    const initProductData = {
      sourceWarehouseAreaId: null,
      targetWarehouseAreaId: null,
      unitPrice: null,
      disassemblyInQty: null,
      disassemblyOutQty: null,
      disassemblyInWeight: null,
      disassemblyOutWeight: null,
      amount: null,
      productPackaging: null,
    }
    const handleAddRow = (index: number, row: any, sourceType: SourceDetailDataEnum) => {
      if (sourceType == SourceDetailDataEnum.SOURCE && form.sourceList?.length) {
        const insertData = {
          ...row,
          ...initProductData
        }
        form.sourceList.splice(index + 1, 0, insertData);
      }
      if (sourceType == SourceDetailDataEnum.TARGET && form.targetList?.length) {
        const insertData = {
          ...row,
          ...initProductData
        }
        form.targetList.splice(index + 1, 0, insertData);
      }
    };

    const handleDeleteRow = (index: number, sourceType: SourceDetailDataEnum) => {
      if(sourceType == SourceDetailDataEnum.SOURCE){
        form.sourceList.splice(index, 1);
      }else{
        form.targetList.splice(index, 1);
      }
      ElMessage.success(t('productDisassemblyAssembleOrder.message.deleteSucess'));
    };

    async function handleClose() {
        await tagsViewStore.delView(route);
        router.go(-1);
    };

    /** 添加/编辑库存转移 */
    function handleSubmit(){
        fromRef.value.validate((valid) => {
            if (!valid) return;
            if(form.sourceList &&  form.sourceList.length==0 || form.targetList &&  form.targetList.length==0){
                submitLoading.value = false;
                return  ElMessage.error(t('productDisassemblyAssembleOrder.message.addOrEditProductDisassemblyAssembleOrderTips'));
            }
            submitLoading.value=true
            let sourceList = []
            let targetList = []
            if(form.sourceList && form.sourceList.length>0){
                form.sourceList.forEach((item)=>{
                    // let sourceWarehouseArea = outWarehouseAreaList.value.filter(out =>item.sourceWarehouseAreaId==out.areaCode)
                    let obj = {
                        id:item.id,
                        // productCode:item.productCode,
                        // productName:item.productName,
                        // productSpec:item.productSpec,
                        // sourceWarehouseAreaId:sourceWarehouseArea[0]?.id,
                        // sourceWarehouseAreaName:sourceWarehouseArea[0].areaName,
                        // disassemblyOutQty:item.disassemblyOutQty,
                        // disassemblyOutWeight:item.disassemblyOutWeight,
                        // productUnitName:item.productUnitName,
                        // sourceOrderType: undefined,
                        // receiptNoticeCode: undefined,
                        // productInventoryQty: undefined,
                        // availableStockQty: item.availableStockQty,
                    }
                    if(item.sourceType==1){
                        obj.sourceOrderType=item.sourceOrderType
                        obj.sourceOrderCode=item.receiptNoticeCode
                        obj.productInventoryQty=item.productInventoryQty
                        obj.entryOrdersProductId=item.entryOrdersProductId
                    }
                    sourceList.push(obj)
                })
            }
            if(form.targetList && form.targetList.length>0){
                form.targetList.forEach((item)=>{
                    let targetWarehouseArea = outWarehouseAreaEnableList.value.filter(out =>item.targetWarehouseAreaId==out.areaCode)
                    let obj = {
                        id:item.id,
                        productCode:item.productCode,
                        productName:item.productName,
                        productSpec:item.productSpec,
                        targetWarehouseAreaId:targetWarehouseArea[0].id,
                        targetWarehouseAreaName:targetWarehouseArea[0].areaName,
                        disassemblyInQty:item.disassemblyInQty,
                        disassemblyInWeight:item.disassemblyInWeight,
                        productUnitName:item.productUnitName,
                    }
                    targetList.push(obj)
                })

                /** 目标商品判重 */
                let mapArr = targetList.map((item: any, index: any) => {
                    item.key = JSON.stringify({ productCode: item.productCode, targetWarehouseAreaId: item.targetWarehouseAreaId })
                    return  item.key;
                });
                let setArr = new Set(mapArr);
                if(setArr.size < mapArr.length){
                    submitLoading.value = false;
                    return  ElMessage.error(t('productDisassemblyAssembleOrder.message.targetProductReapetTips'));
                }
            }
            let params = {
                orderStatus:val,
                orderType:form.orderType,
                remark:form.remark,
                sourceList:sourceList,
                targetList:targetList,
                sourceOrderCode: undefined,
                sourceOrderType: undefined
            }
            if(form.receiptNoticeCode){
                params.sourceOrderCode=form.receiptNoticeCode
                params.sourceOrderType=16
            }
            if(type!=='add'){
               params.id=form.id
               params.disassemblyOrderCode=form.disassemblyOrderCode
            }
            ElMessageBox.confirm(t('productDisassemblyAssembleOrder.message.confirmTips'), t('common.tipTitle'), {
              confirmButtonText: t('common.confirm'),
              cancelButtonText: t('common.cancel'),
              type: "warning",
            }).then(
                () => {
                  ProductDisassemblyAssembleOrderAPI.submitProductDisassemblyAssembleOrder(params)
                      .then((data) => {
                        ElMessage.success(t('productDisassemblyAssembleOrder.message.confirmSucess'));
                        handleClose()
                      })
                      .finally(() => {
                        submitLoading.value = false;
                      });
                },
                () => {
                  submitLoading.value = false;
                  ElMessage.info(t('productDisassemblyAssembleOrder.message.confirmConcel'));
                }
            );
        })
    }

    /** 查询库存转移详情 */
    function getProductDisassemblyAssembleOrderDetail(){
        loading.value = true;
        let params = {
            id:id
        }
        ProductDisassemblyAssembleOrderAPI.getProductDisassemblyAssembleOrderDetail(params)
            .then((data) => {
                Object.assign(form,data)
                form.receiptNoticeCode=form.sourceOrderCode
                if(form.receiptNoticeCode){
                    num.value++
                }
                if(form.sourceList && form.sourceList.length>0){
                    form.sourceList.forEach(item=>{
                        if(type=='detail'){
                            item.sourceWarehouseAreaId=item.sourceWarehouseAreaName + ' | ' + item.sourceWarehouseAreaCode
                            item.sourceWarehouseAreaName=item.targetWarehouseAreaName
                        }else{
                            item.sourceWarehouseAreaId=item.sourceWarehouseAreaCode
                            item.sourceWarehouseAreaName=item.sourceWarehouseAreaName
                        }
                        item.receiptNoticeCode=item.sourceOrderCode
                        if(item.sourceOrderType==16){
                            item.sourceType=1
                        }else{
                            item.sourceType=2
                        }
                    })
                }
                if(form.targetList && form.targetList.length>0){
                    form.targetList.forEach(item=>{
                        if(type=='detail'){
                            item.targetWarehouseAreaId=item.targetWarehouseAreaName + ' | ' + item.targetWarehouseAreaCode
                            item.targetWarehouseAreaName=item.targetWarehouseAreaName
                        }else{
                            let targetWarehouseArea = outWarehouseAreaEnableList.value.filter(out =>item.targetWarehouseAreaCode==out.areaCode)
                            item.targetWarehouseAreaId=targetWarehouseArea && targetWarehouseArea.length>0?item.targetWarehouseAreaCode:''
                            item.targetWarehouseAreaName=targetWarehouseArea && targetWarehouseArea.length>0?item.targetWarehouseAreaName:''
                        }
                    })
                }

                if(type=='detail'){
                    const orderType = orderTypeList.value.filter(out =>form.orderType==out.orderTypeId)
                    form.orderType=orderType[0].orderTypeName
                }
            })
            .finally(() => {
                loading.value = false;
            });
    }


    /** 添加商品 */
   function addProduct(val) {
        outWarehouseAreaShow.value=val==1?true:false
        availableStockQtyShow.value=val==1?true:false
        outWarehouseAreaFromMultipShow.value=val==1?true:false
        dialog.title = val==1? t('productDisassemblyAssembleOrder.title.addSourceProduct'):val==2? t('productDisassemblyAssembleOrder.title.addTargetProduct'):t('productDisassemblyAssembleOrder.title.addProduct');
        let data = {
            typeList:val,
            status: val===1?'':'1',
            hasAvailableStockQty:val==1?true:'',
            isExcludeVirtualArea:val==1?false:'',
            enableExcludeVirtualArea:val==1?true:'',
        }
        addProductRef.value.setFormData({queryParams:data});
        addProductRef.value.getOutWarehouseAreaList();
        addProductRef.value.queryManagerCategoryList();
        dialog.visible = true;
    }

    function handleDetail(row: any, type: string) {
       console.log(row,type)
        detailDialog.visible = true;
        detailDialog.title = `${t('productDisassemblyAssembleOrder.label.ysnCodeTitle')} - ${row.productCode} | ${row.productName}`;

        let params = {
            disassemblyOrderId: row.disassemblyOrderId,
            productCode: row.productCode,
            disassemblyProductType: row.disassemblyProductType,
        }
        if (type==='source'){
            params.sourceWarehouseAreaCode=row.sourceWarehouseAreaCode
        }else {
            params.targetWarehouseAreaCode=row.targetWarehouseAreaCode
        }
        detailListRef.value.handleQuery(params);

    }

    function filterStatus(val:any) {
        if (!isEmpty(val)) {
            return orderStatusList.value.find(item => item.orderStatusId === val)?.orderStatusName || '';
        }
    }
    function filterReceiveStatus(val:any) {
        if (!isEmpty(val)) {
            return receiveOption.value.find(item => item.value === val)?.label || '';
        }
    }

    onMounted(async () => {
        if(type!=='detail'){
            queryTemplateList()

            getOutWarehouseAreaList();
            await getOutWarehouseAreaEnableList({status:1});
        }

       /* if(type=='detail'){
            await  getOutWarehouseAreaEnableList({});
        }else{
            await getOutWarehouseAreaEnableList({status:1});
        }*/
        if(type!=='add'){
            getProductDisassemblyAssembleOrderDetail();
        }
    });
</script>
<style scoped lang="scss">
    .addProductDisassemblyAssembleOrder {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .trapezoid {
                max-width:80%;
                //width:250px;
                padding: 9px 50px 9px 26px;
                clip-path: polygon(0 0, 90% 0, 100% 100%, 0% 100%);
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 16px;
                color: #FFFFFF;
                text-align: left;
                font-style: normal;
            }
            right{
            }
            .fw-400{
                font-weight: 400;
                margin-right: 20px;
            }
            .trapezoid-color1 {
                background-color: var(--el-color-primary);
            }
            .trapezoid-color2 {
                background-color: #008E7C;
            }
            .button-add{
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;
            }
        }
        .link{
            color: var(--el-color-primary);
            cursor: pointer;
        }
    }
</style>
