export function useForm() {
  const formRules = {
    productName: [
      { required: true, message: '请输入商品名称', trigger: ['blur', 'change'] },
      { min: 2, max: 120, message: '长度在 2 到 120 个字符', trigger: ['blur', 'change'] }
    ],
    selectedProductCategory: [
      { required: true, message: '请选择商品分类', trigger: ['blur', 'change'] }
    ],
    /*firstCategoryId: [
      { required: true, message: '请选择一级分类', trigger: ['blur', 'change'] }
    ],
    secondCategoryId: [
      { required: true, message: '请选择二级分类', trigger: ['blur', 'change'] }
    ],
    thirdCategoryId: [
      { required: true, message: '请选择三级分类', trigger: ['blur', 'change'] }
    ],*/
    isStandard: [
      { required: true, message: '请选择是否为标品', trigger: ['blur', 'change'] }
    ],
    productUnitId: [
      { required: true, message: '请选择商品单位', trigger: ['blur', 'change'] }
    ],
    conversionRelFirstNum: [
      { required: true, message: '请输入转换关系', trigger: ['blur', 'change'] }
    ],
    conversionRelSecondNum: [
      { required: true, message: '请输入转换关系', trigger: ['blur', 'change'] }
    ],
    conversionRelSecondUnitId: [
      { required: true, message: '请选择转换关系单位', trigger: ['blur', 'change'] }
    ],
  };

  const formData = ref({
    productCode: "", // *商品编码
    productName: "", // *商品名称 2-120个字
    selectedProductCategory: [], // 商品分类数组集合
    firstCategoryId: "", // *商品分类 一级分类
    secondCategoryId: "", // *商品分类 二级分类
    thirdCategoryId: "", // *商品分类 三级分类
    isStandard: "", // *是否标品 是否标品：1->是，0->否
    productUnitId: "", // *单位
    productUnitName: "", // 单位名称
    conversionRelFirstNum: "", // 换算关系第一个值,范围【0，99999】，单位取商品采购单位id(product_unit_id)
    conversionRelSecondNum: "", // 换算关系第二个值,范围【0，99999】
    conversionRelSecondUnitId: "", // 换算关系第二个值的单位，外键关联（t_product_unit.id）
    conversionRelSecondUnitName: "", // 换算关系第二个值的单位
    attributeType: "", // *商品属性
    productSpec: "", // 规格
    lossRatio: "", // 损耗比例:[0,100]
    length: "", // 商品长宽高
    height: "", // 商品长宽高
    width: "", // 商品长宽高
    volume: "", // 商品长宽高 商品体积
    weight: "", // 商品重量
    productBrandId: "", // 商品品牌
    // productBrandName  // 商品品牌名称
    barcode: "", // 条码信息
    storageCondition: "", // 存储条件
    shelfLife: "", // 保质期
    shelfLifeUnit: 1, // 保质期单位 【1:天，2:月，3:年】
    modelNumber: "", // 型号
    outerProductCode: "", //  外部编码
    remark: "", //  备注
    imagesUrls: "", // 商品图片列表
    mainImageUrl: "", //  *商品图片主图
  });

  return {
    formRules,
    formData
  }
}
