<script setup lang="ts">
import ProductMgAPI from "@wms/api/productManagement";
import { useI18n } from "vue-i18n"; // 导入国际化
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
// 使用国际化
const { t } = useI18n();

const router = useRouter();

const dataList = ref([]);
const searchForm = ref({
  productCode: "", // 商品编码
  productName: "", // 商品名称
  enableStatus: "", // 商品状态 0->禁用，1->启用
});

const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const loading = ref(false);

// 状态选项
const statusOptions = [
  { label: t("WMSProductMangement.label.enable"), value: 1 },
  { label: t("WMSProductMangement.label.disable"), value: 0 },
] as const;

const productOptions = ref<
  { label: string; value: string; name: string; type: string }[]
>([]);
const productLoading = ref(false);

const fetchProductCodeAndName = async (
  query: string = "",
  type: "code" | "name" = "code"
) => {
  if (query.length < 1) return;
  productLoading.value = true;
  try {
    // 根据type传递不同的参数
    const params =
      type === "code" ? { productCode: query } : { productName: query };
    const data = await ProductMgAPI.getProductCodeAndName(params);
    productOptions.value = data.map((item: any) => ({
      label: `${item.code} - ${item.name}`,
      value: item.code,
      name: item.name,
      type, // 添加type用于区分来源
    }));
  } catch (error) {
    console.error("获取商品列表失败:", error);
  } finally {
    productLoading.value = false;
  }
};

const handleProductSelect = (item: any) => {
  if (item) {
    if (item.type === "code") {
      searchForm.value.productCode = item.value;
      searchForm.value.productName = item.name;
    } else {
      searchForm.value.productCode = item.value;
      searchForm.value.productName = item.name;
    }
  }
};

const fetchPageList = async () => {
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm.value,
    };
    const data = await ProductMgAPI.getPageList(params);
    dataList.value = data.records;
    total.value = parseInt(data.total);
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchPageList();
};

const handleReset = () => {
  currentPage.value = 1;
  searchForm.value = {
    productCode: "", // 商品编码
    productName: "", // 商品名称
    enableStatus: "", // 商品状态 0->禁用，1->启用
  };
  fetchPageList();
};

const handleAddProduct = () => {
  router.push({
    path: "/wms/goods/add",
    query: { type: "add" },
  });
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchPageList();
};

const handleEdit = (row: {}) => {
  router.push({
    path: "/wms/goods/add",
    query: {
      id: row.id,
      type: "edit",
    },
  });
};

const handleSwitchChange = (row: {}) => {
  // 切换状态
  ProductMgAPI.updateEnableStatus({
    id: row.id,
    enableStatus: row.enableStatus
  })
    .then(() => {
      ElMessage.success("切换状态成功");
      fetchPageList();
    })
    .catch((error) => {
      console.error("切换状态失败:", error);
    });
}

const attributeTypeFilter = (value: number) => { // 1->库存商品，2->辅材，3->包材
  if (value === 1) {
    return "库存商品";
  } else if (value === 2) {
    return "辅材";
  } else if (value === 3) {
    return "包材";
  }
  return "--";
};
// shelfLifeUnit	保质期单位【1:天，2:月，3:年】
const shelfLifeUnitFilter = (value: number) => {
  if (value === 1) {
    return "天";
  } else if (value === 2) {
    return "月";
  } else if (value === 3) {
    return "年";
  }
  return "--";
};
// dataSourceType	来源类型：0->手工创建；1->PMS
const dataSourceTypeFilter = (value: number) => {
  if (value === 0) {
    return "手工创建";
  } else if (value === 1) {
    return "PMS";
  }
  return "--";
};
const srcList = ref([]);
const showPreview = ref(false); // 控制预览窗口的显示
const previewFile = async (file: {}) => {
  const fileObj = JSON.parse(file);
  const res = await previewSingle(fileObj.bucket, fileObj.fileName, fileObj.originalFileName);
  srcList.value = [res.url];
  showPreview.value = true;
}
onMounted(() => {
  // 初始加载
  fetchPageList();
});
onActivated(() => {
  // 初始加载
  fetchPageList();
});
</script>

<template>
  <div class="contract-container">
    <el-card class="mb-12px search-card">
      <!-- Search Form -->
      <div class="search-form">
        <el-form :model="searchForm">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('WMSProductMangement.label.code')">
                <el-input v-model="searchForm.productCode" :placeholder="t('WMSProductMangement.label.code')" clearable></el-input>
                <!-- <el-select
                  v-model="searchForm.productCode"
                  filterable
                  remote
                  :remote-method="
                    (query) => fetchProductCodeAndName(query, 'code')
                  "
                  :loading="productLoading"
                  clearable
                  :placeholder="t('WMSProductMangement.label.code')"
                  @change="handleProductSelect"
                  icon="Search"
                >
                  <el-option
                    v-for="item in productOptions"
                    :key="item.code"
                    :label="item.code"
                    :value="item.code"
                  />
                </el-select> -->
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('WMSProductMangement.label.name')">
                <el-input v-model="searchForm.productName" :placeholder="t('WMSProductMangement.label.name')" clearable></el-input>
                <!-- <el-select
                  v-model="searchForm.productName"
                  filterable
                  remote
                  :remote-method="
                    (query) => fetchProductCodeAndName(query, 'name')
                  "
                  :loading="productLoading"
                  clearable
                  :placeholder="t('WMSProductMangement.label.name')"
                  @change="handleProductSelect"
                  icon="Search"
                >
                  <el-option
                    v-for="item in productOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item"
                  />
                </el-select> -->
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('WMSProductMangement.label.status')">
                <el-select
                  v-model="searchForm.enableStatus"
                  :placeholder="t('WMSProductMangement.label.status')"
                  clearable
                >
                  <el-option
                    v-for="option in statusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleSearch"
                  v-hasPerm="['pms:contract:page']"
                >
                  {{ t("WMSProductMangement.button.search") }}
                </el-button>
                <el-button
                  @click="handleReset"
                  v-hasPerm="['pms:contract:reset']"
                >
                  {{ t("WMSProductMangement.button.reset") }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <el-card class="content-card">
      <!-- Action Button -->
      <div class="action-bar">
        <el-button
          type="primary"
          @click="handleAddProduct"
          v-hasPerm="['pms:contract:create']"
        >
          {{ t("WMSProductMangement.button.addProduct") }}
        </el-button>
      </div>

      <!-- Contract Table -->
      <el-table :data="dataList" border v-loading="loading">
        <template #empty>
          <Empty />
        </template>

        <el-table-column
          type="index"
          :label="t('WMSProductMangement.label.serialNumber')"
          width="60"
          align="center"
          fixed="left"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.category')"
          prop="fullCategoryName"
          align="center"
          min-width="100"
          fixed="left"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.code')"
          prop="productCode"
          align="center"
          min-width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.name')"
          prop="productName"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.unit')"
          prop="productUnitName"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.minUnit')"
          prop="conversionRelSecondUnitName"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.conversionRelation')"
          prop="conversionRelSecondNum"
          align="center"
          width="100"
        >
          <template #default="{ row }">
            {{row.conversionRelFirstNum || '--'}}{{ row.productUnitName  || '--'}} = {{ row.conversionRelSecondNum || '--' }} {{ row.conversionRelSecondUnitName  || '--'}}
          </template>
        </el-table-column>
        <el-table-column
          :label="t('WMSProductMangement.label.specification')"
          prop="productSpec"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.model')"
          prop="modelNumber"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.shelfLife')"
          prop="shelfLife"
          align="center"
          width="100"
        >
          <template #default="{ row }">
            {{ row.shelfLife || '--' }} {{ row.shelfLife ? shelfLifeUnitFilter(row.shelfLifeUnit) : null }}
          </template>
        </el-table-column>
        <el-table-column
          :label="t('WMSProductMangement.label.storageConditions')"
          prop="storageCondition"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.productProperties')"
          prop="attributeType"
          align="center"
          width="100"
        >
          <template #default="{ row }">
            {{ attributeTypeFilter(row.attributeType) }}
          </template>
        </el-table-column>
        <el-table-column
          :label="t('WMSProductMangement.label.productBrand')"
          prop="productBrandName"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.categoryInfo')"
          prop="barcode"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.remarks')"
          prop="remark"
          align="center"
          width="100"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.imageInfo')"
          prop="mainImageUrl"
          align="center"
          width="100">
          <template #default="{ row }">
            <el-button type="primary" link @click="previewFile(row.mainImageUrl)">查看图片</el-button>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('WMSProductMangement.label.dataSource')"
          prop="dataSourceType"
          align="center"
          width="100"
        >
          <template #default="{ row }">
            {{ dataSourceTypeFilter(row.dataSourceType) }}
          </template>
        </el-table-column>
        <el-table-column
          :label="t('WMSProductMangement.label.externalSystemMapping')"
          prop="outerProductCode"
          align="center"
          min-width="120"
        />
        <el-table-column
          :label="t('WMSProductMangement.label.productStatus')"
          prop="enableStatus"
          align="center"
          width="100"
        >
          <template #default="{ row }">
            <el-switch
              v-model="row.enableStatus"
              :active-value="1"
              :inactive-value="0"
              @change="handleSwitchChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="t('WMSProductMangement.label.operation')"
          align="center"
          width="150"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEdit(row)">
              {{ t("common.edit") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- Pagination -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes , prev, pager, next, jumper"
          @size-change="fetchPageList"
          @current-change="handlePageChange"
        />
      </div>
      <el-image-viewer
        v-if="showPreview"
        :url-list="srcList"
        show-progress
        :initial-index="0"
        @close="showPreview = false"
      />
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-button--primary.el-button--default.is-link) {
  color: #762ADB ;
}

:deep(.el-button--danger.el-button--default.is-link) {
  color: #c00c1d;
}

.contract-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    flex-shrink: 0;
  }

  .content-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .action-bar {
      margin-bottom: 12px;
      flex-shrink: 0;
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.el-form-item--default) {
  // 表单项样式设置
  margin-bottom: 0;
}
</style>
