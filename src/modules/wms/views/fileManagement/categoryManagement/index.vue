<template>
  <div class="dashboard-container">
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <el-form-item
          prop="categoryName"
          :label="$t('WMSProductCategory.label.categoryName')"
        >
          <el-input
            v-model="searchForm.categoryName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="onSearchHandler"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="onSearchHandler"
            v-hasPerm="['wms:product:category:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button @click="onResetHandler" v-hasPerm="['wms:product:category:reset']">
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button type="primary" @click="addFirstCateHandler" v-hasPerm="['wms:product:category:add']">
          {{ $t("WMSProductCategory.button.addCategory") }}
        </el-button>
        <el-button @click="" v-hasPerm="['wms:product:category:sync']">
          {{ $t("WMSProductCategory.button.sync") }}
        </el-button>
      </template>
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        row-key="id"
        lazy
        :load="loadNextLevel"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        border
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          :label="$t('WMSProductCategory.label.categoryName')"
          prop="categoryName"
          show-overflow-tooltip
          min-width="120"
        />
        <!--分类编码-->
        <el-table-column
          :label="$t('WMSProductCategory.label.categoryCode')"
          prop="categoryCode"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          :label="$t('WMSProductCategory.label.count')"
          prop="productCount"
          show-overflow-tooltip
        />
        <el-table-column :label="$t('WMSProductCategory.label.sort')" prop="sort">
          <template #default="scope">
            <div v-if="scope.row.editFlag" style="display: flex">
              <el-input-number
                v-model="sortInputValue"
                :placeholder="$t('common.placeholder.inputTips')"
                controls-position="right"
                :max="1000"
                :min="0"
                :step="1"
                style="width: 150px"
              />
              <el-button
                class="ml-20px"
                type="primary"
                size="small"
                link
                @click="saveSortHandle(scope.row)"
                v-hasPerm="['wms:product:category:sort']"
              >
                <el-icon style="font-size: 15px">
                  <Check />
                </el-icon>
              </el-button>
              <el-button
                type="primary"
                size="small"
                link
                @click="closeSortHandle(scope.row)"
              >
                <el-icon style="font-size: 15px">
                  <CloseBold />
                </el-icon>
              </el-button>
            </div>
            <div v-else>
              {{ scope.row.sort }}
              <el-button
                type="primary"
                size="small"
                link
                @click="editSortHandle(scope.row)"
              >
                <el-icon style="font-size: 15px">
                  <EditPen />
                </el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <!--来源-->
        <el-table-column
          :label="$t('WMSProductCategory.label.dataSource')"
          prop="dataSourceType"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{
              dataSourceTypeFilter[scope.row.dataSourceType]
            }}
          </template>
        </el-table-column>
        <!--外部编码-->
        <el-table-column
          :label="$t('WMSProductCategory.label.outerCode')"
          prop="outerCategoryCode"
          show-overflow-tooltip
          max-width="120"
        />
        <el-table-column :label="$t('common.handle')" fixed="right" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              v-if="checkOperate(scope.row)"
              @click="
                onEditHandler(scope.row);
                dialogMode = 'edit';
              "
              v-hasPerm="['wms:product:category:edit']"
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              type="primary"
              size="small"
              link
              v-if="scope.row.level != 3"
              @click="onAddNextLevelHandler(scope.row)"
              v-hasPerm="['wms:product:category:add']"
            >
              {{ $t("WMSProductCategory.button.add") }}
            </el-button>
            <el-button
              type="primary"
              size="small"
              link
              v-if="checkOperate(scope.row)"
              @click="removeHandle(scope.row)"
              v-hasPerm="['wms:product:category:del']"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="paginationInfo.pageNo"
        v-model:limit="paginationInfo.pageSize"
        @pagination="onPaginationChangeHandler"
      />
    </el-card>
    <el-drawer
      v-model="showDialog"
      :title="
        dialogMode === 'add'
          ? $t('WMSProductCategory.button.addCategory')
          : $t('WMSProductCategory.button.editCategory')
      "
      :close-on-click-modal="false"
      @close="onCloseHandler"
    >
      <el-form
        :model="contentForm"
        :rules="contentFormRules"
        ref="contentFormRef"
        label-position="top"
      >
        <el-form-item
          :label="$t('WMSProductCategory.label.categoryImage')"
          prop="imagesUrl"
        >
          <upload-multiple
            :custom-tips="$t('WMSProductCategory.message.pictureTip')"
            :isPrivate="`public-read`"
            :modelValue="contentForm.imagesUrl"
            @update:model-value="onChangeMultiple"
            ref="detailPicsRef"
            :limit="1"
            :formRef="formUpdateRef"
            class="modify-multipleUpload"
            name="detailPic"
            :isDelete="true"
          >
            <template #default="{ file }">点击上传</template>
          </upload-multiple>
        </el-form-item>
        <el-form-item :label="$t('WMSProductCategory.label.level')" prop="level">
          <el-radio-group v-model="contentForm.level">
            <el-radio
              v-for="(item, i) in cateLevelList"
              :key="i"
              :value="item.id"
              :disabled="item.disabled"
            >
              {{ item.content }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="$t('WMSProductCategory.label.categoryName')"
          prop="categoryName"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.categoryName"
            :maxlength="20"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('WMSProductCategory.label.parentName')"
          prop="parentName"
          v-if="contentForm.level !== 1"
        >
          <el-input v-model="contentForm.parentName" disabled />
        </el-form-item>
        <el-form-item :label="$t('WMSProductCategory.label.outerCode')" prop="outerCategoryCode">
          <el-input
            v-model="contentForm.outerCategoryCode"
            :placeholder="$t('common.placeholder.inputTips')"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item :label="$t('WMSProductCategory.label.categorySequence')" prop="sort">
          <el-input-number
            v-model="contentForm.sort"
            :placeholder="$t('common.placeholder.inputTips')"
            controls-position="right"
            :max="1000"
            :min="0"
            :step="1"
            style="width: 150px"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="onSaveHandler"
          >
            {{ $t("common.confirm") }}
          </el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import formMixin from "@/modules/wms/mixins/form";
import type { FormRules } from "element-plus";
import tableMixin from "@/modules/wms/mixins/table";
import productCategoryAPI, {
  productCategoryForm,
  productCategoryInfo,
} from "@/modules/wms/api/productCategory";

const { proxy } = getCurrentInstance();
const { t } = useI18n();
const tableRef = ref(null) as any;
const formUpdateRef = ref(null);
const loadMap = new Map();
const sortInputValue = ref();
const formRef = ref(null);
const hasEditFlag = ref(false);
const searchForm = reactive({
  categoryName: "",
});
const contentForm = reactive<productCategoryForm>({
  id: "",
  parentId: "0",
  level: 1,
  imagesUrl: "",
  categoryName: "",
  parentName: "",
  outerCategoryCode: "", // 外部编码
});
const dataSourceTypeFilter = { // 来源类型：0->手工创建；1->PMS
  0: proxy.$t("WMSProductCategory.label.dataSourceCreate") ,
  1: proxy.$t("WMSProductCategory.label.dataSourcePMS") ,
};

// 手工创建时可编辑，删除
const checkOperate = (row: {}) => {
  if (row.dataSourceType === 0) {
    return true;
  }
  return false;
}
const cateLevelList = reactive([
  { id: 1, disabled: true, content: proxy.$t("WMSProductCategory.label.firstCategory") },
  { id: 2, disabled: true, content: proxy.$t("WMSProductCategory.label.secondCategory") },
  { id: 3, disabled: true, content: proxy.$t("WMSProductCategory.label.thirdCategory") },
]);
const contentFormRules = reactive<FormRules>({
  categoryName: [
    {
      required: true,
      message: proxy.$t("WMSProductCategory.rules.categoryNameRule"),
      trigger: "blur",
    },
  ],
  sort: [
    {
      required: true,
      message: proxy.$t("WMSProductCategory.rules.sort"),
      trigger: "blur",
    },
    {
      pattern: /^(0|\+?[1-9][0-9]*)$/,
      message: t("WMSProductCategory.rules.sortFomart"),
      trigger: ["blur", "change"],
    },
  ],
});
function saveCallbackHandler() {
  fresh(contentForm.parentId);
}
function editCallbackHandle(row: any) {
  if (contentForm.imagesUrl === null) {
    contentForm.imagesUrl = "";
  }
  checkDetail(row, "edit");
}
const {
  showDialog,
  dialogLoading,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  uselessParams: ["parentName"],
  formAddApi: productCategoryAPI.saveCate,
  formEditApi: productCategoryAPI.updateCate,
  editCallback: editCallbackHandle,
  saveCallback: saveCallbackHandler,
  formatParamsCallback: (params: any) => {
    params.imagesUrl = JSON.stringify(params.imagesUrl);
  },
});
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
} = tableMixin({
  searchForm,
  tableGetApi: productCategoryAPI.getPageList,
  tableDeleteApi: productCategoryAPI.deleteCate,
  removeCallback: saveCallbackHandler,
});
const dialogMode = ref("add");
const addFirstCateHandler = () => {
  dialogMode.value = "add";
  contentForm.parentId = "0";
  contentForm.level = 1;
  onAddHandler();
};
function editSortHandle(row: productCategoryInfo) {
  if (hasEditFlag.value) {
    ElMessage.warning("一次只可编辑一个分类");
  } else {
    sortInputValue.value = row.sort;
    row.editFlag = true;
    hasEditFlag.value = true;
  }
}

function saveSortHandle(row: productCategoryInfo) {
  if (!Number.isInteger(sortInputValue.value)) {
    ElMessage.warning(t("WMSProductCategory.rules.sortFomart"));
    return;
  }
  const params = {
    id: row.id,
    sort: sortInputValue.value,
  };
  productCategoryAPI.updateSort(params).then((res) => {
    row.sort = sortInputValue.value;
    sortInputValue.value = "";
    row.editFlag = false;
    hasEditFlag.value = false;
    fresh(row.parentId);
  });
}
function closeSortHandle(row: productCategoryInfo) {
  sortInputValue.value = "";
  row.editFlag = false;
  hasEditFlag.value = false;
}
function removeHandle(row: any) {
  contentForm.parentId = row.parentId;
  onDeleteHandler(proxy.$t("WMSProductCategory.message.deleteCategoryTip"), "id", row.id);
}
function onAddNextLevelHandler(row: any) {
  contentForm.level = row.level + 1;
  contentForm.parentId = row.id;
  checkDetail(row, "add");
  onAddHandler();
}
function onChangeMultiple(val: any) {
  contentForm.imagesUrl = val ? val : [];
}
// 懒加载表格数据
const loadNextLevel = (
  row: any,
  treeNode: unknown,
  resolve: (date: []) => void
) => {
  loadMap.set(row.id, { row, treeNode, resolve });
  const params = {
    id: row.id,
  };
  productCategoryAPI.getPageList(params).then((res) => {
    resolve(res.records);
    if (res.records.length === 0) {
      tableRef.value.store.states.lazyTreeNodeMap.value[row.id] = [];
    }
  });
};
const fresh = (id: any) => {
  if (id) {
    // 编辑/删除 展开的子节点
    if (loadMap.get(id)) {
      const { row, treeNode, resolve } = loadMap.get(id);
      loadNextLevel(row, treeNode, resolve);
      onSearchHandler();
    } else {
      // 新增根节点下的第一级子节点
      const lazyData = toRaw(tableRef.value.store.states.lazyTreeNodeMap.value);
      for (const [key, courseData] of Object.entries(lazyData) as [any, any]) {
        courseData.map((item: any, index: number) => {
          if (item.id === id) {
            item.hasChildren = 1;
            if (loadMap.get(item.parentId)) {
              const { row, treeNode, resolve } = loadMap.get(item.parentId);
              loadNextLevel(row, treeNode, resolve);
            }
          }
          return item;
        });
      }
      onSearchHandler();
    }
  } else {
    onSearchHandler();
  }
};
function checkDetail(row: any, type: string) {
  const params = {
    id: row.id,
  };
  productCategoryAPI.detailCategory(params).then((res) => {
    contentForm.parentName = res.parentName;
    if (type === "add") {
      if (contentForm.parentName.length > 0) {
        contentForm.parentName =
          contentForm.parentName + "/" + res.categoryName;
      } else {
        contentForm.parentName = res.categoryName;
      }
    }
  }).catch((err) => {
    console.log(err);
  });
}
onMounted(() => {
  onSearchHandler();
});
</script>

<style lang="scss" scoped></style>
