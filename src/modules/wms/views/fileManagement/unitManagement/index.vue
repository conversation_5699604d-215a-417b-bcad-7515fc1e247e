
<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item prop="unitName" :label="$t('WMSUnitManagement.label.name')">
          <el-input
            v-model="queryParams.unitName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleQuery"
            v-hasPerm="['wms:search:product:unit']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button
            @click="handleResetQuery"
            v-hasPerm="['wms:reset:product:unit']"
          >
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          type="primary"
          @click="handleOpenDialog()"
          v-hasPerm="['wms:add:product:unit']"
        >
          {{ $t("WMSUnitManagement.button.addUnit") }}
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="unitList"
        highlight-current-row
        border
      >
          <template #empty>
              <Empty/>
          </template>
        <el-table-column
          :label="$t('WMSUnitManagement.label.name')"
          prop="unitName"
        />
        <el-table-column :label="$t('WMSUnitManagement.label.sort')" prop="sort" />
        <el-table-column
          prop="updateTime"
          :label="$t('WMSUnitManagement.label.updateTime')"
        >
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.updateTime, "dateTime") }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.handle')" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              @click="handleOpenDialog(scope.row)"
              v-hasPerm="['wms:edit:product:unit']"
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row)"
              v-hasPerm="['wms:del:product:unit']"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        :page-sizes="[20, 50, 100, 200]"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 角色表单弹窗 -->
    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      @close="handleCloseDialog"
    >
      <el-form
        ref="roleFormRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item :label="$t('WMSUnitManagement.label.name')" prop="unitName">
          <el-input
            v-model="formData.unitName"
            :placeholder="$t('common.placeholder.inputTips')"
            :maxlength="5"
          />
        </el-form-item>
        <el-form-item :label="$t('WMSUnitManagement.label.order')" prop="sort">
          <el-input-number
            v-model="formData.sort"
            controls-position="right"
            :min="0"
            :step="1"
            style="width: 100px"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">
            {{ $t("common.confirm") }}
          </el-button>
          <el-button @click="handleCloseDialog">
            {{ $t("common.cancel") }}
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Unit",
  inheritAttrs: false,
});
import unitAPI, { PageQuery, unitForm } from "@/modules/wms/api/unit";
import { parseDateTime } from "@/core/utils/index.js";
const { t } = useI18n();
const queryFormRef = ref(ElForm);
const roleFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);

const queryParams = reactive<PageQuery>({
  page: 1,
  limit: 20,
});

// 表格数据
const unitList = ref([]);
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});
// 角色表单
const formData = reactive<unitForm>({
  id: "",
  sort: undefined,
  unitName: "",
});

const rules = reactive({
  unitName: [
    {
      required: true,
      message: t("WMSUnitManagement.rules.name"),
      trigger: "blur",
    },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
      message: t("WMSUnitManagement.rules.nameFomart"),
      trigger: "blur",
    },
  ],
  sort: [
    {
      required: true,
      message: t("WMSUnitManagement.rules.sort"),
      trigger: "blur",
    },
    {
      pattern: /^(0|\+?[1-9][0-9]*)$/,
      message: t("WMSUnitManagement.rules.sortFomart"),
      trigger: ["blur", "change"],
    },
  ],
});

/** 查询 */
function handleQuery() {
  loading.value = true;
  unitAPI
    .getPage(queryParams)
    .then((data: any) => {
      unitList.value = data.records;
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}
/** 打开新增编辑弹窗 */
function handleOpenDialog(row?: any) {
  dialog.visible = true;
  if (row && row.id) {
    dialog.title = t("WMSUnitManagement.title.editUnitTitle");
    Object.assign(formData, row);
  } else {
    dialog.title = t("WMSUnitManagement.title.addUnitTitle");
  }
}

/** 提交角色表单 */
function handleSubmit() {
  roleFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      if (formData.id) {
        unitAPI
          .update(formData)
          .then(() => {
            ElMessage.success(t("WMSUnitManagement.message.editSucess"));
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        unitAPI
          .add(formData)
          .then(() => {
            ElMessage.success(t("WMSUnitManagement.message.addSucess"));
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭角色弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  roleFormRef.value.resetFields();
  roleFormRef.value.clearValidate();

  formData.id = "";
  formData.sort = undefined;
  formData.unitName = "";
}

/** 删除角色 */
function handleDelete(row: any) {
  ElMessageBox.confirm(
    `${t("WMSUnitManagement.message.deleteTips")}${row.unitName}?`,
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      unitAPI
        .deleteByIds({ id: row.id })
        .then(() => {
          ElMessage.success(t("WMSUnitManagement.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("WMSUnitManagement.message.deleteCancel"));
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
