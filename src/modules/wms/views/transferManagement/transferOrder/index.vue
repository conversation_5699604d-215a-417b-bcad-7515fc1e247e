<template>
  <div class="app-container">
    <div class="transfer-order">
      <div class="search-container">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
          <el-form-item prop="transferOrderCode" label="调拨单号">
            <el-input class="!w-[256px]" v-model="queryParams.transferOrderCode" placeholder="请输入调拨单号" clearable />
          </el-form-item>
          <el-form-item prop="transferOrderName" label="调拨主题">
            <el-input class="!w-[256px]" v-model="queryParams.transferOrderName" placeholder="请输入调拨主题" clearable />
          </el-form-item>
          <el-form-item prop="sourceWarehouseCode" label="调出仓库">
            <el-select v-model="queryParams.sourceWarehouseCode" placeholder="请选择调出仓库" clearable filterable
              class="!w-[256px]">
              <el-option v-for="item in warehouseList" :key="item.warehouseCode" :label="item.warehouseName"
                :value="item.warehouseCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="targetWarehouseCode" label="调入仓库">
            <el-select v-model="queryParams.targetWarehouseCode" placeholder="请选择调入仓库" clearable filterable
              class="!w-[256px]">
              <el-option v-for="item in warehouseList" :key="item.warehouseCode" :label="item.warehouseName"
                :value="item.warehouseCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="transferOrderStatus" label="调拨状态">
            <el-select v-model="queryParams.transferOrderStatus" placeholder="请选择调拨状态" clearable filterable multiple
              collapse-tags collapse-tags-tooltip class="!w-[256px]">
              <el-option v-for="(item, index) in transferStatusList" :key="index" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="dateRange">
            <el-select v-model="queryParams.queryTimeType" placeholder="请选择时间类型" class="!w-[120px] mr-2">
              <el-option v-for="item in queryTimeTypeList" :key="item.key" :label="item.value"
                :value="item.key"></el-option>
            </el-select>
            <el-date-picker v-model="queryParams.dateRange" type="datetimerange" range-separator="~"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="defaultTime" class="!w-[350px]" />
            <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)"
              @click="handleChangeDateRange(1)">今天</span>
            <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)"
              @click="handleChangeDateRange(2)">昨天</span>
            <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)"
              @click="handleChangeDateRange(3)">近七天</span>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" v-hasPermi="['wms:transfermgOrder:query']">
              {{ $t('common.search') }}
            </el-button>
            <el-button @click="handleResetQuery" v-hasPermi="['wms:transfermgOrder:query']">
              {{ $t('common.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-card shadow="never" class="table-container">
        <div class="mb-4 flex justify-between">
          <div>
            <el-button type="primary" @click="handleAdd" v-hasPermi="['wms:transgermgAdd']">新建</el-button>
          </div>
        </div>
        <el-table v-loading="loading" :data="tableData" highlight-current-row stripe
          @selection-change="handleSelectionChange" max-height="700">
          <template #empty>
            <Empty />
          </template>

          <!-- 序号列 -->
          <el-table-column label="序号" type="index" width="60" :index="(index) => index + 1" fixed="left" />
          <!-- 调拨主题 -->
          <el-table-column prop="transferOrderName" label="主题" show-overflow-tooltip min-width="120" />
          <!-- 调拨单号 -->
          <el-table-column prop="transferOrderCode" label="调拨单号" show-overflow-tooltip min-width="140" />
          <!-- 调出方 -->
          <el-table-column prop="sourceWarehouseName" label="调出方" show-overflow-tooltip min-width="120" />
          <!-- 调入方 -->
          <el-table-column prop="targetWarehouseName" label="调入方" show-overflow-tooltip min-width="120" />
          <!-- 计划量 -->
          <el-table-column label="计划量" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <div v-if="scope.row.planTotalQty || scope.row.planConvertedQty">
                <div v-if="scope.row.planTotalQty">数量：{{ scope.row.planTotalQty }}</div>
                <div v-if="scope.row.planConvertedQty">转换量：{{ scope.row.planConvertedQty }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 出库量出库量 -->
          <el-table-column label="出库量" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <div v-if="scope.row.outTotalQty || scope.row.outConvertedQty">
                <div v-if="scope.row.outTotalQty">数量：{{ scope.row.outTotalQty }}</div>
                <div v-if="scope.row.outConvertedQty">转换量：{{ scope.row.outConvertedQty }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- 入库量 -->
          <el-table-column label="入库量" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <div v-if="scope.row.inTotalQty || scope.row.inConvertedQty">
                <div v-if="scope.row.inTotalQty">数量：{{ scope.row.inTotalQty }}</div>
                <div v-if="scope.row.inConvertedQty">转换量：{{ scope.row.inConvertedQty }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 计划调出时间 planTransferOutTime-->
          <el-table-column label="计划调出时间" show-overflow-tooltip min-width="150" prop="planTransferOutTime" :formatter="formatDateTime">
          </el-table-column>
          <!-- 计划调入时间 planTransferInTime-->
          <el-table-column label="计划调入时间" show-overflow-tooltip min-width="150" prop="planTransferInTime" :formatter="formatDateTime">
          </el-table-column>

          <!-- 申请人 -->
          <el-table-column label="申请人：" show-overflow-tooltip min-width="150" prop="applicantUserName">
          </el-table-column>
          <!-- 申请时间 -->
          <!-- 创建人 createUserName-->
          <el-table-column label="创建人" show-overflow-tooltip min-width="150" prop="createUserName">
          </el-table-column>
          <!-- 创建时间 createTime-->
          <el-table-column label="创建时间" show-overflow-tooltip min-width="150" prop="createTime" :formatter="formatDateTime">
          </el-table-column>

          <!-- 出库确认 outConfirmInfo-->
          <el-table-column label="出库确认" show-overflow-tooltip min-width="150" prop="outConfirmInfo">
            <template #default="scope">
              <section>
                <div>确认人：{{ scope.row.outConfirmInfo }}</div>
                <div>确认时间：{{ scope.row.outConfirmTime }}</div>
              </section>
            </template>
          </el-table-column>
          <!-- 出库状态 outStatus 出库状态：0=待出库 1=部分出库 2=已出库-->
          <el-table-column label="出库状态" show-overflow-tooltip min-width="150" prop="outStatus">
            <template #default="scope">
              <el-tag v-if="scope.row.outStatus === 0" type="info">待出库</el-tag>
              <el-tag v-else-if="scope.row.outStatus === 1" type="warning">部分出库</el-tag>
              <el-tag v-else-if="scope.row.outStatus === 2" type="success">已出库</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 入库状态 inStatus 入库状态：0=待入库 1=部分入库 2=已入库-->
          <el-table-column label="入库状态" show-overflow-tooltip min-width="150" prop="inStatus">
            <template #default="scope">
              <el-tag v-if="scope.row.inStatus === 0" type="info">待入库</el-tag>
              <el-tag v-else-if="scope.row.inStatus === 1" type="warning">部分入库</el-tag>
              <el-tag v-else-if="scope.row.inStatus === 2" type="success">已入库</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 状态 -->
          <el-table-column label="状态" show-overflow-tooltip min-width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.transferOrderStatus === 0" type="info">草稿</el-tag>
              <el-tag v-else-if="scope.row.transferOrderStatus === 1" type="warning">初始</el-tag>
              <el-tag v-else-if="scope.row.transferOrderStatus === 2" type="primary">调拨中</el-tag>
              <el-tag v-else-if="scope.row.transferOrderStatus === 3" type="success">完结</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- 创建信息 -->
         <!--  <el-table-column label="创建信息" show-overflow-tooltip min-width="150">
            <template #default="scope">
              <div>
                <div>
                  <span>创建人：{{ scope.row.createUserName || '-' }}</span>
                </div>
                <div>
                  <span>创建时间：{{ parseDateTime(scope.row.createTime, 'dateTime') || '-' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
 -->

          <!-- 操作列 -->
          <el-table-column label="操作" fixed="right" width="200">
            <template #default="scope">
              <div class="flex flex-wrap gap-2">
                <!-- 编辑：状态=草稿或初始 -->
                <el-button v-if="scope.row.transferOrderStatus === 0 || scope.row.transferOrderStatus === 1"
                  type="primary" link @click="handleEdit(scope.row)" v-hasPermi="['wms:transgermgAdd']">
                  编辑
                </el-button>

                <!-- 删除：状态=草稿或初始 -->
                <el-button v-if="scope.row.transferOrderStatus === 0 || scope.row.transferOrderStatus === 1"
                  type="danger" link @click="handleDelete(scope.row)" v-hasPermi="['wms:transgermgDel']">
                  删除
                </el-button>

                <!-- 完结：调拨中 -->
                <el-button type="success" link v-if="scope.row.transferOrderStatus === 2"
                  @click="handleComplete(scope.row)" v-hasPermi="['wms:transgermgComplete']">
                  完结
                </el-button>

                <!-- 详情：状态=调拨中或完结  -->
                <el-button type="primary" v-if="scope.row.transferOrderStatus === 2 || scope.row.transferOrderStatus === 3" link @click="handleDetail(scope.row)" v-hasPermi="['wms:transgermgDetali']">
                  详情
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-model:total="total" v-model:page="queryParams.page" v-model:limit="queryParams.limit"
          @pagination="handleQuery" />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import TransferOrderAPI, {
  type TransferOrderPageQuery,
  type TransferOrderListVO,
  type WarehouseVO
} from '@/modules/wms/api/transferOrder';
import { parseDateTime } from '@/core/utils/index.js';
import Empty from '@/core/components/empty/index.vue';
import Pagination from '@/core/components/Pagination/index.vue';

const router = useRouter();

// 响应式数据
const loading = ref(false);
const total = ref(0);
const tableData = ref<TransferOrderListVO[]>([]);
const selectedRows = ref<TransferOrderListVO[]>([]);
const warehouseList = ref<WarehouseVO[]>([]);

// 查询参数
const queryParams = reactive<TransferOrderPageQuery>({
  page: 1,
  limit: 20,
  transferOrderCode: '',
  transferOrderName: '',
  sourceWarehouseCode: '',
  targetWarehouseCode: '',
  transferOrderStatus: [],
  queryTimeType: 4, // 默认创建时间
  dateRange: []
});

// 调拨状态选项
const transferStatusList = [
  { label: '草稿', value: 0 },
  { label: '初始', value: 1 },
  { label: '调拨中', value: 2 },
  { label: '完结', value: 3 }
];

// 时间类型选项
const queryTimeTypeList = [
  { key: 1, value: '申请时间' },
  { key: 2, value: '出库确认时间' },
  { key: 3, value: '入库确认时间' },
  { key: 4, value: '创建时间' },
  { key: 5, value: '最后修改时间' }
];

// 默认时间
const defaultTime = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 1, 1, 23, 59, 59)
];

/** 查询调拨单列表 */
function handleQuery() {
  loading.value = true;

  // 处理时间范围参数
  const params = { ...queryParams };
  if (params.dateRange && params.dateRange.length === 2) {
    params.startTime = params.dateRange[0];
    params.endTime = params.dateRange[1];
    delete params.dateRange;
  }

  TransferOrderAPI.getTransferOrderPage(params)
    .then((response) => {
      tableData.value = response.records || [];
      total.value = parseInt(response.total || '0');
    })
    .catch(() => {
      ElMessage.error('查询失败');
    })
    .finally(() => {
      loading.value = false;
    });
}

function formatDateTime(row: TransferOrderListVO, column: any) {
  return parseDateTime(row[column.property], 'dateTime');
}

/** 重置查询 */
function handleResetQuery() {
  Object.assign(queryParams, {
    page: 1,
    limit: 10,
    transferOrderCode: '',
    transferOrderName: '',
    sourceWarehouseCode: '',
    targetWarehouseCode: '',
    transferOrderStatus: [],
    queryTimeType: 4,
    dateRange: []
  });
  handleQuery();
}

/** 处理时间范围快捷选择 */
function handleChangeDateRange(type: number) {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (type) {
    case 1: // 今天
      queryParams.dateRange = [
        today.toISOString().slice(0, 19).replace('T', ' '),
        new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1000).toISOString().slice(0, 19).replace('T', ' ')
      ];
      break;
    case 2: // 昨天
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      queryParams.dateRange = [
        yesterday.toISOString().slice(0, 19).replace('T', ' '),
        new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1000).toISOString().slice(0, 19).replace('T', ' ')
      ];
      break;
    case 3: // 本周
      const weekStart = new Date(today.getTime() - (today.getDay() || 7) * 24 * 60 * 60 * 1000 + 24 * 60 * 60 * 1000);
      queryParams.dateRange = [
        weekStart.toISOString().slice(0, 19).replace('T', ' '),
        now.toISOString().slice(0, 19).replace('T', ' ')
      ];
      break;
  }
}

/** 表格选择变化 */
function handleSelectionChange(selection: TransferOrderListVO[]) {
  selectedRows.value = selection;
}

/** 新增调拨单 */
function handleAdd() {
  router.push('/wms/transferManagement/transferOrder/add');
}

/** 编辑调拨单 */
function handleEdit(row: TransferOrderListVO) {
  router.push(`/wms/transferManagement/transferOrder/edit/${row.transferOrderCode}`);
}

/** 删除调拨单 */
function handleDelete(row: TransferOrderListVO) {
  ElMessageBox.confirm(
    `是否确认删除已创建的调拨单"${row.transferOrderCode}"？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      return TransferOrderAPI.deleteTransferOrder(row.transferOrderCode!);
    })
    .then(() => {
      ElMessage.success('删除成功');
      handleQuery();
    })
    .catch(() => {
      ElMessage.info('已取消删除');
    });
}

/** 完结调拨单 */
function handleComplete(row: TransferOrderListVO) {
  ElMessageBox.confirm(
    `调拨单${row.transferOrderCode}完结后，如有剩余出/入库商品，则不能继续出/入库操作，是否确认`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      try{
         TransferOrderAPI.completeTransferOrder(row.transferOrderCode!);
         ElMessage.success('完结成功');
         handleQuery();
      }catch(error){
        ElMessage.error(error?.message || '完结失败');
      }
    })
    .catch((error) => {
      ElMessage.info('已取消完结');
    });
}

/** 查看详情 */
function handleDetail(row: TransferOrderListVO) {
  router.push(`/wms/transferManagement/transferOrder/detail/${row.transferOrderCode}`);
}

/** 获取仓库列表 */
function getWarehouseList() {
  TransferOrderAPI.getWarehouseList()
    .then((response) => {
      warehouseList.value = response || [];
    })
    .catch(() => {
      ElMessage.error('获取仓库列表失败');
    });
}


// 初始化
onMounted(() => {
  getWarehouseList();
  handleQuery();
});

onActivated(() => {
  handleQuery();
});
</script>

<style scoped lang="scss">
.transfer-order {
  .search-container {
    background: #fff;
    // padding: 20px;
    border-radius: 4px;
    margin-bottom: 16px;
  }

  .table-container {
    background: #fff;
  }

  .ml16px {
    margin-left: 16px;
  }

  .mr14px {
    margin-right: 14px;
  }

  .mr16px {
    margin-right: 16px;
  }
}
</style>