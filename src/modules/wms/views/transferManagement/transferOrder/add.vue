<template>
  <div class="app-container">
    <div class="transfer-order-add" v-loading="loading">
      <div class="page-title">
        <div @click="handleBack()" class="cursor-pointer mr8px">
          <el-icon>
            <Back />
          </el-icon>
        </div>
        <div>
          <span v-if="isEdit">编辑调拨单</span>
          <span v-else>新增调拨单</span>
        </div>
      </div>

      <div class="page-content">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="108px" label-position="right">
          <!-- 基本信息 -->
          <div class="title-label">
            <div class="title-line"></div>
            <div class="title-content">基本信息</div>
          </div>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="调拨单号" prop="transferOrderCode">
                <el-input v-model="form.transferOrderCode" placeholder="系统生成" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主题描述" prop="transferOrderName">
                <el-input v-model="form.transferOrderName" placeholder="请输入" maxlength="100" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请人" prop="applicantUserName">
                <!-- <el-input
                  v-model="form.applicantUserName"
                  placeholder="默认当前用户"
                  disabled
                /> -->
                <el-select v-model="form.applicantUserId" placeholder="请选择" @change="handleSelectApplicatioUser">
                  <el-option v-for="item in allUserList" :key="item.userId" :label="item.nickName"
                    :value="item.userId" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="申请时间" prop="applicantTime">
                <el-date-picker v-model="form.applicantTime" type="datetime" placeholder="自动时间"
                  value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计划调出时间" prop="planTransferOutTime">
                <el-date-picker v-model="form.planTransferOutTime" type="datetime" placeholder="自动时间"
                  value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="计划调入时间" prop="planTransferInTime">
                <el-date-picker v-model="form.planTransferInTime" type="datetime" placeholder="自动时间"
                  value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
              </el-form-item>
            </el-col>
            <!-- 调出方 -->
            <el-col :span="8">
              <el-form-item label="调出方" prop="sourceWarehouseCode">
                <el-select v-model="form.sourceWarehouseCode" placeholder="请选择调出方仓库" filterable clearable
                  @change="handleSourceWarehouseChange">
                  <el-option v-for="warehouse in warehouseList" :key="warehouse.warehouseCode"
                    :label="warehouse.warehouseName" :value="warehouse.warehouseCode" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="16">
              <el-form-item label="地址">
                <section style="display: flex; gap: 10px;align-items: center;width: 100%;">
                  <el-input v-model="form.sourceCountryName" placeholder="" disabled style="flex:1;" />
                  <el-input :value="form.sourceProvinceName + '/' + form.sourceCityName + '/' + form.sourceDistrictName"
                    placeholder="请选择" disabled style="flex:1;" />
                  <el-input v-model="form.sourceAddress" placeholder="" disabled style="flex:2;" />
                </section>
                <!--  <SelAreaCascader :defaultCountryInfo="form.sourceCountryId" :defaultAreaInfo="getSourceAreaInfo()"
                  :defaultDesAddressInfo="form.sourceAddress" :isEditMode="isEdit"
                  @getCountryInfo="handleSourceCountryChange" @getAreaInfo="handleSourceAreaChange"
                  @getDesAddressInfo="handleSourceAddressChange" :flex="true" :paddingTop="'0px'" /> -->
              </el-form-item>
            </el-col>




            <!-- 调入方 -->
            <el-col :span="8">
              <el-form-item label="调入方" prop="targetWarehouseCode">
                <el-select v-model="form.targetWarehouseCode" placeholder="请选择调入方仓库" filterable clearable
                  @change="handleTargetWarehouseChange">
                  <el-option v-for="warehouse in warehouseList" :key="warehouse.warehouseCode"
                    :label="warehouse.warehouseName" :value="warehouse.warehouseCode" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="地址">
                <section style="display: flex; gap: 10px;align-items: center;width: 100%;">
                  <el-input v-model="form.targetCountryName" placeholder="" disabled style="flex:1;" />
                  <el-input :value="form.targetProvinceName + '/' + form.targetCityName + '/' + form.targetDistrictName"
                    placeholder="请选择" disabled style="flex:1;" />
                  <el-input v-model="form.targetAddress" placeholder="" disabled style="flex:2;" />
                </section>
                <!-- <SelAreaCascader v-model:defaultCountryInfo:="form.targetCountryId"
                  :defaultAreaInfo="getTargetAreaInfo()" :defaultDesAddressInfo="form.targetAddress"
                  :isEditMode="isEdit" @getCountryInfo="handleTargetCountryChange" @getAreaInfo="handleTargetAreaChange"
                  @getDesAddressInfo="handleTargetAddressChange" :flex="true" :paddingTop="'0px'" /> -->
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="计划配送方式" prop="planDeliveryType">
                <el-select v-model="form.planDeliveryType" placeholder="请选择配送方式">
                  <el-option v-for="item in deliveryMethodList" :key="item.id" :label="item.deliveryMethodName"
                    :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="备注">
                <el-input v-model="form.remark" placeholder="请输入" maxlength="500" clearable type="textarea" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 商品明细 -->
          <div class="title-label">
            <div class="title-line"></div>
            <div class="title-content">商品明细</div>
            <div class="flex-1"></div>
            <el-button type="primary" @click="handleAddProduct" class="add-product-btn">
              添加
            </el-button>
          </div>

          <div class="product-table">
            <el-table :data="form.transferOrderDetailList" border stripe max-height="400" show-summary :span-method="arraySpanMethod">
              <el-table-column label="序号" type="index" width="60" :index="(index) => index + 1" />

              <el-table-column label="商品信息" min-width="200">
                <template #default="scope">
                  <div>
                    <div>商品编码：{{ scope.row.productCode }}</div>
                    <div>{{ scope.row.productName }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="商品分类" prop="categoryName" min-width="120" />

              <el-table-column label="规格" prop="productSpec" min-width="100" />

              <el-table-column label="商品属性" prop="attributeTypeName" min-width="100" />

              <el-table-column label="计划量" min-width="120">
                <template #default="scope">
                  <el-form-item :prop="'transferOrderDetailList.' + scope.$index + '.planTransferQty'" class="mt15px"
                    label-width="0px" :rules="[
                      {
                        required: true,
                        message: '计划量不能为空',
                        trigger: ['blur', 'change'],
                      },
                      {
                        pattern:
                          /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                        message: t(
                          '整数位限长8位，小数后3位'
                        ),
                        trigger: ['blur', 'change'],
                      },
                    ]">
                    <el-input v-model="scope.row.planTransferQty" controls-position="right"
                      @change="handleQuantityChange(scope.row, scope.$index)" style="width: 100%" />
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="计划转换量" min-width="120">
                <template #default="scope">
                  <div>{{ scope.row.planTransferConverted || 0 }} {{ scope.row.convertUnit }}</div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="80" fixed="right">
                <template #default="scope">
                  <el-button type="danger" link @click="handleRemoveProduct(scope.$index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- <div class="table-summary">
              <span>合计：{{ totalQuantity }}</span>
              <span class="ml-4">{{ totalConvertedQuantity }}</span>
            </div> -->
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button @click="handleSaveDraft">保存草稿</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 商品选择侧边栏 -->
    <AddProduct ref="addProductRef" v-model:visible="dialog.visible" :title="dialog.title" :totalStockQtyShow="false"
      :availableStockQtyShow="false" :outWarehouseAreaFromShow="false" :hasTotalStockQty="false" @onSubmit="onSubmit" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back } from '@element-plus/icons-vue';
import TransferOrderAPI, {
  type TransferOrderSaveForm
} from '@/modules/wms/api/transferOrder';
import AddProduct from '@/modules/wms/components/addProduct.vue';
import SelAreaCascader from '@/modules/wms/components/SelAreaCascader.vue';
import CurrentWarehouseAPI, { type warehouseInfo } from '@/modules/wms/api/currentWarehouse';
import { parseTime } from '@/core/utils';
import { useUserStore, useWarehouseStore } from '@/core/store';
import CommonAPI from '@/modules/wms/api/common';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const userStore = useUserStore();
const warehouseStore = useWarehouseStore();

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const formRef = ref();
const addProductRef = ref();

// 对话框状态
const dialog = reactive({
  visible: false,
  title: '添加商品'
});

// 判断是否为编辑模式
const isEdit = computed(() => route.params.transferOrderCode !== undefined);

// 表单数据
const form = reactive<TransferOrderSaveForm>({
  // transferOrderCode: '',
  transferOrderName: '',
  applicantUserId: undefined,
  applicantUserName: '',
  applicantTime: '',
  sourceWarehouseId: undefined,
  sourceWarehouseCode: '',
  sourceWarehouseName: '',
  sourceCountryId: '',
  sourceCountryName: '',
  sourceProvinceId: '',
  sourceProvinceName: '',
  sourceCityId: '',
  sourceCityName: '',
  sourceDistrictId: '',
  sourceDistrictName: '',
  sourceAddress: '',
  targetWarehouseId: undefined,
  targetWarehouseCode: '',
  targetWarehouseName: '',
  targetCountryId: '',
  targetCountryName: '',
  targetProvinceId: '',
  targetProvinceName: '',
  targetCityId: '',
  targetCityName: '',
  targetDistrictId: '',
  targetDistrictName: '',
  targetAddress: '',
  planTransferOutTime: '',
  planTransferInTime: '',
  planDeliveryType: undefined,
  remark: '',
  transferOrderDetailList: [],
  transferOrderStatus: 0
});



// 下拉选项数据
const warehouseList = ref<warehouseInfo[]>([]);

// 固定的配送方式选项
const deliveryMethodList = [
  { id: 1, deliveryMethodName: '快递' },
  { id: 2, deliveryMethodName: '自提' }
];

// 表单验证规则
const rules = {
  applicantUserName: [
    { required: true, message: '请选择申请人', trigger: 'blur' }
  ],
  applicantTime: [
    { required: true, message: '请选择申请时间', trigger: 'blur' }
  ],
  sourceWarehouseCode: [
    { required: true, message: '请选择调出仓库', trigger: 'change' }
  ],
  planTransferOutTime: [
    { required: true, message: '请选择计划调出时间', trigger: 'change' }
  ],
  targetWarehouseCode: [
    { required: true, message: '请选择调入仓库', trigger: 'change' }
  ],

  planTransferInTime: [
    { required: true, message: '请选择计划调入时间', trigger: 'change' }
  ],
  planDeliveryType: [
    { required: true, message: '请选择计划配送方式', trigger: 'change' }
  ]
};

// 计算属性
/* const totalQuantity = computed(() => {
  return form.transferOrderDetailList?.reduce((sum, item) => sum + (item.planTransferQty || 0), 0) || 0;
});

const totalConvertedQuantity = computed(() => {
  return form.transferOrderDetailList?.reduce((sum, item) => sum + (item.planTransferConverted || 0), 0) || 0;
}); */

/** 返回上一页 */
function handleBack() {
  router.back();
}

/** 获取基础数据 */
async function getBaseData() {
  try {
    const warehouseRes = await CurrentWarehouseAPI.checkedUserById();
    warehouseList.value = warehouseRes || [];

    // 设置默认申请时间
    /*  if (!form.applicantTime) {
       form.applicantTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
     } */
  } catch (error) {
    ElMessage.error('获取基础数据失败');
  }
}

// 填充表单数据
function fillFormData(data: any) {
  form.planTransferOutTime = parseTime(data.planTransferOutTime);
  form.applicantTime = parseTime(data.applicantTime);
  form.planTransferInTime = parseTime(data.planTransferInTime);
  form.transferOrderName = data.transferOrderName;
  form.applicantUserId = data.applicantUserId;
  form.applicantUserName = data.applicantUserName;
  form.sourceWarehouseCode = data.sourceWarehouseCode;
  form.sourceWarehouseName = data.sourceWarehouseName;
  form.targetWarehouseCode = data.targetWarehouseCode;
  form.targetWarehouseName = data.targetWarehouseName;
  form.planDeliveryType = data.planDeliveryType;
  form.remark = data.remark;
  form.transferOrderStatus = data.transferOrderStatus;
  form.sourceCountryId = data.sourceCountryId;
  form.sourceCountryName = data.sourceCountryName;
  form.sourceProvinceId = data.sourceProvinceId;
  form.sourceProvinceName = data.sourceProvinceName;
  form.sourceCityId = data.sourceCityId;
  form.sourceCityName = data.sourceCityName;
  form.sourceDistrictId = data.sourceDistrictId;
  form.sourceDistrictName = data.sourceDistrictName;
  form.sourceAddress = data.sourceAddress;
  form.targetCountryId = data.targetCountryId;
  form.targetCountryName = data.targetCountryName;
  form.targetProvinceId = data.targetProvinceId;
  form.targetProvinceName = data.targetProvinceName;
  form.targetCityId = data.targetCityId;
  form.targetCityName = data.targetCityName;
  form.targetDistrictId = data.targetDistrictId;
  form.targetDistrictName = data.targetDistrictName;
  form.targetAddress = data.targetAddress;
  form.transferOrderDetailList = data.detailList || [];
}
/** 获取调拨单详情（编辑模式） */
async function getTransferOrderDetail() {
  if (!isEdit.value) return;

  const transferOrderCode = route.params.transferOrderCode as string;
  try {
    loading.value = true;
    const response = await TransferOrderAPI.getTransferOrderDetail(transferOrderCode);
    const data = response;
    // 填充表单数据
    fillFormData(data);
  } catch (error) {
    ElMessage.error('获取调拨单详情失败');
  } finally {
    loading.value = false;
  }
}

async function getWarehouseDetail(warehouseCode: string, suffiexKey: string) {
  if (!warehouseCode) return;
  const warehouseDetail = await TransferOrderAPI.getWarehouseDetail(warehouseCode);
  form[suffiexKey + 'CountryId'] = warehouseDetail.countryId;
  form[suffiexKey + 'CountryName'] = warehouseDetail.countryName;
  form[suffiexKey + 'ProvinceId'] = warehouseDetail.provinceId;
  form[suffiexKey + 'ProvinceName'] = warehouseDetail.provinceName;
  form[suffiexKey + 'CityId'] = warehouseDetail.cityId;
  form[suffiexKey + 'CityName'] = warehouseDetail.cityName;
  form[suffiexKey + 'DistrictId'] = warehouseDetail.districtId;
  form[suffiexKey + 'DistrictName'] = warehouseDetail.districtName;
  form[suffiexKey + 'Address'] = warehouseDetail.address;
}

/** 调出仓库变更 */
async function handleSourceWarehouseChange(warehouseCode: string) {
  const warehouse = warehouseList.value.find(w => w.warehouseCode === warehouseCode);
  if (warehouse) {
    form.sourceWarehouseCode = warehouse.warehouseCode;
    form.sourceWarehouseName = warehouse.warehouseName;
    getWarehouseDetail(warehouse.warehouseCode, 'source');
  }
}

/** 调入仓库变更 */
async function handleTargetWarehouseChange(warehouseCode: string) {
  const warehouse = warehouseList.value.find(w => w.warehouseCode === warehouseCode);
  if (warehouse) {
    form.targetWarehouseCode = warehouse.warehouseCode;
    form.targetWarehouseName = warehouse.warehouseName;
    getWarehouseDetail(warehouse.warehouseCode, 'target');
  }
}

/** 获取调出方区域信息 */
function getSourceAreaInfo() {
  if (form.sourceProvinceId && form.sourceCityId && form.sourceDistrictId) {
    return [form.sourceProvinceId, form.sourceCityId, form.sourceDistrictId];
  }
  return [];
}

/** 获取调入方区域信息 */
function getTargetAreaInfo() {
  if (form.targetProvinceId && form.targetCityId && form.targetDistrictId) {
    return [form.targetProvinceId, form.targetCityId, form.targetDistrictId];
  }
  return [];
}

/** 调出方国家变更 */
function handleSourceCountryChange(countryInfo: any) {
  if (countryInfo) {
    form.sourceCountryId = countryInfo.id;
    form.sourceCountryName = countryInfo.shortName;
  } else {
    form.sourceCountryId = '';
    form.sourceCountryName = '';
  }
}

/** 调出方区域变更 */
function handleSourceAreaChange(areaInfo: any) {
  if (areaInfo && areaInfo.pathValues && areaInfo.pathLabels) {
    const values = areaInfo.pathValues;
    const labels = areaInfo.pathLabels;

    form.sourceProvinceId = values[0] || '';
    form.sourceProvinceName = labels[0] || '';
    form.sourceCityId = values[1] || '';
    form.sourceCityName = labels[1] || '';
    form.sourceDistrictId = values[2] || '';
    form.sourceDistrictName = labels[2] || '';
  } else {
    form.sourceProvinceId = '';
    form.sourceProvinceName = '';
    form.sourceCityId = '';
    form.sourceCityName = '';
    form.sourceDistrictId = '';
    form.sourceDistrictName = '';
  }
}

/** 调出方详细地址变更 */
function handleSourceAddressChange(address: string) {
  form.sourceAddress = address || '';
}

/** 调入方国家变更 */
function handleTargetCountryChange(countryInfo: any) {
  if (countryInfo) {
    form.targetCountryId = countryInfo.id;
    form.targetCountryName = countryInfo.shortName;
  } else {
    form.targetCountryId = '';
    form.targetCountryName = '';
  }
}

/** 调入方区域变更 */
function handleTargetAreaChange(areaInfo: any) {
  if (areaInfo && areaInfo.pathValues && areaInfo.pathLabels) {
    const values = areaInfo.pathValues;
    const labels = areaInfo.pathLabels;

    form.targetProvinceId = values[0] || '';
    form.targetProvinceName = labels[0] || '';
    form.targetCityId = values[1] || '';
    form.targetCityName = labels[1] || '';
    form.targetDistrictId = values[2] || '';
    form.targetDistrictName = labels[2] || '';
  } else {
    form.targetProvinceId = '';
    form.targetProvinceName = '';
    form.targetCityId = '';
    form.targetCityName = '';
    form.targetDistrictId = '';
    form.targetDistrictName = '';
  }
}

/** 调入方详细地址变更 */
function handleTargetAddressChange(address: string) {
  form.targetAddress = address || '';
}

/** 添加商品 */
function handleAddProduct() {
  dialog.title = '添加商品';
  let params = {};
  addProductRef.value.setFormData({ queryParams: params });
  addProductRef.value.getOutWarehouseAreaList();
  addProductRef.value.queryManagerCategoryList();
  dialog.visible = true;
}

/** 商品选择确认 */
function onSubmit(data: any) {
  if (data && data.collection) {
    const newProducts = data.collection.map((product: any) => ({
      productCode: product.productCode,
      productName: product.productName,
      productSpec: product.productSpec,
      categoryName: product.fullCategoryName || `${product.firstCategoryName || ''}/${product.secondCategoryName || ''}/${product.thirdCategoryName || ''}`,
      attributeTypeName: product.attributeTypeName,
      planTransferQty: 0,
      planTransferConverted: 0,
      convertUnit: product.conversionRelSecondUnitName,
      convertedQty: product.conversionRelSecondUnitCoefficient || 1
    }));

    // 合并新选择的商品到现有列表
    form.transferOrderDetailList = [...(form.transferOrderDetailList || []), ...newProducts];
  }
}

/** 删除商品 */
function handleRemoveProduct(index: number) {
  form.transferOrderDetailList?.splice(index, 1);
}

/** 数量变更 */
function handleQuantityChange(row: any, index: number) {
  // 根据转换比例计算转换量
  /* if (row.planTransferQty && row.convertedQty) {
    row.planTransferConverted = Number((row.planTransferQty * row.convertedQty).toFixed(3));
  } else {
    row.planTransferConverted = 0;
  } */
  convertProductUnit(row);
}

/** 表格行合并方法 */
function arraySpanMethod({ row, column, rowIndex, columnIndex }: any) {
  // 需要合并的列索引：商品信息(1)、商品分类(2)、规格(3)、商品属性(4)
  const mergeColumns = [1, 2, 3, 4];
  
  if (mergeColumns.includes(columnIndex)) {
    const currentProductCode = row.productCode;
    const data = form.transferOrderDetailList || [];
    
    // 计算连续相同productCode的行数
    let rowspan = 1;
    let currentIndex = rowIndex;
    
    // 向下查找连续相同的productCode
    while (currentIndex + 1 < data.length && data[currentIndex + 1].productCode === currentProductCode) {
      rowspan++;
      currentIndex++;
    }
    
    // 向上查找是否有相同的productCode（如果有，说明当前行不是第一行）
    if (rowIndex > 0 && data[rowIndex - 1].productCode === currentProductCode) {
      return {
        rowspan: 0,
        colspan: 0
      };
    }
    
    // 如果当前行是相同productCode的第一行，设置rowspan
    return {
      rowspan: rowspan,
      colspan: 1
    };
  }
  
  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  };
}

/** 取消 */
function handleCancel() {
  router.back();
}

/** 保存草稿 */
async function handleSaveDraft() {
  try {
    loading.value = true;
    form.transferOrderStatus = 0; // 草稿状态
    const formCopy = { ...form };
    formCopy.planTransferOutTime = new Date(formCopy.planTransferOutTime).getTime();
    formCopy.applicantTime = new Date(formCopy.applicantTime).getTime();
    formCopy.planTransferInTime = new Date(formCopy.planTransferInTime).getTime();
    const response = await TransferOrderAPI.saveTransferOrder(formCopy);
    ElMessage.success('保存草稿成功');

    router.back();
  } catch (error) {
    ElMessage.error('保存失败');
  } finally {
    loading.value = false;
  }
}

/** 提交 */
async function handleSubmit() {
  try {
    await formRef.value?.validate();

    if (!form.transferOrderDetailList || form.transferOrderDetailList.length === 0) {
      ElMessage.warning('请添加商品明细');
      return;
    }
    // planTransferOutTime、applicantTime、planTransferInTime转换为时间戳
    const formCopy = { ...form };
    formCopy.planTransferOutTime = new Date(formCopy.planTransferOutTime).getTime();
    formCopy.applicantTime = new Date(formCopy.applicantTime).getTime();
    formCopy.planTransferInTime = new Date(formCopy.planTransferInTime).getTime();

    loading.value = true;
    formCopy.transferOrderStatus = 1; // 提交状态

    await TransferOrderAPI.saveTransferOrder(formCopy);
    ElMessage.success(isEdit.value ? '修改成功' : '新增成功');
    router.back();
  } catch (error) {
    ElMessage.error(isEdit.value ? '修改失败' : '新增失败');
  } finally {
    loading.value = false;
  }
}

const allUserList = ref<any[]>([]);
// 获取所有用户列表
async function getAllUser() {
  const response = await TransferOrderAPI.queryAllUser();
  allUserList.value = response || [];
}

const handleSelectApplicatioUser = (value: string) => {
  const user = allUserList.value.find(item => item.userId === value);
  if (user) {
    form.applicantUserName = user.userName;
  }
}



function convertProductUnit(row: any) {
  const data = {
    convertUnitTypeEnum: 'FIRST_TO_SECOND',
    originalValue: row.planTransferQty,
    productCode: row.productCode
  }
  CommonAPI.convertProductUnit(data).then((res) => {
    row.planTransferConverted = res.convertedValue;
  })
}

// 默认当前仓库
watch(() => warehouseStore.selectedWarehouseId, (newVal) => {
  if (!isEdit.value) {
    // 默认当前仓库
    console.log("warehouseStore.selectedWarehouseId newVal---", newVal)
    form.sourceWarehouseCode = newVal;
    form.targetWarehouseCode = newVal;
    getWarehouseDetail(newVal, 'source');
    getWarehouseDetail(newVal, 'target');
  }
});


// 初始化
onMounted(async () => {
  if (!isEdit.value) {
    form.applicantUserId = userStore.user.userId;
    form.applicantUserName = userStore.user.username;
    // 默认当前仓库
    /* form.sourceWarehouseCode = warehouseStore.selectedWarehouseId;
    form.targetWarehouseCode = warehouseStore.selectedWarehouseId;
    getWarehouseDetail(form.sourceWarehouseCode, 'source');
    getWarehouseDetail(form.targetWarehouseCode, 'target'); */

  }

  await getBaseData();
  await getTransferOrderDetail();
  await getAllUser();
});
</script>

<style scoped lang="scss">
.transfer-order-add {
  background: #ffffff;
  border-radius: 4px;

  .page-title {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7f3;
    font-size: 18px;
    font-weight: 500;
    color: #151719;

    .mr8px {
      margin-right: 8px;
    }
  }

  .page-content {
    padding: 0 24px 24px 24px;

    .title-label {
      display: flex;
      align-items: center;
      margin: 32px 0 20px 0;

      .title-line {
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        margin-right: 8px;
      }

      .title-content {
        font-size: 16px;
        font-weight: 500;
        color: #151719;
      }

      .flex-1 {
        flex: 1;
      }

      .add-product-btn {
        margin-left: auto;
      }
    }

    .product-table {
      margin-bottom: 32px;

      .table-summary {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 16px;
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-top: none;
        font-weight: 500;

        .ml-4 {
          margin-left: 16px;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding-top: 24px;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>