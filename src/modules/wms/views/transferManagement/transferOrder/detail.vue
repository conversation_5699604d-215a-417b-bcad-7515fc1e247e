<template>
  <div class="app-container">
    <div class="transfer-order-detail" v-loading="loading">
      <div class="page-title">
        <div @click="handleBack()" class="cursor-pointer mr8px">
          <el-icon>
            <Back />
          </el-icon>
        </div>
        <div>调拨信息</div>
        <div class="flex-1"></div>
        <el-button @click="handleBack()">返回</el-button>
      </div>

      <div class="page-content">
        <!-- 调拨信息 -->
        <div class="info-section">
          <div class="section-title">调拨信息</div>

          <el-row :gutter="20" class="grad-row">
            <el-col :span="8">
              <el-form-item label="调拨单号：">
                <span>{{ detail.transferOrderCode || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主题描述：">
                <span>{{ detail.transferOrderName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请人：">
                <span>{{ detail.applicantUserName || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--        </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item label="申请时间：">
                <span>{{ parseDateTime(detail.applicantTime, 'dateTime') || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--         </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item label="调出方：">
                <span>{{ detail.sourceWarehouseName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地址：">
                <span>{{ getFullAddress('source') || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计划调出时间：">
                <span>{{ parseDateTime(detail.planTransferOutTime, 'dateTime') || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--           </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item label="详细地址：">
                <span>{{ detail.sourceAddress || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--   </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item label="调入方：">
                <span>{{ detail.targetWarehouseName || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地址：">
                <span>{{ getFullAddress('target') || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计划调入时间：">
                <span>{{ parseDateTime(detail.planTransferInTime, 'dateTime') || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--    </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item label="详细地址：">
                <span>{{ detail.targetAddress || '-' }}</span>
              </el-form-item>
            </el-col>
            <!--           </el-row>
          
          <el-row :gutter="20" class="grad-row"> -->
            <el-col :span="8">
              <el-form-item label="计划配送方式：">
                <span>{{ getDeliveryMethodName(detail.planDeliveryType) || '-' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="备注：">
                <span>{{ detail.remark || '-' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 调拨明细 -->
        <div class="info-section">
          <div class="section-title">调拨明细</div>

          <el-table :data="detail.detailList" border stripe max-height="400">
            <el-table-column label="序号" type="index" width="60" :index="(index) => index + 1" />

            <el-table-column label="商品信息" min-width="200">
              <template #default="scope">
                <div>
                  <div>商品编码：{{ scope.row.productCode }}</div>
                  <div>{{ scope.row.productName }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="商品分类" min-width="150">
              <template #default="scope">
                <span>{{ scope.row.firstCategoryName }}/{{ scope.row.secondCategoryName }}/{{
                  scope.row.thirdCategoryName }}</span>
              </template>
            </el-table-column>

            <el-table-column label="规格" prop="productSpec" min-width="100" />

            <el-table-column label="商品属性" prop="attributeTypeName" min-width="100" />

            <el-table-column label="计划量" min-width="120">
              <template #default="scope">
                <div>
                  <div>总量(盒)：{{ scope.row.planTransferQty || 0 }}</div>
                  <div>转换量(kg)：{{ scope.row.planTransferConverted || 0 }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="出库总量" min-width="120">
              <template #default="scope">
                <div>
                  <div>总量(盒)：{{ scope.row.outQty || 0 }}</div>
                  <div>转换量(kg)：{{ scope.row.outConvertedQty || 0 }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="入库总量" min-width="120">
              <template #default="scope">
                <div>
                  <div>总量(盒)：{{ scope.row.inQty || 0 }}</div>
                  <div>转换量(kg)：{{ scope.row.inConvertedQty || 0 }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 出库信息 -->
        <div class="info-section" v-if="detail.outboundNoticeInfoVO?.outboundPickingListVOList?.length > 0">
          <div class="section-title">出库信息</div>
          <section v-for="productItem in detail.outboundNoticeInfoVO?.outboundPickingListVOList">
            <el-row :gutter="20" class="grad-row">
              <el-col :span="6">
                <el-form-item label="出库通知单号：">
                  <span>{{ productItem.outNoticeOrderCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="拣货单：">
                  <span>{{ productItem.pickingOrderCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="实际配送方式：">
                  <span>{{ productItem.deliveryMethod || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="实际出库时间：">
                  <span>{{ parseDateTime(productItem.outConfirmTime, 'dateTime') || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="grad-row">
              <el-col :span="6">
                <el-form-item label="承运商：">
                  <span>{{ productItem.carrier || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="车号：">
                  <span>{{ productItem.trackingNumber || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="磅单编号：">
                  <span>{{ productItem.trackingNumber || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="附件：">
                  <span>{{ productItem.trackingNumber || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注：">
                  <span>{{ productItem.trackingNumber || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-table :data="detail.outboundNoticeInfoVO?.warehouseOutboundDetailVOList" 
            border stripe max-height="400" show-summary>
              <el-table-column label="序号" type="index" width="60" :index="(index) => index + 1" fixed="left" />

              <el-table-column label="商品信息" min-width="200">
                <template #default="scope">
                  <div>
                    <div>商品编码：{{ scope.row.productCode }}</div>
                    <div>{{ scope.row.productName }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="商品分类" min-width="150">
                <template #default="scope">
                  <span>{{ scope.row.firstCategoryName }}/{{ scope.row.secondCategoryName }}/{{
                    scope.row.thirdCategoryName }}</span>
                </template>
              </el-table-column>

              <el-table-column label="规格" prop="productSpecs" min-width="100" />
              <!-- 销售单价(RMB) salePrice-->
              <el-table-column label="销售单价(RMB)" prop="salePrice" min-width="100" />
              <!-- 销售金额(RMB) saleAmount-->
              <el-table-column label="销售金额(RMB)" prop="saleAmount" min-width="100" />
              <!-- 成本单价(RMB) costPrice-->
              <el-table-column label="成本单价(RMB)" prop="costPrice" min-width="100" />
              <!-- 出库量 alreadyOutboundQty-->
              <el-table-column label="出库量" prop="alreadyOutboundQty" min-width="100" />
              <!-- 出库转换量 alreadyOutboundWeight-->
              <el-table-column label="出库转换量" prop="alreadyOutboundWeight" min-width="100" />
              <!-- 成本金额(RMB) costAmount-->
              <el-table-column label="成本金额(RMB)" prop="costAmount" min-width="100" />
              <!-- 入库库区 -->
              <el-table-column label="入库库区" prop="warehouseAreaName" min-width="100">
                <template #default="scope">
                  <span>{{ scope.row.warehouseAreaName || '-' }}|{{ scope.row.warehouseName || '-'  }}</span>
                </template>
              </el-table-column>
              <!-- 商品包装 productPackaging-->
              <el-table-column label="商品包装" prop="productPackaging" min-width="100" />

            </el-table>
          </section>
        </div>
        <!-- 入库信息 -->
        <div class="info-section" v-if="detail.inboundNoticeInfoVO">
          <div class="section-title">入库信息</div>
          <section v-for="productItem in detail.inboundNoticeInfoVO">
           <el-row :gutter="20" class="grad-row">
              <el-col :span="6">
                <el-form-item label="入库通知单号：">
                  <span>{{ productItem.outNoticeOrderCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="入库单号：">
                  <span>{{ productItem.entryOrderCode || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="磅单编号：">
                  <span>{{ productItem.trackingNumber || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="附件：">
                  <span>{{ productItem.trackingNumber || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="入库车号：">
                  <span>{{ productItem.trackingNumber || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="入库备注：">
                  <span>{{ productItem.remark || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
           
            <el-table :data="productItem.productList" border stripe max-height="400" show-summary>
              <el-table-column label="序号" type="index" width="60" :index="(index) => index + 1" fixed="left" />

              <el-table-column label="商品信息" min-width="200">
                <template #default="scope">
                  <div>
                    <div>商品编码：{{ scope.row.productCode }}</div>
                    <div>{{ scope.row.productName }}</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="商品分类" min-width="150">
                <template #default="scope">
                  <span>{{ scope.row.firstCategoryName }}/{{ scope.row.secondCategoryName }}/{{
                    scope.row.thirdCategoryName }}</span>
                </template>
              </el-table-column>

              <el-table-column label="规格" prop="productSpecs" min-width="100" />
              <!-- 销售单价(RMB) salePrice-->
              <el-table-column label="销售单价(RMB)" prop="salePrice" min-width="100" />
              <!-- 销售金额(RMB) saleAmount-->
              <el-table-column label="销售金额(RMB)" prop="saleAmount" min-width="100" />
              <!-- 成本单价(RMB) costPrice-->
              <el-table-column label="成本单价(RMB)" prop="costPrice" min-width="100" />
              <!-- 出库量 alreadyOutboundQty-->
              <el-table-column label="出库量" prop="alreadyOutboundQty" min-width="100" />
              <!-- 出库转换量 alreadyOutboundWeight-->
              <el-table-column label="出库转换量" prop="alreadyOutboundWeight" min-width="100" />
              <!-- 成本金额(RMB) costAmount-->
              <el-table-column label="成本金额(RMB)" prop="costAmount" min-width="100" />
              <el-table-column label="入库库区" prop="warehouseAreaName" min-width="100">
                <template #default="scope">
                  <span>{{ scope.row.warehouseAreaName || '-' }}|{{ scope.row.warehouseName || '-'  }}</span>
                </template>
              </el-table-column>
              <!-- 商品包装 productPackaging-->
              <el-table-column label="商品包装" prop="productPackaging" min-width="100" />
            </el-table>
          </section>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back } from '@element-plus/icons-vue';
import TransferOrderAPI, {
  type TransferOrderDetailVO,
  type DeliveryMethodVO
} from '@/modules/wms/api/transferOrder';
import { parseDateTime } from '@/core/utils/index';

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const detail = ref<TransferOrderDetailVO>({});
const deliveryMethodList = ref<DeliveryMethodVO[]>([]);

/** 返回上一页 */
function handleBack() {
  router.back();
}

/** 获取调拨单详情 */
async function getTransferOrderDetail() {
  const transferOrderCode = route.params.transferOrderCode as string;
  if (!transferOrderCode) {
    ElMessage.error('调拨单号不能为空');
    return;
  }

  try {
    loading.value = true;
    const response = await TransferOrderAPI.getTransferOrderDetail(transferOrderCode);
    detail.value = response || {};
  } catch (error) {
    ElMessage.error('获取调拨单详情失败');
  } finally {
    loading.value = false;
  }
}

/** 获取配送方式列表 */
async function getDeliveryMethodList() {
  try {
    const response = await TransferOrderAPI.getDeliveryMethodList();
    deliveryMethodList.value = response.data || [];
  } catch (error) {
    console.error('获取配送方式列表失败', error);
  }
}

/** 获取完整地址 */
function getFullAddress(type: 'source' | 'target') {
  const prefix = type === 'source' ? 'source' : 'target';
  const countryName = detail.value[`${prefix}CountryName` as keyof TransferOrderDetailVO];
  const provinceName = detail.value[`${prefix}ProvinceName` as keyof TransferOrderDetailVO];
  const cityName = detail.value[`${prefix}CityName` as keyof TransferOrderDetailVO];
  const districtName = detail.value[`${prefix}DistrictName` as keyof TransferOrderDetailVO];

  const parts = [countryName, provinceName, cityName, districtName].filter(Boolean);
  return parts.join('/');
}

/** 获取配送方式名称 */
function getDeliveryMethodName(id?: number) {
  if (!id) return '';
  const method = deliveryMethodList.value.find(m => m.id === id);
  return method?.deliveryMethodName || '';
}

/** 计算计划总量 */
function getTotalPlanQty() {
  const total = detail.value.detailList?.reduce((sum, item) => sum + (item.planTransferQty || 0), 0) || 0;
  return total;
}

/** 计算计划转换总量 */
function getTotalPlanConverted() {
  const total = detail.value.detailList?.reduce((sum, item) => sum + (item.planTransferConverted || 0), 0) || 0;
  return total.toFixed(3);
}

// 初始化
onMounted(async () => {
  await Promise.all([
    getTransferOrderDetail(),
    getDeliveryMethodList()
  ]);
});
</script>

<style scoped lang="scss">
:deep(.el-form-item--default){
  margin-bottom: 10px;
}
.transfer-order-detail {
  background: #ffffff;
  border-radius: 4px;

  .page-title {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7f3;
    font-size: 18px;
    font-weight: 500;
    color: #151719;

    .mr8px {
      margin-right: 8px;
    }

    .flex-1 {
      flex: 1;
    }
  }

  .page-content {
    padding: 24px;

    .info-section {
      margin-bottom: 24px;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        font-size: 16px;
        font-weight: 500;
        color: #151719;

        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background: var(--el-color-primary);
          margin-right: 8px;
        }
      }

      .table-summary {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 12px 16px;
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        border-top: none;
        font-weight: 500;

        .ml-4 {
          margin-left: 16px;
        }
      }
    }
  }
}

.link-text {
  color: var(--el-color-primary);
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
</style>