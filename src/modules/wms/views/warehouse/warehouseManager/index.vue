<template>
  <div class="dashboard-container">
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <el-form-item
          prop="warehouseCode"
          :label="$t('warehouse.label.warehouseCoding')"
        >
          <el-input
            v-model="searchForm.warehouseCode"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="onSearchHandler"
          />
        </el-form-item>
        <el-form-item
          prop="warehouseName"
          :label="$t('warehouse.label.warehouseName')"
        >
          <el-input
            v-model="searchForm.warehouseName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="onSearchHandler"
          />
        </el-form-item>
        <el-form-item prop="status" :label="$t('warehouse.label.status')">
          <el-select
            v-model="searchForm.status"
            :placeholder="$t('common.placeholder.selectTips')"
            clearable
            class="!w-[198px]"
          >
            <el-option
              v-for="item in statusList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearchHandler">
            {{ $t("common.search") }}
          </el-button>
          <el-button @click="onResetHandler">
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button type="primary" @click="onAddHandler">
          {{ $t("warehouse.button.addWarehouse") }}
        </el-button>
      </template>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        border
      >
        <el-table-column
          type="index"
          :label="$t('warehouse.label.sort')"
          width="55"
        />
        <el-table-column
          :label="$t('warehouse.label.warehouseCoding')"
          prop="warehouseCode"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.warehouseName')"
          prop="warehouseName"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.country')"
          prop="countryName"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.province')"
          prop="provinceName"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.city')"
          prop="cityName"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.area')"
          prop="districtName"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.detailAddress')"
          prop="address"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.contact')"
          prop="contactPerson"
          show-overflow-tooltip
          min-width="160"
        />

        <el-table-column
          :label="$t('warehouse.label.contactNumber')"
          prop="mobile"
          show-overflow-tooltip
          min-width="160"
        >
          <template #default="scope">
            <EncryptPhone :phone="scope.row.mobile" />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('warehouse.label.fixedLine')"
          prop="contactLandline"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          :label="$t('warehouse.label.remark')"
          prop="notes"
          show-overflow-tooltip
          min-width="160"
        />
        <el-table-column
          prop="mobile"
          :label="$t('warehouse.label.status')"
          min-width="120"
        >
          <template #default="scope">
            <el-switch
              :active-text="$t('common.activeBtn')"
              :inactive-text="$t('common.inactiveBtn')"
              inline-prompt
              :model-value="scope.row.status === 1"
              :active-value="true"
              :inactive-value="false"
              @change="(val) => handleStatusChange(val, scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('warehouse.label.operate')"
          fixed="right"
          min-width="200"
        >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              @click="onEditHandler(scope.row)"
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              link
              @click="
                onDeleteHandler(
                  $t('warehouse.message.deleteWarehouse'),
                  'id',
                  scope.row.id
                )
              "
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="paginationInfo.pageNo"
        v-model:limit="paginationInfo.pageSize"
        @pagination="onPaginationChangeHandler"
      />
    </el-card>
    <el-drawer
      :key="refresh"
      v-model="showDialog"
      :title="diglogTitle"
      :close-on-click-modal="false"
      width="1800px"
      @close="onCloseHandler"
    >
      <el-form
        :model="contentForm"
        :rules="contentFormRules"
        ref="contentFormRef"
        label-position="top"
      >
        <el-form-item
          :label="$t('warehouse.label.ownWarehouseOrg')"
          prop="roleName"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="currentTenant.tenantName"
            :maxlength="20"
            clearable
            :disabled="true"
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.warehouseCoding')"
          prop="roleDesc"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.warehouseCode"
            :maxlength="20"
            clearable
            :disabled="true"
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.warehouseName')"
          prop="warehouseName"
        >
          <el-input
            type="text"
            :placeholder="$t('warehouse.placeholder.input30Tips')"
            v-model="contentForm.warehouseName"
            :maxlength="30"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.warehouseAddress')"
          prop="fullAddress"
        >
          <SelAreaCascader
            v-loading="areaLoading"
            ref="selAreaCascaderRef"
            v-model:defaultCountryInfo="defaultCountryInfo"
            v-model:defaultAreaInfo="defaultAreaInfo"
            v-model:defaultDesAddressInfo="defaultDesAddress"
            v-model:isEditMode="isEditMode"
            @get-country-info="getCountryInfo"
            @get-area-info="getAreaInfo"
            @get-des-address-info="getDesAddressInfo"
          />
        </el-form-item>
        <el-form-item :label="$t('warehouse.label.status')" prop="status">
          <el-switch
            :active-text="$t('common.activeBtn')"
            :inactive-text="$t('common.inactiveBtn')"
            inline-prompt
            v-model="contentForm.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.contact')"
          prop="contactPerson"
        >
          <el-input
            type="text"
            :placeholder="$t('warehouse.placeholder.input30Tips')"
            v-model="contentForm.contactPerson"
            :maxlength="30"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.contactNumber')"
          prop="mobile"
        >
          <el-input
            v-model="contentForm.mobile"
            :placeholder="$t('common.placeholder.inputTips')"
            @input.native="mobileInput"
          >
            <template #prepend>
              <el-select
                v-model="contentForm.countryAreaCode"
                style="width: 80px"
              >
                <el-option
                  v-for="item in countryNumCodeList"
                  :key="item.id"
                  :label="item.internationalCode"
                  :value="item.internationalCode"
                />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          :label="$t('warehouse.label.fixedLine')"
          prop="contactLandline"
        >
          <el-input
            :placeholder="$t('common.placeholder.inputTips')"
            v-model="contentForm.contactLandline"
            :maxlength="20"
            @input.native="fixedLineInput"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('warehouse.label.remark')" prop="notes">
          <el-input
            type="textarea"
            show-word-limit
            :placeholder="$t('warehouse.placeholder.input200Tips')"
            v-model="contentForm.notes"
            :maxlength="200"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="saveHandler"
          >
            {{ $t("common.confirm") }}
          </el-button>
        </span>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import formMixin from "@/modules/wms/mixins/form";
import tableMixin from "@/modules/wms/mixins/table";
import type { FormRules } from "element-plus";
import WarehouseAPI, { warehouseForm } from "@/modules/wms/api/warehouse";
import SelAreaCascader from "@/modules/wms/components/SelAreaCascader.vue";
import CommonAPI from "@/modules/wms/api/common";
import UserAPI from "@/core/api/accountManagement";
import { emitter } from '@/core/utils/eventBus';

const { proxy } = getCurrentInstance();
const { t } = useI18n();
const defaultCountryInfo = ref("");
const defaultAreaInfo = ref([]);
const defaultDesAddress = ref("");
const selAreaCascaderRef = ref(null);
const refresh = ref(0);
const countryNumCodeList = ref();
const isEditMode = ref(false);

const diglogTitle = computed(() =>
  formType.value === "add"
    ? t("warehouse.title.addWarehouseTitle")
    : t("warehouse.title.editWarehouseTitle")
);
const searchForm = reactive({
  warehouseCode: "",
  warehouseName: "",
  status: "",
});
const currentTenant = ref();
const contentForm = reactive<warehouseForm>({
  id: "",
  warehouseName: "",
  warehouseCode: "系统生成",
  address: "",
  countryId: "",
  countryName: "",
  provinceId: "",
  provinceName: "",
  cityId: "",
  cityName: "",
  districtId: "",
  districtName: "",
  contactPerson: "",
  mobile: "",
  contactLandline: "",
  notes: "",
  status: 1,
  fullAddress: "",
  fullAddressClone: "",
  countryAreaCode: "+86",
});
const contentFormRules = reactive<FormRules>({
  warehouseName: [
    {
      required: true,
      message: proxy.$t("warehouse.rules.wareHouseName"),
      trigger: "blur",
    },
  ],
  fullAddress: [
    /*  {
      required: true,
      message: proxy.$t("warehouse.rules.address"),
      trigger: ["blur", "change"],
    }, */
    {
      required: true,
      message: proxy.$t("warehouse.rules.address"),
      trigger: ["change", "blur"],
      validator: (rule, value, callback) => {
        if (!defaultCountryInfo.value) {
          callback(new Error(proxy.$t("warehouse.rules.address")));
        } else if (!defaultAreaInfo.value?.length > 0) {
          callback(new Error(proxy.$t("warehouse.rules.address")));
        } else if (!defaultDesAddress.value) {
          callback(new Error(proxy.$t("warehouse.rules.address")));
        } else {
          callback();
        }
      },
    },
  ],
  contactPerson: [
    {
      required: true,
      message: proxy.$t("warehouse.rules.contactPerson"),
      trigger: "blur",
    },
  ],
  mobile: [
    {
      required: true,
      message: proxy.$t("warehouse.rules.mobile"),
      trigger: "blur",
    },
  ],
});
function formatParamsCallbackHandler(params) {
  params.mobile = params.countryAreaCode + params.mobile;
}
function saveHandler() {
  checkAddress();
  onSaveHandler();
}
function saveCallbackHandler() {
  onSearchHandler();
  emitter.emit("refreshWarehouseList");
}

function addCallbackHandler() {
  isEditMode.value = false;
  contentForm.warehouseName = "";
  contentForm.contactPerson = "";
  contentForm.mobile = "";
  contentForm.contactLandline = "";
  contentForm.notes = "";
  contentForm.warehouseCode = "系统生成";
}

function editCallbackHandler(row) {
  isEditMode.value = true;
  contentForm.mobile = contentForm.mobile.replace(
    contentForm.countryAreaCode,
    ""
  );
  defaultCountryInfo.value = row.countryId;
  if (row.provinceId) {
    defaultAreaInfo.value[0] = row.provinceId;
  }
  if (row.cityId) {
    defaultAreaInfo.value[1] = row.cityId;
  }
  if (row.districtId) {
    defaultAreaInfo.value[2] = row.districtId;
  }

  // defaultAreaInfo.value = Array.of(row.provinceId, row.cityId, row.districtId);
  defaultDesAddress.value = row.address;
  contentForm.fullAddressClone = `${row.countryName}/${row.provinceName}/${row.cityName}/${row.districtName}/${row.address}`;
}

const {
  showDialog,
  dialogLoading,
  formType,
  contentFormRef,
  onAddHandler,
  onEditHandler,
  onSaveHandler,
  onCloseHandler,
} = formMixin({
  contentForm,
  idName: "id",
  uselessParams: ["fullAddress"],
  formAddApi: WarehouseAPI.addWarehouse,
  formEditApi: WarehouseAPI.editWarehouse,
  saveCallback: saveCallbackHandler,
  editCallback: editCallbackHandler,
  closeCallback: closeCallbackHandler,
  formatParamsCallback: formatParamsCallbackHandler,
  addCallback: addCallbackHandler,
});
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
} = tableMixin({
  searchForm,
  tableGetApi: WarehouseAPI.getWarehousePage,
  tableDeleteApi: WarehouseAPI.deleteWarehouse,
  tableEnableApi: WarehouseAPI.updateStatus,
});

function mobileInput(event) {
  contentForm.mobile = event.replace(/[^\d.]/g, "");
}
function fixedLineInput(event) {
  contentForm.contactLandline = event.replace(/[^\d.]/g, "");
}

function getCountryInfo(data: any) {
  contentForm.countryId = data?.id || "";
  contentForm.countryName = data?.shortName || "";
  defaultCountryInfo.value = data?.id || "";
  checkAddress();
}
function getAreaInfo(data) {
  const pathLabels = data?.pathLabels;
  const pathValues = data?.pathValues;
  pathLabels?.forEach((value, index) => {
    if (index === 0) {
      contentForm.provinceId = pathValues[index];
      contentForm.provinceName = value;
      contentForm.cityId = "";
      contentForm.cityName = "";
      contentForm.districtId = "";
      contentForm.districtName = "";
    } else if (index === 1) {
      contentForm.cityId = pathValues[index];
      contentForm.cityName = value;
      contentForm.districtId = "";
      contentForm.districtName = "";
    } else if (index === 2) {
      contentForm.districtId = pathValues[index];
      contentForm.districtName = value;
    }
  });
  defaultAreaInfo.value = data?.pathValues || [];
  checkAddress();
}
function getDesAddressInfo(data) {
  contentForm.address = data.value;
  defaultDesAddress.value = data.value;
  checkAddress();
}

function checkAddress() {
  if (
    contentForm.countryId.length > 0 &&
    contentForm.provinceId.length > 0 &&
    (contentForm.address.length > 0 || contentForm.fullAddressClone.length > 0)
  ) {
    contentForm.fullAddress = contentForm.countryId;
  } else {
    contentForm.fullAddress = "";
  }
}

function closeCallbackHandler() {
  contentForm.fullAddress = "";
  defaultAreaInfo.value = [];
  defaultCountryInfo.value = "";
  defaultDesAddress.value = "";
  refresh.value++;
}

const statusList = ref([
  {
    statusId: 0,
    statusName: t("common.statusEmun.disable"),
  },
  {
    statusId: 1,
    statusName: t("common.statusEmun.enable"),
  },
]);

const areaLoading = ref(false);
// 获取区号
function getAreaList() {
  areaLoading.value = true;
  UserAPI.getAllCountry()
    .then((data) => {
      countryNumCodeList.value = data;
    })
    .finally(() => {
      areaLoading.value = false;
    });
}
function getCurrentTenant() {
  CommonAPI.getCurrentTenant()
    .then((data) => {
      currentTenant.value = data;
    })
    .finally(() => {});
}
function handleStatusChange(val, row) {
  WarehouseAPI.updateStatus({ ids: [row.id], status: val ? 1 : 0 })
    .then(() => {
      row.status = val ? 1 : 0;
    })
    .finally(() => {});
}
onMounted(() => {
  onSearchHandler();
  getCurrentTenant();
  getAreaList();
});
</script>

<style lang="scss" scoped></style>
