<template>
  <div class="app-container">
    <div class="purchaseOrder">
      <div class="search-container">
        <el-form
          ref="queryFormRef"
          :model="queryParams"
          :inline="true"
          label-width="96px"
        >
          <!-- 入库通知单 -->
          <el-form-item
            prop="receiptNoticeCode"
            :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
          >
            <el-input
              v-model="queryParams.receiptNoticeCode"
              :placeholder="
                $t('warehouseEntryNotice.placeholder.inputLimtTips')
              "
              clearable
              class="!w-[256px]"
            />
          </el-form-item>
          <!-- 入库类型 -->
          <el-form-item
            :label="$t('warehouseEntryNotice.label.receiptType')"
            prop="receiptType"
          >
            <el-select
              v-model="queryParams.receiptTypeList"
              multiple
              :placeholder="$t('common.placeholder.selectTips')"
              clearable
              collapse-tags
              collapse-tags-tooltip
              class="!w-[256px]"
            >
              <el-option
                v-for="item in receiptTypeList"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
          <!-- 状态 -->
          <el-form-item
            :label="$t('warehouseEntryNotice.label.statusCopy')"
            prop="status"
          >
            <el-select
              v-model="queryParams.statusList"
              multiple
              :placeholder="$t('common.placeholder.selectTips')"
              clearable
              collapse-tags
              collapse-tags-tooltip
              class="!w-[256px]"
            >
              <el-option
                v-for="item in statusList"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
          <!-- 来源单号 -->
          <el-form-item
            prop="sourceOrderCode"
            :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
          >
            <el-input
              v-model="queryParams.sourceOrderCode"
              :placeholder="
                $t('warehouseEntryNotice.placeholder.inputLimtTips')
              "
              clearable
              class="!w-[256px]"
            />
          </el-form-item>
          <!-- 时间-->
          <el-form-item prop="dateRange">
            <!-- 查询类型:1:计划交货时间、2:单据同步时间 -->
            <el-select
              v-model="queryParams.queryType"
              :placeholder="$t('common.placeholder.selectTips')"
              class="!w-[180px] multipleSelect"
            >
              <el-option
                v-for="item in dateTypeList"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
            <el-date-picker
              :editable="false"
              class="!w-[370px]"
              v-model="dateRange"
              type="datetimerange"
              :range-separator="$t('warehouseEntryNotice.label.to')"
              :start-placeholder="$t('warehouseEntryNotice.label.startTime')"
              :end-placeholder="$t('warehouseEntryNotice.label.endTime')"
              :default-time="defaultTime"
              :placeholder="$t('common.placeholder.selectTips')"
            />
            <span
              class="ml16px mr14px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(1)"
            >
              {{ $t("warehouseEntryNotice.label.today") }}
            </span>
            <span
              class="mr14px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(2)"
            >
              {{ $t("warehouseEntryNotice.label.yesterday") }}
            </span>
            <span
              class="mr16px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(3)"
            >
              {{ $t("warehouseEntryNotice.label.weekday") }}
            </span>
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPerm="['wms:storeManagement:warehouseEntryNotice:search']"
              type="primary"
              @click="handleQuery"
            >
              {{ $t("common.search") }}
            </el-button>
            <el-button
              v-hasPerm="['wms:storeManagement:warehouseEntryNotice:reset']"
              @click="handleResetQuery"
            >
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-card shadow="never" class="table-container">
        <template #header>
          <el-button
            type="primary"
            @click="handleAdd(undefined, 'add')"
            v-hasPerm="['wms:storeManagement:warehouseEntryNotice:add']"
          >
            {{ $t("warehouseEntryNotice.button.addBtn") }}
          </el-button>
        </template>

        <el-table
          v-loading="loading"
          :data="tableList"
          highlight-current-row
          stripe
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column
            type="index"
            :label="$t('common.sort')"
            width="80"
            align="center"
          />
          <!-- 入库通知单 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
            prop="receiptNoticeCode"
            show-overflow-tooltip
            width="160"
          />
          <!-- 入库类型 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.receiptType')"
            show-overflow-tooltip
            width="110"
          >
            <template #default="scope">
              <!-- 入库类型:1:采购入库、2:退货入库 -->
              <span v-if="scope.row.receiptType == 1">
                {{ t("warehouseEntryNotice.label.purchaseInventory") }}
              </span>
              <span v-if="scope.row.receiptType == 2">
                {{ t("warehouseEntryNotice.label.returnStorage") }}
              </span>
            </template>
          </el-table-column>
          <!-- 客户-->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.customerName')"
            show-overflow-tooltip
            prop="customerName"
            width="110"
          />
          <!-- 供应商-->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.supplierName')"
            show-overflow-tooltip
            prop="supplierName"
            width="110"
          />
          <!-- 地址 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.address')"
            width="160"
          >
            <template #default="scope">
              <span class="encryptBox">
                <span v-if="scope.row.addressShow">
                  {{ scope.row.addressFormat }}
                  <el-icon
                    v-if="scope.row.fullAddress"
                    @click="
                      scope.row.addressShow ? getRealAddress(scope.$index) : ''
                    "
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                  >
                    <component :is="scope.row.addressShow ? 'View' : ''" />
                  </el-icon>
                </span>
                <span v-else>
                  {{ scope.row.fullAddress ? scope.row.fullAddress : "-" }}
                </span>
              </span>
            </template>
          </el-table-column>
          <!-- 联系人 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.contactPerson')"
            width="130"
          >
            <template #default="scope">
              <EncryptPhone :nameType="true" :name="scope.row.contactPerson" />
            </template>
          </el-table-column>
          <!-- 联系电话 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.mobile')"
            width="160"
          >
            <template #default="scope">
              <span class="encryptBox">
                <span v-if="scope.row.mobile">
                  {{ scope.row.countryAreaCode }}
                  <span v-if="scope.row.countryAreaCode">-</span>
                </span>

                <span v-if="scope.row.mobile && scope.row.mobile.length <= 4">
                  {{ scope.row.mobile }}
                </span>
                <span
                  v-else-if="scope.row.mobile && scope.row.mobile.length > 4"
                >
                  {{ scope.row.mobile }}
                  <el-icon
                    v-if="scope.row.mobile"
                    @click="
                      scope.row.mobilePhoneShow
                        ? getRealPhone(scope.row.id, scope.$index)
                        : ''
                    "
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="[
                      'wms:storeManagement:warehouseEntryNotice:eye',
                    ]"
                  >
                    <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                  </el-icon>
                </span>
                <span v-else>-</span>
              </span>
            </template>
          </el-table-column>
          <!-- 商品计划总数量 -->
          <el-table-column
                  :label="$t('warehouseEntryNotice.label.expectedQty')"
                  prop="expectedQty"
                  show-overflow-tooltip
                  align="right"
                  width="150"
          >
          </el-table-column>
          <!-- 商品计划总重量 -->
          <el-table-column
                  :label="$t('warehouseEntryNotice.label.expectedWeight')"
                  prop="expectedWeight"
                  show-overflow-tooltip
                  align="right"
                  width="150"
          >
          </el-table-column>
          <!-- 商品数量 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.productExpectQty')"
            prop="productQty"
            show-overflow-tooltip
            align="right"
            width="100"
          >
           <!-- <template #default="scope">
              <span v-if="scope.row.productQty" class="skuQtyColor">
                {{ scope.row.productQty }}
              </span>
              <span v-else>-</span>
            </template>-->
          </el-table-column>
          <!-- 计划交货时间 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.plannedDeliveryTime')"
            prop="plannedDeliveryTime"
            show-overflow-tooltip
            width="170"
          >
            <template #default="scope">
              {{
                parseTime(
                  scope.row.plannedDeliveryTime,
                  "{y}-{m}-{d} {h}:{i}:{s}"
                )
              }}
            </template>
          </el-table-column>
          <!-- 采购/销售员 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.purchaseSalesPerson')"
            prop="purchaseSalesPerson"
            show-overflow-tooltip
            width="130"
          />
          <!-- 来源单号 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
            prop="sourceOrderCode"
            show-overflow-tooltip
            width="160"
          />
          <!-- 备注 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.remark')"
            prop="remark"
            show-overflow-tooltip
            width="160"
          />
          <!-- 来源 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.source')"
            show-overflow-tooltip
            width="160"
          >
            <template #default="scope">
              <!-- 来源：1:手动创建 2:同步 -->
              <span v-if="scope.row.source == 1">
                {{ t("warehouseEntryNotice.label.manuallyCreate") }}
              </span>
              <span v-if="scope.row.source == 2">
                {{ t("warehouseEntryNotice.label.synchronous") }}
              </span>
            </template>
          </el-table-column>
          <!-- 来源系统 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.sourceSystem')"
            prop="sourceSystem"
            show-overflow-tooltip
          />
          <!-- 创建人 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.createUserName')"
            prop="createUserName"
            show-overflow-tooltip
            width="130"
          />
          <!-- 创建时间 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.createTime')"
            prop="createTime"
            show-overflow-tooltip
            width="170"
          >
            <template #default="scope">
              {{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
          <!-- 最后修改人 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.updateUserName')"
            prop="updateUserName"
            show-overflow-tooltip
            width="110"
          />
          <!-- 最后修改时间 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.updateTime')"
            prop="updateTime"
            show-overflow-tooltip
            width="170"
          >
            <template #default="scope">
              {{ parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
          <!-- 状态 -->
          <el-table-column
            :label="$t('warehouseEntryNotice.label.statusCopy')"
            prop="status"
            show-overflow-tooltip
            width="120"
            fixed="right"
          >
            <template #default="scope">
              <div class="purchase">
                <!-- 状态:1:初始、2:完结、0：草稿 -->
                <div
                  v-if="scope.row.status == 1"
                  type="success"
                  class="purchase-status purchase-status-color1"
                >
                  {{ t("warehouseEntryNotice.label.initial") }}
                </div>
                <div
                  v-if="scope.row.status == 2"
                  type="info"
                  class="purchase-status purchase-status-color3"
                >
                  {{ t("warehouseEntryNotice.label.finished") }}
                </div>
                  <div
                  v-if="scope.row.status == 0"
                  type="info"
                  class="purchase-status purchase-status-color0"
                  >
                   {{ t("warehouseEntryNotice.label.draft") }}
                  </div>
              </div>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column
            fixed="right"
            :label="$t('common.handle')"
            width="210"
          >
            <template #default="scope">
              <el-button
                v-hasPerm="['wms:storeManagement:warehouseEntryNotice:edit']"
                v-if="scope.row.status == 0 && scope.row.source == 1"
                type="primary"
                link
                @click="handleAdd(scope.row.id, 'edit')"
              >
                {{ $t("common.edit") }}
              </el-button>

              <el-button
                v-if="scope.row.status !== 0"
                v-hasPerm="['wms:storeManagement:warehouseEntryNotice:detail']"
                type="primary"
                link
                @click="handleCheck(scope.row.id, scope.row.receiptNoticeCode)"
              >
                {{ $t("common.detailBtn") }}
              </el-button>

              <el-button
                v-if="scope.row.status == 0 && scope.row.source == 1"
                v-hasPerm="['wms:storeManagement:warehouseEntryNotice:delete']"
                type="danger"
                link
                @click="handleDelete(scope.row.id)"
              >
                {{ $t("common.delete") }}
              </el-button>
              <!-- 打印  -->
              <el-button
                v-if="scope.row.status == 1"
                v-hasPerm="['wms:storeManagement:warehouseEntryNotice:print']"
                type="primary"
                link
                @click="handerPrint(scope.row)"
              >
                {{ $t("common.print") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
    <Print ref="printRef" class="display-none" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "WarehouseEntryNotice",
  inheritAttrs: false,
});

import warehouseEntryNoticeAPI, {
  WarehousePageQuery,
} from "@/modules/wms/api/warehouseEntryNotice";
import { useUserStore } from "@/core/store";

import { useRouter } from "vue-router";
import { parseTime, changeDateRange } from "@/core/utils/index.js";
import Print from "./components/print.vue";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";

const router = useRouter();
const { t } = useI18n();
const userStore = useUserStore();
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const dateRange = ref([
  moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
  moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
]);

const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];

const receiptTypeList = ref([
  {
    key: 1,
    value: t("warehouseEntryNotice.label.purchaseInventory"),
  },
  {
    key: 2,
    value: t("warehouseEntryNotice.label.returnStorage"),
  },
]);
const statusList = ref([
  /*{
    key: 0,
    value: t("warehouseEntryNotice.label.initial"),
  },
  {
    key: 1,
    value: t("warehouseEntryNotice.label.finished"),
  },
  {
      key: 2,
      value: t("warehouseEntryNotice.label.draft"),
  },*/


    {
        key: 1,
        value: t("warehouseEntryNotice.label.initial"),
    },
    {
        key: 2,
        value: t("warehouseEntryNotice.label.finished"),
    },
    {
        key: 0,
        value: t("warehouseEntryNotice.label.draft"),
    },
]);
const dateTypeList = ref([
  {
    key: 1,
    value: t("warehouseEntryNotice.label.plannedDeliveryTime"),
  },
  {
    key: 2,
    value: t("warehouseEntryNotice.label.createTime"),
  },
]);

const queryParams = reactive<WarehousePageQuery>({
  queryType: 1,
  page: 1,
  limit: 20,
});

const tableList = ref([]);
const printRef = ref();

function handleChangeDateRange(val: any) {
  dateRange.value = changeDateRange(val);
}

/** 查询 */
function handleQuery() {
  if (
    queryParams.receiptNoticeCode &&
    queryParams.receiptNoticeCode.length < 4
  ) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }
  if (queryParams.sourceOrderCode && queryParams.sourceOrderCode.length < 4) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }
  loading.value = true;
  let params: any = {
    ...queryParams,
  };
  if (dateRange.value && dateRange.value.length == 2) {
    params.queryStartTime = new Date(dateRange.value[0]).getTime();
    params.queryEndTime = new Date(dateRange.value[1]).getTime();
  }
  warehouseEntryNoticeAPI
    .queryPageList(params)
    .then((data: any) => {
      tableList.value = data.records.map((item: any, index: any) => {
        item.mobilePhoneShow = true;
        if (item.fullAddress && containsNumber(item.fullAddress)) {
          item.addressShow = true;
          item.addressFormat = replaceNumbersWithAsterisk(item.fullAddress);
        } else {
          //不包含数字
          item.addressShow = false;
        }

        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

// 获取真实电话号码
function getRealPhone(id: any, index: any) {
  warehouseEntryNoticeAPI
    .queryRealPhone({ id: id })
    .then((data: any) => {
      tableList.value[index].mobile = data.mobile;
      tableList.value[index].mobilePhoneShow = false;
    })
    .finally(() => {});
}

function getRealAddress(index: any) {
  tableList.value[index].addressShow = false;
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.queryType = 1;
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.receiptTypeList = [];
  queryParams.statusList = [];
  dateRange.value = [
    moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
  ];
  handleQuery();
}

/** 新增/编辑*/
function handleAdd(id?: string, type?: string) {
  let title;
  if (type == "edit") {
    title = t("warehouseEntryNotice.title.receiptNoticeCode");
  } else {
    title = t("warehouseEntryNotice.title.addTitle");
  }
  router.push({
    path: "/wms/storeManagement/addWarehouseEntryNotice",
    query: { title: title, id: id, type: type },
  });
}

/** 详情*/
function handleCheck(id?: string, code?: string) {
  router.push({
    path: "/wms/storeManagement/warehouseEntryNoticeDetail",
    query: { id: id, receiptNoticeCode: code },
  });
}

function handleDelete(id?: string) {
  ElMessageBox.confirm(
    t("warehouseEntryNotice.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let params: any = {
        id: id,
      };
      warehouseEntryNoticeAPI
        .delete(params)
        .then(() => {
          ElMessage.success(t("warehouseEntryNotice.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("warehouseEntryNotice.message.deleteCancel"));
    }
  );
}

const fetchDetailData = async (id: string) => {
  try {
    const data = await warehouseEntryNoticeAPI.queryDetail({ id: id });
    return data;
  } catch (error) {
    console.error("Failed to fetch detail data:", error);
    return null;
  } finally {
  }
};

/**打印*/
const print = ref(false);
async function handerPrint(row: any) {
  print.value = false;
  const detailData: any = await fetchDetailData(row.id);
  print.value = true;
  const printData = {
    title: t("warehouseEntryNotice.title.receiptNoticeCode"),
    warehouseName: detailData.warehouseName || "-", //仓库名称
    purchaseSalesPerson: detailData.purchaseSalesPerson || "-", //采购/销售员
    receiptNoticeCode: detailData.receiptNoticeCode || "-",
    supplierName: detailData.supplierName || "-",
    customerName: detailData.customerName || "-",
    sourceOrderCode: detailData.sourceOrderCode || "-", //来源单号
    printPerson: userStore.user.nickName || "-",
    printTime: moment().format("YYYY-MM-DD HH:mm:ss"), // 格式化时间
    receiptType: detailData.receiptType || 1,
    plannedDeliveryTime: detailData.plannedDeliveryTime
      ? parseTime(detailData.plannedDeliveryTime, "{y}-{m}-{d} {h}:{i}:{s}")
      : "-",
    remark: detailData.remark || "-",
    items: [],
  };
  printData.items = detailData.productList.map((product: any) => ({

   /** 商品编码  */
   productCode: product.productCode || "-",

    /** 商品名称  */
    productName: product.productName || "-",

    /** 规格 */
    productSpecs: product.productSpecs || "-",

    /** 单位 */
    productUnitName: product.productUnitName || "-",

    /** 计划数量  */
    productExpectQty: product.productExpectQty || "-",
    /** 计划重量  */
    expectedWeight: product.expectedWeight || "-",
    /** 收运数量  */
    productActualQty: product.productActualQty || "-",
    /** 收运重量  */
    receivedWeight: product.receivedWeight || "-",
  }));
  // 执行打印
  nextTick(() => {
    printRef.value.handlePrint(printData);
  });
}

onActivated(() => {
  handleQuery();
});
emitter.on("reloadListByWarehouseId", (e) => {
  nextTick(() => {
    handleQuery();
  });
});
</script>

<style lang="scss" scoped>
.encryptBox {
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  vertical-align: text-top;
}
.skuQtyColor {
  color: #762adb;
}
:deep(.multipleSelect .el-select__selected-item) {
  color: #151719 !important;
}
</style>
