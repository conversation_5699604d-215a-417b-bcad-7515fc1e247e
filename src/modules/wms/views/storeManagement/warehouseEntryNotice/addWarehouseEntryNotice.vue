<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          <span v-if="type == 'add'">
            {{ $t("warehouseEntryNotice.title.addTitle") }}
          </span>
          <span v-else>
            {{ $t("warehouseEntryNotice.title.receiptNoticeCode") }}:{{
              form.receiptNoticeCode
            }}
          </span>
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="112px"
          label-position="right"
        >
          <div class="basicInformation item_content">
            <div class="title">
              {{ $t("warehouseEntryNotice.label.basicInformation") }}
            </div>
            <el-row>
              <el-col :span="8">
                <!-- 入库通知单 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
                  prop="receiptNoticeCode"
                >
                  <el-input
                    v-model="form.receiptNoticeCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 入库类型 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receiptType')"
                  prop="receiptType"
                >
                  <el-select
                    v-model="form.receiptType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    clearable
                    @change="receiptTypeChange"
                    :disabled="type == 'edit'"
                  >
                    <el-option
                      v-for="(item, index) in receiptTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 计划交货时间 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.plannedDeliveryTime')"
                  prop="plannedDeliveryTime"
                >
                  <el-date-picker
                    v-model="form.plannedDeliveryTime"
                    type="datetime"
                    style="width: 100%"
                    :placeholder="$t('common.placeholder.selectTips')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <!-- 采购员 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.purchaseSalesPerson')"
                  prop="purchaseSalesPerson"
                >
                  <el-input
                    v-model="form.purchaseSalesPerson"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 来源单号 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
                  prop="sourceOrderCode"
                >
                  <el-input
                    v-model="form.sourceOrderCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <!-- 供应商/客户 入库类型:1:采购入库（供应商）、2:退货入库(客户)-->
                <span v-if="form.receiptType == 2">
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.customerName')"
                    prop="customerName"
                  >
                    <el-input
                      v-model="form.customerName"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="30"
                      clearable
                    />
                  </el-form-item>
                </span>
                <span v-else>
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.supplierName')"
                    prop="supplierName"
                  >
                    <el-input
                      v-model="form.supplierName"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="30"
                      clearable
                    />
                  </el-form-item>
                </span>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <!-- 联系人 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.contactPerson')"
                  prop="contactPerson"
                >
                  <el-input
                    v-model="form.contactPerson"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-row>
                  <el-col :span="10">
                    <!-- 手机号码 -->
                    <el-form-item
                      :label="$t('warehouseEntryNotice.label.mobile')"
                      prop="countryAreaCode"
                      class="custom-select"
                    >
                      <el-select
                        v-model="form.countryAreaCode"
                        :placeholder="$t('warehouseEntryNotice.label.areaCode')"
                        clearable
                      >
                        <el-option
                          v-for="(item, index) in areaList"
                          :key="index"
                          :label="item.internationalCode"
                          :value="item.internationalCode"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item prop="mobile" label-width="0">
                      <el-input
                        v-model="form.mobile"
                        :placeholder="$t('common.placeholder.inputTips')"
                        clearable
                        maxlength="30"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="8">
                <!-- 国家 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.country')"
                  prop="countryId"
                >
                  <el-select
                    v-model="form.countryId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="countryChange"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in countryList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <!-- 地区 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.area')"
                  prop="areaInfo"
                >
                  <!-- 省 -->
                  <el-select
                    :disabled="!form.countryId || isEdit == 'false'"
                    v-model="form.provinceId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleProvinceChange"
                    style="width: 33%"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in provinceList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 市 -->
                  <el-select
                    v-if="showCityInput"
                    :disabled="isEdit == 'false'"
                    v-model="form.cityId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleCityChange"
                    style="width: 33%"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in cityList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                  <!-- 区 -->
                  <el-select
                    v-if="showDistrictInput"
                    :disabled="isEdit == 'false'"
                    v-model="form.districtId"
                    :placeholder="$t('common.placeholder.selectTips')"
                    @change="handleDistrictChange"
                    style="width: 33%"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in districtList"
                      :key="index"
                      :label="item.shortName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <!-- 详细地址 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.detailAddress')"
                  prop="address"
                >
                  <el-input
                    v-model="form.address"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="100"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 备注 -->
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.remark')"
                  prop="remark"
                >
                  <el-input
                    v-model="form.remark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>
                  {{ $t("warehouseEntryNotice.label.goodsDetails") }}
                </div>
                <div>
                  <el-button
                    type="primary"
                    key="primary"
                    text
                    @click="handleGoodsSelect"
                  >
                    <span class="required_style">
                      {{ $t("warehouseEntryNotice.button.goodsAdd") }}
                    </span>
                  </el-button>
                </div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0" prop="warehouseIds">
                  <el-table
                    :data="form.productList"
                    v-loading="formLoading"
                    stripe
                    highlight-current-row
                    v-if="form.productList && form.productList.length > 0"
                  >
                    <el-table-column
                      type="index"
                      :label="$t('common.sort')"
                      width="60"
                      align="center"
                    />
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.goodsInfor')"
                      min-width="150"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <div class="product-code">
                          {{ $t("warehouseEntryNotice.label.productCode") }}：
                          {{ scope.row.productCode }}
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.goodsCategory')"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ scope.row.firstCategoryName }}/
                        {{ scope.row.secondCategoryName }}/
                        {{ scope.row.thirdCategoryName }}
                      </template>
                    </el-table-column>
                    <!-- 规格 -->
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.productSpecs')"
                      prop="productSpecs"
                      min-width="100"
                    />
                    <!-- 计划数量 -->
                    <el-table-column
                      prop="productExpectQty"
                      min-width="130"
                      :label="
                        '*' + $t('warehouseEntryNotice.label.productPlanQty')
                      "
                    >
                      <template #default="scope">
                      <!--  <el-form-item
                          class="mt15px"
                          label-width="0px"
                          :prop="
                            'productList.' + scope.$index + '.productExpectQty'
                          "
                          :rules="
                            goodsDialog.visible
                              ? []
                              : [
                                  {
                                    required: true,
                                    message: t(
                                      'warehouseEntryNotice.rules.productPlanQty'
                                    ),
                                    trigger: 'blur',
                                  },
                                  {
                                    pattern:
                                      /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                    message: t(
                                      'warehouseEntryNotice.rules.productPlanQtyFomart'
                                    ),
                                    trigger: 'blur',
                                  },
                                ]
                          "
                        >
                          <el-input
                            v-model="scope.row.productExpectQty"
                            :placeholder="$t('common.placeholder.inputTips')"
                            clearable
                            @change="productExpectQtyChange(scope.$index)"
                          >
                            <template #append>
                              {{ scope.row.productUnitName }}
                            </template>
                          </el-input>
                        </el-form-item>-->
                        <el-form-item
                          class="mt15px"
                          label-width="0px"
                          :prop="
                            'productList.' + scope.$index + '.productExpectQty'
                          "
                          :rules="
                            goodsDialog.visible
                              ? []
                              : [
                                  {
                                    required: true,
                                    message: t(
                                      'warehouseEntryNotice.rules.productPlanQty'
                                    ),
                                    trigger: 'blur',
                                  },
                                  {
                                    pattern:
                                      /^[1-9]\d*$/,
                                    message: t(
                                      'warehouseEntryNotice.rules.productPlanQtyFomart'
                                    ),
                                    trigger: 'blur',
                                  },
                                ]
                          "
                        >
                          <el-input
                            v-model="scope.row.productExpectQty"
                            :placeholder="$t('common.placeholder.inputTips')"
                            clearable
                            @change="productExpectQtyChange(scope.$index)"
                            maxlength="8"
                          >
                            <template #append>
                              {{ scope.row.productUnitName }}
                            </template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                      <!-- 计划重量 -->
                      <el-table-column
                              prop="expectedWeight"
                              min-width="130"
                              :label="
                        '*' + $t('warehouseEntryNotice.label.productPlanWeight')
                      "
                      >
                          <template #default="scope">
                              <el-form-item
                                      class="mt15px"
                                      label-width="0px"
                                      :prop="
                            'productList.' + scope.$index + '.expectedWeight'
                          "
                                      :rules="
                            goodsDialog.visible
                              ? []
                              : [
                                  {
                                    required: true,
                                    message: t(
                                      'warehouseEntryNotice.rules.expectedWeight'
                                    ),
                                    trigger: 'blur',
                                  },
                                  {
                                    pattern:
                                      /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                                    message: t(
                                      'warehouseEntryNotice.rules.expectedWeightFomart'
                                    ),
                                    trigger: 'blur',
                                  },
                                ]
                          "
                              >
                                  <el-input
                                          v-model="scope.row.expectedWeight"
                                          :placeholder="$t('common.placeholder.inputTips')"
                                          clearable
                                  >
                                      <template #append>
                                          {{$t('warehouseEntryNotice.label.productPlanWeightUnit')}}
                                      </template>
                                  </el-input>
                              </el-form-item>
                          </template>
                      </el-table-column>
                    <el-table-column :label="$t('common.handle')">
                      <template #default="scope">
                        <el-button
                          type="danger"
                          size="small"
                          link
                          @click="handleDelete(scope.$index)"
                        >
                          {{ $t("common.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("warehouseEntryNotice.button.cancel") }}
          </el-button>
          <el-button type="primary" plain @click="handleSubmit(0)">
              {{ $t("warehouseEntryNotice.button.saveDraft") }}
          </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit(1)">
            {{ $t("warehouseEntryNotice.button.submit") }}
          </el-button>
        </div>
      </div>
    </div>

    <AddGoods
      ref="addGoodsRef"
      v-model:visible="goodsDialog.visible"
      @on-submit="onSubmitGoods"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddWarehouseEntryNotice",
  inheritAttrs: false,
});
import CommonAPI from "@/modules/wms/api/common";
import AddGoods from "./components/addProduct.vue";
// import AddWarehouse from "./components/addWarehouse.vue";
// import AddContract from "./components/addContract.vue";
import warehouseEntryNoticeAPI, {
  addFormData,
} from "@/modules/wms/api/warehouseEntryNotice";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;
const receiptTypeList = ref([
  {
    code: 1,
    name: t("warehouseEntryNotice.label.purchaseInventory"),
  },
  {
    code: 2,
    name: t("warehouseEntryNotice.label.returnStorage"),
  },
]);
const areaList = ref([]);
const countryList = ref([]);
const provinceList = ref([]);
const cityList = ref([]);
const districtList = ref([]);

const isEdit = route.query.isEdit;
const addGoodsRef = ref();
const formRef = ref(ElForm);

const loading = ref(false);
const formLoading = ref(false);

const showCityInput = ref(false);
const showDistrictInput = ref(false);

const goodsDialog = reactive({
  visible: false,
});

// 角色表单
const form = reactive<addFormData>({
  id: "",
  productList: [],
});

const rules = reactive({
  receiptType: [
    {
      required: true,
      message: t("warehouseEntryNotice.rules.receiptTypeTip"),
      trigger: "change",
    },
  ],
  plannedDeliveryTime: [
    {
      required: true,
      message: t("warehouseEntryNotice.rules.plannedDeliveryTimeTip"),
      trigger: "change",
    },
  ],
  customerName: [
    {
      required: true,
      message: t("warehouseEntryNotice.rules.customerNameTip"),
      trigger: "blur",
    },
  ],
  supplierName: [
    {
      required: true,
      message: t("supplierManagement.rules.supplierNameTip"),
      trigger: "blur",
    },
  ],
});

function productExpectQtyChange(index) {
    if(form.productList[index].productExpectQty!==undefined && form.productList[index].weight!==undefined){
        let expectedWeight = (form.productList[index].productExpectQty * form.productList[index].weight).toFixed(3)
        form.productList[index].expectedWeight=expectedWeight
    }
}

// 获取区号
function getAreaList() {
  warehouseEntryNoticeAPI
    .getAllCountry()
    .then((data: any) => {
      areaList.value = data;
    })
    .finally(() => {});
}

/** 获取国家列表 */
function queryAllCountry() {
  const queryParams = {
    pid: "0",
  };
  CommonAPI.getAreaList(queryParams)
    .then((data: any) => {
      countryList.value = data;
    })
    .finally(() => {});
}

function countryChange(val: any) {
  form.provinceId = "";
  provinceList.value = [];
  form.cityId = "";
  cityList.value = [];
  form.districtId = "";
  districtList.value = [];
  showCityInput.value = false;
  showDistrictInput.value = false;
  form.areaInfo = [];
  form.provinceName = "";
  form.cityName = "";
  form.districtName = "";
  if (form.countryId) {
    let data: any = countryList.value.find(
      (item: any) => item.id === form.countryId
    );
    if (form.countryId) {
      form.countryName = data.shortName ? data.shortName : "";
    } else {
      form.countryName = "";
    }

    getAreaApi(val, "province");
  }
}

function getAreaApi(val: any, tab?: any) {
  let params = {
    pid: val,
  };
  CommonAPI.getAreaList(params)
    .then((data: any) => {
      if (tab == "province") {
        provinceList.value = data;
      } else if (tab == "city") {
        if (data.length > 0) {
          cityList.value = data;
          showCityInput.value = true;
        } else {
          showCityInput.value = false;
        }
      } else {
        if (data.length > 0) {
          showDistrictInput.value = true;
          districtList.value = data;
        } else {
          showDistrictInput.value = false;
        }
      }
    })
    .finally(() => {});
}

function handleProvinceChange(val: any) {
  form.cityId = "";
  cityList.value = [];
  form.districtId = "";
  districtList.value = [];
  showCityInput.value = false;
  showDistrictInput.value = false;
  form.areaInfo = [];
  let data: any = provinceList.value.find(
    (item: any) => item.id === form.provinceId
  );
  if (form.provinceId) {
    form.provinceName = data.shortName ? data.shortName : "";
  } else {
    form.provinceName = "";
  }
  if (val) {
    getAreaApi(val, "city");
  }
}

function handleCityChange(val: any) {
  form.districtId = "";
  districtList.value = [];
  showDistrictInput.value = false;
  // form.areaInfo[1] = "";
  let data: any = cityList.value.find((item: any) => item.id === form.cityId);
  if (form.cityId) {
    form.cityName = data.shortName ? data.shortName : "";
  } else {
    form.cityName = "";
  }
  if (val) {
    getAreaApi(val, "district");
  }
}

function handleDistrictChange(val: any) {
  let data: any = districtList.value.find(
    (item: any) => item.id === form.districtId
  );
  if (form.districtId) {
    form.districtName = data.shortName ? data.shortName : "";
  } else {
    form.districtName = "";
  }
}

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/storeManagement/warehouseEntryNotice",
  });
};

function handleGoodsSelect() {
  goodsDialog.visible = true;
  addGoodsRef.value.queryGoodList();
  addGoodsRef.value.queryManagerCategoryList();
}
function onSubmitGoods(data: any) {
  let arr = data.concat(form.productList);
  let uniqueArr = [
    ...new Map(arr.map((item: any) => [item.productCode, item])).values(),
  ];
  form.productList = uniqueArr;
}
function handleDelete(index?: number) {
  form.productList.splice(index, 1);
}

function receiptTypeChange() {
  form.customerName = "";
  form.supplierName = "";
}

// 提交
function handleSubmit(val) {
  if (form.productList && form.productList.length == 0) {
    return ElMessage.error(
      t("warehouseEntryNotice.message.addOrEditGoodsTips")
    );
  }
  // checkAddress();
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    loading.value = true;
    let params: any = {
      ...form,
        status:val,
    };
    params.plannedDeliveryTime = new Date(params.plannedDeliveryTime).getTime();
    delete params.areaInfo;
    if (type == "add") {
      delete params.id;
      warehouseEntryNoticeAPI
        .addWarehouseReceiptNotice(params)
        .then((data: any) => {
          ElMessage.success(t("warehouseEntryNotice.message.addSucess"));
          loading.value = false;
          handleCancel();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      warehouseEntryNoticeAPI
        .editWarehouseReceiptNotice(params)
        .then((data: any) => {
          ElMessage.success(t("warehouseEntryNotice.message.editSucess"));
          loading.value = false;
          handleCancel();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

function queryDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  warehouseEntryNoticeAPI
    .queryDetail(params)
    .then((data: any) => {
      if (data.countryId) {
        getAreaApi(data.countryId, "province");
      }
      if (data.provinceId) {
        getAreaApi(data.provinceId, "city");
      }
      if (data.cityId) {
        getAreaApi(data.cityId);
      }
      Object.assign(form, data);
    })
    .finally(() => {
      formLoading.value = false;
    });
}

onMounted(() => {
  queryAllCountry();
  getAreaList();
  if (type == "edit" || type == "details") {
    queryDetail();
  }
});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 20px 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 30px 24px 30px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 22px 30px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .mt15px {
    margin-bottom: 15px !important;
  }
}
</style>
<style scoped>
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
</style>
