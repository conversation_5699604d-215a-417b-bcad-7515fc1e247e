<template>
  <el-drawer :model-value="visible" :title="$t('warehouseEntry.title.addProduct')" :close-on-click-modal="false"
    :size="850" @close="close">
    <section style="display: flex;flex-direction: column;height: 100%;">
      <el-form :model="goodsQueryParams" ref="goodsQueryFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <!-- 查询类型:0:收运单号、1:来源单号 、2：入库通知单号-->
              <el-select v-model="goodsQueryParams.queryType" :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[180px]">
                <el-option v-for="item in queryTypeList" :key="item.key" :label="item.value" :value="item.key" />
              </el-select>
              <!-- 根据类型搜索 -->
              <el-input v-model="goodsQueryParams.queryText" :placeholder="$t('common.placeholder.inputTips')"
                class="input_style" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="9">
            <!-- 商品名称 -->
            <el-form-item :label="$t('warehouseEntry.label.productNameCopy')" prop="productName">
              <el-input v-model="goodsQueryParams.productName"
                :placeholder="$t('warehouseEntry.placeholder.productInputTips')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <!-- 商品分类 -->
            <el-form-item :label="$t('warehouseEntry.label.goodsCategory')" prop="category">
              <el-cascader ref="cascaderRef" :props="propsCategory" :options="categoryList"
                :placeholder="$t('common.placeholder.selectTips')" v-model="goodsQueryParams.category"
                @change="handleChange" clearable filterable collapse-tags collapse-tags-tooltip />
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button type="primary" @click="queryGoodList">
              {{ $t("common.search") }}
            </el-button>
            <el-button @click="handleResetQuery">
              {{ $t("common.reset") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <div class="selected-info">
        已选
        <span class="selected-count">{{ multipleSelection.length }}</span>
        个商品
      </div>

      <el-table style="min-height: 0;flex-grow: 1;" ref="dataTableRef" v-loading="productLoading"
        highlight-current-row stripe :data="productList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :label="$t('warehouseEntry.label.goodsInfor')" min-width="160">
          <template #default="scope">
            <div class="product-code">
              {{ $t("warehouseEntry.label.productCode") }}：
              {{ scope.row.productCode }}
            </div>
            <div class="product-name">
              {{ scope.row.productName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('warehouseEntry.label.receiptNoticeCode')" prop="receiptNoticeCode"
          min-width="110" />
        <el-table-column :label="$t('warehouseEntry.label.receivingOrderCode')" prop="receivingOrderCode"
          min-width="110" />
        <!-- 入库类型 -->
        <el-table-column :label="$t('warehouseEntry.label.receiptType')" min-width="100">
          <template #default="scope">
            <!-- 入库类型:1:采购入库、2:退货入库 -->
            <span v-if="scope.row.receiptType == 1">
              {{ $t("warehouseEntry.label.purchaseInventory") }}
            </span>
            <span v-else-if="scope.row.receiptType == 2">
              {{ $t("warehouseEntry.label.returnStorage") }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('warehouseEntry.label.productSpecs')" prop="productSpecs" min-width="80" />
        <!-- 收运数量 -->
        <el-table-column :label="$t('warehouseEntry.label.actualQuantityCopy')" min-width="90">
          <template #default="scope">
            {{ scope.row.productActualQty }}
            <span v-if="scope.row.productActualQty">
              {{ scope.row.productUnitName }}
            </span>
          </template>
        </el-table-column>
        <!-- 已入库数量 -->
        <el-table-column :label="$t('warehouseEntry.label.productInventoryQty')" min-width="100">
          <template #default="scope">
            {{ scope.row.productInventoryQty || '-' }}
            <span v-if="scope.row.productInventoryQty">
              {{ scope.row.productUnitName }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination style="flex-grow: 0;height: 120px;" v-if="productListTotal > 0" v-model:total="productListTotal" v-model:page="goodsQueryParams.page"
        v-model:limit="goodsQueryParams.limit" @pagination="queryGoodList" />
    </section>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">
          {{ $t("warehouseEntry.button.cancel") }}
        </el-button>
        <el-button type="primary" :disabled="!multipleSelection.length" @click="submitForm">
          {{ $t("warehouseEntry.button.comfirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import warehouseEntryAPI from "@/modules/wms/api/warehouseEntry";
import warehouseEntryNoticeAPI from "@/modules/wms/api/warehouseEntryNotice";
import type { CascaderProps } from "element-plus";
import CommonAPI from "@/modules/wms/api/common";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();

const cascaderRef = ref();
const goodsQueryFormRef = ref(ElForm);
const productListTotal = ref(0);
const goodsQueryParams = reactive({
  page: 1,
  limit: 20,
  queryText: "",
  queryType: 1,
  productName: "",
  category: [], // 商品分类
  thirdCategoryIds: [],
  firstCategoryIds: [],
  secondCategoryIds: [],
  isSorting: 0 // 是否分拣:0:否、1:是
});

const productLoading = ref(false);
const productList = ref([]);
const multipleSelection = ref([]);
const categoryList = ref([]);

const queryTypeList = ref([
  {
    key: 1,
    value: t("warehouseEntry.label.receivingOrderCode"),
  },
  {
    key: 2,
    value: t("warehouseEntry.label.sourceOrderCode"),
  },
  {
    key: 3,
    value: t("warehouseEntry.label.receiptNoticeCode"),
  },
]);

const propsCategory: CascaderProps = {
  multiple: true,
  checkStrictly: false,
  value: "id",
  label: "categoryName",
  children: "children",
};

// const propsCategory: CascaderProps = {
//   lazy: true,
//   checkStrictly: false,
//   multiple: true,
//   async lazyLoad(node, resolve) {
//     const { level, data } = node;
//     let arr: any = [];
//     if (level == 0) {
//       arr = await getCategoryList(null);
//     } else {
//       arr = await getCategoryList(data.value);
//     }
//     const nodes = arr.map((item: any) => ({
//       value: item.id,
//       label: item.categoryName,
//       parentId: item.parentId,
//       leaf: level >= 2,
//     }));
//     resolve(nodes);
//   },
// };

function getCategoryList() {
  CommonAPI.queryCategoryTreeList({}).then((data: any) => {
    categoryList.value = data;
  });
  // return new Promise((resolve, reject) => {
  //   let params: any = {};
  //   if (id) {
  //     params.id = id;
  //   }
  //   warehouseEntryNoticeAPI
  //     .queryCategoryTreeList(params)
  //     .then((data: any) => {
  //       resolve(data);
  //     })
  //     .catch((error) => {
  //       reject(error);
  //     });
  // });
}

function handleChange() {
  let valueArr = goodsQueryParams.category;
  let firstCategoryIds: any = [];
  let secondCategoryIds: any = [];
  let thirdCategoryIds: any = [];

  if (valueArr && valueArr.length > 0) {
    valueArr.forEach((item: any) => {
      if (item[0]) {
        firstCategoryIds.push(item[0]);
      }
      if (item[1]) {
        secondCategoryIds.push(item[1]);
      }
      if (item[2]) {
        thirdCategoryIds.push(item[2]);
      }
    });
  }

  goodsQueryParams.firstCategoryIds = Array.from(new Set(firstCategoryIds));
  goodsQueryParams.secondCategoryIds = Array.from(new Set(secondCategoryIds));
  goodsQueryParams.thirdCategoryIds = Array.from(new Set(thirdCategoryIds));
  // goodsQueryParams.thirdCategoryIds = [];
  // if (cascaderRef.value.getCheckedNodes()) {
  //   const checkedNodes = cascaderRef.value.getCheckedNodes();
  //   checkedNodes.forEach((node: any) => {
  //     if (node.level == 3) {
  //       goodsQueryParams.thirdCategoryIds.push(node.value);
  //     }
  //   });
  // }
}

function close() {
  emit("update:visible", false);
  reset();
}

function reset() {
  goodsQueryFormRef.value.clearValidate();
  goodsQueryFormRef.value.resetFields();
  goodsQueryParams.page = 1;
  goodsQueryParams.limit = 20;
  goodsQueryParams.queryText = "";
  goodsQueryParams.productName = "";
  goodsQueryParams.queryType = 1;
  goodsQueryParams.category = [];
  goodsQueryParams.firstCategoryIds = [];
  goodsQueryParams.secondCategoryIds = [];
  goodsQueryParams.thirdCategoryIds = [];
}

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
}

function queryGoodList() {
  productLoading.value = true;
  let params: any = {
    ...goodsQueryParams,
  };
  delete params.category;
  warehouseEntryAPI
    .queryProductPageList(params)
    .then((res: any) => {
      productList.value = res.records;
      productListTotal.value = parseInt(res.total) || 0;
    })
    .finally(() => {
      productLoading.value = false;
    });
}

function handleResetQuery() {
  goodsQueryParams.page = 1;
  goodsQueryParams.limit = 20;
  goodsQueryParams.queryText = "";
  goodsQueryParams.productName = "";
  goodsQueryParams.queryType = 1;
  goodsQueryParams.category = [];
  goodsQueryParams.firstCategoryIds = [];
  goodsQueryParams.secondCategoryIds = [];
  goodsQueryParams.thirdCategoryIds = [];
  queryGoodList();
}

function submitForm() {
  emit("onSubmit", multipleSelection.value);
  close();
}

// 初始化
// onMounted(() => {
//   getCategoryList();
// });

defineExpose({
  queryGoodList,
  getCategoryList,
});

// Add a default export
// export default { name: 'AddGoods' };
</script>

<style scoped lang="scss">
.input_style {
  width: calc(100% - 180px);
}

.product-code {
  color: #90979e;
}

.product-name {
  color: #151719;
}

.selected-info {
  margin: 16px 0;
  color: #606266;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #52585f;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  margin-bottom: 10px;

  .selected-count {
    color: #762adb;
  }
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.product-info {
  display: flex;
  align-items: center;

  img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }

  .product-info-content {
    margin-left: 10px;

    .product-info-content-item {
      &.code {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #90979e;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      &.name {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>
