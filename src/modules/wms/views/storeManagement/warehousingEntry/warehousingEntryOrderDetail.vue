<template>
  <div class="app-container">
    <div class="warehouseEntryNoticeDetail" v-loading="loading">
      <div class="page-title">
        <div class="purchase-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon><Back /></el-icon>
          </div>
          <div>
            {{ t("warehouseEntry.label.entryOrderCode") }}：{{
              route.query.entryOrderCode
            }}
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" label-width="116px" label-position="right">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntry.label.basicInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntry.label.entryOrderCode')"
                >
                  {{ form.entryOrderCode ? form.entryOrderCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="'订单类型'">
                  <span v-if="form.orderType == 1">按分拣单入库</span>
                  <span v-else-if="form.orderType == 2">按商品入库</span>
                  <span v-else-if="form.orderType == 3">按收运单入库</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntry.label.entryOperator')">
                  {{ form.entryOperator ? form.entryOperator : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntry.label.entryTime')">
                  <span v-if="form.entryTime">
                    {{ parseTime(form.entryTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item :label="'状态'">
                  <!-- 0:初始;1:已入库;3:入库中 -->
                  <span v-if="form.status == 0">初始</span>
                  <span v-else-if="form.status == 1">已入库</span>
                  <span v-else-if="form.status == 3">入库中</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="'是否领单'">
                  <span v-if="form.receivingStatus == 0">未领用</span>
                  <span v-else-if="form.receivingStatus == 1">已领用</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="'领单人'">
                  {{ form.receivingPerson ? form.receivingPerson : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="'领单时间'">
                  <span v-if="form.receivingTime">
                    {{ parseTime(form.receivingTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
               <!--  <el-form-item :label="'入库总量'">
                  {{ form.productInventoryQty ? form.productInventoryQty : "-" }}
                </el-form-item> -->
                <el-form-item :label="'入库总量'">
                  {{ form.productTotalInQty ? form.productTotalInQty : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
               <!--  <el-form-item :label="'入库总重量(Kg)'">
                  {{ form.productActualWeight ? form.productActualWeight : "-" }}
                </el-form-item> -->
                <el-form-item :label="'入库总重量(Kg)'">
                  {{ form.productTotalInWeight ? form.productTotalInWeight : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntry.label.orderDetails") }}
            </div>
          </div>
          <div>
            <el-table
              ref="tableSumRef1"
              :data="form.productList"
              highlight-current-row
              stripe
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
                fixed="left"
              />
              <!-- 商品信息 -->
              <el-table-column
                :label="$t('warehouseEntry.label.goodsInfor')"
                min-width="150"
                show-overflow-tooltip

                fixed="left"
              >
                <template #default="scope">
                  <div class="product-code">
                    {{ $t("warehouseEntry.label.productCode") }}：
                    {{ scope.row.productCode }}
                  </div>
                  <div class="product-name">
                    {{ scope.row.productName }}
                  </div>
                </template>
              </el-table-column>
              <!-- 入库通知单 -->
              <el-table-column
                :label="$t('warehouseEntry.label.receiptNoticeCode')"
                prop="receiptNoticeCode"
                show-overflow-tooltip
                min-width="130"
              />
              <!-- 收运单 -->
              <el-table-column
                :label="$t('warehouseEntry.label.receivingOrderCode')"
                prop="receivingOrderCode"
                show-overflow-tooltip
                min-width="130"
              />
                 <!-- 分拣单号 -->
                <el-table-column
                :label="'分拣单号'"
                prop="sortingCode"
                show-overflow-tooltip
                min-width="130"
              />
              <!-- 规格 -->
              <el-table-column
                :label="$t('warehouseEntry.label.productSpecs')"
                prop="productSpecs"
                show-overflow-tooltip
              />
              
              <el-table-column label="单位" prop="productUnitName" show-overflow-tooltip width="100" />
              
               <!-- 收运/分拣后商品重量(Kg) -->
              
               <el-table-column
                :label="'收运/分拣后商品总重量(Kg)'"
                prop="productSortingTotalWeight"
                show-overflow-tooltip
                width="100"
              />
              <!-- 收运数量 -->
              <el-table-column
                :label="'收运/分拣后商品总数量'"
                prop="productSortingTotalQty"
                show-overflow-tooltip
                width="100"
              />
              <!-- 已入库数量 -->
             <!--  <el-table-column
                :label="$t('warehouseEntry.label.inventoryQty')"
                prop="productInventoryQty"
                show-overflow-tooltip
                align="right"
              /> -->
              <el-table-column
                :label="$t('warehouseEntry.label.inventoryQty')"
                prop="productActualQty"
                show-overflow-tooltip
              />
              <!-- 库区 -->
              <el-table-column
                :label="$t('warehouseEntry.label.warehouseAreaName')"
                show-overflow-tooltip
                min-width="150"
              >
                <template #default="scope">
                  {{ scope.row.warehouseAreaName }}

                  <span
                    v-if="
                      scope.row.warehouseAreaName && scope.row.warehouseAreaCode
                    "
                  >
                    |
                  </span>
                  {{ scope.row.warehouseAreaCode }}
                  <!-- <el-select
                    v-model="scope.row.warehouseAreaCode"
                    :placeholder="$t('common.placeholder.selectTips')"
                    disabled
                  >
                    <el-option
                      v-for="item in warehouseAreaList"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select> -->
                </template>
              </el-table-column>
              <!-- 本次入库数量 提交时覆盖productInventoryQty-->
              <!-- <el-table-column
                :label="$t('warehouseEntry.label.productInventoryQtyCopy')"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.productInventoryQtyCopy"
                    :placeholder="$t('common.placeholder.inputTips')"
                    disabled
                  >
                    <template #append>
                      {{ scope.row.productUnitName }}
                    </template>
                  </el-input>
                </template>
              </el-table-column> -->
               <!-- 实际入库重量 -->
               <el-table-column
                :label="'实际入库重量(Kg)'"
                prop="productActualWeight"
                show-overflow-tooltip
                width="150"
              />

               <!-- 入库商品YSN -->
               <el-table-column
                :label="'入库商品YSN'"
                show-overflow-tooltip
                width="120"
              >
                <template #default="scope">
                  <el-button type="text" @click="handleViewYSN(scope.row)">查看</el-button>
                </template>
              </el-table-column>
              <!-- 入库类型 -->
              <el-table-column
                :label="$t('warehouseEntry.label.receiptType')"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <!-- 入库类型:1:采购入库、2:退货入库 -->
                  <span v-if="scope.row.receiptType == 1">
                    {{ $t("warehouseEntry.label.purchaseInventory") }}
                  </span>
                  <span v-else-if="scope.row.receiptType == 2">
                    {{ $t("warehouseEntry.label.returnStorage") }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">
          {{ $t("common.reback") }}
        </el-button>
      </div>
    </div>
    
    <!-- YSN列表弹窗 -->
    <el-dialog
      v-model="ysnDialog.visible"
      :title="`商品YSN码-${ysnDialog.productCode} | ${ysnDialog.productName}`"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-table
        :data="ysnList"
        v-loading="ysnLoading"
        border
        stripe
        max-height="400"
      >
        <template #empty>
          <el-empty v-if="!ysnList.length && !ysnLoading" description="暂无YSN数据" />
        </template>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="ysnCode" label="YSN码" min-width="240" show-overflow-tooltip />
        <el-table-column prop="weight" label="重量(kg)" width="120" align="right">
          <template #default="{ row }">
            {{ row.productWeight || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="scanTime" label="扫描时间" width="160" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.scanTime ? parseTime(row.scanTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
          </template>
        </el-table-column>
      </el-table>
      
      <div style="margin-top: 16px; text-align: center;">
        <pagination
          v-if="ysnTotal > 0"
          v-model:total="ysnTotal"
          v-model:page="ysnQueryParams.page"
          v-model:limit="ysnQueryParams.limit"
          @pagination="handleYsnPageChange"
        />
      </div>
      
      <template #footer>
        <el-button @click="ysnDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "WarehouseEntryNoticeDetail",
  inheritAttrs: false,
});
import warehouseEntryAPI, {
  addFormData,
} from "@/modules/wms/api/warehouseEntry";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import { parseTime } from "@/core/utils/index.js";
import Pagination from '@/core/components/Pagination/index.vue';

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const loading = ref(false);
const tableSumRef1 = ref();
const form = reactive<addFormData>({
  productList: [],
});
const warehouseAreaList = ref([]);

// YSN相关状态
const ysnDialog = reactive({
  visible: false,
  productCode: '',
  productName: '',
  entryOrderId: '',
  warehouseAreaCode: '',
  sortingCode: ''
});
const ysnList = ref([]);
const ysnLoading = ref(false);
const ysnTotal = ref(0);
const ysnQueryParams = reactive({
  page: 1,
  limit: 10,
  entryOrderId: '',
  productCode: '',
  warehouseAreaCode: '',
  sortingCode: ''
});

async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}

/** 查询采购单详情 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: route.query.id,
  };
  warehouseEntryAPI
    .queryDetail(params)
    .then((data: any) => {
      Object.assign(form, data);
    })
    .finally(() => {
      loading.value = false;
    });
}

// 查看YSN列表
function handleViewYSN(row: any) {
  console.log('handleViewYSN called with row:', row);
  console.log('form data:', form);
  
  ysnDialog.visible = true;
  ysnDialog.productCode = row.productCode;
  ysnDialog.productName = row.productName;
  
  // 从路由或form中获取entryOrderId，确保类型正确
  const entryOrderId = String(route.query.id || form.id || '');
  ysnDialog.entryOrderId = entryOrderId;
  ysnDialog.warehouseAreaCode = row.warehouseAreaCode;
  ysnDialog.sortingCode = row.sortingCode;
  
  // 设置查询参数
  ysnQueryParams.entryOrderId = entryOrderId;
  ysnQueryParams.productCode = row.productCode;
  ysnQueryParams.warehouseAreaCode = row.warehouseAreaCode;
  ysnQueryParams.sortingCode = row.sortingCode;
  ysnQueryParams.page = 1;
  
  console.log('YSN查询参数:', ysnQueryParams);
  
  queryYsnList();
}

// 查询YSN列表
function queryYsnList() {
  ysnLoading.value = true;
  warehouseEntryAPI.queryYSNList(ysnQueryParams)
    .then((res: any) => {
      ysnList.value = res.records || [];
      ysnTotal.value = parseInt(res.total) || 0;
    })
    .catch((error) => {
      console.error('查询YSN列表失败', error);
      ElMessage.error('查询YSN列表失败');
    })
    .finally(() => {
      ysnLoading.value = false;
    });
}

// 处理YSN分页变化
function handleYsnPageChange(pagination: any) {
  ysnQueryParams.page = pagination.page;
  ysnQueryParams.limit = pagination.limit;
  queryYsnList();
}

onMounted(() => {
  queryDetail();
});
</script>
<style scoped lang="scss">
.warehouseEntryNoticeDetail {
  background: #ffffff;
  border-radius: 4px;
}
</style>
<style lang="scss">
.warehouseEntryNoticeDetail {
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .cursor-pointer {
    cursor: pointer;
  }
}
</style>
