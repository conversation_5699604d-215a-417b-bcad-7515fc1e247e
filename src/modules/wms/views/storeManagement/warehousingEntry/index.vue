
<template>
  <div class="app-container">
    <div class="warehousingEntry">
      <div class="search-container">
        <el-form
          ref="queryFormRef"
          :model="queryParams"
          :inline="true"
          label-width="96px"
        >
          <!-- 入库单 -->
          <el-form-item
            prop="entryOrderCode"
            :label="$t('warehouseEntry.label.entryOrderCode')"
          >
            <el-input
              v-model="queryParams.entryOrderCode"
              :placeholder="
                $t('warehouseEntryNotice.placeholder.inputLimtTips')
              "
              clearable
              class="!w-[256px]"
            />
          </el-form-item>
          <!-- 是否领单 -->
          <el-form-item
            prop="receivingStatus"
            label="领用状态"
          >
            <el-select
              v-model="queryParams.receivingStatus"
              clearable
              placeholder="请选择领用状态"
              class="!w-[256px]"
            >
              <el-option :label="'否'" :value="0" />
              <el-option :label="'是'" :value="1" />
            </el-select>
          </el-form-item>
          <!-- 入库状态 -->
          <el-form-item
            prop="status"
            label="入库状态"
          >
            <el-select
              v-model="queryParams.status"
              clearable
              placeholder="请选择入库状态"
              class="!w-[256px]"
            >
              <el-option :label="'初始'" :value="0" />
              <el-option :label="'入库中'" :value="3" />
              <el-option :label="'已入库'" :value="1" />
            </el-select>
          </el-form-item>
          <!-- 入库人 -->
          <el-form-item
            prop="entryOperator"
            :label="$t('warehouseEntry.label.entryOperator')"
          >
            <el-input
              v-model="queryParams.entryOperator"
              :placeholder="$t('common.placeholder.inputTips')"
              clearable
              class="!w-[256px]"
            />
          </el-form-item>
          <!-- 时间-->
          <el-form-item
            prop="dateRange"
            :label="$t('warehouseEntry.label.entryTime')"
          >
            <el-select
              v-model="queryParams.queryType"
              style="width: 120px; margin-right: 8px"
            >
              <el-option :label="'入库时间'" :value="1" />
              <el-option :label="'创建时间'" :value="2" />
              <el-option :label="'领用时间'" :value="3" />
            </el-select>
            <el-date-picker
              :editable="false"
              class="!w-[370px]"
              v-model="dateRange"
              type="datetimerange"
              :range-separator="$t('warehouseEntry.label.to')"
              :start-placeholder="$t('warehouseEntry.label.startTime')"
              :end-placeholder="$t('warehouseEntry.label.endTime')"
              :default-time="defaultTime"
              :placeholder="$t('common.placeholder.selectTips')"
            />
            <span
              class="ml16px mr14px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(1)"
            >
              {{ $t("warehouseEntry.label.today") }}
            </span>
            <span
              class="mr14px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(2)"
            >
              {{ $t("warehouseEntry.label.yesterday") }}
            </span>
            <span
              class="mr16px cursor-pointer"
              style="color: var(--el-color-primary)"
              @click="handleChangeDateRange(3)"
            >
              {{ $t("warehouseEntry.label.weekday") }}
            </span>
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPerm="['wms:storeManagement:warehouseEntry:search']"
              type="primary"
              @click="handleQuery"
            >
              {{ $t("common.search") }}
            </el-button>
            <el-button
              v-hasPerm="['wms:storeManagement:warehouseEntry:reset']"
              @click="handleResetQuery"
            >
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-card shadow="never" class="table-container">
        <template #header>
          <el-button
            type="primary"
            @click="handleAdd(undefined, 'add')"
            v-hasPerm="['wms:storeManagement:warehouseEntry:add']"
          >
            {{ $t("warehouseEntry.button.addBtn") }}
          </el-button>
        </template>

        <el-table
          v-loading="loading"
          :data="tableList"
          highlight-current-row
          stripe
          height="500px"
        >
          <template #empty>
            <Empty />
          </template>
          <el-table-column
            type="index"
            :label="$t('common.sort')"
            width="80"
            align="center"
          />
          <!-- 入库单 -->
          <el-table-column
            :label="$t('warehouseEntry.label.entryOrderCode')"
            prop="entryOrderCode"
            show-overflow-tooltip
            min-width="160"
          />
          <!-- 入库类型 -->
          <el-table-column
            label="入库类型"
            prop="orderType"
            show-overflow-tooltip
            min-width="120"
          >
            <template #default="scope">
              <span v-if="scope.row.orderType === 1">按分拣单入库</span>
              <span v-else-if="scope.row.orderType === 2">按商品入库</span>
              <span v-else-if="scope.row.orderType === 3">按收运单入库</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 入库量 -->
          <el-table-column label="入库量" min-width="180">
            <template #default="scope">
              <div>
                <div>商品个数：<span v-if="scope.row.skuQty" class="skuQtyColor">{{ scope.row.skuQty }}</span><span v-else>-</span></div>
                <div>数量：<span>{{ scope.row.productTotalInQty || '-' }}</span></div>
                <div>重量：<span>{{ scope.row.productTotalInWeight || '-' }}</span></div>
              </div>
            </template>
          </el-table-column>
          <!-- 使用信息 -->
          <el-table-column label="领用信息" min-width="180">
            <template #default="scope">
              <div>
                <div>领单人：<span>{{ scope.row.receivingUserName || '-' }}</span></div>
                <div>领单时间：<span>{{ scope.row.receivingTime ? parseTime(scope.row.receivingTime, "{y}-{m}-{d} {h}:{i}:{s}") : '-' }}</span></div>
              </div>
            </template>
          </el-table-column>
          <!-- 商品种类数量 -->
          <el-table-column
            :label="$t('warehouseEntry.label.productType')"
            prop="skuQty"
            show-overflow-tooltip
            align="right"
            min-width="100"
          >
            <template #default="scope">
              <span v-if="scope.row.skuQty" class="skuQtyColor">
                {{ scope.row.skuQty }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <!-- <el-table-column
            :label="$t('warehouseEntry.label.remark')"
            prop="remark"
            show-overflow-tooltip
            width="130"
          /> -->
          <!-- 创建人 -->
          <el-table-column
            :label="$t('warehouseEntry.label.createUserName')"
            prop="createUserName"
            show-overflow-tooltip
          />
          <!-- 创建时间 -->
          <el-table-column
            :label="$t('warehouseEntry.label.createTime')"
            prop="createTime"
            show-overflow-tooltip
            min-width="180"
          >
            <template #default="scope">
              {{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
          <!-- 入库时间 -->
          <el-table-column
            :label="$t('warehouseEntry.label.entryTime')"
            prop="entryTime"
            show-overflow-tooltip
            min-width="180"
          >
            <template #default="scope">
              {{ parseTime(scope.row.entryTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
          <!-- 入库人 -->
          <el-table-column
            :label="$t('warehouseEntry.label.entryOperator')"
            prop="entryOperator"
            show-overflow-tooltip
          />
          <!-- 状态 -->
          <el-table-column
            :label="$t('warehouseEntry.label.status')"
            prop="status"
            show-overflow-tooltip
            min-width="120"
            fixed="right"
          >
            <template #default="scope">
              <div class="purchase">
                <!-- >0:初始;1:已入库;3:入库中 -->
                <div
                  v-if="scope.row.status == 1"
                  type="info"
                  class="purchase-status purchase-status-color2"
                >
                  {{ $t("warehouseEntry.label.alreadyStock") }}
                </div>
                <div
                  v-else-if="scope.row.status == 3"
                  type="info"
                  class="purchase-status purchase-status-color3"
                >
                  {{ $t("warehouseEntry.label.stocking") }}
                </div>
                <div
                  v-else-if="scope.row.status == 0"
                  type="info"
                  class="purchase-status purchase-status-color0"
                >
                  {{ $t("warehouseEntry.label.initStock") }}
                </div>
                <div v-else>-</div>
              </div>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column
            fixed="right"
            :label="$t('common.handle')"
            width="220"
          >
            <template #default="scope">
              <el-button
                v-hasPerm="['wms:storeManagement:warehouseEntry:detail']"
                type="primary"
                link
                @click="handleCheck(scope.row.id, scope.row.entryOrderCode)"
              >
                {{ $t("common.detailBtn") }}
              </el-button>
              <el-button
                v-hasPerm="['wms:storeManagement:warehouseEntry:reveive:order']"
                v-if="scope.row.receivingStatus === 0 && scope.row.status === 0"
                type="primary"
                link
                @click="handleReceive(scope.row)"
                :loading="scope.row.btnLoading"
              >
                领单
              </el-button>

              <!-- 取消领单按钮  && userStore.user.userId == scope.row.receivingUserId-->
              <el-button
                v-hasPerm="['wms:storeManagement:warehouseEntry:cancel:order']"
                v-if="scope.row.receivingStatus === 1 && scope.row.status === 0"
                type="primary"
                link
                @click="handleCancelReceive(scope.row)"
                :loading="scope.row.btnLoading"
              >
                取消领单
              </el-button>

              <!-- 编辑按钮 orderType === 2 PDA端商品入库-->
              <el-button
                v-hasPerm="['wms:storeManagement:warehouseEntry:edit']"
                v-if="scope.row.receivingStatus === 1
                && scope.row.status === 0
                && userStore.user.userId == scope.row.receivingUserId
                && scope.row.orderType !== 2"
                type="primary"
                link
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handleQuery"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "WarehousingEntry",
  inheritAttrs: false,
});

import warehouseEntryAPI, {
  WarehousePageQuery,
} from "@/modules/wms/api/warehouseEntry";

import { useRouter } from "vue-router";
import { parseTime, changeDateRange } from "@/core/utils/index.js";
import moment from "moment";
import { emitter } from "@/core/utils/eventBus";
import { useUserStoreHook } from "@/core/store/modules/user";
import { ElMessage, ElMessageBox } from "element-plus";

const router = useRouter();
const { t } = useI18n();
const userStore = useUserStoreHook();
const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const dateRange = ref<string[]>([
  moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
  moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
]);
const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];

const queryParams = reactive<WarehousePageQuery>({
  page: 1,
  limit: 20,
  queryType: 1,
});

const tableList = ref([]);

/** 时间转换 */
function handleChangeDateRange(val: any) {
  const range = changeDateRange(val);
  if (range && range.length === 2) {
    // Filter out any undefined values and provide default values if needed
    dateRange.value = range.map(date => date || '') as string[];
  }
}

// 领用状态=否且状态=初始
const showReceiveBtn = (row: any) => {
  return row.receivingStatus === 0 && row.status === 0;
};
// 领用状态=是且状态=初始
const showCancelReceiveBtn = (row: any) => {
  return row.receivingStatus === 1 && row.status === 0;
};

/** 查询 */
function handleQuery() {
  if (queryParams.entryOrderCode && queryParams.entryOrderCode.length < 4) {
    return ElMessage.error(t("warehouseEntryNotice.message.codeValideTips"));
  }
  loading.value = true;
  let params: any = {
    ...queryParams,
  };
  if (dateRange.value && dateRange.value.length == 2) {
    params.queryStartTime = new Date(dateRange.value[0]).getTime();
    params.queryEndTime = new Date(dateRange.value[1]).getTime();
  }
  warehouseEntryAPI
    .queryPageList(params)
    .then((data: any) => {
      tableList.value = data.records.map((item: any, index: any) => {
        item.mobilePhoneShow = true;
        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.queryType = 1;
  queryParams.receivingStatus = undefined;
  queryParams.status = undefined;
  dateRange.value = [
    moment().subtract(29, "days").startOf("days").format("YYYY-MM-DD HH:mm:ss"),
    moment().endOf("days").format("YYYY-MM-DD HH:mm:ss"),
  ];
  handleQuery();
}

/** 新增*/
function handleAdd() {
  router.push({
    path: "/wms/storeManagement/addWarehousingEntryOrders",
  });
}

/** 详情*/
function handleCheck(id?: string, code?: string) {
  router.push({
    path: "/wms/storeManagement/warehousingEntryOrderDetail",
    query: { id: id, entryOrderCode: code },
  });
}

/** 领单 */
function handleReceive(row: any) {
  row.btnLoading = true;
    warehouseEntryAPI.receiveOrder({ id: row.id })
      .then(() => {
        ElMessage.success('领单成功');
        handleQuery();
      })
      .catch((error) => {
        console.error('领单失败', error);
        ElMessage.error('领单失败');
      })
      .finally(() => {
        row.btnLoading = false;
      });
}

/** 取消领单 */
function handleCancelReceive(row: any) {
  ElMessageBox.confirm('确认要取消领取单据', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    row.btnLoading = true;
    warehouseEntryAPI.cancelReceiveOrder({ id: row.id })
      .then(() => {
        ElMessage.success('取消领单成功');
        handleQuery();
      })
      .catch((error) => {
        console.error('取消领单失败', error);
        ElMessage.error('取消领单失败');
      })
      .finally(() => {
        row.btnLoading = false;
      });
  }).catch(() => {
    // 用户取消操作
  });
}
/** 编辑 */
function handleEdit(row: any) {
  router.push({
    path: "/wms/storeManagement/editWarehousingEntryOrders",
    query: { id: row.id, isEdit: "1", entryOrderCode: row.entryOrderCode },
  });
}

onActivated(() => {
  handleQuery();
});
emitter.on("reloadListByWarehouseId", (e) => {
  nextTick(() => {
    handleQuery();
  });
});
</script>

<style lang="scss" scoped>
.encryptBox {
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  vertical-align: text-top;
}
.skuQtyColor {
  color: #762adb;
}
</style>
