<template>
  <div class="app-container">
    <div
      class="container-wrapper"
      v-loading="loading"
    >
      <div class="page-title">
        <div class="purchase-title">
          <div
            @click="handleClose()"
            class="cursor-pointer mr8px"
          >
            <el-icon>
              <Back />
            </el-icon>
          </div>
          <div>
            <span v-if="pageType === 'detail'">
              {{ $t("pickOrder.title.sortingDetail") }}
            </span>
            <span v-else>{{ $t("pickOrder.title.editSorting") }}</span>
          </div>
          <div></div>
        </div>
      </div>
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
      >
        <div class="page-content">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("pickOrder.title.basicInformation") }}
            </div>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item :label="$t('pickOrder.label.sortingCode')">
                {{ formData.sortingCode ? formData.sortingCode : "-" }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('pickOrder.label.sortingType')">
                {{
                  !isEmpty(formData.sortingType)
                    ? filterSortingType(formData.sortingType)
                    : "-"
                }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('pickOrder.label.sortingStatus')">
                {{ filterSortingStatus(formData.sortingStatus) }}
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item
                :label="$t('pickOrder.label.remark')"
                prop="remark"
              >
                <template v-if="pageType == 'detail'">
                  {{ formData.remark || "-" }}
                </template>
                <el-input
                  v-else
                  class="!w-[256px]"
                  v-model="formData.remark"
                  :placeholder="$t('common.placeholder.inputTips')"
                  maxlength="200"
                  clearable
                />
              </el-form-item>
            </el-col>
            <template v-if="pageType == 'detail'">
              <el-col :span="6">
                <el-form-item :label="$t('pickOrder.label.createUserName')">
                  <span>
                    {{
                      formData.createUserName ? formData.createUserName : "-"
                    }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('pickOrder.label.createTime')">
                  <span>
                    {{
                      formData.createTime
                        ? parseDateTime(formData.createTime, "dateTime")
                        : "-"
                    }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('pickOrder.label.sortingUserName')">
                  <span>
                    {{
                      formData.handleUserName ? formData.handleUserName : "-"
                    }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('pickOrder.label.sortingTime')">
                  <span>
                    {{
                      formData.completeTime
                        ? parseDateTime(formData.completeTime, "dateTime")
                        : "-"
                    }}
                  </span>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item :label="$t('pickOrder.label.useTotalQuantity')">
                  <span>
                    {{
                      formData.receiptProductQty
                        ? formData.receiptProductQty
                        : "-"
                    }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('pickOrder.label.sortingBeforeWeight')"
                >
                  <span>
                    {{
                      formData.receiptProductWeight
                        ? formData.receiptProductWeight
                        : "-"
                    }}
                  </span>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item
                  :label="$t('pickOrder.label.sortingTotalQuantity')"
                >
                  {{
                    formData.sortingProductQty
                      ? formData.sortingProductQty
                      : "-"
                  }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('pickOrder.label.sortingConversionQuantity')"
                >
                  {{
                    formData.sortingProductWeight
                      ? formData.sortingProductWeight
                      : "-"
                  }}
                </el-form-item>
              </el-col>
            </template>
          </el-row>
          <div class="line"></div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("pickOrder.title.sortingTable") }}
            </div>
            <div class="title-action">
              <el-button
                v-if="pageType !== 'detail'"
                type="primary"
                @click="handleAddProduct"
              >
                {{ $t("pickOrder.button.selectProduct") }}
              </el-button>
            </div>
          </div>
        </div>

        <div class="table-container">
          <el-table
            :data="formData.detailVOList"
            :span-method="spanMethod"
            default-expand-all
            row-class-name="table-row"
            style="border: none; --el-table-border-color: none"
          >
            <template #empty>
              <Empty />
            </template>

            <el-table-column
              type="expand"
              width="1"
            >
              <template #default="props">
                <el-table
                  :data="props.row.sortingResultList"
                  class="table-children"
                  :show-header="false"
                  :cell-style="cellStyleChildren"
                >
                  <template #append>
                    <div class="table-summaries">
                      <div class="summaries">
                        {{ $t("pickOrder.label.summation") }}：
                      </div>
                      <div class="column">
                        {{ $t("pickOrder.label.sortingAfterQty") }}:{{
                          calcAmount(props)
                        }}
                      </div>
                      <div class="column">
                        {{
                          $t("pickOrder.label.sortingAfterConversionQuantity")
                        }}:{{ calcWeight(props) }}
                      </div>
                    </div>
                  </template>
                  <el-table-column
                    type="index"
                    :label="$t('common.sort')"
                    width="60"
                    prop="index"
                  >
                    <template #default="scope">
                      {{ scope.row.isIndex }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.goodsCategory')"
                    prop="firstCategoryName"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      {{ scope.row.firstCategoryName }} /
                      {{ scope.row.secondCategoryName }} /
                      {{ scope.row.thirdCategoryName }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.goods')"
                    prop="productName"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      {{ scope.row.productCode }} |{{ scope.row.productName }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.specification')"
                    prop="productSpec"
                    show-overflow-tooltip
                  ></el-table-column>

                  <el-table-column
                    :label="$t('pickOrder.label.sortingAfterQty')"
                    prop="sortingAfterQty"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    :label="
                      $t('pickOrder.label.sortingAfterConversionQuantity')
                    "
                    prop="sortingAfterWeight"
                    show-overflow-tooltip
                  ></el-table-column>

                  <el-table-column
                    :label="$t('pickOrder.label.warehouseAreaCode')"
                    prop="warehouseAreaCode"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-form-item
                        :prop="
                          'detailVOList.' +
                          props.$index +
                          '.sortingResultList.' +
                          scope.$index +
                          '.warehouseAreaCode'
                        "
                        :rules="rules.warehouseAreaCode"
                      >
                        <template v-if="pageType == 'detail'">
                          {{ scope.row.warehouseAreaName }} |
                          {{ scope.row.warehouseAreaCode }}
                        </template>
                        <el-select
                          v-else
                          v-model="scope.row.warehouseAreaCode"
                          :placeholder="$t('common.placeholder.selectTips')"
                          clearable
                        >
                          <el-option
                            v-for="(item, index) in warehouseAreaOption"
                            :key="index"
                            :label="`${item.areaName} | ${item.areaCode}`"
                            :value="item.areaCode"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>

                  <el-table-column
                    :label="$t('pickOrder.label.inWarehouseQty')"
                    prop="inWarehouseQty"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-form-item
                        :prop="
                          'detailVOList.' +
                          props.$index +
                          '.sortingResultList.' +
                          scope.$index +
                          '.inWarehouseQty'
                        "
                        :rules="rules.inWarehouseQty"
                      >
                        <template v-if="pageType == 'detail'">
                          {{ scope.row.inWarehouseQty || "-"
                          }}{{ scope.row.productUnit }}
                        </template>
                        <el-input
                          v-else
                          v-model="scope.row.inWarehouseQty"
                          maxlength="12"
                          :placeholder="$t('common.placeholder.inputTips')"
                          @input="(value) => onInput(value, props, scope)"
                        >
                          <template #append>
                            {{ scope.row.productUnit }}
                          </template>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('pickOrder.label.inWarehouseWeight')"
                    prop="inWarehouseWeight"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-form-item>
                        <template v-if="pageType == 'detail'">
                          {{ scope.row.inWarehouseWeight || "-"
                          }}{{ scope.row.conversionRelSecondUnitName }}
                        </template>
                        <el-input
                          v-else
                          v-model="scope.row.inWarehouseWeight"
                          maxlength="12"
                          :placeholder="
                            $t('pickOrder.placeholder.calcConversionQuantity')
                          "
                          disabled
                        >
                          <template #append>
                            {{ scope.row.conversionRelSecondUnitName }}
                          </template>
                        </el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>

                  <template v-if="pageType !== 'detail'">
                    <el-table-column
                      fixed="right"
                      :label="$t('pickOrder.label.operation')"
                      width="160"
                    >
                      <template #default="scope">
                        <el-button
                          type="primary"
                          link
                          @click="handleAdd(props, scope)"
                        >
                          {{ $t("pickOrder.button.actionAdd") }}
                        </el-button>

                        <el-button
                          type="danger"
                          link
                          :disabled="disabledDeletee(props, scope)"
                          @click="handleDelete(props, scope)"
                        >
                          {{ $t("pickOrder.button.actionDelete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column
              type="index"
              :label="$t('common.sort')"
              width="60"
            >
              <template #default="scope">
                <div class="table-header">
                  <div class="sort">
                    {{
                      scope.$index >= 9
                        ? scope.$index + 1
                        : "0" + (scope.$index + 1)
                    }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.goodsCategory") }}:
                    {{ scope.row.firstCategoryName }} /
                    {{ scope.row.secondCategoryName }} /
                    {{ scope.row.thirdCategoryName }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.goods") }}:
                    {{ scope.row.productCode }} |{{ scope.row.productName }}
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.specification") }}:{{
                      scope.row.productSpec
                    }}
                  </div>
                  <div class="column">
                    <span class="column-label">
                      {{ $t("pickOrder.label.sortingBeforeQty") }}({{
                        scope.row.productUnit
                      }}):
                    </span>
                    <el-form-item
                      class="column-form-item"
                      :prop="
                        'detailVOList.' + scope.$index + '.sortingBeforeQty'
                      "
                      :rules="rules.sortingBeforeQty"
                    >
                      <template v-if="pageType == 'detail'">
                        {{ scope.row.sortingBeforeQty || "-" }}
                      </template>
<!--                      <el-input-->
<!--                        v-else-->
<!--                        v-model="scope.row.sortingBeforeQty"-->
<!--                        class="!w-[100px]"-->
<!--                        maxlength="12"-->
<!--                        :placeholder="$t('common.placeholder.inputTips')"-->
<!--                        @input="-->
<!--                          (value) => onInputSortingBeforeQty(value, scope)-->
<!--                        "-->
<!--                      ></el-input>-->
                        <el-input
                          v-else
                          v-model="scope.row.sortingBeforeQty"
                          class="!w-[100px]"
                          maxlength="12"
                          :placeholder="$t('common.placeholder.inputTips')"
                          @blur="() => onInputSortingBeforeQty(scope.row.sortingBeforeQty, scope)"
                        ></el-input>
                    </el-form-item>
                  </div>
                  <div class="column">
                    <span class="column-label">
                      {{ $t("pickOrder.label.sortingBeforeWeight") }}({{
                        scope.row.conversionRelSecondUnitName
                      }}):
                    </span>

                    <el-form-item
                      class="column-form-item"
                      :prop="
                        'detailVOList.' + scope.$index + '.sortingBeforeWeight'
                      "
                      :rules="rules.sortingBeforeWeight"
                    >
                      <template v-if="pageType == 'detail'">
                        {{ scope.row.sortingBeforeWeight || "-" }}
                      </template>
<!--                      <el-input-->
<!--                        v-else-->
<!--                        v-model="scope.row.sortingBeforeWeight"-->
<!--                        class="!w-[100px]"-->
<!--                        maxlength="12"-->
<!--                        :placeholder="$t('common.placeholder.inputTips')"-->
<!--                        @input="-->
<!--                          (value) => onInputSortingBeforeWeight(value, scope)-->
<!--                        "-->
<!--                      ></el-input>-->
                        <el-input
                          v-else
                          v-model="scope.row.sortingBeforeWeight"
                          class="!w-[100px]"
                          maxlength="12"
                          :placeholder="$t('common.placeholder.inputTips')"
                          @blur="() => onInputSortingBeforeWeight(scope.row.sortingBeforeWeight, scope)"
                        ></el-input>
                    </el-form-item>
                  </div>
                  <div class="column">
                    {{ $t("pickOrder.label.warehouseAreaName") }}:
                    {{ scope.row.warehouseAreaName }}
                  </div>
                  <div
                    class="column"
                    v-if="pageType !== 'detail'"
                  >
                    {{ $t("pickOrder.label.availableStockQty") }}({{
                      scope.row.productUnit
                    }}):{{ scope.row.availableStockQty }}
                  </div>
                  <div
                    class="column"
                    v-if="pageType !== 'detail'"
                  >
                    {{ $t("pickOrder.label.availableConversionQuantity") }}({{
                      scope.row.conversionRelSecondUnitName
                    }}):{{ scope.row.availableStockWeight }}
                  </div>
                  <div
                    v-if="pageType !== 'detail'"
                    class="action"
                  >
                    <el-button
                      type="primary"
                      link
                      @click="handleAddGoods(scope)"
                    >
                      {{ $t("pickOrder.button.addGoods") }}
                    </el-button>
                    <el-button
                      type="danger"
                      link
                      @click="handleDeleteGoods(scope)"
                    >
                      {{ $t("pickOrder.button.actionDelete") }}
                    </el-button>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.goodsCategory')"
              prop="firstCategoryName"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.goods')"
              prop="productName"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.specification')"
              prop="productSpec"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              :label="$t('pickOrder.label.sortingAfterQty')"
              prop="sortingAfterQty"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.sortingAfterConversionQuantity')"
              prop="sortingAfterWeight"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label-class-name="label-required"
              :label="$t('pickOrder.label.warehouseAreaCode')"
              prop="warehouseAreaCode"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              label-class-name="label-required"
              :label="$t('pickOrder.label.inWarehouseQty')"
              prop="inWarehouseQty"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('pickOrder.label.inWarehouseWeight')"
              prop="inWarehouseWeight"
              show-overflow-tooltip
            ></el-table-column>

            <template v-if="pageType !== 'detail'">
              <el-table-column
                fixed="right"
                :label="$t('pickOrder.label.operation')"
                width="160"
              ></el-table-column>
            </template>
          </el-table>
        </div>
      </el-form>
      <div
        class="page-footer"
        v-if="pageType !== 'detail'"
      >
        <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        <el-button @click="handleSubmit(0)">
          {{ $t("pickOrder.button.confirmDraft") }}
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit(2)"
        >
          {{ $t("pickOrder.button.confirmSorting") }}
        </el-button>
      </div>
    </div>

    <SelectProduct
      ref="selectProductRef"
      v-model:visible="selectProductDialog.visible"
      :title="selectProductDialog.title"
      @onSubmit="handleSubmitSelectProduct"
    />
    <AddProduct
      ref="addProductRef"
      v-model:visible="addProductDialog.visible"
      :title="addProductDialog.title"
      @onSubmit="handleSubmitProduct"
    />
  </div>
</template>

<script setup lang="ts">
import { useTagsViewStore, useUserStore } from "@/core/store";
import {
  changeDateRange,
  convertToTimestamp,
  parseDateTime,
  isEmpty,
} from "@/core/utils";

import API from "@/modules/wms/api/quickPickOrder";
import CommonAPI from "@/modules/wms/api/common";

import SelectProduct from "./components/selectProduct.vue";
import AddProduct from "./components/addProduct.vue";

const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();

const state = reactive({
  loading: false,
  query: route.query,
  pageType: route.query?.pageType,
  formRef: null,
  formData: {
    detailVOList: [],
  },
  sortingStatusOption: [
    { label: t("pickOrder.label.quickSortingStatusOption[0]"), value: 0 },
    { label: t("pickOrder.label.quickSortingStatusOption[2]"), value: 2 },
  ],
  sortingTypeOption: [
    { label: t("pickOrder.label.sortingTypeOption[0]"), value: 0 },
    { label: t("pickOrder.label.sortingTypeOption[1]"), value: 1 },
  ],
  detailListRef: null,
  detailDialog: {
    title: "",
    visible: false,
  },
  addProductRef: null,
  addProductDialog: {
    title: "",
    visible: false,
  },
  selectProductRef: null,
  selectProductDialog: {
    title: "",
    visible: false,
  },
  currentOperationIndex: -1,
  originalData: {},
  submitState: false,
  warehouseAreaOption: [],
}) as any;

const {
  loading,
  query,
  pageType,
  formRef,
  formData,
  sortingStatusOption,
  sortingTypeOption,
  selectProductRef,
  selectProductDialog,
  addProductRef,
  addProductDialog,
  currentOperationIndex,
  originalData,
  submitState,
  warehouseAreaOption,
} = toRefs(state);

const rules = reactive({
  sortingBeforeQty: [
    {
      required: true,
      message: `${t("common.placeholder.inputTips")}`,
      trigger: "blur",
    },
    {
      pattern: /^([1-9]\d{0,7}(\.\d{1,3})?|0\.(?!0+$)\d{1,3})$/,
      message: `${t("pickOrder.rules.positiveInteger8")}`,
      trigger: "blur",
    },
  ],
  sortingBeforeWeight: [
    {
      required: true,
      message: `${t("common.placeholder.inputTips")}`,
      trigger: "blur",
    },
    {
      pattern: /^([1-9]\d{0,7}(\.\d{1,3})?|0\.(?!0+$)\d{1,3})$/,
      message: `${t("pickOrder.rules.positiveInteger8")}`,
      trigger: "blur",
    },
  ],

  warehouseAreaCode: [
    {
      required: true,
      message: `${t("common.placeholder.selectTips")}`,
      trigger: "change",
    },
  ],
  inWarehouseQty: [
    {
      required: true,
      message: `${t("common.placeholder.inputTips")}`,
      trigger: "blur",
    },
    {
      pattern: /^([1-9]\d{0,7}(\.\d{1,3})?|0\.(?!0+$)\d{1,3})$/,
      message: `${t("pickOrder.rules.positiveInteger8")}`,
      trigger: "blur",
    },
  ],
});

/**
 * 查询详情
 */
function queryDetail() {
  loading.value = true;
  let sortingCode = query.value.sortingCode;
  API.queryDetail(sortingCode)
    .then((data: any) => {
      data.detailVOList.forEach((item: any) => {
        if (item.sortingResultList && Array.isArray(item.sortingResultList)) {
          item.sortingResultList.forEach((resultItem: any) => {
            resultItem.isChildren = Number(resultItem?.sortingAfterQty) == 0;
          });
          updateIndexForList(item.sortingResultList);
        }
      });

      formData.value = data;
      originalData.value = JSON.parse(JSON.stringify(data));
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 合并列
 * @param row
 * @param column
 * @param rowIndex
 * @param columnIndex
 */
function spanMethod({ row, column, rowIndex, columnIndex }: any) {
  if (columnIndex === 1) {
    return {
      rowspan: 1,
      colspan: 99,
    };
  } else {
    return {
      rowspan: 0,
      colspan: 0,
    };
  }
}

/**
 * 关闭窗口
 */

async function handleClose() {
  // await tagsViewStore.delView(route);
  router.go(-1);
}

/**
 *  确认分拣
 */
function handleSubmit(sortingStatus: number) {
  console.log(formData.value);

  formRef.value.validate((valid: any) => {
    if (valid) {
      const detailList = formData.value?.detailVOList?.map((item: any) => ({
        productCode: item.productCode,
        sortingBeforeQty: item.sortingBeforeQty,
        sortingBeforeWeight: item.sortingBeforeWeight,
        warehouseAreaCode: item.warehouseAreaCode,
        resultList: item?.sortingResultList?.map((sub: any) => ({
          sortingAfterQty: sub.sortingAfterQty,
          sortingAfterWeight: sub.sortingAfterWeight,
          productCode: sub.productCode,
          warehouseAreaCode: sub.warehouseAreaCode,
          inWarehouseQty: sub.inWarehouseQty,
          inWarehouseWeight: sub.inWarehouseWeight,
        })),
      }));

      const isEmptyProduct = detailList.every((item: any) => {
        // 判断列表中有没有存在未分拣的商品
        const resultList = item.resultList;
        return resultList && resultList.length > 0;
      });

      if (!isEmptyProduct) {
        return ElMessage.error(t("pickOrder.message.isEmptyProduct"));
      }
      console.log(detailList);

      let params = {
        sortingCode: query.value.sortingCode || "",
        detailList: detailList,
        remark: formData.value.remark,
        sortingStatus: sortingStatus,
      };
      console.log(params);

      loading.value = true;

      API.saveOrder(params)
        .then((res: any) => {
          ElMessage.success(t("pickOrder.message.actionSucess"));
          submitState.value = true;
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

/**
 * 新增库区商品
 * @param props
 * @param scope
 */

function handleAdd(props: any, scope: any) {
  let list = formData.value?.detailVOList[props.$index]?.sortingResultList;

  if (list && list.length > scope.$index) {
    // 深拷贝指定索引的对象
    const copiedItem = JSON.parse(JSON.stringify(list[scope.$index]));
    //
    let newItem = {
      productCode: copiedItem.productCode,
      conversionRelSecondUnitName: copiedItem.conversionRelSecondUnitName,
      productUnit: copiedItem.productUnit,
      sortingAfterQty: 0,
      sortingAfterWeight: 0,
      isChildren: true,
    };

    // 在原索引位置之后插入新对象
    list.splice(scope.$index + 1, 0, newItem);

    //重建数组内索引
    updateIndexForList(list);
  }
}

/**
 * 删除库区商品
 * @param props
 * @param scope
 */
function handleDelete(props: any, scope: any) {

  formData.value?.detailVOList[props.$index]?.sortingResultList?.splice(
    scope.$index,
    1
  );
    calcQtyAndWeight(props, scope);
}

/**
 * 禁用删除
 * @param props
 * @param scope
 */
function disabledDeletee(props: any, scope: any) {

    const list = formData.value.detailVOList[props.$index].sortingResultList;
    const length = list?.filter((item: any) => item.productCode === scope.row.productCode)?.length;
    return !scope.row?.isChildren && length > 1;

}


/**
 * 更新索引
 * @param list
 */
function updateIndexForList(list: any[]) {
  let isIndex = 1;
  list.forEach((item) => {
    if (!item.isChildren) {
      item.isIndex = isIndex++;
    } else {
      item.isIndex = null;
    }
  });
}

/**
 * 输入入库量(计算分拣量等)
 * @param val
 * @param props
 * @param scope
 */
async function onInput(val: any, props: any, scope: any) {
  await calcQtyAndWeight(props, scope);

    validateField(`detailVOList.${props.$index}.sortingBeforeQty`)
    validateField(`detailVOList.${props.$index}.sortingBeforeWeight`)

  //   console.log(val);
  //   console.log(
  //   formData.value.detailVOList[props.$index].sortingResultList[scope.$index].inWarehouseQty
  // );
  //   const list = formData.value.detailVOList[props.$index].sortingResultList;
  // // console.log(props);
  // // console.log(scope);
  // // console.log(list);
  // // console.log(scope.row.productCode);
  //
  // // 计算分拣量，过滤出符合条件的productCode累加
  // const totalSortingAfterQty = list
  //   ?.filter((item: any) => item.productCode === scope.row.productCode)
  //   ?.reduce(
  //     (total: number, item: any) => total + Number(item?.inWarehouseQty || 0),
  //     0
  //   )
  //   .toFixed(3);
  // // 获取第一个符合条件的productCode的索引
  // const firstIndex = list?.findIndex(
  //   (item: any) => item.productCode === scope.row.productCode
  // );
  // // 设置第一个符合条件的productCode的sortingAfterQty
  // list[firstIndex].sortingAfterQty = totalSortingAfterQty;
  //
  // //获取入库转换量
  //
  // list[scope.$index].inWarehouseWeight = await convertProductUnit(
  //   val,
  //   scope.row.productCode
  // );
  //
  // // 计算分拣转换量，过滤出符合条件的productCode累加
  // const totalSortingAfterWeight = list
  //   ?.filter((item: any) => item.productCode === scope.row.productCode)
  //   ?.reduce(
  //     (total: number, item: any) => total + Number(item?.inWarehouseWeight || 0),
  //     0
  //   )
  //   .toFixed(3);
  //
  // // 设置第一个符合条件的productCode的sortingAfterWeight
  // list[firstIndex].sortingAfterWeight = totalSortingAfterWeight;
  //
  // ///sortingBeforeQty赋值
  // formData.value.detailVOList[props.$index].sortingBeforeQty =
  //   calcAmount(props);
  // ///sortingBeforeWeight赋值
  // formData.value.detailVOList[props.$index].sortingBeforeWeight =
  //   calcWeight(props);
}

/**
 * 计算分拣量等
 */

async function calcQtyAndWeight(props: any, scope: any) {
  const list = formData.value.detailVOList[props.$index].sortingResultList || [];
  // 计算分拣量，过滤出符合条件的productCode累加
  const totalSortingAfterQty = list
    ?.filter((item: any) => item.productCode === scope.row.productCode)
    ?.reduce(
      (total: number, item: any) => total + Number(item?.inWarehouseQty || 0),
      0
    )
    .toFixed(3);
  // 获取第一个符合条件的productCode的索引
  const firstIndex = list?.findIndex(
    (item: any) => item.productCode === scope.row.productCode
  );

    if (firstIndex!== -1) {
        // 设置第一个符合条件的productCode的sortingAfterQty
        list[firstIndex].sortingAfterQty = totalSortingAfterQty;
        if (list[scope.$index]){
            const inWarehouseQty = list[scope.$index]?.inWarehouseQty;
            //获取入库转换量
            list[scope.$index].inWarehouseWeight = await convertProductUnit(
              inWarehouseQty,
              scope.row.productCode
            );
        }
        // 计算分拣转换量，过滤出符合条件的productCode累加
        const totalSortingAfterWeight = list
          ?.filter((item: any) => item.productCode === scope.row.productCode)
          ?.reduce(
            (total: number, item: any) =>
              total + Number(item?.inWarehouseWeight || 0),
            0
          )
          .toFixed(3);

        // 设置第一个符合条件的productCode的sortingAfterWeight
        list[firstIndex].sortingAfterWeight = totalSortingAfterWeight;
    }
  ///sortingBeforeQty赋值
  formData.value.detailVOList[props.$index].sortingBeforeQty =
    calcAmount(props);
  ///sortingBeforeWeight赋值
  formData.value.detailVOList[props.$index].sortingBeforeWeight =
    calcWeight(props);
}

/**
 *  分拣状态
 * @param val
 */
function filterSortingStatus(val: any) {
  if (!isEmpty(val)) {
    return (
      sortingStatusOption.value.find((item: any) => item.value === val)
        ?.label || ""
    );
  } else {
    return "-";
  }
}

/**
 * 计算数量
 * @param scope
 * @returns {*}
 */
function calcAmount(scope: any) {
  let sortingResultList = scope.row.sortingResultList;
  if (!sortingResultList) {
    return;
  }
  let calc = sortingResultList
    ?.reduce((total: any, item: any) => {
      return Number(total) + Number(item.sortingAfterQty || 0);
    }, 0)
    .toFixed(3);

  return calc;
}

/**
 * 计算重量
 * @param scope
 * @returns {*}
 */
function calcWeight(scope: any) {
  let sortingResultList = scope.row.sortingResultList;
  if (!sortingResultList) {
    return;
  }
  let calc = sortingResultList
    ?.reduce((total: any, item: any) => {
      return Number(total) + Number(item.sortingAfterWeight || 0);
    }, 0)
    .toFixed(3);

  return calc;
}

/**
 * 选择商品
 */
function handleAddProduct() {
  selectProductDialog.value.visible = true;
  selectProductDialog.value.title = `${t("pickOrder.button.selectProduct")}`;
  selectProductRef.value.handleResetQuery();
}

/**
 * 选择商品(确认)
 */

function handleSubmitSelectProduct(multipleSelection: any) {
  // 将 multipleSelection 中的商品数据进行字段映射处理
  multipleSelection = multipleSelection.map((item: any) => {
    return {
      ...item,
      productUnit: item.productUnitName,
    };
  });


  // 获取当前操作项的 detailVOList（子项列表）
  let detailVOList = formData.value?.detailVOList;
  // 过滤 multipleSelection，只保留那些在当前 detailVOList 中不存在（根据 productCode和warehouseAreaCode 判断）的商品

    let multiple = multipleSelection.filter(
      (bItem: any) =>
        !detailVOList.some(
          (aItem: any) => (aItem.productCode === bItem.productCode && aItem.warehouseAreaCode === bItem.warehouseAreaCode)
        )
    );
  // 将新选中的商品插入到 detailVOList 的最前面（保持去重后的结果）
  formData.value.detailVOList = [...multiple, ...detailVOList];

  selectProductDialog.value.visible = false;
}

/**
 * 添加商品
 */
function handleAddGoods(scope: any) {
  currentOperationIndex.value = scope.$index;
  addProductDialog.value.visible = true;
  addProductDialog.value.title = `${t("pickOrder.button.addGoods")}`;
  addProductRef.value.handleResetQuery();
}

/**
 * 删除商品
 */
function handleDeleteGoods(scope: any) {
  ElMessageBox.confirm(
    t("pickOrder.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      formData.value?.detailVOList?.splice(scope.$index, 1);
    })
    .catch(() => {});
}

/**
 * 添加商品(确认)
 */
function handleSubmitProduct(multipleSelection: any) {
  // 将 multipleSelection 中的商品数据进行字段映射处理
  multipleSelection = multipleSelection.map((item: any) => {
    return {
      ...item,
      productUnit: item.productUnitName,
      productWeight: item.weight,
    };
  });
  // 获取当前操作项的 sortingResultList（子项列表）
  let sortingResultList =
    formData.value?.detailVOList[currentOperationIndex.value]
      ?.sortingResultList || [];
  // 过滤 multipleSelection，只保留那些在当前 sortingResultList 中不存在（根据 productCode 判断）的商品
  let multiple = multipleSelection.filter(
    (bItem: any) =>
      !sortingResultList.some(
        (aItem: any) => aItem.productCode === bItem.productCode
      )
  );
  // 将新选中的商品插入到 sortingResultList 的最前面（保持去重后的结果）
  formData.value.detailVOList[currentOperationIndex.value].sortingResultList = [
    ...multiple,
    ...sortingResultList,
  ];
  // 更新列表中的 index 字段：对非 isChildren 的项递增赋值，isChildren 为 true 的设为 null
  updateIndexForList(
    formData.value.detailVOList[currentOperationIndex.value].sortingResultList
  );
  addProductDialog.value.visible = false;
}

/**
 * 对比两个对象是否相等
 * @param obj1
 * @param obj2
 */
function deepCompare(obj1: any, obj2: any) {
  return JSON.stringify(obj1) === JSON.stringify(obj2);
}

/**
 * 子表格样式
 */
function cellStyleChildren({ row, column, rowIndex, columnIndex }: any) {
  if ([0, 1, 2, 3, 4, 5].includes(columnIndex) && row.isChildren) {
    return {
      visibility: "hidden",
      border: "none",
    };
  }
  return {
    border: "none",
    borderTop: "1px solid #E5E9F2",
  };
}

/**
 * 获取库区列表
 */
function getWarehouseAreaList() {
  let params: any = {
    status: 1,
  };

  CommonAPI.getOutWarehouseAreaList(params)
    .then((data) => {
      warehouseAreaOption.value = data || [];
    })
    .catch(() => {})
    .finally(() => {});
}

/**
 * 转换单位
 * @param originalValue //原始值
 * @param productCode  // 商品编码
 * @param convertUnitTypeEnum //转换单位类型,FIRST_TO_SECOND:一级转二级;SECOND_TO_FIRST：二级转一级,
 * @returns
 */
async function convertProductUnit(
  originalValue: any,
  productCode: string,
  convertUnitTypeEnum = "FIRST_TO_SECOND"
) {
  if (!originalValue || !productCode) {
    return;
  }

  let reg=/^([1-9]\d{0,7}(\.\d{1,3})?|0\.(?!0+$)\d{1,3})$/
    if(!reg.test(originalValue)){
      return;
    }

  let params: any = {
    convertUnitTypeEnum: convertUnitTypeEnum,
    originalValue: originalValue,
    productCode: productCode,
  };
  let res = await CommonAPI.convertProductUnit(params);
  return res.convertedValue;
}

/**
 * 分拣类型
 * @param val
 */
function filterSortingType(val: any) {
  if (!isEmpty(val)) {
    return (
      sortingTypeOption.value.find((item: any) => item.value === val)?.label ||
      ""
    );
  }
}

/**
 * 输入使用量
 * @param val
 * @param scope
 */
async function onInputSortingBeforeQty(val: any, scope: any) {
  const obj = formData.value.detailVOList[scope.$index];
  console.log(obj);

  //若商品的【一级单位增减】为1【是】输入数量时，转换量自动转换
  //若商品的【一级单位增减】为0【否】输入数量时，转换量内无值时自动转换，有值时不转换
  if (obj.isDiscreteUnit === 0 && obj.sortingBeforeWeight) {
    return;
  }

  //获取使用转换量
  obj.sortingBeforeWeight = await convertProductUnit(
    val,
    scope.row.productCode
  );

    validateField(`detailVOList.${scope.$index}.sortingBeforeWeight`)
}

/**
 * 输入使用转换量
 * @param val
 * @param scope
 */
async function onInputSortingBeforeWeight(val: any, scope: any) {
  const obj = formData.value.detailVOList[scope.$index];
  //若商品的【一级单位增减】为1【是】输入转换量时，数量不管，用户手动填写
  //若商品的【一级单位增减】为0【否】输入转换量时，数量内无值时自动转换，，有值时不转换

  if (
    obj.isDiscreteUnit === 1 ||
    (obj.isDiscreteUnit === 0 && obj.sortingBeforeQty)
  ) {
    return;
  }

  //获取使用量
  obj.sortingBeforeQty = await convertProductUnit(
    val,
    scope.row.productCode,
    "SECOND_TO_FIRST"
  );
    validateField(`detailVOList.${scope.$index}.sortingBeforeQty`)
}

/**
 * 校验指定字段
 * @param field
 */
function validateField(field:string) {
    formRef.value.validateField(field, (valid: boolean) => {
        if (!valid) {
            console.log('字段验证失败');
        } else {
            console.log('字段验证成功');
        }
    });
};

onMounted(() => {
  submitState.value = false;
  if (pageType.value !== "add") {
    queryDetail();
  }
  getWarehouseAreaList();
});

onBeforeRouteLeave((to, from, next) => {
  if (
    pageType.value === "edit" &&
    !submitState.value &&
    !deepCompare(formData.value, originalData.value)
  ) {
    setTimeout(() => {
      ElMessageBox.confirm(
        t("pickOrder.message.saveDataTips"),
        t("common.tipTitle"),
        {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          tagsViewStore.delView(route);
          next();
        })
        .catch(() => {
          next(false);
        });
    }, 200);
  } else {
    tagsViewStore.delView(route);
    next();
  }
});
</script>

<style lang="scss" scoped>
.container-wrapper {
  background: #ffffff;
  border-radius: 4px;

  .page-content {
    padding-bottom: 10px;

    :deep(.el-form-item__label) {
      font-weight: 400 !important;
      font-size: 14px !important;
      color: #90979e !important;
    }
  }

  .title-lable {
    position: relative;

    .title-action {
      position: absolute;
      right: 0;
      top: 26px;
    }
  }

  .table-container {
    padding: 0 20px;
    margin-bottom: 10px;

    .table-header {
      display: flex;
      background: #f4f6fa;
      border: 1px solid #e5e9f2;
      position: relative;

      font-weight: 600;
      font-size: 14px;
      color: #151719;
      padding: 15px;
      margin: 0 -20px 0 -20px;
      align-items: center;

      .sort {
        padding: 0 6px;
        background: #e5e7f3;
        border-radius: 4px;
        font-size: 14px;
        color: #151719;
        margin-right: 38px;
      }

      .column {
        padding-right: 16px;

        .column-label {
        }

        .column-form-item {
          display: inline-block;
          margin: 0;
            :deep(.el-form-item__content){

                position: relative;
                .el-form-item__error{
                    position: absolute;
                    left: 0;
                    top: 30px;
                    z-index: 999;
                    white-space: nowrap;
                }
            }
        }
      }

      .action {
        position: absolute;
        right: 10px;
        top: 10px;
        color: #762adb;
        cursor: pointer;
        background-color: #f4f6fa;
        padding: 10px 20px;
      }
    }

    .table-summaries {
      display: flex;
      background: #f4f6fa;
      border: 1px solid #e5e9f2;
      position: relative;
      font-weight: 600;
      font-size: 14px;
      color: #151719;
      padding: 15px;

      .sort {
        margin-right: 38px;
      }

      .column {
        padding-right: 50px;
      }
    }

    :deep(.table-row) {
      padding: 0 !important;
      margin: 0 !important;

      > td {
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
      }
    }

    :deep(.el-table__expand-icon .el-icon) {
      display: none;
    }

    :deep(.el-table__header) {
      margin-bottom: 16px;
      border: 1px solid #e5e9f2;
    }

    .table-children {
      margin-top: -20px;
      border: 1px solid #e5e9f2;
    }

    :deep(.label-required .cell) {
      &:before {
        content: "*";
        color: red;
        margin-right: 5px;
      }
    }

    :deep(.summaries) {
      font-weight: bold;
    }
  }
}
</style>
