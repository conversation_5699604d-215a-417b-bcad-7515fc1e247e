<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          {{ $t("warehouseEntryNotice.title.addReceivingOrderTitle") }}
        </div>
      </div>
      <div class="page-content">
        <el-form
          :model="form"
          :rules="rules"
          ref="formRef"
          label-width="112px"
          label-position="right"
        >
          <div class="basicInformation item_content">
            <div class="title">
              {{ $t("warehouseEntryNotice.label.documentSelection") }}
            </div>
            <el-row>
              <el-col :span="12">
                <!-- 入库通知单 来源单号搜索-->
                <el-form-item label-width="0">
                  <!-- 初期用queryType区分来源单号2和入库通知单1 提交删除 -->
                  <el-select
                    v-model="form.queryType"
                    @change="selectChangeQueryType"
                    :placeholder="$t('common.placeholder.selectTips')"
                    class="!w-[130px]"
                  >
                    <el-option
                      v-for="item in queryTypeList"
                      :key="item.key"
                      :label="item.value"
                      :value="item.key"
                    />
                  </el-select>
                  <!-- 初期用queryCode表示来源单号和入库通知单 提交删除 -->
                  <el-select
                    v-model="form.queryCode"
                    filterable
                    remote
                    reserve-keyword
                    :loading="searchLoading"
                    :remote-method="remoteMethod"
                    :placeholder="$t('common.placeholder.inputTips')"
                    class="!w-[240px]"
                    @change="selectChange($event)"
                    value-key="id"
                    clearable
                  >
                    <el-option
                      v-for="item in warehouseReceiptNoticeList"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="grad-row">
              <el-row>
                <el-col :span="6">
                  <!-- 入库通知单号 -->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
                  >
                    {{ form.receiptNoticeCode ? form.receiptNoticeCode : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 来源单号 -->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
                  >
                    {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 计划交货时间 -->
                  <el-form-item
                    :label="
                      $t('warehouseEntryNotice.label.plannedDeliveryTime')
                    "
                  >
                    <span v-if="form.plannedDeliveryTime">
                      {{
                        parseTime(
                          form.plannedDeliveryTime,
                          "{y}-{m}-{d} {h}:{i}:{s}"
                        )
                      }}
                    </span>
                    <span v-else>-</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!--  入库类型:1:采购入库（供应商）、2:退货入库(客户)-->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.receiptType')"
                  >
                    <span v-if="form.receiptType == 1">
                      {{ $t("warehouseEntryNotice.label.purchaseInventory") }}
                    </span>
                    <span v-else-if="form.receiptType == 2">
                      {{ $t("warehouseEntryNotice.label.returnStorage") }}
                    </span>
                    <span v-else>-</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6">
                  <!-- 采购/销售员 -->
                  <el-form-item
                    :label="
                      $t('warehouseEntryNotice.label.purchaseSalesPerson')
                    "
                  >
                    {{
                      form.purchaseSalesPerson ? form.purchaseSalesPerson : "-"
                    }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 客户 -->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.customerName')"
                  >
                    {{ form.customerName ? form.customerName : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 供应商 -->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.supplierName')"
                  >
                    {{ form.supplierName ? form.supplierName : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 地址 -->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.address')"
                  >
                    <!-- {{ form.countryName }}
                    {{ form.provinceName }}
                    {{ form.cityName }}
                    {{ form.districtName }} -->
                    {{ form.fullAddress ? form.fullAddress : "-" }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6">
                  <!-- 联系人 -->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.contactPerson')"
                  >
                    {{ form.contactPerson ? form.contactPerson : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 联系电话 -->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.mobile')"
                  >
                    <span v-if="form.mobile">{{ form.countryAreaCode }}</span>
                    {{ form.mobile ? form.mobile : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <!-- 备注 提交的时候不传-->
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.remark')"
                  >
                    {{ form.remark ? form.remark : "-" }}
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              {{ $t("warehouseEntryNotice.label.goodsReceiving") }}
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 备注 提交的时候goodsRemark覆盖remark后删除-->
                <el-form-item
                  label-width="50"
                  :label="$t('warehouseEntryNotice.label.remark')"
                  prop="goodsRemark"
                >
                  <el-input
                    v-model="form.goodsRemark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0" prop="warehouseIds">
                  <el-table
                    :data="form.productList"
                    v-loading="formLoading"
                    stripe
                    highlight-current-row
                  >
                    <el-table-column
                      type="index"
                      :label="$t('common.sort')"
                      width="60"
                      align="center"
                    />
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.goodsInfor')"
                      min-width="150"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <div class="product-code">
                          {{ $t("warehouseEntryNotice.label.productCode") }}：
                          {{ scope.row.productCode }}
                        </div>
                        <div class="product-name">
                          {{ scope.row.productName }}
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 规格 -->
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.productSpecs')"
                      prop="productSpecs"
                      show-overflow-tooltip
                    />
                    <!-- 计划数量 -->
                    <el-table-column
                      :label="$t('warehouseEntryNotice.label.plannedQuantity')"
                      prop="productExpectQty"
                    />
                    <!-- 收运数量 -->
                    <el-table-column
                      prop="productActualQty"
                      min-width="138"
                      :label="
                        '*' + $t('warehouseEntryNotice.label.receivedQty')
                      "
                    >
                      <template #default="scope">
                        <el-form-item
                          class="mt15px"
                          label-width="0px"
                          :prop="
                            'productList.' + scope.$index + '.productActualQty'
                          "
                          :rules="[
                            {
                              required: true,
                              message: t(
                                'warehouseEntryNotice.rules.productActualQty'
                              ),
                              trigger: 'blur',
                            },
                            {
                              pattern:
                                /(^[1-9](\d{1,7})?(\.\d{1,3})?$)|(^0\.[1-9]\d{0,2}$)|(^0\.0[1-9]\d{0,1}$)|(^0\.00[1-9]$)/,
                              message: t(
                                'warehouseEntryNotice.rules.productActualQtyFomart'
                              ),
                              trigger: 'blur',
                            },
                          ]"
                        >
                          <el-input
                            v-model="scope.row.productActualQty"
                            :placeholder="$t('common.placeholder.inputTips')"
                            clearable
                          >
                            <template #append>
                              {{ scope.row.productUnitName }}
                            </template>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')">
                      <template #default="scope">
                        <div v-if="scope.row.imagesNum">
                          <el-badge
                            :value="scope.row.imagesNum"
                            :offset="[10, 8]"
                            class="item"
                            type="primary"
                          >
                            <!-- 上传 -->
                            <el-button
                              type="primary"
                              link
                              @click="handleUpload(scope.row, scope.$index)"
                            >
                              {{ $t("warehouseEntryNotice.button.upload") }}
                            </el-button>
                          </el-badge>
                        </div>
                        <div v-else>
                          <!-- 上传 -->
                          <el-button
                            type="primary"
                            link
                            @click="handleUpload(scope.row, scope.$index)"
                          >
                            {{ $t("warehouseEntryNotice.button.upload") }}
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("warehouseEntryNotice.button.cancel") }}
          </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            {{ $t("warehouseEntryNotice.button.confirmBtn") }}
          </el-button>
        </div>
      </div>
    </div>
    <!--    上传-->
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      @on-submit="onSubmitUpload"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "AddReceivingOrders",
  inheritAttrs: false,
});
import shippingReceiptAPI, {
  addFormData,
} from "@/modules/wms/api/shippingReceipt";
import warehouseEntryNoticeAPI from "@/modules/wms/api/warehouseEntryNotice";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import { parseTime } from "@/core/utils/index.js";
import UploadDialog from "./components/uploadDialog.vue";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const queryTypeList = ref([
  {
    key: 1,
    value: t("warehouseEntryNotice.label.receiptNoticeCode"),
  },
  {
    key: 2,
    value: t("warehouseEntryNotice.label.sourceOrderCode"),
  },
]);
const formRef = ref(ElForm);
const loading = ref(false);
const formLoading = ref(false);
const searchLoading = ref(false);
const warehouseReceiptNoticeList = ref([]); //远程搜索
const tableIndex = ref();

// 角色表单
const form = reactive<addFormData>({
  productList: [],
  queryType: 1,
  queryCode: null,
});

const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();

const rules = reactive({});

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/storeManagement/shippingReceipt",
  });
};

const remoteMethod = (query: string) => {
  if (query) {
    searchLoading.value = true;
    let params: any = {};
    if (form.queryType == 1) {
      //1是入库通知单  2是来源单号
      params.receiptNoticeCode = query;
    } else {
      params.sourceOrderCode = query;
    }
    warehouseEntryNoticeAPI
      .queryList(params)
      .then((data: any) => {
        searchLoading.value = false;
        warehouseReceiptNoticeList.value = data.map((item: any) => ({
          label:
            form.queryType == 1 ? item.receiptNoticeCode : item.sourceOrderCode,
          value: item,
          id: item.id,
        }));
      })
      .finally(() => {
        searchLoading.value = false;
      });
  } else {
    warehouseReceiptNoticeList.value = [];
  }
};

function selectChangeQueryType() {
  resetData();
  form.queryCode = null;
}

function resetData() {
  formRef.value.resetFields();
  formRef.value.clearValidate();
  form.productList = [];
  form.customerName = "";
  form.supplierName = "";
  form.address = "";
  form.contactPerson = "";
  form.countryAreaCode = "";
  form.mobile = "";
  form.remark = "";
  form.purchaseSalesPerson = "";
  form.receiptType = undefined;
  form.plannedDeliveryTime = "";
  form.receiptNoticeCode = "";
  form.sourceOrderCode = "";
  form.receiptNoticeId = "";
  form.fullAddress = "";
}

function selectChange(event: any) {
  if (event) {
    form.receiptNoticeCode = event.receiptNoticeCode
      ? event.receiptNoticeCode
      : "";
    form.sourceOrderCode = event.sourceOrderCode ? event.sourceOrderCode : "";
    form.plannedDeliveryTime = event.plannedDeliveryTime
      ? event.plannedDeliveryTime
      : "";
    form.receiptType = event.receiptType ? event.receiptType : "";
    form.purchaseSalesPerson = event.purchaseSalesPerson
      ? event.purchaseSalesPerson
      : "";
    form.customerName = event.customerName ? event.customerName : "";
    form.supplierName = event.supplierName ? event.supplierName : "";
    form.address = event.address ? event.address : "";
    form.fullAddress = event.fullAddress ? event.fullAddress : "";
    form.contactPerson = event.contactPerson ? event.contactPerson : "";
    form.countryAreaCode = event.countryAreaCode ? event.countryAreaCode : "";
    form.mobile = event.mobile ? event.mobile : "";
    form.productList = event.productList ? event.productList : "";
    form.remark = event.remark ? event.remark : "";
    form.receiptNoticeId = event.id ? event.id : "";
    form.receiptType = event.receiptType ? event.receiptType : "";
  } else {
    resetData();
  }
}
// 上传
function handleUpload(row: any, index: any) {
  tableIndex.value = index;
  uploadDialog.visible = true;
  uploadDialogRef.value.setEditType("add");
  if (row.imagesUrls) {
    uploadDialogRef.value.setFormData(JSON.parse(row.imagesUrls));
  }
}

//上传提交
function onSubmitUpload(data: any) {
  if (data) {
    form.productList[tableIndex.value].imagesUrls = JSON.stringify(data);
    form.productList[tableIndex.value].imagesNum = data.length;
  }
}
// 提交
function handleSubmit() {
  if (form.productList && form.productList.length == 0) {
    return ElMessage.error(
      t("warehouseEntryNotice.message.addOrEditGoodsTips")
    );
  }
  // 最少有一行收运数量大于等于0
  const sum = computed(() => {
    return form.productList.reduce((total: number, obj: any) => {
      return total + (obj.productActualQty || 0); // 如果元素不存在，默认为 0
    }, 0);
  });
  if (sum.value <= 0) {
    return ElMessage.error(
      t("warehouseEntryNotice.message.productActualQtyTips")
    );
  }

  formRef.value.validate((valid: any) => {
    if (!valid) return;

    ElMessageBox.confirm(
      `${t("warehouseEntryNotice.message.shippingReceiptTips")}`,
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(
      () => {
        loading.value = true;
        let params = {
          ...form,
        };
        params.remark = params.goodsRemark;
        // delete params.areaInfo;
        delete params.queryType;
        delete params.queryCode;
        delete params.goodsRemark;

        shippingReceiptAPI
          .addReceivingOrders(params)
          .then((data: any) => {
            ElMessage.success(
              t("warehouseEntryNotice.message.shippingReceiptSucess")
            );
            loading.value = false;
            handleCancel();
          })
          .finally(() => {
            loading.value = false;
          });
      },
      () => {
        ElMessage.info(t("warehouseEntryNotice.message.cancelTip"));
      }
    );
  });
}

onMounted(() => {});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 20px 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 30px 24px 30px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 22px 30px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .mt15px {
    margin-bottom: 15px !important;
  }
}
</style>
<style scoped>
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
</style>
