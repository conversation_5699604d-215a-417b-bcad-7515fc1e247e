<template>
  <div class="app-container">
    <div class="receivingOrderDetail" v-loading="loading">
      <div class="page-title">
        <div class="purchase-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon><Back /></el-icon>
          </div>
          <div>
            {{ t("warehouseEntryNotice.label.receivingOrderCode") }}：{{
              route.query.receivingOrderCode
            }}
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" label-width="145px" label-position="right">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntryNotice.label.basicInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.sourceOrderCode')"
                >
                  {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receiptNoticeCode')"
                >
                  {{ form.receiptNoticeCode ? form.receiptNoticeCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.plannedDeliveryTime')"
                >
                  <span v-if="form.plannedDeliveryTime">
                    {{
                      parseTime(
                        form.plannedDeliveryTime,
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receiver')"
                >
                  {{ form.receiver ? form.receiver : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receivingTime')"
                >
                  <span v-if="form.receivingTime">
                    {{
                      parseTime(form.receivingTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <span v-if="form.receiptType == 2">
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.customerName')"
                  >
                    {{ form.customerName ? form.customerName : "-" }}
                  </el-form-item>
                </span>
                <span v-else>
                  <el-form-item
                    :label="$t('warehouseEntryNotice.label.supplierName')"
                  >
                    {{ form.supplierName ? form.supplierName : "-" }}
                  </el-form-item>
                </span>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.purchaseSalesPerson')"
                >
                  {{
                    form.purchaseSalesPerson ? form.purchaseSalesPerson : "-"
                  }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntryNotice.label.status')">
                  <span v-if="form.status == 0">
                    {{ $t("warehouseEntryNotice.label.initial") }}
                  </span>
                  <span v-else-if="form.status == 1">
                    {{ $t("warehouseEntryNotice.label.partialStorage") }}
                  </span>
                  <span v-else-if="form.status == 2">
                    {{ $t("warehouseEntryNotice.label.allStorage") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.isSorting')"
                >
                  <span v-if="form.isSorting == 0">{{$t('warehouseEntryNotice.label.not')}}</span>
                  <span v-else-if="form.isSorting == 1">{{$t('warehouseEntryNotice.label.yes')}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.isReceived')"
                >
                  <span v-if="form.useStatus == 0">{{$t('warehouseEntryNotice.label.not')}}</span>
                  <span v-else-if="form.useStatus == 1">{{$t('warehouseEntryNotice.label.yes')}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntryNotice.label.useUserName')">
                  {{form.useUserName?form.useUserName:'-'}}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.useTime')"
                >
                  <span v-if="form.useTime">
                    {{
                      parseTime(form.useTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.receivingStatus')"
                >
                  <span v-if="form.receivedStatus == 0">{{ $t("warehouseEntryNotice.label.initial") }}</span>
                  <span v-else-if="form.receivedStatus == 1">{{ $t("warehouseEntryNotice.label.receivingShipped") }}</span>
                  <span v-else-if="form.receivedStatus == 2">{{ $t("warehouseEntryNotice.label.receivedShipped") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.productTotalPlanQty')"
                >
                  {{ form.expectedQty ? form.expectedQty : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('warehouseEntryNotice.label.productTotalPlanWeight')"
                >
                  {{ form.expectedWeight ? form.expectedWeight : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntryNotice.label.productTotalReceiveQty')">
                  {{ form.receivedQty ? form.receivedQty : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('warehouseEntryNotice.label.productTotalReceiveWeight')">
                  {{ form.receivedWeight ? form.receivedWeight : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-form-item :label="$t('warehouseEntryNotice.label.remark')">
                  {{ form.remark ? form.remark : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("warehouseEntryNotice.label.goodsDetails") }}
            </div>
          </div>
          <div>
            <el-table
              ref="tableSumRef1"
              :data="form.productList"
              highlight-current-row
              stripe
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
              />
              <el-table-column
                :label="$t('warehouseEntryNotice.label.goodsInfor')"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="product-code">
                    {{ $t("warehouseEntryNotice.label.productCode") }}：
                    {{ scope.row.productCode }}
                  </div>
                  <div class="product-name">
                    {{ scope.row.productName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('warehouseEntryNotice.label.productSpecs')"
                prop="productSpecs"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('warehouseEntryNotice.label.unitName')"
                prop="productUnitName"
                show-overflow-tooltip
              />
              <!-- 计划数量 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.plannedQuantity')"
                prop="productExpectQty"
                show-overflow-tooltip
              />
              <!-- 收运数量 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.receivedQty')"
                prop="productActualQty"
                show-overflow-tooltip
              />
              <!-- 入库数量 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.inventoryQuantity')"
                prop="productInventoryQty"
                show-overflow-tooltip
              />
              <!-- 计划重量 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.productPlanWeightCopy')"
                prop="expectedWeight"
                show-overflow-tooltip
              />
              <!-- 收运重量 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.actualWeightCopy')"
                prop="receivedWeight"
                show-overflow-tooltip
              />
              <!-- 入库重量 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.storageWeight')"
                prop="productInventoryWeight"
                show-overflow-tooltip
              />
              <!-- 是否全部收运 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.isAllReceived')"
                prop="weight"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span v-if="scope.row.isAllReceived == 1">{{$t('warehouseEntryNotice.label.yes')}}</span>
                  <span v-if="scope.row.isAllReceived == 0">{{$t('warehouseEntryNotice.label.not')}}</span>
                </template>
              </el-table-column>
              <!-- 收运状态 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.receivingStatus')"
                prop="weight"
                min-width="120"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="purchase">
                    <!-- 状态:0:初始、1:收运中 2:已收运 -->
                    <div
                      v-if="scope.row.receivedStatus == 0"
                      type="success"
                      class="purchase-status purchase-status-color1"
                    >
                      {{ t("warehouseEntryNotice.label.initial") }}
                    </div>
                    <div
                      v-else-if="scope.row.receivedStatus == 1"
                      type="info"
                      class="purchase-status purchase-status-color2"
                    >
                      {{ t("warehouseEntryNotice.label.receivingShipped") }}
                    </div>
                    <div
                      v-else-if="scope.row.receivedStatus == 2"
                      type="info"
                      class="purchase-status purchase-status-color3"
                    >
                      {{ t("warehouseEntryNotice.label.receivedShipped") }}
                    </div>
                    <div v-else>-</div>
                  </div>
                </template>
              </el-table-column>
              <!-- 入库状态 -->
              <el-table-column
                :label="$t('warehouseEntryNotice.label.status')"
                prop="status"
                min-width="120"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="purchase">
                    <!-- 状态:0:初始、1:部分入库 2:全部入库 -->
                    <div
                      v-if="scope.row.status == 0"
                      type="success"
                      class="purchase-status purchase-status-color1"
                    >
                      {{ t("warehouseEntryNotice.label.initial") }}
                    </div>
                    <div
                      v-else-if="scope.row.status == 1"
                      type="info"
                      class="purchase-status purchase-status-color2"
                    >
                      {{ t("warehouseEntryNotice.label.partialStorage") }}
                    </div>
                    <div
                      v-else-if="scope.row.status == 2"
                      type="info"
                      class="purchase-status purchase-status-color3"
                    >
                      {{ t("warehouseEntryNotice.label.allStorage") }}
                    </div>
                    <div v-else>-</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('warehouseEntryNotice.label.receiveDetail')" width="120">
                <template #default="scope">
                  <el-button
                    type="primary"
                    link
                    @click="openDetail(scope.row)"
                  >
                    {{$t('warehouseEntryNotice.button.openDetail')}}
                  </el-button>
                </template>
              </el-table-column>
              <!-- 操作 -->
              <el-table-column
                fixed="right"
                :label="$t('common.handle')"
                width="120"
              >
                <template #default="scope">
                  <div v-if="scope.row.imagesUrls">
                    <el-badge
                      :value="JSON.parse(scope.row.imagesUrls).length"
                      :offset="[10, 8]"
                      class="item"
                      type="primary"
                    >
                      <!-- 上传 -->
                      <el-button
                        type="primary"
                        link
                        @click="openAttachment(scope.row)"
                      >
                        {{ $t("warehouseEntryNotice.label.attachment") }}
                      </el-button>
                    </el-badge>
                  </div>
                  <!-- 附件  -->
                  <!-- <el-button
                    type="primary"
                    link
                    @click="openAttachment(scope.row)"
                    v-if="scope.row.imagesUrls"
                  >
                    {{ $t("warehouseEntryNotice.label.attachment") }}
                  </el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button
          v-hasPerm="['pms:purchase:purchaseOrder:close']"
          @click="handleClose"
        >
          {{ $t("common.reback") }}
        </el-button>
      </div>
    </div>
    <!--    上传-->
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      :showUploadBtn="false"
    />
    <!-- 收运明细 -->
    <el-dialog width="750" style="max-height: 87%;overflow: hidden;" v-model="detailDialog" :title="$t('warehouseEntryNotice.title.receivedProductDetailTitle') + detailTitle" :close-on-click-modal="false" @close="close">
      <el-table v-loading="detailLoading" :data="detailDataList" highlight-current-row stripe :max-height="425" style="overflow:auto;">
        <template #empty>
          <Empty />
        </template>
        <el-table-column type="index" :label="$t('common.sort')" width="60" />
        <el-table-column :label="$t('warehouseEntryNotice.label.boxCode')" prop="boxCode" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('warehouseEntryNotice.label.quantity')" align="right" prop="quantity" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('warehouseEntryNotice.label.weight')" align="right" prop="weight" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('warehouseEntryNotice.label.scanTime')" prop="scanTime" width="180" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.scanTime? parseTime(scope.row.scanTime,"{y}-{m}-{d} {h}:{i}:{s}") : '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseDetailHandler">{{ $t("warehouseEntryNotice.button.closeBtn") }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ReceivingOrderDetail",
  inheritAttrs: false,
});
import shippingReceiptAPI, {
  addFormData,queryDetailDto
} from "@/modules/wms/api/shippingReceipt";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import { parseTime } from "@/core/utils/index.js";
import UploadDialog from "./components/uploadDialog.vue";

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const loading = ref(false);
const tableSumRef1 = ref();
const form = reactive<addFormData>({
  productList: [],
});
const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();
const queryDetailParams = reactive<queryDetailDto>({});
const detailDialog = ref(false);
const detailTitle = ref('');
const detailLoading = ref(false);
const detailDataList = ref([]);

async function handleClose() {
  await tagsViewStore.delView(route);
  router.push({
    path: "/wms/storeManagement/shippingReceipt",
  });
}

/** 查询采购单详情 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: route.query.id,
  };
  shippingReceiptAPI
    .queryDetail(params)
    .then((data: any) => {
      Object.assign(form, data);
    })
    .finally(() => {
      loading.value = false;
    });
}

function openAttachment(row: any) {
  uploadDialog.visible = true;
  uploadDialogRef.value.showUploadBtn = false;
  uploadDialogRef.value.setEditType("detail");
  uploadDialogRef.value.setFormData(JSON.parse(row.imagesUrls));
}

function openDetail(row){
  queryDetailParams.businessId = row.id
  handleQueryDetail()
  detailTitle.value = '-' + row.productCode + ' | ' + row.productName
  detailDialog.value = true
}
function handleQueryDetail(){
  detailLoading.value = true
  let params = {
    ...queryDetailParams
  }
  shippingReceiptAPI.queryScanPageInfo(params).then((res)=>{
    detailDataList.value = res.receivingOrdersScanVOList
  }).catch(()=>{
    detailLoading.value = false
  }).finally(()=>{
    detailLoading.value = false
  })
}
function onCloseDetailHandler(){
  detailDialog.value = false
  detailLoading.value = false
  detailTitle.value = ''
  detailDataList.value = []
}

onMounted(() => {
  queryDetail();
});
</script>
<style scoped lang="scss">
.receivingOrderDetail {
  background: #ffffff;
  border-radius: 4px;
}
</style>
<style lang="scss">
.receivingOrderDetail {
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .cursor-pointer {
    cursor: pointer;
  }
}
</style>
