<template>
  <div class="app-container">
    <div class="page-title">
      <el-page-header @back="handleBack">
        <template #content>
          <span class="cursor-pointer mr8px">{{ pageTitle }}</span>
        </template>
      </el-page-header>
    </div>

    <el-form ref="formRef" :model="formData" label-width="125px" class="page-content">
      <!-- 基本信息 -->
      <div class="title-label">
        <div class="title-line"></div>
        <div class="title-content">基本信息</div>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="入库通知单号" prop="receiptNoticeCode">
            {{ formData.receiptNoticeCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库类型" prop="receiptType">
            {{ formData.receiptType }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="主题描述" prop="themeDesc">
            {{ formData.themeDesc }}
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="业务员" prop="salesmanId">
            <el-select v-model="formData.salesmanId" placeholder="请选择" class="w-full" @change="handleSalesmanChange">
              <el-option v-for="item in businessPersonList" :key="item.userId" :label="item.nickName"
                :value="item.userId" />
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :span="6">
          <el-form-item label="来源单号" prop="sourceOrderCode">
            {{ formData.sourceOrderCode }}
          </el-form-item>
        </el-col>
        <!-- 供应商 supplierName，供应商地址，供应商联系人，供应商联系电话 -->
        <el-col :span="6">
          <el-form-item label="供应商" prop="supplierName">
            {{ formData.supplierName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商地址" prop="supplierAddress">
            {{ formData.countryName + formData.provinceName + formData.cityName + formData.districtName +
              formData.address}}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商联系人" prop="contactPerson">
            {{ formData.contactPerson }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商联系电话" prop="mobile">
            {{ formData.mobile }}
          </el-form-item>
        </el-col>
        <!-- 客户，客户地址，客户联系人，客户联系电话 -->
        <el-col :span="6">
          <el-form-item label="客户" prop="customerName">
            {{ formData.customerName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户地址" prop="customerAddress">
            {{ formData.customerCountryName + formData.customerProvinceName + formData.customerCityName +
              formData.customerDistrictName + formData.customerAddress}}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户联系人" prop="contactPerson">
            {{ formData.customerContactPerson }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户联系电话" prop="customerMobile">
            {{ formData.customerMobile }}
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="计划交货时间" prop="plannedDeliveryTime">
            {{ parseTime(formData.plannedDeliveryTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </el-form-item>
        </el-col>
        <!-- 合同名称，合同编号，合同分类 -->
        <el-col :span="6">
          <el-form-item label="合同名称" prop="contractName">
            {{ formData.contractName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合同编号" prop="contractCode">
            {{ formData.contractCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合同分类" prop="contractType">
            {{ formData.contractType }}
          </el-form-item>
        </el-col>
        <!-- 业务员，原单据号，商品计划总量，商品计划总转换量 -->
        <el-col :span="6">
          <el-form-item label="业务员" prop="salesmanName">
            {{ formData.salesmanName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="原单据号" prop="originOrderCode">
            {{ formData.originOrderCode || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品计划总量" prop="expectedQty">
            {{ formData.expectedQty }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品计划总转换量" prop="expectedWeight">
            {{ formData.expectedWeight }}
          </el-form-item>
        </el-col>
        <!-- 来源单据备注sourceOrderRemark，磅单编号，入库车号，入库备注 -->
        <el-col :span="6">
          <el-form-item label="来源单据备注" prop="sourceOrderRemark">
            {{ formData.sourceOrderRemark }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="磅单编号" prop="weighbridgeNo">
            <el-input v-model="formData.weighbridgeNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库车号" prop="vehicleNo">
            <el-input v-model="formData.vehicleNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入库备注" prop="remark">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 商品明细 -->
      <section class="flex align-center justify-between">
        <div class="title-label">
          <div class="title-line"></div>
          <div class="title-content">商品明细</div>
        </div>
        <el-button type="primary" @click="handleAddProduct">
          添加
        </el-button>
      </section>
      <!-- 转换量:用户填写入库量后根据转换关系自动计算，可修改 -->
      <!-- 入库金额:用户填写单价/入库量/入库转换量后自动计算，用户可修改，计算公式:金额(2位小数，四舍五入)=单价(4位小数)*入库量或入库转换量 -->
      <!-- 入库单价:同一商品多个库区时入库单价相同，填写一次，其他默认 -->
      <!-- 入库库区:可选择已启用库区 -->
      <!-- 必填项:入库单价/入库量/入库库区/入库金额/入库转换量 -->
      <el-table :data="formData.productList" border style="width: 100%" class="mb-4" show-summary>
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" />
        <el-table-column label="商品信息" width="200" fixed="left">
          <template #default="scope">
            <div v-if="!scope.row.parentId">
              <div>商品编码：{{ scope.row.productCode }}</div>
              <div>{{ scope.row.productName }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品分类" width="120">
          <template #default="scope">
            <span v-if="!scope.row.parentId">{{ scope.row.fullCategoryName }}{{ scope.row.parentId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="规格" width="100">
          <template #default="scope">
            <span v-if="!scope.row.parentId">{{ scope.row.productSpecs }}</span>
          </template>
        </el-table-column>
        <el-table-column label="入库单价(RMB)" min-width="180" align="right">
          <template #default="scope">
            <el-input-number v-model="scope.row.unitPrice" :precision="4" :min="0" controls-position="right"
              @change="calculateAmount(scope.row)" :disabled="scope.row.parentId && scope.row.parentId !== '0'"/>
          </template>
        </el-table-column>
        <el-table-column label="入库量" min-width="180" align="right">
          <template #default="scope">
            <el-input v-model="scope.row.actualInQty" :precision="3" :min="0" controls-position="right"
              @change="calculateInWeight(scope.row)">
              <template #append>
                <div class="text-gray-500 text-xs">{{ scope.row.productUnitName }}</div>
              </template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="入库转换量" min-width="180" align="right">
          <template #default="scope">
            <el-input v-model="scope.row.actualInWeight" @change="calculateAmount(scope.row)" :precision="3" :min="0"
              controls-position="right">
              <template #append>
                <div class="text-gray-500 text-xs">{{ scope.row.conversionRelSecondUnitName }}</div>
              </template>
            </el-input>

          </template>
        </el-table-column>
        <el-table-column label="入库金额(RMB)" min-width="180" align="right">
          <template #default="scope">
            <el-input-number v-model="scope.row.amount" :precision="2" :min="0" controls-position="right" />
          </template>
        </el-table-column>
        <el-table-column label="入库库区" min-width="200">
          <template #default="scope">
            <el-select v-model="scope.row.warehouseAreaCode" placeholder="请选择">
              <el-option v-for="item in warehouseAreaList" :key="item.areaCode"
                :label="item.warehouseName + '|' + item.areaName" :value="item.areaCode" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="商品包装" min-width="180">
          <template #default="scope">
            <el-input v-model="scope.row.productPackaging" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column label="质检" width="80" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleQualityCheck(formData.productList, scope.row, scope.$index)">
              质检
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="handleAddRow(scope.$index, scope.row)">
              <el-icon>
                <Plus />
              </el-icon>
            </el-button>
            <el-button v-if="formData.productList && formData.productList.length > 1" type="danger" link size="small"
              @click="handleDeleteRow(scope.$index)">
              <el-icon>
                <Minus />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- <div class="text-right text-lg font-medium">
        <span class="mr-8">合计：{{ totalQuantity }}</span>
        <span class="mr-8">{{ totalAmount.toFixed(2) }}</span>
        <span>{{ totalConvertedQuantity }}</span>
      </div> -->
    </el-form>

    <!-- 底部按钮 -->
    <div class="fixed-footer">
      <el-button @click="handleCancel">取消</el-button>
      <!-- <el-button @click="handleSaveDraft" :loading="saving">保存草稿</el-button> -->
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        提交
      </el-button>
    </div>

    <!-- 商品选择弹窗 -->
    <AddProduct ref="addGoodsRef" v-model:visible="productDialog.visible" :title="productDialog.title"
      @onSubmit="onProductSubmit" />

    <!-- 质检商品弹窗 -->
    <QualityCheckDialog v-model:visible="qualityDialog.visible" :product-data="qualityDialog.productData"
      @confirm="onQualityConfirm" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Plus, Minus, Edit } from "@element-plus/icons-vue";
import QuickWarehousingAPI, { QuickWarehousingFormData } from "@/modules/wms/api/quickWarehousing";
import AddProduct from "./components/addProduct.vue";
import QualityCheckDialog from "./components/QualityCheckDialog.vue";
import SelAreaCascader from "@/modules/wms/components/SelAreaCascader.vue";
import CommonAPI from "@/modules/wms/api/common";
import { parseTime, changeDateRange } from "@/core/utils/index.js";
defineOptions({
  name: "QuickWarehousingAdd",
  inheritAttrs: false,
});
const addGoodsRef = ref();
const route = useRoute();
const router = useRouter();

const pageTitle = computed(() => {
  return "去入库";
});

const isEdit = computed<boolean>(() => {
  return route.query.type === "edit";
});

const formRef = ref();
const saving = ref(false);
const submitting = ref(false);

// 表单数据
const formData = reactive<QuickWarehousingFormData>({
  // 基本信息
  receiptNoticeCode: "", // 入库通知单号
  receiptType: 4, // 入库类型:1:采购入库、2:退货入库、3:调拨入库、4:直接入库、5:地头入库
  themeDesc: "", // 主题描述
  sourceOrderCode: "", // 来源单号
  // purchaseSalesPerson: "", // 采购/销售员
  plannedDeliveryTime: "", // 计划交货时间
  // status: 0, // 状态:0:草稿、1:初始 2：完结 3:取消

  // 供应商信息
  supplierCode: "", // 供应商编码
  supplierName: "", // 供应商名称

  // 客户信息
  // customerCode: "", // 客户编码
  // customerName: "", // 客户名称

  // 地址信息
  countryId: "", // 国家id
  countryName: "", // 国家名称
  countryAreaCode: "", // 国家区域代码
  provinceId: "", // 省份id
  provinceName: "", // 省名称
  cityId: "", // 城市id
  cityName: "", // 市名称
  districtId: "", // 区县id
  districtName: "", // 区县名称
  address: "", // 详细地址

  // 联系信息
  contactPerson: "", // 联系人
  mobile: "", // 手机号

  // 合同信息
  contractCode: "", // 合同编码
  contractName: "", // 合同名称
  contractType: undefined, // 合同类型

  // 业务信息
  salesmanId: "", // 业务员ID
  salesmanName: "", // 业务员姓名
  weighbridgeNo: "", // 榜单编号
  weighbridgeNoAttachment: "", // 榜单编号附件
  vehicleNo: "", // 入库车号
  remark: "", // 备注

  // 仓库信息
  /*  warehouseCode: "", // 仓库编码
   warehouseId: undefined, // 仓库id
   warehouseName: "", // 仓库名称 */

  // 金额信息
  // currency: "CNY", // 交易币种：CNY->人民币，USD->美元
  // totalAmount: 0, // 总入库金额
  // totalCostAmount: 0, // 总成本金额
  // expectedQty: 0, // 计划总数
  // expectedWeight: 0, // 计划总重量
  // actualTotalQuantity: 0, // 总入库量（实际总量）
  // actualTotalWeight: 0, // 总入库转换量（实际总数量）

  // 其他信息
  // source: 1, // 来源：1:手动创建 2:同步
  // sourceSystem: "", // 来源系统
  // productQty: 0, // 商品个数

  // 商品列表
  productList: [
    /*  {
       id: undefined,
       productCode: "", // 商品编码
       productName: "", // 商品名称
       productSpecs: "", // 规格
       productPackaging: "", // 商品包装
       
       // 分类信息
       firstCategoryId: undefined, // 一级分类id
       firstCategoryName: "", // 一级分类名称
       secondCategoryId: undefined, // 二级分类id
       secondCategoryName: "", // 二级分类名称
       thirdCategoryId: undefined, // 三级分类id
       thirdCategoryName: "", // 三级分类名称
       
       // 单位信息
       productUnitId: undefined, // 商品采购单位id
       productUnitName: "", // 商品采购单位名称
       conversionRelSecondUnitId: undefined, // 商品换算关系第二个值的单位id
       conversionRelSecondUnitName: "", // 商品换算关系第二个值的单位名称
       
       // 数量信息
       productExpectQty: 0, // 商品数量(期望)
       actualInQty: 0, // 本次实际入库数量
       inWarehouseQty: 0, // 已入库数量
       
       // 重量信息
       expectedWeight: 0, // 计划重量
       actualInWeight: 0, // 本次实际入库重量
       inWarehouseWeight: 0, // 已入库重量
       receivedWeight: 0, // 实收总重量
       weight: 0, // 重量（KG）
       
       // 价格信息
       unitPrice: 0, // 入库单价
       amount: 0, // 入库金额
       costUnitPrice: 0, // 成本单价
       costAmount: 0, // 成本金额
       currency: "CNY", // 交易币种
       
       // 仓库信息
       warehouseCode: "", // 仓库编码
       warehouseName: "", // 仓库名称
       warehouseAreaCode: "", // 仓库库区编码
       warehouseAreaName: "", // 仓库库区名称
       
       // 其他信息
       receiptNoticeCode: "", // 入库通知单号
       receiptNoticeId: undefined, // 入库通知单id
       parentId: undefined, // 父id
       isSku: 0, // 是否sku->1:是;0:否
       isDiscreteUnit: 0, // 一级单位增减->1:开启；0:关闭
       pricingScheme: 0, // 计价模式->0:一级单位;1:二级单位
       remark: "", // 备注
     }, */
  ],

  // 质检列表
  qualityInspectionList: [
    /* {
      productCode: "", // 商品编码
      deductionAmount: 0, // 扣款金额
      deductionDesc: "", // 扣款说明
      deductionAttachment: "", // 附件
      detailList: [
        {
          specification: "", // 规格
          quantity: 0, // 数量
          proportion: 0, // 占比
        }
      ],
    } */
  ],
});

// 表单验证规则
const rules = {
  receiptType: [{ required: true, message: "请选择入库类型", trigger: "change" }],
  plannedDeliveryTime: [
    { required: true, message: "请选择计划交货时间", trigger: "change" },
  ],
};

// 基础数据
const businessPersonList = ref<any[]>([]);
const supplierList = ref<any[]>([]);
const warehouseAreaList = ref<any[]>([]);

// 弹窗控制
const productDialog = reactive({
  visible: false,
  title: "选择商品",
});

const qualityDialog = reactive({
  visible: false,
  productData: {},
});

// 计算属性
/* const totalQuantity = computed(() => {
  return formData.productList?.reduce((sum: number, item: any) => sum + (item.actualInQty || 0), 0) || 0;
});

const totalAmount = computed(() => {
  return formData.productList?.reduce(
    (sum: number, item: any) => sum + (item.unitPrice || 0) * (item.actualInQty || 0),
    0
  ) || 0;
});

const totalConvertedQuantity = computed(() => {
  return formData.productList?.reduce(
    (sum: number, item: any) => sum + (item.actualInWeight || 0),
    0
  ) || 0;
}); */

// 方法
const handleBack = () => {
  router.back();
};

const handleAddProduct = () => {
  productDialog.visible = true;
  nextTick(() => {
    addGoodsRef.value.queryGoodList();
    addGoodsRef.value.queryManagerCategoryList();
  })
};

const onProductSubmit = (products: any[]) => {
  console.log(products);
  products.forEach((product: any) => {
    const existingIndex = formData.productList?.findIndex(
      (item: any) => item.productCode === product.productCode
    );

    if (existingIndex !== undefined && existingIndex >= 0) {
      // 如果商品已存在，更新数量
      if (formData.productList && formData.productList[existingIndex]) {
        formData.productList[existingIndex].actualInQty += product.actualInQty || 1;
      }
    } else {
      // 如果是新商品，添加到列表
      if (formData.productList) {

        formData.productList.push({
          // 商品信息
          id: product.id,
          productCode: product.productCode,
          productName: product.productName,

          productUnitName: product.productUnitName,
          // 商品分类
          firstCategoryId: product.firstCategoryId,
          firstCategoryName: product.firstCategoryName,
          secondCategoryId: product.secondCategoryId,
          secondCategoryName: product.secondCategoryName,
          thirdCategoryId: product.thirdCategoryId,
          thirdCategoryName: product.thirdCategoryName,

          fullCategoryName: product.fullCategoryName,
          // 规格
          productSpecs: product.productSpec,
          // 入库单价
          unitPrice: null,
          // 入库量
          actualInQty: null,
          // 入库转换量
          actualInWeight: null,
          // 入库金额
          amount: null,
          // 入库库区
          warehouseAreaCode: null,
          // 商品包装
          productPackaging: null,
          isSku: product.isSku,
          isDiscreteUnit: product.isDiscreteUnit,
          pricingScheme: product.pricingScheme,
          conversionRelSecondUnitId: product.conversionRelSecondUnitId,
          conversionRelSecondUnitName: product.conversionRelSecondUnitName,

          productUnitId: product.productUnitId,
          productUnitName: product.productUnitName,


          /*  productExpectQty: product.productExpectQty || 0,
           actualInQty: product.actualInQty || 1,
           inWarehouseQty: product.inWarehouseQty || 0,
           expectedWeight: product.expectedWeight || 0,
           actualInWeight: product.actualInWeight || 0,
           inWarehouseWeight: product.inWarehouseWeight || 0,
           receivedWeight: product.receivedWeight || 0,
           weight: product.weight || 0,
           unitPrice: product.unitPrice || 0,
           amount: product.amount || 0,
           costUnitPrice: product.costUnitPrice || 0,
           costAmount: product.costAmount || 0,
           currency: product.currency || "CNY",
           warehouseCode: product.warehouseCode || "",
           warehouseName: product.warehouseName || "",
           warehouseAreaCode: product.warehouseAreaCode || "",
           warehouseAreaName: product.warehouseAreaName || "",
           receiptNoticeCode: product.receiptNoticeCode || "",
           receiptNoticeId: product.receiptNoticeId || undefined,
           parentId: product.parentId || undefined,
          
           remark: product.remark || "", */
        });
      }
    }
  });
};

//  供应商选择
/* const handleSupplierChange = (value: string) => {
  supplierList.value.forEach((item: any) => {
    if (item.supplierCode === value) {
      formData.supplierName = item.supplierName;
    }
  });
}

//  业务员选择
const handleSalesmanChange = (value: string) => {
  businessPersonList.value.forEach((item: any) => {
    if (item.userId === value) {
      formData.salesmanName = item.userName;
    }
  });
} */
const handleAddRow = (index: number, row: any) => {
  if (formData.productList) {
    const insertData = {
      ...row,
      actualInQty: null, // 清空需要重新输入和计算的字段
      actualInWeight: null,
      amount: null,
      warehouseAreaCode: null,
      productPackaging: null,
      parentId: row.id, // 父级id,用户和父级产品id构建层级关系
    }
    formData.productList.splice(index + 1, 0, insertData);
  }
};

const handleDeleteRow = (index: number) => {
  if (formData.productList && formData.productList.length > 1) {
    formData.productList.splice(index, 1);
  }
};

/* const calculateAmount = (index: number) => {
  if (formData.productList && formData.productList[index]) {
    const item = formData.productList[index];
    // 这里可以添加转换量的计算逻辑
    if (item.actualInQty && (item as any).actualInWeight) {
      item.actualInWeight = item.actualInQty * (item as any).actualInWeight;
    }
  }
}; */

// 计算入库转换量
const calculateInWeight = (row: any) => {
  CommonAPI.convertProductUnit({
    convertUnitTypeEnum: 'FIRST_TO_SECOND',
    originalValue: row.actualInQty,
    productCode: row.productCode,
  }).then((res) => {
    row.actualInWeight = res.convertedValue;
    calculateAmount(row);
  });
};

// 计算入库金额
const calculateAmount = (row: any) => {
  CommonAPI.calculateAmount({
    convertedQty: row.actualInWeight,
    productCode: row.productCode,
    qty: row.actualInQty,
    unitPrice: row.unitPrice,
  }).then((res) => {
    row.amount = res.amount;
  });
};

// 质检弹窗
const handleQualityCheck = (list: any, product: any, index: number) => {
  // 入库量和转换量取同商品productCode对应的字段之和
  const productCode = product.productCode;
  const actualInQty = list.filter((item: any) => item.productCode === productCode).reduce((sum: number, item: any) => sum + (parseFloat(item.actualInQty) || 0), 0);
  const actualInWeight = list.filter((item: any) => item.productCode === productCode).reduce((sum: number, item: any) => sum + (parseFloat(item.actualInWeight) || 0), 0);
  qualityDialog.productData = {
    ...product, index, list, actualInQty, actualInWeight, detailList: [
      {
        specification: "",
        quantity: null,
        proportion: null,
        proportionDisplay: "",
      },
    ],
    deductionAmount: null, // 扣款金额
    deductionDesc: "", // 扣款说明
    deductionAttachment: "", // 扣款附件  
  };
  qualityDialog.visible = true;
};

const onQualityConfirm = (data: any) => {
  // 处理质检结果
  const { index, ...qualityData } = data;
  debugger
  // 根据productCode判断是否存在，存在则更新，不存在则新增  
  const existingIndex = formData.qualityInspectionList?.findIndex((item: any) => item.productCode === qualityData.productCode);
  if (existingIndex !== undefined && existingIndex >= 0) {
    formData.qualityInspectionList[existingIndex] = qualityData;
  } else {
    formData.qualityInspectionList?.push(qualityData);
  }
  console.log("formData.qualityInspectionList---------",formData.qualityInspectionList);
};

// 地址选择相关方法
/* const getSupplierAreaInfo = () => {
  const formDataAny = formData as any;
  if (formDataAny.provinceId && formDataAny.cityId && formDataAny.districtId) {
    return [formDataAny.provinceId, formDataAny.cityId, formDataAny.districtId];
  }
  return [];
};


const handleSupplierCountryChange = (countryInfo: any) => {
  const formDataAny = formData as any;
  if (countryInfo) {
    formDataAny.countryId = countryInfo.id;
    formDataAny.countryName = countryInfo.name;
    formDataAny.countryAreaCode = countryInfo.areaCode;
  } else {
    formDataAny.countryId = '';
    formDataAny.countryName = '';
    formDataAny.countryAreaCode = '';
  }
};

const handleSupplierAreaChange = (areaInfo: any) => {
  const formDataAny = formData as any;
  if (areaInfo && areaInfo.pathValues && areaInfo.pathLabels) {
    const values = areaInfo.pathValues;
    const labels = areaInfo.pathLabels;

    formDataAny.provinceId = values[0] || '';
    formDataAny.provinceName = labels[0] || '';
    formDataAny.cityId = values[1] || '';
    formDataAny.cityName = labels[1] || '';
    formDataAny.districtId = values[2] || '';
    formDataAny.districtName = labels[2] || '';
  } else {
    formDataAny.provinceId = '';
    formDataAny.provinceName = '';
    formDataAny.cityId = '';
    formDataAny.cityName = '';
    formDataAny.districtId = '';
    formDataAny.districtName = '';
  }
}; */

/* const handleSupplierAddressChange = (address: string) => {
  formData.address = address || '';
};

const handleCustomerCountryChange = (countryInfo: any) => {
  const formDataAny = formData as any;
  if (countryInfo) {
    formDataAny.customerCountryId = countryInfo.id;
    formDataAny.customerCountryName = countryInfo.shortName;
  } else {
    formDataAny.customerCountryId = '';
    formDataAny.customerCountryName = '';
  }
};

const handleCustomerAreaChange = (areaInfo: any) => {
  const formDataAny = formData as any;
  if (areaInfo && areaInfo.pathValues && areaInfo.pathLabels) {
    const values = areaInfo.pathValues;
    const labels = areaInfo.pathLabels;

    formDataAny.customerProvinceId = values[0] || '';
    formDataAny.customerProvinceName = labels[0] || '';
    formDataAny.customerCityId = values[1] || '';
    formDataAny.customerCityName = labels[1] || '';
    formDataAny.customerDistrictId = values[2] || '';
    formDataAny.customerDistrictName = labels[2] || '';
  } else {
    formDataAny.customerProvinceId = '';
    formDataAny.customerProvinceName = '';
    formDataAny.customerCityId = '';
    formDataAny.customerCityName = '';
    formDataAny.customerDistrictId = '';
    formDataAny.customerDistrictName = '';
  }
};

const handleCustomerAddressChange = (address: string) => {
  formData.customerAddress = address || '';
}; */

const handleCancel = () => {
  router.back();
};

/* const handleSaveDraft = async () => {
  try {
    saving.value = true;
    formData.status = 0; // 草稿状态
    await QuickWarehousingAPI.addDraft(formData);
    ElMessage.success("保存成功");
    router.back();
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
    saving.value = false;
  }
}; */

const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    submitting.value = true;
    // formData.status = 1; // 初始状态
    const formDataCopy = { ...formData };
    formDataCopy.plannedDeliveryTime = new Date(formDataCopy.plannedDeliveryTime).getTime();

    await QuickWarehousingAPI.add(formDataCopy);

    ElMessage.success("提交成功");
    router.back();
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    submitting.value = false;
  }
};

// 获取基础数据
const getBaseData = async () => {
  try {
    // 获取仓库库区列表
    const areaData: any = await QuickWarehousingAPI.getWarehouseAreaList();
    warehouseAreaList.value = areaData;

    // 获取业务员列表
    const businessPersonData: any = await QuickWarehousingAPI.querySalesPersonUser();
    businessPersonList.value = businessPersonData || [];

    // 获取供应商列表
    const supplierData: any = await QuickWarehousingAPI.getSupplierListAll();
    supplierList.value = supplierData || [];
  } catch (error) {
    console.error("获取基础数据失败:", error);
  }
};

// 获取详情数据
const getDetailData = async () => {
  if (route.query.id) {
    try {
      const data: any = await QuickWarehousingAPI.queryDetail({ id: route.query.id });
      Object.assign(formData, data);
      formData.qualityInspectionList = data.qualityInspectionList || [];
    } catch (error) {
      console.error("获取详情失败:", error);
    }
  }
};

onMounted(() => {
  getBaseData();
  getDetailData();
});
</script>

<style scoped lang="scss">
.align-center {
  align-items: center;
}

.title-label {
  display: flex;
  align-items: center;
  margin: 32px 0 20px 0;

  .title-line {
    width: 4px;
    height: 16px;
    background: var(--el-color-primary);
    margin-right: 8px;
  }

  .title-content {
    font-size: 16px;
    font-weight: 500;
    color: #151719;
  }

  .flex-1 {
    flex: 1;
  }

  .add-product-btn {
    margin-left: auto;
  }
}

.page-title {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7f3;
  font-size: 18px;
  font-weight: 500;
  color: #151719;

  .mr8px {
    margin-right: 8px;
  }
}

.fixed-footer {
  /* position: fixed;
  bottom: 0;
  right: 0;
  left: 0; */
  width: 100%;
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 24px;
  text-align: right;
  z-index: 100;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-container {
  background: #ffffff;
  border-radius: 4px;
}

.page-content {
  padding: 0px 24px 0 24px;
}
</style>