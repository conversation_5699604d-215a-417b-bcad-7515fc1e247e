{
    "orgCode": null,
    "createTime": 1741918237000,
    "updateTime": 1742353336000,
    "createUser": "1894947841447854085",
    "updateUser": "1894947841447854084",
    "id": "1900368941514252289",
    "pickingListCode": "PK250314W000001",
    "deliveryNoticeCode": "ON250313C00017",
    "billOfLadingCode": "3333333",
    "pickingProductCount": 1,
    "plannedReceivedTime": 1741795200000,
    "actualPicker": "bins租户",
    "actualPickingTime": 1742353336000,
    "printStatus": 0,
    "printStatusStr": "否",
    "printCount": 0,
    "pickingType": 1,
    "pickingTypeStr": null,
    "createUserName": "bins租户",
    "updateUserName": "bins租户",
    "warehouseCode": "WT0052A01",
    "warehouseName": "bins仓库001",
    "tenantId": "T0052",
    "isDeleted": "0",
    "status": 2,
    "statusStr": "拣货失败",
    "purchaseSalesPerson": null,
    "customerName": "qq",
    "remark": null,
    "pickingListProductVOS": [
        {
            "orgCode": null,
            "createTime": 1741918237000,
            "updateTime": 1741921041000,
            "createUser": "1894947841447854085",
            "updateUser": "1894947841447854085",
            "id": "1900368941614915586",
            "pickingListId": "1900368941514252289",
            "pickingListCode": "PK250314W000001",
            "productCode": "P03113594861207",
            "productName": "茄块",
            "productSpecs": "1/斤",
            "productUnitName": "斤",
            "totalPlanedPick": "1.000",
            "totalActualPick": null,
            "createUserName": "bins租户",
            "updateUserName": "bins租户",
            "warehouseCode": "WT0052A01",
            "tenantId": "T0052",
            "isDeleted": "0",
            "status": 4,
            "statusStr": "取消",
            "pickingListProductDetailVOList": [
                {
                    "orgCode": null,
                    "createTime": 1741918238000,
                    "updateTime": 1741918238000,
                    "createUser": "1894947841447854085",
                    "updateUser": "1894947841447854085",
                    "id": "1900368941744939009",
                    "pickingListProductId": "1900368941614915586",
                    "warehouseAreaCode": "A01K001",
                    "warehouseAreaName": "库区1",
                    "warehouseLocationCode": "A01K001S001",
                    "warehouseLocationName": "默认库位",
                    "warehouseAreaPlanedPick": "1.000",
                    "actualPickThisTime": null,
                    "availableStockQty": "1115.766",
                    "createUserName": "bins租户",
                    "updateUserName": "bins租户",
                    "warehouseCode": "WT0052A01",
                    "tenantId": "T0052"
                }
            ]
        }
    ]
}

/**
 * ResultBodyPickingListVO
 */
 export interface Response {
    /**
     * 响应编码
     * 响应编码:0-请求处理成功
     */
    code?: number;
    /**
     * 响应数据
     */
    data?: PickingListVO;
    /**
     * 附加数据
     */
    extra?: MapObject;
    /**
     * http状态码
     */
    httpStatus?: number;
    /**
     * 提示消息
     */
    message?: string;
    /**
     * 请求路径
     */
    path?: string;
    /**
     * 响应时间
     */
    timestamp?: number;
    [property: string]: any;
}

/**
 * 响应数据
 *
 * PickingListVO
 */
export interface PickingListVO {
    /**
     * 实际拣货人
     */
    actualPicker?: string;
    /**
     * 实际拣货时间
     */
    actualPickingTime?: string;
    /**
     * 提货单号
     */
    billOfLadingCode?: string;
    /**
     * 创建时间
     */
    createTime?: string;
    /**
     * 创建用户id
     */
    createUser?: number;
    /**
     * 创建用户
     */
    createUserName?: string;
    /**
     * 客户（供应商）
     */
    customerName?: string;
    /**
     * 出库通知单号
     */
    deliveryNoticeCode?: string;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 删除（0 表示未删除，大于0 表示删除）
     */
    isDeleted?: number;
    /**
     * 组织编码
     */
    orgCode?: string;
    /**
     * 拣货单号
     */
    pickingListCode?: string;
    /**
     * 拣货商品
     */
    pickingListProductVOS?: PickingListProductVO[];
    /**
     * 商品个数
     */
    pickingProductCount?: number;
    /**
     * 拣货方式:1:按单拣货
     */
    pickingType?: number;
    /**
     * 拣货方式:前端直接显示
     */
    pickingTypeStr?: string;
    /**
     * 要求到货时间
     */
    plannedReceivedTime?: string;
    /**
     * 打印次数
     */
    printCount?: number;
    /**
     * 打印状态:0:否 1:是
     */
    printStatus?: number;
    /**
     * 打印状态:前端直接显示
     */
    printStatusStr?: string;
    /**
     * 销售员（采购员）
     */
    purchaseSalesPerson?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 状态:1 初始 2 拣货失败 3 拣货完成 4 已撤销
     */
    status?: number;
    /**
     * 状态:前端直接显示
     */
    statusStr?: string;
    /**
     * 租户ID
     */
    tenantId?: string;
    /**
     * 修改时间
     */
    updateTime?: string;
    /**
     * 更新用户id
     */
    updateUser?: number;
    /**
     * 更新用户
     */
    updateUserName?: string;
    /**
     * 仓库编码
     */
    warehouseCode?: string;
    /**
     * 仓库名称
     */
    warehouseName?: string;
    [property: string]: any;
}

/**
 * PickingListProductVO对象
 *
 * PickingListProductVO
 */
export interface PickingListProductVO {
    /**
     * 创建时间
     */
    createTime?: string;
    /**
     * 创建用户id
     */
    createUser?: number;
    /**
     * 创建用户
     */
    createUserName?: string;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 删除（0 表示未删除，大于0 表示删除）
     */
    isDeleted?: number;
    /**
     * 组织编码
     */
    orgCode?: string;
    /**
     * 拣货单号
     */
    pickingListCode?: string;
    /**
     * 拣货单id
     */
    pickingListId?: number;
    /**
     * 拣货商品拣货明细
     */
    pickingListProductDetailVOList?: PickingListProductDetailVO[];
    /**
     * 商品编码
     */
    productCode?: string;
    /**
     * 商品名称
     */
    productName?: string;
    /**
     * 规格
     */
    productSpecs?: string;
    /**
     * 单位
     */
    productUnitName?: string;
    /**
     * 状态:1 初始 2 拣货失败 3 拣货完成 4 已撤销
     */
    status?: number;
    /**
     * 状态:前端直接显示
     */
    statusStr?: string;
    /**
     * 租户ID
     */
    tenantId?: string;
    /**
     * 实际拣货总量
     */
    totalActualPick?: number;
    /**
     * 计划拣货总量
     */
    totalPlanedPick?: number;
    /**
     * 修改时间
     */
    updateTime?: string;
    /**
     * 更新用户id
     */
    updateUser?: number;
    /**
     * 更新用户
     */
    updateUserName?: string;
    /**
     * 仓库编码
     */
    warehouseCode?: string;
    [property: string]: any;
}

/**
 * PickingListProductDetailVO对象
 *
 * PickingListProductDetailVO
 */
export interface PickingListProductDetailVO {
    /**
     * 本次实际拣货量
     */
    actualPickThisTime?: number;
    /**
     * 可用库存数量
     */
    availableStockQty?: number;
    /**
     * 创建时间
     */
    createTime?: string;
    /**
     * 创建用户id
     */
    createUser?: number;
    /**
     * 创建用户
     */
    createUserName?: string;
    /**
     * 主键id
     */
    id?: number;
    /**
     * 组织编码
     */
    orgCode?: string;
    /**
     * 拣货单商品表id
     */
    pickingListProductId?: number;
    /**
     * 租户ID
     */
    tenantId?: string;
    /**
     * 修改时间
     */
    updateTime?: string;
    /**
     * 更新用户id
     */
    updateUser?: number;
    /**
     * 更新用户
     */
    updateUserName?: string;
    /**
     * 仓库库区编码
     */
    warehouseAreaCode?: string;
    /**
     * 仓库库区名称
     */
    warehouseAreaName?: string;
    /**
     * 库区计划拣货量
     */
    warehouseAreaPlanedPick?: number;
    /**
     * 仓库编码
     */
    warehouseCode?: string;
    /**
     * 仓库库位编码
     */
    warehouseLocationCode?: string;
    /**
     * 仓库库位名称
     */
    warehouseLocationName?: string;
    [property: string]: any;
}

/**
 * 附加数据
 *
 * MapObject
 */
export interface MapObject {
    key?: { [key: string]: any };
    [property: string]: any;
}