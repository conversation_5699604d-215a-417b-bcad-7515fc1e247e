<template>
  <PrintTemplate ref="printRef">
    <div class="print-container">
      <div class="print-header">
        <h1 class="print-title">{{$t('WMSPickingOrder.print.title')}}</h1>
      </div>

      <!-- 条码区域 -->
      <div class="barcode-section">
        <div class="barcode-image">
          <!-- 使用JSBarcode生成条形码 -->
          <svg id="pickingBarcode" ref="barcodeRef"></svg>
          <div class="barcode-text">
            {{ printData?.pickingListCode || "" }}
          </div>
        </div>
        <div class="warehouse-info">
          <div class="info-code">{{ printData?.warehouseCode || "" }}</div>
          <div class="order-number">
            {{ printData?.pickingListCode ? printData.pickingListCode.slice(-6) : "" }}
          </div>
        </div>
      </div>

      <div class="print-info">
        <div class="info-row">
          <div class="info-item-2">
            <span class="info-label">{{$t('WMSPickingOrder.print.warehouse')}}：</span>
            <span class="info-value">{{ printData?.warehouseName || "" }}</span>
          </div>
          <div class="info-item-2">
            <span class="info-label">{{$t('WMSPickingOrder.print.purchaserSalesperson')}}：</span>
            <span class="info-value">{{ printData?.purchaseSalesPerson || "--" }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item-2">
            <span class="info-label">{{$t('WMSPickingOrder.print.pickingListCode')}}：</span>
            <span class="info-value">
              {{ printData?.pickingListCode || "--" }}
            </span>
          </div>
          <div class="info-item-2">
<!--            supplierName：供应商-->
<!--            customerName：客户-->
            <span class="info-label">{{$t('WMSPickingOrder.print.customerSupplier')}}：</span>
            <span class="info-value">{{ printData?.customerName || printData?.supplierName || "--" }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item-2">
            <span class="info-label">{{$t('WMSPickingOrder.print.remark')}}：</span>
            <span class="info-value">
              {{printData?.remark || "--" }}
            </span>
          </div>
          <div class="info-item-2">
            <span class="info-label">{{$t('WMSPickingOrder.print.plannedArrivalTime')}}：</span>
            <span class="info-value">
              {{ formatTime(printData?.plannedReceivedTime, "{y}-{m}-{d} {h}:{i}:{s}") || "" }}
            </span>
          </div>
        </div>

        <div class="info-row text-left">
          <div class="info-item">
            <span class="info-label">{{$t('WMSPickingOrder.print.productCount')}}：</span>
            <span class="info-value">
              {{ (printData?.pickingProductCount) || '--' }}
            </span>
          </div>
        </div>
      </div>

      <div class="print-table-container">
        <table class="print-table">
          <thead>
            <tr>
              <th>{{$t('WMSPickingOrder.print.tableHeaders.serialNumber')}}</th>
              <th>{{$t('WMSPickingOrder.print.tableHeaders.productName')}}</th>
              <th>{{$t('WMSPickingOrder.print.tableHeaders.specification')}}</th>
              <th>{{$t('WMSPickingOrder.print.tableHeaders.unit')}}</th>
              <th>{{$t('WMSPickingOrder.print.tableHeaders.totalPlannedPick')}}</th>
              <th>{{$t('WMSPickingOrder.print.tableHeaders.plannedAreaPick')}}</th>
              <th>{{$t('WMSPickingOrder.print.tableHeaders.actualPick')}}</th>
            </tr>
          </thead>
          <tbody>
            <template
              v-for="(product, index) in printData?.pickingListProductVOS ||
              []"
              :key="product.id"
            >
              <template
                v-for="(
                  detail, detailIndex
                ) in product.pickingListProductDetailVOList || []"
                :key="detail.id"
              >
                <tr>
                  <!-- 序号 -->
                  <td
                    v-if="detailIndex === 0"
                    :rowspan="product.pickingListProductDetailVOList.length"
                  >
                    {{ index + 1 }}
                  </td>
                  <!-- 商品名 -->
                  <td
                    v-if="detailIndex === 0"
                    :rowspan="product.pickingListProductDetailVOList.length"
                  >
                    {{ product.productName }}
                  </td>
                  <!-- 规格 -->
                  <td
                    v-if="detailIndex === 0"
                    :rowspan="product.pickingListProductDetailVOList.length"
                  >
                    {{ product.productSpecs || "-" }}
                  </td>
                  <!-- 单位 -->
                  <td
                    v-if="detailIndex === 0"
                    :rowspan="product.pickingListProductDetailVOList.length"
                  >
                    {{ product.productUnitName }}
                  </td>
                  <!-- 计划拣货总量 -->
                  <td
                    v-if="detailIndex === 0"
                    :rowspan="product.pickingListProductDetailVOList.length"
                  >
                    {{ product.totalPlanedPick }}
                  </td>
                  <!-- 计划库区|拣货量 -->
                  <td>
                    {{detail.warehouseAreaName}} | {{detail.warehouseAreaPlanedPick}}
                  </td>
                  <!-- 实际拣货量 -->
                  <td>{{ detail.actualPickThisTime || '--' }}</td>
                </tr>
              </template>
            </template>
          </tbody>
        </table>
      </div>

      <div class="print-footer">
        <div class="signature-section">
          <div class="signature-item">
            <span class="signature-label">{{$t('WMSPickingOrder.print.signatures.printer')}}：</span>
            <span class="signature-value">
              {{  userStore.user.nickName || "--" }}
            </span>
          </div>
          <div class="signature-item">
            <span class="signature-label">{{$t('WMSPickingOrder.print.signatures.printTime')}}：</span>
            <span class="signature-value">
              {{ formatTime(new Date(), "{y}-{m}-{d} {h}:{i}:{s}") }}
            </span>
          </div>
          <div class="signature-item">
            <span class="signature-label">{{$t('WMSPickingOrder.print.signatures.picker')}}：</span>
            <span class="signature-value">
              {{ printData?.actualPicker || "--" }}
            </span>
          </div>
          <div class="signature-item">
            <span class="signature-label">{{$t('WMSPickingOrder.print.signatures.pickingDate')}}：</span>
            <span class="signature-value">
              {{ formatTime(printData?.updateTime) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </PrintTemplate>
</template>

<script lang="ts" setup>
import { parseTime } from "@/core/utils/index";
import PrintTemplate from "@/core/components/Print/PrintTemplate.vue";
import { useI18n } from "vue-i18n";
import JsBarcode from "jsbarcode";
import {
  useUserStore,
} from "@/core/store";

const userStore = useUserStore();
interface PickingListProductDetailVO {
  id: string | number;
  warehouseAreaName?: string;
  warehouseLocationCode?: string;
  warehouseAreaPlanedPick?: number;
  actualPickThisTime?: number;
}

interface PickingListProductVO {
  id: string | number;
  productName: string;
  specification?: string;
  unitName: string;
  planedPickQty: number;
  pickingListProductDetailVOList: PickingListProductDetailVO[];
}

interface PrintData {
  id?: string | number;
  pickingListCode?: string;
  warehouseCode?: string;
  warehouseName?: string;
  createUser?: string;
  customerName?: string;
  remark?: string;
  plannedReceivedTime?: string | Date;
  updateUser?: string;
  updateTime?: string | Date;
  pickingListProductVOList?: PickingListProductVO[];
}

const { t } = useI18n();
const printRef = ref();
const barcodeRef = ref<SVGElement | null>(null);
const printData = ref<PrintData>({});

// 生成条形码
const generateBarcode = (): void => {
  if (barcodeRef.value && printData.value?.pickingListCode) {
    try {
      JsBarcode(barcodeRef.value, printData.value.pickingListCode, {
        format: "CODE128",
        width: 2,
        height: 60,
        displayValue: false,
        margin: 0,
      });
    } catch (error) {
      console.error("Barcode generation error:", error);
    }
  }
};

// 监听打印数据变化，生成条形码
watch(
  () => printData.value?.pickingListCode,
  () => {
    setTimeout(() => {
      generateBarcode();
    }, 100);
  },
  { immediate: false }
);

// 格式化时间
const formatTime = (
  time: string | Date | undefined,
  format = "{y}-{m}-{d}"
): string => {
  if (!time) return "-";
  return parseTime(time, format) || "";
};

// 打印方法
const handlePrint = (data: PrintData): void => {
  printData.value = data;
  // 使用延时确保数据渲染完成
  setTimeout(() => {
    generateBarcode();
    setTimeout(() => {
      printRef.value?.print();
    }, 200);
  }, 300);
};

defineExpose({
  handlePrint,
});
</script>

<style lang="scss">
/* 基础样式 - 最小化但保留结构 */
.print-container {
}
@media print {
  .print-container {
    display: block;
    width: calc(100vw - 40px) !important; /* 使用!important确保优先级 */
    max-width: 100% !important; /* 添加最大宽度限制 */
    height: calc(100vh - 40px) !important;
    margin: 20px !important; /* 确保没有外边距 */
    padding: 20px;
    box-sizing: border-box;
    font-family: SimSun, "宋体", Arial, sans-serif;
    color: black;
    border: 1px solid #000;
    position: fixed !important; /* 使用fixed定位 */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important; /* 确保右侧贴合 */
  }
  .print-header {
    text-align: center;
    margin-bottom: 20px;

    .print-title {
      font-size: 24px;
      font-weight: bold;
      margin: 0;
    }
  }

  .barcode-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    padding: 10px;

    .barcode-image {
      display: flex;
      flex-direction: column;
      align-items: center;

      svg {
        width: 200px;
        margin-bottom: 5px;
      }

      .barcode-text {
        font-size: 12px;
      }
    }

    .warehouse-info {
      text-align: right;

      .info-code,
      .order-number {
        margin-bottom: 5px;
        font-size: 14px;
      }
    }
  }

  .print-info {
    margin-bottom: 20px;

    .info-row {
      display: flex;
      margin-bottom: 10px;

      &.text-right {
        justify-content: flex-end;
      }
      &.text-left {
        justify-content: flex-start;
      }
    }

    .info-item,
    .info-item-2,
    .info-item-3 {
      display: flex;
      //align-items: center;
      margin-right: 20px;

      &:last-child {
        margin-right: 0;
      }
    }
    .info-item-2 {
      width: 50%;
    }
    .info-item-3 {
      width: 33%;
    }
    .info-value{
      word-break: break-all;
    }
    .info-label {
      font-weight: bold;
      margin-right: 5px;
      word-break: keep-all;
      white-space: nowrap;
    }

    .ecllipse {
      max-width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .print-table-container {
    margin-bottom: 20px;

    .print-table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
      }

      th {
        // background-color: #f2f2f2;
        font-weight: bold;
      }

      tr:nth-child(even) {
        // background-color: #f9f9f9;
      }
    }
  }

  .print-footer {
    .signature-section {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .signature-item {
        width: 50%;
        margin-bottom: 10px;
        display: flex;

        .signature-label {
          font-weight: bold;
          margin-right: 5px;
        }
      }
    }
  }
}
</style>
