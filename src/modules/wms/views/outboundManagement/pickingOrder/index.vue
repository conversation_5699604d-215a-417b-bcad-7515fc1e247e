

<script setup lang="ts">
import ProductMgAPI from "@wms/api/pickingOrder";
import { useI18n } from "vue-i18n"; // 导入国际化
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import { parseTime } from "@/core/utils/index";
import { useForm } from "./composables";
const { statusClass } = useForm();
import Print from "./components/print.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { hasAuth } from "@/core/plugins/permission";
import moment from "moment";
import { useUserStore } from "@/core/store/modules/user";

const userStore = useUserStore();
// 使用国际化
const { t } = useI18n();

const router = useRouter();

// 定义行记录类型
interface RowRecord {
  id: number;
  pickSn: string;
  purchaseOrderSn: string;
  productQty: number;
  requestTime: string;
  requestUser: string;
  createTime: string;
  isPrinted: string | number;
  status: number;
  btnLoading?: boolean;
  receivingStatus?: number; // 领用状态 0-未领用 1-已领用
  [key: string]: any;
}

// 搜索表单接口定义
interface SearchForm {
  pickingListCode: string;
  billOfLadingCode: string;
  deliveryNoticeCode: string;
  statusList: number[];
  printStatus: any;
  queryType: any;
  queryStartTime: string;
  queryEndTime: string;
  receivingStatus?: number; // 是否领单 0-否 1-是
}

const dataList = ref<RowRecord[]>([]);
const timeRange = ref<string[]>([]);
const searchForm = ref<SearchForm>({
  pickingListCode: "", // 拣货单号
  billOfLadingCode: "", // 提货单号
  deliveryNoticeCode: "", // 出库通知单
  statusList: [], // 状态
  printStatus: null, // 是否打印
  queryType: 2, // 查询类型
  queryStartTime: "", // 查询开始时间
  queryEndTime: "", // 查询结束时间
  receivingStatus: undefined, // 是否领单
});

const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const loading = ref(false);

// 状态选项
const statusOptions = [
  // 状态:1 初始 2 拣货失败 3 拣货完成 4 已撤销 5 拣货中
  { label: "初始", value: 1 },
  // { label: "拣货失败", value: 2 },
  { label: "拣货中", value: 5 },
  { label: "完结", value: 3 },
  { label: "已撤销", value: 4 },
] as const;

/* const statusClass = (status: number) => {
  switch (status) {
    case 1:
      return "unwokring";
    case 2:
      return "failed";
    case 3:
      return "executing";
    case 4:
      return "cancelled";
    default:
      return "";
  }
}; */
const fetchPageList = async (): Promise<void> => {
  loading.value = true;
  try {
    // 如果拣货单号，出库通知单，提货单号小于4个字符，不传参
    const params: Partial<SearchForm> & { page: number; limit: number } = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm.value,
    };
    if (searchForm.value.pickingListCode.length < 4) {
      if (searchForm.value.pickingListCode.length > 0) {
        // "拣货单号必须大于4位"
        ElMessage.error(t('WMSPickingOrder.rules.pickingListCode'));
      }
      delete params.pickingListCode;
    }
    if (searchForm.value.deliveryNoticeCode.length < 4) {
      if (searchForm.value.deliveryNoticeCode.length > 0) {
        // "出库通知单号必须大于4位"
        ElMessage.error(t('WMSPickingOrder.rules.deliveryNoticeCode'));
      }
      delete params.deliveryNoticeCode;
    }
    if (searchForm.value.billOfLadingCode.length < 4) {
      if (searchForm.value.billOfLadingCode.length > 0) {
        // "提货单号必须大于4位"
        ElMessage.error(t('WMSPickingOrder.rules.billOfLadingCode'));
      }
      delete params.billOfLadingCode;
    }
    const formData = JSON.parse(JSON.stringify(params));
    formData.queryStartTime = new Date(formData.queryStartTime).getTime();
    formData.queryEndTime = new Date(formData.queryEndTime).getTime();
    const data = await ProductMgAPI.getPageList(formData);
    dataList.value = data.records;
    total.value = parseInt(data.total) || 0;
  } catch (error) {
    // Error handling can be added here
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchPageList();
};

const handleReset = () => {
  currentPage.value = 1;
  searchForm.value = {
    pickingListCode: "", // 拣货单号
    billOfLadingCode: "", // 提货单号
    deliveryNoticeCode: "", // 出库通知单
    statusList: [], // 状态
    printStatus: null, // 是否打印
    queryType: null, // 查询类型
    queryStartTime: "", // 查询开始时间
    queryEndTime: "", // 查询结束时间
    receivingStatus: undefined, // 是否领单
  };
  timeRange.value = [];
  initDefaultDateRange();
  fetchPageList();
};

const handleTimeChange = (time: string[]) => {
  if (time && time.length === 2) {
    searchForm.value.queryStartTime = time[0];
    searchForm.value.queryEndTime = time[1];
  } else {
    searchForm.value.queryStartTime = "";
    searchForm.value.queryEndTime = "";
  }
  console.log(searchForm.value.queryStartTime)
  fetchPageList();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchPageList();
};

// 查看详情
const handleDetail = (row: RowRecord) => {
  // 实现查看详情功能
  console.log("查看详情", row);
  router.push({
    path: "/wms/pickingOrder/detail",
    query: { pickingListCode: row.pickingListCode, id: row.id },
  });
};

// 编辑
const handleEdit = (row: RowRecord) => {
  console.log("编辑", row);
  // 页面跳转或打开弹窗编辑
};

// 拣货完成
const handleComplete = (row: RowRecord) => {
  console.log("拣货完成", row);
  router.push({
    path: "/wms/pickingOrder/add",
    query: { pickingListCode: row.pickingListCode, id: row.id },
  });
  // 实现拣货完成功能
};

const pickBtnShow = (row: RowRecord) => {
  return (row.status === 1) && row.receivingStatus == 1 && row.receivingUserName === userStore.user.nickName;
};

const printBtnShow = (row: RowRecord) => {
  return row.status !== 4;
};

const fetchDetail = async (data: Record<string, any> = {}): Promise<any> => {
  loading.value = true;
  return new Promise<any>((resolve, reject) => {
    ProductMgAPI.getProductDetailById(data)
      .then((res: any) => {
        resolve(res);
      })
      .catch((err: any) => {
        reject(err);
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

/**
 * 处理打印功能
 * @param {RowRecord} row 当前行数据
 */
const print = ref(false);
const printRef = ref<InstanceType<typeof Print>>();
async function handlePrint(row: RowRecord): Promise<void> {
  print.value = false;
  // 获取详细数据

  try {
    const detailData = await fetchDetail({ id: row.id });
    print.value = true;
    ProductMgAPI.pickingOrderPrint({ pickListId: row.id });
    // 执行打印
    nextTick(() => {
      printRef.value?.handlePrint(detailData);
    });
  } catch (e: any) {
    ElMessage.error(e.message);
    return;
  }
}

const handleDeliveryNotice = (row: RowRecord) => {
  console.log("出库通知单", row);
  // 页面跳转或打开弹窗编辑
  loading.value = true;
  ProductMgAPI.queryDetailByDeliveryNoticeCode({
    deliveryNoticeCode: row.deliveryNoticeCode,
  })
    .then((res: any) => {
      router.push({
        path: "/wms/outboundManagement/detailOutboundNotice",
        query: { id: res.id },
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

/** 领单 */
const handleReceiveOrder = (row: RowRecord) => {
  row.btnLoading = true;
    ProductMgAPI.receiveOrCancelOrder({
      id: row.id,
      receivingStatus: 1
    })
      .then(() => {
        ElMessage.success('领单成功');
        fetchPageList();
      })
      .catch((error) => {
        console.error('领单失败', error);
        ElMessage.error('领单失败');
      })
      .finally(() => {
        row.btnLoading = false;
      });
};

/** 取消领单 */
const handleCancelReceiveOrder = (row: RowRecord) => {
  ElMessageBox.confirm('确认要取消领取该拣货单吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    row.btnLoading = true;
    ProductMgAPI.receiveOrCancelOrder({
      id: row.id,
      receivingStatus: 0
    })
      .then(() => {
        ElMessage.success('取消领单成功');
        fetchPageList();
      })
      .catch((error) => {
        console.error('取消领单失败', error);
        ElMessage.error('取消领单失败');
      })
      .finally(() => {
        row.btnLoading = false;
      });
  }).catch(() => {
    // 用户取消操作
  });
};

/** 时间转换 */
function changeDateRange(val) {
  if (val === 1) {
    // var date = moment(new Date()).format('YYYY-MM-DD')
    var date1 = moment().subtract("days", 0).format("YYYY-MM-DD");
    timeRange.value = [date1, date1];
  } else if (val === 2) {
    var date1 = moment().subtract("days", 1).format("YYYY-MM-DD");
    timeRange.value = [date1, date1];
  } else if (val === 3) {
    var endDate1 = moment(new Date()).format("YYYY-MM-DD");
    var startDate = moment().subtract("days", 6).format("YYYY-MM-DD");
    timeRange.value = [startDate, endDate1];
  } else if (val === 30) {
    // Last 30 days
    var endDate1 = moment(new Date()).format("YYYY-MM-DD");
    var startDate = moment().subtract("days", 29).format("YYYY-MM-DD");
    timeRange.value = [startDate, endDate1];
  }
  searchForm.value.queryStartTime = moment( `${timeRange.value[0]} 00:00:00`).format("YYYY-MM-DD HH:mm:ss");
  searchForm.value.queryEndTime = moment(`${timeRange.value[1]} 23:59:59`).format("YYYY-MM-DD HH:mm:ss");
  timeRange.value = [searchForm.value.queryStartTime, searchForm.value.queryEndTime];
}

// Initialize 30-day default date range
const initDefaultDateRange = () => {
  // Set query type to default (1-拣货时间, 2-创建时间)
  searchForm.value.queryType = 2;
  // Set last 30 days as default
  changeDateRange(30);
};

onMounted(() => {
  // Set default date range
  initDefaultDateRange();
  // 初始加载
  fetchPageList();
});

onActivated(() => {
  // Set default date range
  initDefaultDateRange();
  // 初始加载
  fetchPageList();
});
</script>

<template>
  <div class="contract-container">
    <el-card class="mb-12px search-card">
      <!-- Search Form -->
      <div class="search-form">
        
        <el-form :model="searchForm">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="$t('WMSPickingOrder.label.pickingListCode')" class="mb-20">
                <el-input v-model="searchForm.pickingListCode" :placeholder="$t('WMSPickingOrder.rules.pickingCodeTip')" clearable minLength="4" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('WMSPickingOrder.label.pickOrderCode')" class="mb-20">
                <el-input v-model="searchForm.billOfLadingCode" :placeholder="$t('WMSPickingOrder.rules.pickingCodeTip')" clearable minLength="4" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('WMSPickingOrder.label.status')" class="mb-20">
                <el-select v-model="searchForm.statusList" :placeholder="$t('WMSPickingOrder.placeholder.select')" clearable multiple>
                  <el-option v-for="option in statusOptions" :key="option.value" :label="option.label"
                    :value="option.value" />
                </el-select>
              </el-form-item>
            </el-col>
          
            <el-col :span="6">
              <el-form-item :label="$t('WMSPickingOrder.label.printStatus')" class="mb-20">
                <el-select v-model="searchForm.printStatus" :placeholder="$t('WMSPickingOrder.placeholder.select')" clearable>
                  <el-option :label="$t('WMSPickingOrder.label.is')" value="1" />
                  <el-option :label="$t('WMSPickingOrder.label.not')" value="0" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('WMSPickingOrder.label.deliveryNoticeCode')" class="mb-20">
                <el-input v-model="searchForm.deliveryNoticeCode" :placeholder="$t('WMSPickingOrder.rules.pickingCodeTip')" clearable minLength="4" />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item :label="'是否领单'" class="mb-20">
                <el-select
                  v-model="searchForm.receivingStatus"
                  clearable
                  placeholder="请选择是否领单"
                >
                  <el-option :label="'否'" :value="0" />
                  <el-option :label="'是'" :value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <section class="custom-flex" >
                <el-select class="time-type" v-model="searchForm.queryType" :placeholder="$t('WMSPickingOrder.placeholder.select')" clearable>
                  <el-option :label="$t('WMSPickingOrder.label.createTime')" :value="2" />
                  <el-option :label="'领用时间'" :value="3" />
                  <el-option :label="$t('WMSPickingOrder.label.pickTime')" :value="1" />
                </el-select>
                <el-date-picker :disabled="!searchForm.queryType" @change="handleTimeChange" v-model="timeRange"
                  type="datetimerange" :range-separator="$t('WMSPickingOrder.label.rangeSeparator')" :start-placeholder="$t('WMSPickingOrder.label.startTime')" :end-placeholder="$t('WMSPickingOrder.label.endTime')"
                  value-format="YYYY-MM-DD HH:mm:ss" />
                <span class="ml16px mr14px cursor-pointer" style="color: var(--el-color-primary)"
                  @click="changeDateRange(1)">
                  {{ $t("outboundNotice.label.today") }}
                </span>
                <span class="mr14px cursor-pointer" style="color: var(--el-color-primary)" @click="changeDateRange(2)">
                  {{ $t("outboundNotice.label.yesterday") }}
                </span>
                <span class="mr16px cursor-pointer" style="color: var(--el-color-primary)" @click="changeDateRange(3)">
                  {{ $t("purchaseRequirements.label.weekday") }}
                </span>
               <!--  <span class="mr16px cursor-pointer" style="color: var(--el-color-primary)" @click="changeDateRange(30)">
                  近30天
                </span> -->
              </section>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="handleSearch" v-hasPerm="['wms:pickingorder:reset']">
                  {{ $t("WMSPickingOrder.button.search") }}
                </el-button>
                <el-button @click="handleReset" v-hasPerm="['wms:pickingorder:search']">
                  {{ $t("WMSPickingOrder.button.reset") }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <el-card class="content-card">
      <!-- Action Button -->
      <div class="action-bar">
<!--        <el-button type="primary" v-hasPerm="['wms:pickingorder:print']" @click="
          (event: MouseEvent) => {
            handlePrint({ id: 0 } as RowRecord);
          }
        ">
          {{ $t("WMSPickingOrder.button.print") }}
        </el-button>-->
      </div>

      <!-- Picking Order Table -->
      <el-table :data="dataList" v-loading="loading" element-loading-text="Loading..."
        element-loading-background="rgba(255, 255, 255, 0.8)" style="width: 100%">
        <template #empty>
          <Empty />
        </template>

        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" :label="$t('WMSPickingOrder.label.sort')" width="60" align="center" fixed="left" />
        <el-table-column :label="$t('WMSPickingOrder.label.pickListCode')" prop="pickingListCode" align="center" min-width="160" fixed="left" />
        <el-table-column :label="$t('WMSPickingOrder.label.deliveryNoticeCode')"  prop="deliveryNoticeCode" align="center" min-width="160">
          <template #default="{ row }">
            <el-link type="primary" :underline="false" @click="handleDeliveryNotice(row)"
              v-if="hasAuth(['wms:pickingorder:print'])">
              {{ row.deliveryNoticeCode }}
            </el-link>
            <span v-else>{{ row.deliveryNoticeCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('WMSPickingOrder.label.pickOrderCode')" prop="billOfLadingCode" align="center" min-width="120" />
        <el-table-column :label="'计划量'" prop="billOfLadingCode" align="left" min-width="180">
          <template #default="{ row }">
            <div>
              <div><span class="text-left">个数：</span>{{ row.pickingProductCount }}</div>
              <div><span class="text-left">计划总数：</span>{{ row.pickingProductQty }}</div>
              <div><span class="text-left">计划重量(Kg)：</span>{{ row.pickingProductWeight }}</div>
            </div>
           
          </template>
        </el-table-column>
        <el-table-column :label="'实拣量'" prop="billOfLadingCode" align="left" min-width="180">
          <template #default="{ row }">
            <div>
              <div><span class="text-left">个数：</span>{{ row.status === 1 ? '' : row.pickingProductCount }}</div>
              <div><span class="text-left">实际数量：</span>{{ row.pickingProductActualCount }}</div>
              <div><span class="text-left">实际重量(Kg)：</span>{{ row.pickingProductActualWeight }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="'计划发货时间'" prop="billOfLadingCode" align="center" min-width="180">
          <template #default="{ row }">
            {{ parseTime(row.planDeliveryTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </el-table-column>
      <!--   
        <el-table-column :label="$t('WMSPickingOrder.label.productCount')" prop="pickingProductCount" align="center" width="100">
          <template #default="{ row }">
            <span class="product-count">
              {{ row.pickingProductCount }}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column :label="$t('WMSPickingOrder.label.requestedDeliveryTime')" prop="plannedReceivedTime" align="center" min-width="160">
          <template #default="{ row }">
            {{ parseTime(row.plannedReceivedTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </el-table-column>

        <el-table-column :label="'领单信息'" prop="billOfLadingCode" align="left" min-width="180">
          <template #default="{ row }">
            <div>
              <div><span class="text-left">领单人：</span>{{ row.receivingUserName }}</div>
              <div><span class="text-left">领单时间：</span>{{ parseTime(row.receivingTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('WMSPickingOrder.label.actualPicker')" prop="actualPicker" align="center" min-width="120" />
        <el-table-column :label="$t('WMSPickingOrder.label.actualPickingTime')" prop="actualPickingTime" align="center" min-width="160">
          <template #default="{ row }">
            {{ parseTime(row.actualPickingTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('WMSPickingOrder.label.printStatusStr')" prop="printStatusStr" align="center" width="100" />
        <el-table-column :label="$t('WMSPickingOrder.label.printCount')" prop="printCount" align="center" width="100" />
        <el-table-column :label="$t('WMSPickingOrder.label.pickingType')" prop="pickingType" align="center" width="100">
          <template #default="{ row }">
            {{ row.pickingType === 1 ? $t('WMSPickingOrder.label.pickByOrder') : '--' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('WMSPickingOrder.label.createUserName')" prop="createUserName" align="center" width="100" />
        <el-table-column :label="$t('WMSPickingOrder.label.createTime')" prop="createTime" align="center" width="160">
          <template #default="{ row }">
            {{ parseTime(row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('WMSPickingOrder.label.statusStr')" prop="statusStr" align="center" width="120">
          <template #default="{ row }">
            <span class="custom-status status" :class="statusClass(row.status)">
              {{ row.statusStr }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('WMSPickingOrder.label.operation')" align="left" min-width="250" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleDetail(row)" v-hasPerm="['wms:pickingorder:detail']">
              {{$t('WMSPickingOrder.button.detail')}}
            </el-button>
            <el-button v-if="printBtnShow(row)" link type="primary" @click="handlePrint(row)"
              v-hasPerm="['wms:pickingorder:print']">
              {{$t('WMSPickingOrder.button.print')}}
            </el-button>
            <el-button v-if="pickBtnShow(row)" link type="primary" @click="handleComplete(row)"
              v-hasPerm="['wms:pickingorder:pick']">
              {{$t('WMSPickingOrder.button.pick')}}
            </el-button>

            <!-- 领单按钮 -->
            <el-button
              v-if="(row.receivingStatus === 0 || row.receivingStatus === undefined) && row.status === 1"
              type="primary"
              v-hasPerm="['wms:pickingorder:receive:order']"
              link
              @click="handleReceiveOrder(row)"
              :loading="row.btnLoading"
            >
              领单
            </el-button>

            <!-- 取消领单按钮 -->
            <el-button
            v-hasPerm="['wms:pickingorder:cancel:order']"
              v-if="row.receivingStatus === 1 && row.status === 1"
              type="primary"
              link
              @click="handleCancelReceiveOrder(row)"
              :loading="row.btnLoading"
            >
              取消领单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- Pagination -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
          :page-sizes="[20, 50, 100, 200]" layout="total, sizes , prev, pager, next, jumper"
          @size-change="fetchPageList" @current-change="handlePageChange" />
      </div>
    </el-card>
    <Print ref="printRef" v-if="print" class="display-none" />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-button--primary.el-button--default.is-link) {
  color: #762adb;
}

:deep(.el-button--danger.el-button--default.is-link) {
  color: #c00c1d;
}

.contract-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    flex-shrink: 0;
  }

  .content-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .action-bar {
      margin-bottom: 12px;
      flex-shrink: 0;
    }

    .el-table {
      flex: 1;
      overflow: auto;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.el-form-item--default) {
  // 表单项样式设置
  margin-bottom: 0;
}

.custom-flex {
  display: flex;
  align-items: center;
  .time-type {
    width: 100px;
  }
}

.product-count {
  color: #762ADB;
}
</style>
