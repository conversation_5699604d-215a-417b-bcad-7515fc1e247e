<template>
  <div class="app-container">
    <div class="outboundNoticeDetail" v-loading="loading">
      <div class="page-title">
        <div class="purchase-title">
          <div @click="handleClose()" class="cursor-pointer mr8px">
            <el-icon><Back /></el-icon>
          </div>
          <div>
            {{ t("outboundNotice.label.outboundNoticeCode") }}：{{
              form.outboundNoticeCode
            }}
          </div>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" label-width="145px" label-position="right">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("outboundNotice.label.basicInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <!-- 出库通知单号 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.outboundNoticeCode')"
                >
                  {{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}
                </el-form-item>
              </el-col>
              <!-- 提货单号 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.sourceOrderCode')"
                >
                  {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 出库类型:1:采购出库、2:退货出库 -->
                <el-form-item :label="$t('outboundNotice.label.outboundType')">
                  <span v-if="form.outboundType == 1">
                    {{
                      t("outboundNotice.outboundTypeList.procurementOutbound")
                    }}
                  </span>
                  <span v-else-if="form.outboundType == 2">
                    {{ t("outboundNotice.outboundTypeList.returnOutbound") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 	配送类型:1:配送、2:自提 -->
                <el-form-item :label="$t('outboundNotice.label.deliveryType')">
                  <span v-if="form.deliveryType == 1">
                    {{ t("outboundNotice.label.delivery") }}
                  </span>
                  <span v-else-if="form.deliveryType == 2">
                    {{ t("outboundNotice.label.selfPickup") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 客户下单时间 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.orderCreateTime')"
                >
                  <span v-if="form.orderCreateTime">
                    {{
                      parseTime(form.orderCreateTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 计划发货时间 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.plannedDeliveryTime')"
                >
                  <span v-if="form.plannedDeliveryTime">
                    {{
                      parseTime(
                        form.plannedDeliveryTime,
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 要求到货时间 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.plannedReceivedTime')"
                >
                  <span v-if="form.plannedReceivedTime">
                    {{
                      parseTime(
                        form.plannedReceivedTime,
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 采购/销售员 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.purchaseSalesPerson')"
                >
                  {{
                    form.purchaseSalesPerson ? form.purchaseSalesPerson : "-"
                  }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 客户、供应商 -->
              <el-col :span="6">
                <span v-if="form.outboundType == 1">
                  <el-form-item
                    :label="$t('outboundNotice.label.customerName')"
                  >
                    {{ form.customerName ? form.customerName : "-" }}
                  </el-form-item>
                </span>
                <span v-else>
                  <el-form-item
                    :label="$t('outboundNotice.label.supplierName')"
                  >
                    {{ form.supplierName ? form.supplierName : "-" }}
                  </el-form-item>
                </span>
              </el-col>
              <el-col :span="6">
                <!-- 联系人 -->
                <el-form-item :label="$t('outboundNotice.label.contactPerson')">
                  <!-- <EncryptPhone
                    :nameType="true"
                    :name="form.contactPerson"
                    :hasPermEye="['wms:outboundManagement:outboundNotice:eye']"
                  /> -->
                  {{
                    form.nameShow
                      ? form.contactPerson
                      : encryptName(form.contactPerson)
                  }}
                  <el-icon
                    v-if="form.contactPerson"
                    @click="form.contactPerson ? getRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:outboundNotice:eye']"
                  >
                    <component :is="form.nameShow ? '' : 'View'" />
                  </el-icon>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 联系电话 -->
                <el-form-item
                  :label="$t('outboundNotice.label.customerMobile')"
                >
                  <span class="encryptBox">
                    <span v-if="form.customerMobile">
                      {{ form.customerAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.customerMobile && form.customerMobile.length <= 4
                      "
                    >
                      {{ form.customerMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.customerMobile && form.customerMobile.length > 4
                      "
                    >
                      {{ form.customerMobile }}
                      <el-icon
                        v-if="form.customerMobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:outboundNotice:eye',
                        ]"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <!-- 地址 -->
                <el-form-item :label="$t('outboundNotice.label.address')">
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:outboundNotice:eye',
                        ]"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <!-- 审核人-->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.approveUserName')"
                >
                  {{ form.approveUserName ? form.approveUserName : "-" }}
                </el-form-item>
              </el-col>
              <!-- 审核时间 -->
              <el-col :span="6">
                <el-form-item :label="$t('outboundNotice.label.approveTime')">
                  <span v-if="form.approveTime">
                    {{ parseTime(form.approveTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 审核状态 -->
              <el-col :span="6">
                <el-form-item :label="$t('outboundNotice.label.approveStatus')">
                  <span v-if="form.approveStatus == 0">
                    {{ t("outboundNotice.approveStatusList.Unaudited") }}
                  </span>
                  <span v-else-if="form.approveStatus == 1">
                    {{ t("outboundNotice.approveStatusList.success") }}
                  </span>
                  <span v-else-if="form.approveStatus == 2">
                    {{ t("outboundNotice.approveStatusList.fail") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 创建人 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.createUserName')"
                >
                  {{ form.createUserName ? form.createUserName : "-" }}
                </el-form-item>
              </el-col>
              <!-- 创建时间 -->
              <el-col :span="6">
                <el-form-item :label="$t('outboundNotice.label.createTime')">
                  <span v-if="form.createTime">
                    {{ parseTime(form.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 提交人-->
              <el-col :span="6">
                <el-form-item :label="$t('outboundNotice.label.presenter')">
                  {{ form.submitUserName ? form.submitUserName : "-" }}
                </el-form-item>
              </el-col>
              <!-- 提交时间-->
              <el-col :span="6">
                <el-form-item
                  :label="$t('outboundNotice.label.submissionTime')"
                >
                  <span v-if="form.submitTime">
                    {{ parseTime(form.submitTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('outboundNotice.label.totalPlanProductQty')">
                  {{ form.totalPlanProductQty ? form.totalPlanProductQty : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('outboundNotice.label.totalPlanProductWeight')">
                  {{ form.totalPlanProductWeight ? form.totalPlanProductWeight : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('outboundNotice.label.remark')">
                  <span style="word-break: break-all">{{ form.remark ? form.remark : "-" }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("outboundNotice.label.goodsDetails") }}
            </div>
          </div>
          <div>
            <el-table
              ref="tableSumRef1"
              :data="form.warehouseOutboundDetailVOList"
              highlight-current-row
              stripe
            >
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
                align="center"
              />
              <el-table-column
                :label="$t('outboundNotice.label.goodsInfor')"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="product-code">
                    {{ $t("outboundNotice.label.productCode") }}：
                    {{ scope.row.productCode }}
                  </div>
                  <div class="product-name">
                    {{ scope.row.productName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('outboundNotice.label.goodsCategory')"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.firstCategoryName }}/
                  {{ scope.row.secondCategoryName }}/
                  {{ scope.row.thirdCategoryName }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('outboundNotice.label.productSpecs')"
                prop="productSpecs"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('outboundNotice.label.remark')"
                prop="remark"
                show-overflow-tooltip
              />
              <el-table-column
                :label="$t('outboundNotice.label.productPlanQty')"
                prop="planProductQty"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span v-if="scope.row.planProductQty">{{ scope.row.planProductQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('outboundNotice.label.productPlanWeight')"
                prop="planProductWeight"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span v-if="scope.row.planProductWeight">{{ scope.row.planProductWeight }}kg</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('outboundNotice.label.productMiniWeight')"
                prop="productMiniWeight"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span v-if="scope.row.productMiniWeight">{{ scope.row.productMiniWeight }}kg</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">
          {{ $t("common.reback") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "DetailOutboundNotice",
  inheritAttrs: false,
});
import OutboundNoticeAPI, {
  addFormData,
} from "@/modules/wms/api/outboundNotice";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import { parseTime } from "@/core/utils/index.js";

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const loading = ref(false);
const tableSumRef1 = ref();
const form = reactive<addFormData>({
  warehouseOutboundDetailVOList: [],
});

async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}

/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 获取真实电话号码
function getRealName() {
  form.nameShow = true;
}

/** 查询采购单详情 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: route.query.id,
  };
  OutboundNoticeAPI.queryDetailMobileEncrypt(params)
    .then((data: any) => {
      Object.assign(form, data);
      form.mobilePhoneShow = true;
      form.fullAddress = ` ${form.countryName}${form.provinceName}${form.cityName}${form.districtName}${form.address}`;
      if (form.contactPerson?.length > 1) {
        form.nameShow = false;
      } else {
        form.nameShow = true;
      }

      // 地址包含数字
      if (form.fullAddress && containsNumber(form.fullAddress)) {
        form.addressShow = true;
        form.addressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      } else {
        // 不包含数字
        form.addressShow = false;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

// 获取真实电话号码
function getRealPhone() {
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.customerMobile = data.mobile;
      form.mobilePhoneShow = false;
    })
    .finally(() => {});
}

onMounted(() => {
  queryDetail();
});
</script>
<style scoped lang="scss">
.outboundNoticeDetail {
  background: #ffffff;
  border-radius: 4px;
}
</style>
<style lang="scss">
.outboundNoticeDetail {
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .cursor-pointer {
    cursor: pointer;
  }
}
</style>
