<template>
  <PrintTemplate ref="printRef">
    <div class="print-container">
      <div v-for="(list,index) in printData.outboundPickingListVOList" style="border:1px solid #D9DCEB;margin-bottom: 10px;padding: 0px 10px;">
        <div style="display: flex;justify-content: space-between;">
          <div>编号：{{list.pickingListCode}}</div>
          <div class="barcode">
            <Barcode :value="printData.outboundNoticeCode" :options="barcodeOptions"/>
          </div>
        </div>
        <div class="print-header">
          <h1 class="print-title">{{ printData.title }}</h1>
        </div>
        <div class="print-info">
          <div class="grad-row-print">
            <!-- 客户 -->
            <div class="el-form-item--div">
              <span class="el-form-item__label">{{$t('quickOutbound.label.customerName')}}：</span>
              <span class="el-form-item__content">{{printData.customerName}}</span>
            </div>
            <!-- 业务员 -->
            <div class="el-form-item--div">
              <span class="el-form-item__label">{{$t('quickOutbound.label.salesperson')}}：</span>
              <span class="el-form-item__content"> {{printData.purchaseSalesPerson}}</span>
            </div>
            <!-- 合同编码 -->
            <div class="el-form-item--div">
              <span class="el-form-item__label">{{$t('quickOutbound.label.contractCode')}}：</span>
              <span class="el-form-item__content"> {{printData.contractCode}}</span>
            </div>
          </div>
          <div class="grad-row-print">
            <!-- 出库通知单号 -->
            <div class="el-form-item--div">
              <span class="el-form-item__label">{{$t('quickOutbound.label.outboundNoticeCode')}}：</span>
              <span class="el-form-item__content">{{printData.outboundNoticeCode}}</span>
            </div>
            <!-- 结算方式 -->
            <div class="el-form-item--div">
              <span class="el-form-item__label">{{$t('quickOutbound.label.settlementMethod')}}：</span>
              <span class="el-form-item__content"> {{printData.paymentType}}</span>
            </div>
            <!-- 合同分类 -->
            <div class="el-form-item--div">
              <span class="el-form-item__label">{{$t('quickOutbound.label.contractClassification')}}：</span>
              <span class="el-form-item__content"> {{printData.contractType}}</span>
            </div>
          </div>
        </div>
        <div class="print-table-container">
          <table class="print-table">
            <thead>
            <tr>
              <th style="width: 180px">{{ $t("quickOutbound.label.productName") }}</th>
              <th style="width: 40px">{{ $t("quickOutbound.label.unit") }}</th>
              <th>{{ $t("quickOutbound.label.quantity") }}</th>
              <th>{{ $t("quickOutbound.label.unitPriceNoUnit")}}</th>
              <th>{{ $t("quickOutbound.label.amountNoUnit") }}</th>
              <th>{{ $t("quickOutbound.label.remark") }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item, index) in list.warehouseOutboundNotice" :key="index">
              <td style="width: 180px">
                <div style="display: flex;align-items: center;justify-content: flex-start">
                  <div class="barcode">
                    <Barcode :value="item.productCode" :options="productCodeBarcodeOptions"/>
                  </div>
                  {{ item.productName }}
                </div>
              </td>
              <td style="width: 40px">{{ item.productUnitName }}</td>
              <td>{{ item.actualPickThisTime }}</td>
              <td>{{ item.salePrice }}</td>
              <td>{{ item.saleAmount }}</td>
              <td>{{ item.remark }}</td>
            </tr>
            <tr>
              <td colspan="2" style="text-align: left !important;">合计金额：{{list.totalSaleAmount}}</td>
              <td colspan="3" style="text-align: left !important;">总数量：{{list.pickingProductActualCount}}</td>
              <td style="text-align: left !important;">优惠：{{list.totalDiscountAmount}}</td>
            </tr>
            <tr>
              <td colspan="1" style="text-align: left !important;">收货人：{{printData.contactPerson}}</td>
              <td colspan="2" style="text-align: left !important;">电话：{{printData.customerMobile}}</td>
              <td colspan="3" style="text-align: left !important;">收货地址：{{printData.addressFormat}}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="print-info">
          <div class="info-row">
            <div class="info-item-4">
            <span class="info-label" style="margin-left: 15px">
              {{ $t("quickOutbound.label.createOrder") }}：
            </span>
              <span class="info-value">{{ printData.purchaseSalesPerson}}</span>
            </div>
            <div class="info-item-4">
            <span class="info-label">
              {{ $t("quickOutbound.label.outboundUserName") }}：
            </span>
              <span class="info-value">{{ list.outboundUserName}}</span>
            </div>
            <div class="info-item-4">
            <span class="info-label">
              {{ $t("quickOutbound.label.sendPerson") }}：
            </span>
              <span class="info-value">{{ printData.sendPerson }}</span>
            </div>
            <div class="info-item-4">
            <span class="info-label">
              {{ $t("quickOutbound.label.extractPerson") }}：
            </span>
              <span class="info-value">{{ printData.extractPerson }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PrintTemplate>
</template>

<script setup lang="ts">
import { ref } from "vue";
import PrintTemplate from "@/core/components/Print/PrintTemplate.vue";
const { t } = useI18n();

const barcodeOptions = ref({
  format: "CODE128", // 条形码格式
  width: 1, // 条形码宽度
  height: 40, // 条形码高度
  displayValue: true, // 是否显示条形码下方的文本
});
const productCodeBarcodeOptions = ref({
  format: "CODE128", // 条形码格式
  width: 1, // 条形码宽度
  height: 22, // 条形码高度
  displayValue: false, // 是否显示条形码下方的文本
});

const printData = ref<any>({});
const printRef = ref<InstanceType<typeof PrintTemplate>>();
// 暴露打印方法给父组件
const handlePrint = (data: any) => {
  printData.value = data;
  nextTick(() => {
    printRef.value?.print();
  });
};

defineExpose({
  handlePrint,
});
</script>

<style lang="scss">
/* 基础样式 - 最小化但保留结构 */
.print-container {
}

/* 打印媒体查询样式 */
@media print {
  .print-container {
    display: block;
    width: calc(100vw - 40px) !important; /* 使用!important确保优先级 */
    max-width: 100% !important; /* 添加最大宽度限制 */
    height: calc(100vh - 40px) !important;
    margin: 20px !important; /* 确保没有外边距 */
    padding: 20px;
    box-sizing: border-box;
    font-family: SimSun, "宋体", Arial, sans-serif;
    color: black;
    /*border: 1px solid #000;*/
    position: fixed !important; /* 使用fixed定位 */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important; /* 确保右侧贴合 */
  }

  /* 确保打印时页面没有默认边距 */
  @page {
    margin: 0;
    size: auto;
  }

  body {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 标题样式 */
  .print-header {
    text-align: center;
    margin-bottom: 15px;

    .print-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #151719 !important;
      line-height: 32px;
      font-style: normal;
    }
  }

  /* 信息区域样式 */
  .print-info {
    margin-bottom: 15px;

    .barcode {
      text-align: center;
      margin-bottom: 16px;
      .barcode_text {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #151719;
        line-height: 32px;
        font-style: normal;
      }
    }

    .info-row {
      display: flex;
      margin-bottom: 8px;
      flex-wrap: wrap;
      page-break-inside: avoid;

      .info-item-4 {
        width: 25% !important;
        display: flex;
        margin-bottom: 5px;

        .info-label {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #90979E !important;
          line-height: 26px;
          text-align: left;
          font-style: normal;
        }

        .info-value {
          flex: 1;
          padding-right: 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #151719 !important;
          line-height: 26px;
          text-align: left;
          font-style: normal;
        }
      }
      .info-item-2 {
        width: 50% !important;
        display: flex;
        margin-bottom: 5px;

        .info-label {
          width: 85px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #90979E !important;
          line-height: 26px;
          text-align: left;
          font-style: normal;
        }

        .info-value {
          flex: 1;
          word-wrap: break-word; /* 允许长单词换行 */
          overflow-wrap: break-word; /* 更标准的写法 */
          white-space: normal; /* 默认换行行为 */
          word-break: break-all; /* 强制所有字符换行 */
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #151719 !important;
          line-height: 26px;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .grad-row-print {
      display: flex;
      justify-content: flex-start;
      .el-form-item--div{
        width: 33%;
      }
      .el-form-item--div100{
        width: 100%;
      }
      .el-form-item__label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #90979E;
        font-style: normal;
      }
      .el-form-item__content {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 12px;
        color: #151719;
        font-style: normal;
        word-break:break-word;
      }
    }
  }

  /* 表格样式 */
  .print-table-container {
    margin-bottom: 60px;

    .print-table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
      page-break-inside: avoid;

      th,
      td {
        border: 1px solid black;
        padding: 5px;
        text-align: center;
        font-size: 10pt;
      }

      th {
        // background-color: #e0e0e0;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      tfoot td {
        font-weight: bold;
      }

      .text-right {
        text-align: right;
      }
    }
  }

  .footerStyle {
    position: fixed;
    bottom: 0;
    width: 100%;
  }
}
</style>
