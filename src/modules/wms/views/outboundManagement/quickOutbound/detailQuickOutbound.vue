<template>
  <div class="app-container">
    <div class="outboundNoticeDetail" v-loading="loading">
      <div class="page-content">
        <el-form :model="form" label-width="145px" label-position="right">
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("quickOutbound.label.documentInformation") }}
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <!-- 出库通知单号 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.outboundNoticeCode')">
                  {{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 出库类型:1:销售出库、2:提货卡兑换、3：业务招待、4：补发、5：地头售卖、6:采购退货、7：调拨出库 -->
                <el-form-item :label="$t('quickOutbound.label.outboundType')">
                  <span v-if="form.outboundType == 1">{{ $t("quickOutbound.outboundTypeList.procurementOutbound") }}</span>
                  <span v-else-if="form.outboundType == 2">{{ $t("quickOutbound.outboundTypeList.pickupCardRedemption") }}</span>
                  <span v-else-if="form.outboundType == 3">{{ $t("quickOutbound.outboundTypeList.businessReception") }}</span>
                  <span v-else-if="form.outboundType == 4">{{ $t("quickOutbound.outboundTypeList.reissue") }}</span>
                  <span v-else-if="form.outboundType == 5">{{ $t("quickOutbound.outboundTypeList.sellAtTheFieldEdge") }}</span>
                  <span v-else-if="form.outboundType == 6">{{ $t("quickOutbound.outboundTypeList.returnOutbound") }}</span>
                  <span v-else-if="form.outboundType == 7">{{ $t("quickOutbound.outboundTypeList.allotOutbound") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 主题描述 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.themeDescription')">
                  {{ form.orderTheme ? form.orderTheme : "-" }}
                </el-form-item>
              </el-col>
              <!-- 来源单号 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.sourceOrderCode')">
                  {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 业务员 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.salesperson')">
                  {{ form.purchaseSalesPerson ? form.purchaseSalesPerson : "-" }}
                </el-form-item>
              </el-col>
              <!-- 原单号 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.originalOrderNumber')">
                  {{ form.sourceCode ? form.sourceCode : "-" }}
                </el-form-item>
              </el-col>
              <!-- 申请人 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('quickOutbound.label.createUserName')"
                >
                  {{ form.createUserName ? form.createUserName : "-" }}
                </el-form-item>
              </el-col>
              <!-- 申请时间 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.createTime')">
                  <span v-if="form.createTime">
                    {{ parseTime(form.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 客户 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.customerName')">
                  {{ form.customerName ? form.customerName : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户地址 -->
                <el-form-item :label="$t('quickOutbound.label.customerAddress')">
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户联系人 -->
                <el-form-item :label="$t('quickOutbound.label.customerPerson')">
                  {{ form.nameShow ? (form.contactPerson ? form.contactPerson : '-') : encryptName(form.contactPerson) }}
                  <el-icon
                    v-if="form.contactPerson"
                    @click="form.contactPerson ? getRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.nameShow ? '' : 'View'" />
                  </el-icon>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户联系电话 -->
                <el-form-item :label="$t('quickOutbound.label.customerMobile')">
                  <span class="encryptBox">
                    <span v-if="form.customerMobile">
                      {{ form.customerAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.customerMobile && form.customerMobile.length <= 4
                      "
                    >
                      {{ form.customerMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.customerMobile && form.customerMobile.length > 4
                      "
                    >
                      {{ form.customerMobile }}
                      <el-icon
                        v-if="form.customerMobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 供应商 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.supplierName')" >
                  {{ form.supplierName ? form.supplierName : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商地址 -->
                <el-form-item :label="$t('quickOutbound.label.supplierAddress')">
                  <span class="encryptBox">
                    <span v-if="form.supplierAddressShow">
                      {{ form.supplierAddressFormat }}
                      <el-icon
                        v-if="form.supplierFullAddress"
                        @click="form.supplierAddressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierAddressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.supplierFullAddress ? form.supplierFullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商联系人 -->
                <el-form-item :label="$t('quickOutbound.label.supplierPerson')">
                  {{ form.supplierNameShow ? (form.supplierContactPerson ? form.supplierContactPerson : '-') : encryptName(form.supplierContactPerson) }}
                  <el-icon
                    v-if="form.supplierContactPerson"
                    @click="form.supplierContactPerson ? getSupplierRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.supplierNameShow ? '' : 'View'" />
                  </el-icon>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商联系电话 -->
                <el-form-item :label="$t('quickOutbound.label.supplierMobile')">
                  <span class="encryptBox">
                    <span v-if="form.supplierContactMobile">
                      {{ form.supplierContactAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.supplierContactMobile && form.supplierContactMobile.length <= 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.supplierContactMobile && form.supplierContactMobile.length > 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                      <el-icon
                        v-if="form.supplierContactMobile"
                        @click="form.supplierMobilePhoneShow ? getSupplierRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierMobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 合同名称-->
              <el-col :span="6">
<!--                cursor:pointer-->
                <el-form-item :label="$t('quickOutbound.label.contractName')" >
                  <span style="color: #762ADB;" v-if="form.contractName">{{form.contractName}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 合同编码-->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractCode')" >
                  <span style="color: #762ADB;" v-if="form.contractCode">{{form.contractCode}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 合同分类-->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractClassification')" >
                  <span v-if="form.contractType == 1">销售合同</span>
                  <span v-else-if="form.contractType == 2">采购合同</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 结算方式-->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.settlementMethod')" >
                  {{ form.paymentType == 1 ? $t('quickOutbound.label.presentSettlement') : form.paymentType == 2 ? $t('quickOutbound.label.accountHanging') : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 要求到货时间 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.plannedReceivedTime')">
                  <span v-if="form.plannedReceivedTime">{{ parseTime(form.plannedReceivedTime,"{y}-{m}-{d} {h}:{i}:{s}") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 计划发货时间 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.plannedDeliveryTime')">
                  <span v-if="form.plannedDeliveryTime">{{ parseTime(form.plannedDeliveryTime,"{y}-{m}-{d} {h}:{i}:{s}") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 计划配送方式 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.plannedDistributionMethod')">
                  {{form.deliveryName ? form.deliveryName : "-"}}
                </el-form-item>
              </el-col>
              <!-- 来源单备注 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.sourceOrderRemark')">
                  {{form.remark ? form.remark : "-"}}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("quickOutbound.label.detailList") }}
            </div>
          </div>
          <div class="panelContent">
            <div class="panelItem" @click="changePanel(item)" :class="{ active: item.active }" v-for="(item, index) in tabs" :key="item.key">
              {{ item.value }}
            </div>
          </div>
          <div v-if="checkType == 1">
            <el-table show-summary :summary-method="getSummaries" :data="form.warehouseOutboundDetailVOList" highlight-current-row stripe>
              <el-table-column fixed="left" type="index" :label="$t('common.sort')" width="60" align="center"/>
              <el-table-column :label="$t('quickOutbound.label.productInfo')" min-width="150" show-overflow-tooltip>
                <template #default="scope">
                  <div><span style="color: #90979E;">{{ scope.row.productCode }} | </span><span style="color:#52585F">{{ scope.row.productName }}</span></div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.goodsCategory')" min-width="150" show-overflow-tooltip>
                <template #default="scope">{{ scope.row.firstCategoryName }}/ {{ scope.row.secondCategoryName }}/ {{ scope.row.thirdCategoryName }}</template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.productSpecs')" prop="productSpecs" show-overflow-tooltip/>
              <el-table-column :label="$t('quickOutbound.label.commodityProperty')" prop="attributeTypeName" show-overflow-tooltip/>
              <el-table-column :label="$t('quickOutbound.label.returnVolume')" min-width="120" prop="returnQty" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.returnQty">{{ scope.row.returnQty }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.salesUnitPrice')" min-width="120" align="right" prop="salePrice" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.salePrice">{{ scope.row.salePrice }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.salesAmount')" min-width="120" align="right" prop="saleAmount" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.saleAmount">{{ scope.row.saleAmount }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.plannedQuantity')" min-width="120" prop="planProductQty" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.planProductQty">{{ scope.row.planProductQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.plannedConversionQuantity')" min-width="150" prop="planProductWeight" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.planProductWeight">{{ scope.row.planProductWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.outboundQuantity')" min-width="120" prop="alreadyOutboundQty" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.alreadyOutboundQty">{{ scope.row.alreadyOutboundQty }}{{ scope.row.productUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('quickOutbound.label.outboundConversionQuantity')" min-width="150" prop="alreadyOutboundWeight" show-overflow-tooltip>
                <template #default="scope">
                  <span v-if="scope.row.alreadyOutboundWeight">{{ scope.row.alreadyOutboundWeight }}{{ scope.row.conversionRelSecondUnitName }}</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="table-container">
            <!--<div class="flex-center-start title-div">
              <div style="width: 5%;" class="text-center">{{$t('common.sort')}}</div>
              <div style="width: 10%">{{$t('quickOutbound.label.productInfo')}}</div>
              <div style="width: 15%">{{$t('quickOutbound.label.goodsCategory')}}</div>
              <div style="width: 10%">{{$t('quickOutbound.label.productSpecs')}}</div>
              <div style="width: 10%" class="text-right">{{$t('quickOutbound.label.salesUnitPrice')}}</div>
              <div style="width: 10%" class="text-right">{{$t('quickOutbound.label.salesAmount')}}</div>
              <div style="width: 10%" class="text-right">{{$t('quickOutbound.label.outboundQuantity')}}</div>
              <div style="width: 10%" class="text-right">{{$t('quickOutbound.label.outboundConversionQuantity')}}</div>
              <div style="width: 10%" class="text-center">{{$t('quickOutbound.label.outboundStorageArea')}}</div>
              <div style="width: 10%" class="text-center">{{$t('quickOutbound.label.goodsPackaging')}}</div>
            </div>
            <div v-for="(item,index) in form.outboundPickingListVOList" :key="index" style="margin-top: 10px">
              <div class="flex-center-start title-div" style="background: #F6F0FF !important;font-weight: 400 !important;">
                <div style="width: 5%" class="text-center"><span style="width:28px;height:28px;line-height:16px;color: #FFFFFF;background-color: #762ADB;border-radius: 4px;padding: 6px">{{index < 9 ? '0' + (index+1) : (index+1)}}</span></div>
                <div style="width: 10%;">{{$t('quickOutbound.label.pickingList')}}：{{item.pickingListCode}}</div>
                <div style="width: 10%">{{$t('quickOutbound.label.actualDistributionMethod')}}：{{item.deliveryName}}</div>
                <div style="width: 15%">{{$t('quickOutbound.label.actualOutboundTime')}}：{{item.outboundTime ? parseTime(item.outboundTime, "{y}-{m}-{d} {h}:{i}:{s}"):'-'}}</div>
                <div style="width: 10%">{{$t('quickOutbound.label.carrier')}}：{{item.carrier}}</div>
                <div style="width: 10%"></div>
                <div style="width: 10%;">{{$t('quickOutbound.label.vehicleNumber')}}：{{item.carNumber}}</div>
                <div style="width: 10%">{{$t('quickOutbound.label.weighbillNumber')}}：{{item.poundCode}}</div>
                <div style="width: 10%">{{$t('quickOutbound.label.outboundRemark')}}：{{item.remark}}</div>
                <div style="width: 10%;color: #762ADB" class="text-center">
                  <div v-if="item.poundAttachmentFiles">
                    <el-badge
                      :value="JSON.parse(item.poundAttachmentFiles).length"
                      :offset="[10, 8]"
                      class="item"
                      type="primary"
                    >
                      <el-button
                        type="primary"
                        link
                        @click="openAttachment(item.poundAttachmentFiles)"
                      >
                        {{ $t("quickOutbound.label.attachment") }}
                      </el-button>
                    </el-badge>
                  </div>
                </div>
              </div>
              <div v-for="(data,index1) in item.outboundPickingListProductVOList" :key="index1" style="border-bottom: 1px solid #E5E7F3;">
                <div class="flex-center-start data-div">
                  <div style="width:5%" class="text-center">{{ index1 + 1 }}</div>
                  <div style="width: 10%">{{data.productCode}} | <span style="color:#52585F">{{data.productName}}</span></div>
                  <div style="width: 15%">{{data.firstCategoryName}}/{{data.secondCategoryName}}/{{data.thirdCategoryName}}</div>
                  <div style="width: 10%">{{data.productSpecs}}</div>
                  <div style="width: 10%" class="text-right">{{data.salePrice}}</div>
                  <div style="width: 10%" class="text-right">{{data.saleAmount}}</div>
                  <div style="width: 10%" class="text-right">{{data.actualPickThisTime}}</div>
                  <div style="width: 10%" class="text-right">{{data.warehouseAreaActualPickWeight}}</div>
                  <div style="width: 10%" class="text-center">{{data.warehouseAreaName}}|{{data.warehouseAreaCode}}</div>
                  <div style="width: 10%" class="text-center">{{data.productPackage}}</div>
                </div>
              </div>
              <div class="flex-center-start title-div">
                <div style="width: 50%"></div>
                <div style="width: 10%;display: flex;justify-content: space-between">合计：<div>{{item.totalSaleAmount}}</div></div>
                <div style="width: 10%" class="text-right">{{item.pickingProductActualCount}}</div>
                <div style="width: 10%" class="text-right">{{item.pickingProductActualWeight}}</div>
                <div style="width: 20%"></div>
              </div>
            </div>-->
            <el-table
              :data="form.outboundPickingListVOList"
              :span-method="spanMethod"
              default-expand-all
              row-class-name="table-row"
              style="border: none; --el-table-border-color: none"
            >
              <template #empty>
                <Empty />
              </template>

              <el-table-column
                type="expand"
                width="1"
              >
                <template #default="props">
                  <el-table
                    :data="props.row.outboundPickingListProductVOList"
                    class="table-children"
                    :show-header="false"
                    :cell-style="cellStyleChildren"
                    show-summary
                    :summary-method="getSummaries1"
                  >
                    <el-table-column
                      type="index"
                      :label="$t('common.sort')"
                      width="60"
                      prop="index"
                    >
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.productInfo')"
                      prop="productName"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ scope.row.productCode }} |{{ scope.row.productName }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.goodsCategory')"
                      prop="firstCategoryName"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{ scope.row.firstCategoryName }} /
                        {{ scope.row.secondCategoryName }} /
                        {{ scope.row.thirdCategoryName }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.productSpecs')"
                      prop="productSpecs"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      v-if="form.outboundType == 7"
                      :label="$t('quickOutbound.label.salesUnitPrice')"
                      prop="salePrice"
                      align="right"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      v-if="form.outboundType == 7"
                      :label="$t('quickOutbound.label.salesAmount')"
                      prop="saleAmount"
                      align="right"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.outboundQuantity')"
                      prop="actualPickThisTime"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{scope.row.actualPickThisTime}}{{scope.row.productUnitName}}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.outboundConversionQuantity')"
                      prop="warehouseAreaActualPickWeight"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{scope.row.warehouseAreaActualPickWeight}}{{scope.row.conversionRelSecondUnitName}}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.outboundStorageArea')"
                      prop="warehouseAreaCode"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        {{scope.row.warehouseAreaName}}|{{scope.row.warehouseAreaCode}}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('quickOutbound.label.goodsPackaging')"
                      prop="productPackage"
                      show-overflow-tooltip
                    ></el-table-column>
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column
                type="index"
                :label="$t('common.sort')"
                width="60"
              >
                <template #default="scope">
                  <div class="table-header">
                    <div class="sort">
                      {{
                      scope.$index >= 9
                      ? scope.$index + 1
                      : "0" + (scope.$index + 1)
                      }}
                    </div>
                    <div class="column">
                      {{ $t("quickOutbound.label.pickingList") }}:
                      {{ scope.row.pickingListCode ? scope.row.pickingListCode : '-' }}
                    </div>
                    <div class="column">
                      {{ $t("quickOutbound.label.actualDistributionMethod") }}:
                      {{ scope.row.deliveryName ? scope.row.deliveryName : '-' }}
                    </div>
                    <div class="column">
                      {{ $t("quickOutbound.label.actualOutboundTime") }}:
                      {{scope.row.outboundTime ? parseTime(scope.row.outboundTime, "{y}-{m}-{d} {h}:{i}:{s}"):'-'}}
                    </div>
                    <div class="column">
                      {{$t('quickOutbound.label.carrier')}}:
                      {{ scope.row.carrier ? scope.row.carrier : '-' }}
                    </div>
                    <div class="column" style="margin-left: 20%">
                      {{$t('quickOutbound.label.vehicleNumber')}}:
                      {{ scope.row.carNumber ? scope.row.carNumber : '-' }}
                    </div>
                    <div class="column">
                      {{$t('quickOutbound.label.weighbillNumber')}}:
                      {{ scope.row.poundCode ? scope.row.poundCode : '-' }}
                    </div><div class="column">
                      {{$t('quickOutbound.label.outboundRemark')}}:
                      {{ scope.row.remark ? scope.row.remark : '-' }}
                    </div>
                    <div class="column">
                      <div v-if="scope.row.poundAttachmentFiles">
                        <el-badge
                          :value="JSON.parse(scope.row.poundAttachmentFiles).length"
                          :offset="[10, 8]"
                          class="item"
                          type="primary"
                        >
                          <el-button
                            type="primary"
                            link
                            @click="openAttachment(scope.row.poundAttachmentFiles)"
                          >
                            {{ $t("quickOutbound.label.attachment") }}
                          </el-button>
                        </el-badge>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.productInfo')"
                prop="productName"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.goodsCategory')"
                prop="goodsCategory"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.productSpecs')"
                prop="productSpecs"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                v-if="form.outboundType == 7"
                :label="$t('quickOutbound.label.salesUnitPrice')"
                prop="salePrice"
                align="right"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                v-if="form.outboundType == 7"
                :label="$t('quickOutbound.label.salesAmount')"
                prop="saleAmount"
                align="right"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.outboundQuantity')"
                prop="actualPickThisTime"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.outboundConversionQuantity')"
                prop="warehouseAreaActualPickWeight"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.outboundStorageArea')"
                prop="warehouseAreaName"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                :label="$t('quickOutbound.label.goodsPackaging')"
                prop="productPackage"
                show-overflow-tooltip
              ></el-table-column>
            </el-table>

          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">
          {{ $t("common.reback") }}
        </el-button>
      </div>
    </div>
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      :showUploadBtn="false"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "DetailQuickOutbound",
  inheritAttrs: false,
});
import OutboundNoticeAPI from "@/modules/wms/api/outboundNotice";
import QuickOutboundApi, {detailResponse} from "@/modules/wms/api/quickOutbound";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import { parseTime } from "@/core/utils/index.js";
import UploadDialog from "./components/uploadDialog.vue";

const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();
const loading = ref(false);
const form = reactive<detailResponse>({
  warehouseOutboundDetailVOList: [],
  outboundPickingListVOList: [],
});
const checkType = ref(1);
const tabs = ref([
  {
    key:1,
    value:t('quickOutbound.label.productDetail'),
    active: true
  },
  {
    key:2,
    value:t('quickOutbound.label.outboundDetail'),
    active: false
  },
]);
const changePanel = (data) => {
  tabs.value.map(item => item.active = false);
  data.active =true;
  checkType.value = data.key
}
async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}
/**
 * 合并列
 * @param row
 * @param column
 * @param rowIndex
 * @param columnIndex
 */
function spanMethod({ row, column, rowIndex, columnIndex }: any) {
  if (columnIndex === 1) {
    return {
      rowspan: 1,
      colspan: 99,
    };
  } else {
    return {
      rowspan: 0,
      colspan: 0,
    };
  }
}
/**
 * 子表格样式
 */
function cellStyleChildren({ row, column, rowIndex, columnIndex }: any) {
  if ([0, 1, 2, 3, 4, 5].includes(columnIndex) && row.isChildren) {
    return {
      visibility: "hidden",
      border: "none",
    };
  }
  return {
    border: "none",
    borderTop: "1px solid #E5E9F2",
  };
}
function openAttachment(files) {
  uploadDialog.visible = true;
  uploadDialogRef.value.showUploadBtn = false;
  uploadDialogRef.value.setEditType("detail");
  uploadDialogRef.value.setFormData(JSON.parse(files));
}

/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 获取真实客户联系人
function getRealName() {
  form.nameShow = true;
}
//获取真实供应商联系人
function getSupplierRealName() {
  form.supplierNameShow = true;
}

/** 查询出库通知单详情 */
function queryDetail() {
  loading.value = true;
  let params = {
    id: route.query.id,
  };
  QuickOutboundApi.queryFastDetailEncrypt(params)
    .then((data: any) => {
      Object.assign(form, data);
      form.mobilePhoneShow = true;
      form.supplierMobilePhoneShow = true;
      form.fullAddress = ` ${form.countryName}${form.provinceName}${form.cityName}${form.districtName}${form.address}`;
      form.supplierFullAddress = (form.supplierCountryName && form.supplierProvinceName && form.supplierCityName && form.supplierDistrictName && form.supplierAddress) ?`${form.supplierCountryName}${form.supplierProvinceName}${form.supplierCityName}${form.supplierDistrictName}${form.supplierAddress}` : '-';
      if (form.contactPerson?.length > 1) {
        form.nameShow = false;
      } else {
        form.nameShow = true;
      }
      if(form.supplierContactPerson?.length > 1){
        form.supplierNameShow = false;
      }else {
        form.supplierNameShow = true;
      }

      // 地址包含数字
      if (form.fullAddress && containsNumber(form.fullAddress)) {
        form.addressShow = true;
        form.addressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      } else {
        // 不包含数字
        form.addressShow = false;
      }
      if(form.supplierFullAddress  && containsNumber(form.supplierFullAddress)) {
        form.supplierAddressShow = true;
        form.supplierAddressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      }else {
        form.supplierAddressShow = false;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function getSummaries(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 4) {
      sums[index] = '合计：';
      return;
    }
    else if(index === 8 || index === 10){
      const values = data.map(item => Number(item[column.property]));
      if (!values.every(value => isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      }
    }else {
      sums[index] = ' '
    }
  })
  return sums;
}
function getSummaries1(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if ((form.outboundType == 7 && index === 4) || (form.outboundType != 7 && index === 3)) {
      sums[index] = '合计：';
      return;
    }
    else if(form.outboundType == 7 && (index === 5 || index === 6 || index === 7)){
      const values = data.map(item => Number(item[column.property]));
      if (!values.every(value => isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      }
    }else if(form.outboundType != 7 && (index === 4 || index === 5)){
      const values = data.map(item => Number(item[column.property]));
      if (!values.every(value => isNaN(value))) {
        sums[index] = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      }
    }else {
      sums[index] = ' '
    }
  })
  return sums;
}
function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}

// 获取真实客户联系电话
function getRealPhone() {
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.customerMobile = data.mobile;
      form.mobilePhoneShow = false;
    })
    .finally(() => {});
}
//获取真实供应商联系电话
function getSupplierRealPhone(){
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.supplierContactMobile = data.supplierContactMobile;
      form.supplierMobilePhoneShow = false;
    })
    .finally(() => {});
}

onMounted(() => {
  queryDetail();
});
</script>
<style scoped lang="scss">
.outboundNoticeDetail {
  background: #ffffff;
  border-radius: 4px;
  .table-container {
    padding: 0 20px;
    margin-bottom: 10px;

    .table-header {
      display: flex;
      background: #F6F0FF !important;
      font-weight: 400 !important;
      border: 1px solid #e5e9f2;
      position: relative;

      font-weight: 600;
      font-size: 14px;
      color: #151719;
      padding: 15px;
      margin: 0 -20px 0 -20px;
      align-items: center;

      .sort {
        padding: 0 6px;
        background-color: #762ADB;
        border-radius: 4px;
        font-size: 14px;
        color: #ffffff;
        margin-right: 38px;
      }

      .column {
        padding-right: 16px;
      }

      .action {
        position: absolute;
        right: 10px;
        top: 10px;
        color: #762adb;
        cursor: pointer;
        background-color: #f4f6fa;
        padding: 10px 20px;
      }
    }

    .table-summaries {
      display: flex;
      background: #f4f6fa;
      border: 1px solid #e5e9f2;
      position: relative;
      font-weight: 600;
      font-size: 14px;
      color: #151719;
      padding: 15px;

      .sort {
        margin-right: 38px;
      }

      .column {
        padding-right: 50px;
      }
    }

    :deep(.table-row) {
      padding: 0 !important;
      margin: 0 !important;

      > td {
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
      }
    }

    :deep(.el-table__expand-icon .el-icon) {
      display: none;
    }

    :deep(.el-table__header) {
      margin-bottom: 16px;
      border: 1px solid #e5e9f2;
    }

    .table-children {
      margin-top: -20px;
      border: 1px solid #e5e9f2;
    }

    :deep(.label-required .cell) {
      &:before {
        content: "*";
        color: red;
        margin-right: 5px;
      }
    }

    :deep(.summaries) {
      font-weight: bold;
    }
  }
}
</style>
<style lang="scss">
.outboundNoticeDetail {
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .panelContent {
    display: flex;
    border-bottom: 1px solid #F2F3F4;
    width: 100%;
    margin-bottom: 16px;
    .panelItem {
      font-size: 14px;
      color: #151719;
      padding: 10px 39px;
      cursor: pointer;
      &.active {
        color: var(--el-color-primary);
        border-bottom: 2px solid var(--el-color-primary);
      }
    }
  }
  .title-div{
    background: #F4F6FA !important;
    border-radius: 2px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #52585f;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding:15px 0px;
  }
  .data-div{
    border-radius: 2px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #90979E;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding:15px 0px;
  }
  .text-center{
    text-align: center;
  }
  .text-right{
    text-align: right;
  }
}
</style>
