<template>
  <div class="app-container">
    <div class="addPage">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          <span>{{ $t("quickOutbound.label.outboundNoticeCode") }}:{{form.outboundNoticeCode}}</span>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" ref="formRef" label-width="112px" label-position="right">
          <div class="basicInformation item_content">
            <div class="title">{{ $t("quickOutbound.label.documentInformation") }}</div>
            <el-row>
              <!-- 出库通知单号 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.outboundNoticeCode')">
                  {{ form.outboundNoticeCode ? form.outboundNoticeCode : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 出库类型:1:销售出库、2:提货卡兑换、3：业务招待、4：补发、5：地头售卖、6:采购退货、7：调拨出库 -->
                <el-form-item :label="$t('quickOutbound.label.outboundType')">
                  <span v-if="form.outboundType == 1">{{ $t("quickOutbound.outboundTypeList.procurementOutbound") }}</span>
                  <span v-else-if="form.outboundType == 2">{{ $t("quickOutbound.outboundTypeList.pickupCardRedemption") }}</span>
                  <span v-else-if="form.outboundType == 3">{{ $t("quickOutbound.outboundTypeList.businessReception") }}</span>
                  <span v-else-if="form.outboundType == 4">{{ $t("quickOutbound.outboundTypeList.reissue") }}</span>
                  <span v-else-if="form.outboundType == 5">{{ $t("quickOutbound.outboundTypeList.sellAtTheFieldEdge") }}</span>
                  <span v-else-if="form.outboundType == 6">{{ $t("quickOutbound.outboundTypeList.returnOutbound") }}</span>
                  <span v-else-if="form.outboundType == 7">{{ $t("quickOutbound.outboundTypeList.allotOutbound") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 主题描述 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.themeDescription')">
                  {{ form.orderTheme ? form.orderTheme : "-" }}
                </el-form-item>
              </el-col>
              <!-- 来源单号 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.sourceOrderCode')">
                  {{ form.sourceOrderCode ? form.sourceOrderCode : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 业务员 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.salesperson')">
                  {{ form.purchaseSalesPerson ? form.purchaseSalesPerson : "-" }}
                </el-form-item>
              </el-col>
              <!-- 原单号 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.originalOrderNumber')">
                  {{ form.sourceCode ? form.sourceCode : "-" }}
                </el-form-item>
              </el-col>
              <!-- 申请人 -->
              <el-col :span="6">
                <el-form-item
                  :label="$t('quickOutbound.label.createUserName')"
                >
                  {{ form.createUserName ? form.createUserName : "-" }}
                </el-form-item>
              </el-col>
              <!-- 申请时间 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.createTime')">
                  <span v-if="form.createTime">
                    {{ parseDateTime(form.createTime, "dateTime") }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 客户 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.customerName')">
                  {{ form.customerName ? form.customerName : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户地址 -->
                <el-form-item :label="$t('quickOutbound.label.customerAddress')">
                  <span class="encryptBox">
                    <span v-if="form.addressShow">
                      {{ form.addressFormat }}
                      <el-icon
                        v-if="form.fullAddress"
                        @click="form.addressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.addressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.fullAddress ? form.fullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户联系人 -->
                <el-form-item :label="$t('quickOutbound.label.customerPerson')">
                  {{ form.nameShow ? (form.contactPerson ? form.contactPerson : '-') : encryptName(form.contactPerson) }}
                  <el-icon
                    v-if="form.contactPerson"
                    @click="form.contactPerson ? getRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.nameShow ? '' : 'View'" />
                  </el-icon>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 客户联系电话 -->
                <el-form-item :label="$t('quickOutbound.label.customerMobile')">
                  <span class="encryptBox">
                    <span v-if="form.customerMobile">
                      {{ form.customerAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.customerMobile && form.customerMobile.length <= 4
                      "
                    >
                      {{ form.customerMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.customerMobile && form.customerMobile.length > 4
                      "
                    >
                      {{ form.customerMobile }}
                      <el-icon
                        v-if="form.customerMobile"
                        @click="form.mobilePhoneShow ? getRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 供应商 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.supplierName')" >
                  {{ form.supplierName ? form.supplierName : "-" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商地址 -->
                <el-form-item :label="$t('quickOutbound.label.supplierAddress')">
                  <span class="encryptBox">
                    <span v-if="form.supplierAddressShow">
                      {{ form.supplierAddressFormat }}
                      <el-icon
                        v-if="form.supplierFullAddress"
                        @click="form.supplierAddressShow = false"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierAddressShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>
                      {{ form.supplierFullAddress ? form.supplierFullAddress : "-" }}
                    </span>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商联系人 -->
                <el-form-item :label="$t('quickOutbound.label.supplierPerson')">
                  {{ form.supplierNameShow ? (form.supplierContactPerson ? form.supplierContactPerson : '-') : encryptName(form.supplierContactPerson) }}
                  <el-icon
                    v-if="form.supplierContactPerson"
                    @click="form.supplierContactPerson ? getSupplierRealName() : ''"
                    class="encryptBox-icon"
                    color="#762ADB "
                    size="16"
                    v-hasPermEye="['wms:outboundManagement:quickOutbound:eye']"
                  >
                    <component :is="form.supplierNameShow ? '' : 'View'" />
                  </el-icon>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- 供应商联系电话 -->
                <el-form-item :label="$t('quickOutbound.label.supplierMobile')">
                  <span class="encryptBox">
                    <span v-if="form.supplierContactMobile">
                      {{ form.supplierContactAreaCode + " " }}
                    </span>
                    <span
                      v-if="
                        form.supplierContactMobile && form.supplierContactMobile.length <= 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                    </span>
                    <span
                      v-else-if="
                        form.supplierContactMobile && form.supplierContactMobile.length > 4
                      "
                    >
                      {{ form.supplierContactMobile }}
                      <el-icon
                        v-if="form.supplierContactMobile"
                        @click="form.supplierMobilePhoneShow ? getSupplierRealPhone() : ''"
                        class="encryptBox-icon"
                        color="#762ADB "
                        size="16"
                        v-hasPermEye="[
                          'wms:outboundManagement:quickOutbound:eye',
                        ]"
                      >
                        <component :is="form.supplierMobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 合同名称-->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractName')" >
                  <span style="color: #762ADB;" v-if="form.contractName">{{form.contractName}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 合同编码-->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractCode')" >
                  <span style="color: #762ADB;" v-if="form.contractCode">{{form.contractCode}}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 合同分类-->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.contractClassification')" >
                  <span v-if="form.contractType == 1">销售合同</span>
                  <span v-else-if="form.contractType == 2">采购合同</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 结算方式-->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.settlementMethod')" >
                  {{ form.paymentType == 1 ? $t('quickOutbound.label.presentSettlement') : form.paymentType == 2 ? $t('quickOutbound.label.accountHanging') : "-" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <!-- 要求到货时间 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.plannedReceivedTime')">
                  <span v-if="form.plannedReceivedTime">{{ parseDateTime(form.plannedReceivedTime,"dateTime") }}</span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <!-- 计划发货时间 -->
              <el-col :span="6">
                <el-form-item prop="plannedDeliveryTime" :label="$t('quickOutbound.label.plannedDeliveryTime')" :rules="form.outboundNoticeStatus == 1 ? [{required:true,message:t('quickOutbound.rules.plannedDeliveryTime'),trigger:['change','blur']}] : []">
                  <el-date-picker
                    class="!w-[256px]"
                    v-if="form.outboundNoticeStatus == 1"
                    v-model="form.plannedDeliveryTime"
                    type="datetime"
                    :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                  </el-date-picker>
                  <span v-else>{{form.plannedDeliveryTime ? parseDateTime(form.plannedDeliveryTime,"dateTime") : '-'}}</span>
                </el-form-item>
              </el-col>
              <!-- 计划配送方式 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.plannedDistributionMethod')">
                  {{ form.deliveryName ? form.deliveryName : "-" }}
                </el-form-item>
              </el-col>
              <!-- 来源单备注 -->
              <el-col :span="6">
                <el-form-item :label="$t('quickOutbound.label.sourceOrderRemark')">
                  {{form.remark ? form.remark : "-"}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <!-- 实际配送方式 -->
                <el-form-item :label="$t('quickOutbound.label.actualDistributionMethod')" prop="actualDeliveryType" :rules="[{required:true,message:t('quickOutbound.rules.actualDeliveryType'),trigger:['change','blur']}]">
                  <el-select
                    v-model="form.actualDeliveryType"
                    :placeholder="$t('quickOutbound.placeholder.actualDeliveryType')"
                    clearable
                    class="!w-[256px]"
                    @change="setName"
                  >
                    <el-option
                      v-for="(item, index) in deliveryTypeList"
                      :key="index"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 实际出库时间 -->
              <el-col :span="6">
                <el-form-item prop="outboundTime" :label="$t('quickOutbound.label.actualOutboundTime')" :rules="[{required:true,message:t('quickOutbound.rules.actualOutboundTime'),trigger:['change','blur']}]">
                  <el-date-picker
                    class="!w-[256px]"
                    v-model="form.outboundTime"
                    type="datetime"
                    :placeholder="$t('quickOutbound.placeholder.defaultTips')">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!--  承运商 -->
                <el-form-item :label="$t('quickOutbound.label.carrier')" prop="carrier">
                  <el-input
                    v-model="form.carrier"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!--  车号 -->
                <el-form-item :label="$t('quickOutbound.label.vehicleNumber')" prop="carNumber">
                  <el-input
                    v-model="form.carNumber"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    class="!w-[256px]"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <!--  磅单编号 -->
                <el-form-item :label="$t('quickOutbound.label.weighbillNumber')" prop="poundCode">
                  <el-input
                    v-model="form.poundCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    maxlength="30"
                    clearable
                    class="!w-[180px]"
                  />
                  <div v-if="form.fileNum">
                    <el-badge
                      :value="form.fileNum"
                      :offset="[10, 8]"
                      class="item"
                      type="primary"
                    >
                      <!-- 上传 -->
                      <el-button
                        type="primary"
                        link
                        @click="handleUpload(form.poundAttachmentFiles)"
                      >
                        {{ $t("quickOutbound.button.upload") }}
                      </el-button>
                    </el-badge>
                  </div>
                  <div v-else>
                    <!-- 上传 -->
                    <el-button
                      type="primary"
                      link
                      @click="handleUpload(form.poundAttachmentFiles)"
                    >
                      {{ $t("quickOutbound.button.upload") }}
                    </el-button>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <!-- 出库备注 -->
                <el-form-item :label="$t('quickOutbound.label.outboundRemark')" prop="outboundRemark">
                  <el-input
                    v-model="form.outboundRemark"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    type="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>{{ $t("quickOutbound.label.waitOutboundProductDetail") }}</div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 商品列表 -->
                <el-form-item label-width="0">
                  <el-table
                    :data="form.warehouseOutboundDetailVOList"
                    v-loading="formLoading"
                    stripe
                    highlight-current-row
                    show-summary
                    :summary-method="getSummaries"
                  >
                    <el-table-column fixed="left" prop="index" :label="$t('common.sort')" width="60" align="center">
                      <template #default="scope">
                        <span v-if="scope.row.isParent">{{scope.row.index}}</span>
                        <span v-else>{{''}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('quickOutbound.label.productInfo')" min-width="150" show-overflow-tooltip>
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <span style="color: #90979E;">{{ scope.row.productCode }} | </span><span style="color:#52585F">{{ scope.row.productName }}</span>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    <!-- 规格 -->
                    <el-table-column :label="$t('quickOutbound.label.productSpecs')" prop="productSpecs" min-width="100">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">{{ scope.row.productSpecs }}</div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    <!-- 商品属性 -->
                    <el-table-column :label="$t('quickOutbound.label.commodityProperty')" prop="attributeTypeName" min-width="100">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">{{ scope.row.attributeTypeName }}</div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    <!-- 计划量 -->
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.plannedQuantity')">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <div>{{scope.row.planProductQty == null ? '-' : scope.row.planProductQty}}</div>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    <!-- 计划转换量 -->
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.plannedConversionQuantity')">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <div>{{scope.row.planProductWeight == null ? '-' : scope.row.planProductWeight}}</div>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    <!-- 剩余量 -->
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.residualQuantity')">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <div>{{scope.row.surplusQty == null ? '-' : scope.row.surplusQty}}</div>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    <!-- 剩余转换量 -->
                    <el-table-column min-width="120" :label="$t('quickOutbound.label.residualConversionQuantity')">
                      <template #default="scope">
                        <div v-if="scope.row.isParent">
                          <div>{{scope.row.surplusWeight == null ? '-' : scope.row.surplusWeight}}</div>
                        </div>
                        <div v-else>{{''}}</div>
                      </template>
                    </el-table-column>
                    <!-- 出库库区 -->
                    <el-table-column min-width="200" :label="'*' + $t('quickOutbound.label.outboundStorageArea')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.warehouseAreaCode'"
                          :rules="[
                            {
                              required: true,
                              message: t(
                                'quickOutbound.rules.warehouseAreaCode'
                              ),
                              trigger: ['blur'],
                            },
                          ]"
                        >
                          <el-select
                            v-model="scope.row.warehouseAreaCode"
                            :placeholder="$t('common.placeholder.selectTips')"
                            @change="getWarehouseAreaDetail($event, scope.$index)"
                            @focus="getOutWarehouseAreaList(scope.row.productCode,scope.row.isDiscreteUnit,scope.$index)"
                            clearable
                            :loading="scope.row.areaLoading"
                          >
                            <el-option
                              v-for="item in scope.row.outWarehouseAreaList"
                              :key="item.areaCode"
                              :label="`${item.areaName} | ${item.areaCode}`"
                              :value="item.areaCode"
                            />
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 单价 -->
                    <el-table-column v-if="form.outboundType == 7" min-width="200" align="right" :label="'*' + $t('quickOutbound.label.unitPrice')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.salePrice'" :rules="[
                            {
                              required: true,
                              message: t(
                                'quickOutbound.rules.salePrice'
                              ),
                              trigger: ['blur'],
                            },
                            {
                              pattern:
                                /^(-?0(?:\.\d{1,4})?|[1-9]\d{0,6}(?:\.\d{1,4})?)$/,
                              message: t(
                                'quickOutbound.rules.salePriceFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]">
                          <el-input :disabled="!scope.row.isParent" v-model="scope.row.salePrice" @change="setSaleAmount(scope.row)"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 出库量 -->
                    <el-table-column min-width="200" :label="'*' + $t('quickOutbound.label.outboundQuantity')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.outboundQty'"
                          :rules="[
                            {
                              required: true,
                              message: t(
                                'quickOutbound.rules.outboundQty'
                              ),
                              trigger: ['blur'],
                            },
                            {
                              pattern:
                                /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                              message: t(
                                'quickOutbound.rules.outboundQtyFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]"
                        >
                          <el-input v-model="scope.row.outboundQty" @change="setOutboundWeight(scope.$index)" clearable :placeholder="$t('common.placeholder.inputTips')"><template #append>{{ scope.row.productUnitName }}</template></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 出库转换量 -->
                    <el-table-column min-width="200" :label="'*' + $t('quickOutbound.label.outboundConversionQuantity')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.outboundWeight'"
                          :rules="[
                            {
                              required: true,
                              message: t(
                                'quickOutbound.rules.outboundWeight'
                              ),
                              trigger: ['blur'],
                            },
                            {
                              pattern:
                                /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                              message: t(
                                'quickOutbound.rules.outboundWeightFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]"
                        >
                          <el-input v-model="scope.row.outboundWeight" @change="changeWeight(scope.$index)" clearable :placeholder="$t('common.placeholder.inputTips')"><template #append>{{ scope.row.conversionRelSecondUnitName }}</template></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 金额 -->
                    <el-table-column v-if="form.outboundType == 7" min-width="200" align="right" :label="'*' + $t('quickOutbound.label.amount')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.saleAmount'" :rules="[
                            {
                              required: true,
                              message: t(
                                'quickOutbound.rules.saleAmount'
                              ),
                              trigger: ['blur'],
                            },
                            {
                              pattern:
                                /^(-?0(?:\.\d{1,2})?|-?[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                              message: t(
                                'quickOutbound.rules.saleAmountFormat'
                              ),
                              trigger: ['blur','change'],
                            },
                          ]">
                          <el-input v-model="scope.row.saleAmount"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!-- 剩余可用库存 -->
                    <el-table-column min-width="200" :label="$t('quickOutbound.label.remainingAvailableInventory')">
                      <template #default="scope">
                        <div>{{$t('quickOutbound.label.remainingAvailableQuantity')}}：<span style="color: #90979E ">{{scope.row.availableQty == null ? '-' : scope.row.availableQty}}</span></div>
                        <div>{{$t('quickOutbound.label.remainingConversionQuantity')}}：<span style="color: #90979E ">{{scope.row.availableWeight == null ? '-' : scope.row.availableWeight}}</span></div>
                      </template>
                    </el-table-column>
                    <!-- 商品包装 -->
                    <el-table-column min-width="130" :label="$t('quickOutbound.label.goodsPackaging')">
                      <template #default="scope">
                        <el-form-item class="mt15px" label-width="0px" :prop="'warehouseOutboundDetailVOList.' +scope.$index +'.productPackage'">
                          <el-input v-model="scope.row.productPackage" clearable :placeholder="$t('common.placeholder.inputTips')" maxlength="30"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="140">
                      <template #default="scope">
                        <el-button
                          type="success"
                          circle
                          plain
                          size="small"
                          @click="handleAdd(scope.row.productCode,scope.$index)"
                        >
                          {{ $t("quickOutbound.button.add") }}
                        </el-button>
                        <el-button
                          type="danger"
                          circle
                          plain
                          size="small"
                          v-show="!scope.row.isParent"
                          @click="handleDelete(scope.$index)"
                        >
                          {{ $t("quickOutbound.button.delete") }}
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("quickOutbound.button.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            {{ $t("quickOutbound.button.submit") }}
          </el-button>
        </div>
      </div>
    </div>
    <UploadDialog
      v-model:visible="uploadDialog.visible"
      ref="uploadDialogRef"
      @on-submit="onSubmitUpload"
    />
  </div>
</template>

<script setup lang="ts">
import {parseDateTime} from "@/core/utils";

defineOptions({
  name: "ConfirmOutbound",
  inheritAttrs: false,
});
import CommonAPI from "@/modules/wms/api/common";
import OutboundNoticeAPI from "@/modules/wms/api/outboundNotice";
import QuickOutboundApi, {confirmOutboundRequest} from "@/modules/wms/api/quickOutbound"
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
import UploadDialog from "./components/uploadDialog.vue";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const type = route.query.type;
const uploadDialog = reactive({
  visible: false,
});
const uploadDialogRef = ref();
const deliveryTypeList = ref([]);
const formRef = ref(ElForm);

const loading = ref(false);
const formLoading = ref(false);

// 表单
const form = reactive<confirmOutboundRequest>({
  id: "",
  fileNum:'',
  warehouseOutboundDetailVOList: [],
  totalPlanProductQty:'',//计划量合计
  totalPlanProductWeight:'',//计划转换量合计
  totalOutboundQty:'',//出库量合计
  totalOutboundWeight:'',//出库转换量合计
  totalAmount:'',//金额合计
});
// 上传
function handleUpload() {
  uploadDialog.visible = true;
  uploadDialogRef.value.setEditType("add");
  if (form.poundAttachmentFiles) {
    uploadDialogRef.value.setFormData(JSON.parse(form.poundAttachmentFiles));
  }
}

//上传提交
function onSubmitUpload(data: any) {
  if (data) {
    form.poundAttachmentFiles = JSON.stringify(data);
    form.fileNum = data.length;
  }
}
function getSummaries(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 3) {
      sums[index] = '合计:';
      return;
    }else if(index === 4){
      sums[index] = form.totalPlanProductQty;
      return;
    }else if(index === 5){
      sums[index] = form.totalPlanProductWeight;
      return;
    }else if(form.outboundType != 7 && index === 9){
      sums[index] = form.totalOutboundQty;
      return;
    }else if(form.outboundType != 7 && index === 10){
      sums[index] = form.totalOutboundWeight;
      return;
    }else if(form.outboundType == 7 && index === 10){
      sums[index] = form.totalOutboundQty;
      return;
    }else if(form.outboundType == 7 && index === 11){
      sums[index] = form.totalOutboundWeight;
      return;
    }else{
      sums[index] = ' '
      return
    }
  });
  return sums;
}
//出库量合计
function getTotalOutboundQty(){
  let totalOutboundQty = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.outboundQty){
      totalOutboundQty = (parseFloat(totalOutboundQty) + parseFloat(list.outboundQty)).toFixed(3)
    }
    form.totalOutboundQty = totalOutboundQty
  })
}
//出库转换量合计
function getTotalOutboundWeight() {
  let totalOutboundWeight = '0'
  form.warehouseOutboundDetailVOList.forEach(list=>{
    if(list.outboundWeight){
      totalOutboundWeight = (parseFloat(totalOutboundWeight) + parseFloat(list.outboundWeight)).toFixed(3)
    }
    form.totalOutboundWeight = totalOutboundWeight
  })
}
//查询当前商品总库存大于0的库区
function getOutWarehouseAreaList(productCode,isDiscreteUnit,index) {
  form.warehouseOutboundDetailVOList[index].areaLoading = true
  let params = {
    productCode:productCode,
    isDiscreteUnit:isDiscreteUnit,
  }
  CommonAPI.queryHasStockListByReq(params).then((res)=>{
    form.warehouseOutboundDetailVOList[index].outWarehouseAreaList = res
  }).finally(()=>{
    form.warehouseOutboundDetailVOList[index].areaLoading = false
  })

}
//查询所选库区详情
function getWarehouseAreaDetail(e,index) {
  if(e){
    form.warehouseOutboundDetailVOList[index].outWarehouseAreaList.forEach(list=>{
      if(list.areaCode == e){
        form.warehouseOutboundDetailVOList[index].warehouseAreaName = list.areaName
        form.warehouseOutboundDetailVOList[index].availableStockQty = list.availableStockQty
        form.warehouseOutboundDetailVOList[index].availableStockWeight = list.availableStockWeight
      }
    })
  }else{
    form.warehouseOutboundDetailVOList[index].warehouseAreaName = ''
    form.warehouseOutboundDetailVOList[index].availableStockQty = ''
    form.warehouseOutboundDetailVOList[index].availableStockWeight = ''
  }
  getAvailableQty(index)
  getAvailableWeight(index)
}
//计算剩余可用库存数量
function getAvailableQty(index) {
  if(form.warehouseOutboundDetailVOList[index].availableStockQty && form.warehouseOutboundDetailVOList[index].outboundQty){
    form.warehouseOutboundDetailVOList[index].availableQty = (parseFloat(form.warehouseOutboundDetailVOList[index].availableStockQty) - parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty)).toFixed(3)
  }else if(form.warehouseOutboundDetailVOList[index].availableStockQty && !form.warehouseOutboundDetailVOList[index].outboundQty){
    form.warehouseOutboundDetailVOList[index].availableQty = form.warehouseOutboundDetailVOList[index].availableStockQty
  }
}
//计算剩余可用库存转换量
function getAvailableWeight(index) {
  if(form.warehouseOutboundDetailVOList[index].availableStockWeight && form.warehouseOutboundDetailVOList[index].outboundWeight){
    form.warehouseOutboundDetailVOList[index].availableWeight = (parseFloat(form.warehouseOutboundDetailVOList[index].availableStockWeight) - parseFloat(form.warehouseOutboundDetailVOList[index].outboundWeight)).toFixed(3)
  }else if(form.warehouseOutboundDetailVOList[index].availableStockWeight && !form.warehouseOutboundDetailVOList[index].outboundWeight){
    form.warehouseOutboundDetailVOList[index].availableWeight = form.warehouseOutboundDetailVOList[index].availableStockWeight
  }
}
//一级单位转二级单位
function setOutboundWeight(index) {
  getTotalOutboundQty()
  getAvailableQty(index)
  if(form.warehouseOutboundDetailVOList[index].isDiscreteUnit){
    if(form.warehouseOutboundDetailVOList[index].outboundQty){
      let params = {
        convertUnitTypeEnum:'FIRST_TO_SECOND',
        originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty).toFixed(3),
        productCode:form.warehouseOutboundDetailVOList[index].productCode
      }
      CommonAPI.convertProductUnit(params).then((res)=>{
        form.warehouseOutboundDetailVOList[index].outboundWeight = res.convertedValue
        if(form.outboundType == 7){
          calculateAmount(index)
        }
        getTotalOutboundWeight()
        getAvailableWeight(index)
      })
    }
  }else{
    if(form.warehouseOutboundDetailVOList[index].outboundQty && !form.warehouseOutboundDetailVOList[index].outboundWeight){
      let params = {
        convertUnitTypeEnum:'FIRST_TO_SECOND',
        originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundQty).toFixed(3),
        productCode:form.warehouseOutboundDetailVOList[index].productCode
      }
      CommonAPI.convertProductUnit(params).then((res)=>{
        form.warehouseOutboundDetailVOList[index].outboundWeight = res.convertedValue
        if(form.outboundType == 7){
          calculateAmount(index)
        }
        getTotalOutboundWeight()
        getAvailableWeight(index)
      })
    }
  }
}
//改变出库转换量联动计算
function changeWeight(index) {
  getTotalOutboundWeight()
  getAvailableWeight(index)
  if(!form.warehouseOutboundDetailVOList[index].isDiscreteUnit && form.warehouseOutboundDetailVOList[index].outboundWeight && !form.warehouseOutboundDetailVOList[index].outboundQty){
    let params = {
      convertUnitTypeEnum:'SECOND_TO_FIRST',
      originalValue:parseFloat(form.warehouseOutboundDetailVOList[index].outboundWeight).toFixed(3),
      productCode:form.warehouseOutboundDetailVOList[index].productCode
    }
    CommonAPI.convertProductUnit(params).then((res)=>{
      form.warehouseOutboundDetailVOList[index].outboundQty = res.convertedValue
      getTotalOutboundQty()
      getAvailableQty(index)
    })
  }
  nextTick(()=>{
    if(form.outboundType == 7){
      calculateAmount(index)
    }
  })
}
function handleAdd(productCode,index) {
  for(let i = 0; i < form.warehouseOutboundDetailVOList.length; i++){
    let item = form.warehouseOutboundDetailVOList[i]
    if(item.productCode == productCode && i == index){
      let num = i + 1
      form.warehouseOutboundDetailVOList.splice(num, 0, {
        productCode:item.productCode,
        productName:item.productName,
        productSpecs:item.productSpecs,
        attributeTypeName:item.attributeTypeName,
        pricingScheme:item.pricingScheme,
        isDiscreteUnit:item.isDiscreteUnit,
        salePrice:item.salePrice,
        isParent:false,
      })
      break
    }
  }
}

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.go(-1);
};
/* 姓名加密 */
function encryptName(name: any) {
  if (!name?.trim()) return "-";

  const firstChar = name[0]; // 获取第一位
  const shouldShowFourStars = name.length > 5;

  return shouldShowFourStars
    ? `${firstChar}****`
    : `${firstChar}${"*".repeat(name.length - 1)}`;
}

// 获取真实联系人
function getRealName() {
  form.nameShow = true;
}
//获取真实供应商联系人
function getSupplierRealName() {
  form.supplierNameShow = true;
}
// 获取真实电话号码
function getRealPhone() {
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.customerMobile = data.mobile;
      form.mobilePhoneShow = false;
    })
    .finally(() => {});
}
//获取真实供应商联系电话
function getSupplierRealPhone(){
  OutboundNoticeAPI.queryRealPhone({ id: route.query.id })
    .then((data: any) => {
      form.supplierContactMobile = data.supplierContactMobile;
      form.supplierMobilePhoneShow = false;
    })
    .finally(() => {});
}

function handleDelete(index?: number) {
  form.warehouseOutboundDetailVOList.splice(index, 1);
  getTotalOutboundQty()
  getTotalOutboundWeight()
}

// 提交
function handleSubmit() {
  formRef.value.validate((valid) => {
    if (!valid) return;
    loading.value = true
    let sameArr = []
    form.warehouseOutboundDetailVOList.forEach(item=>{
      let obj = {
        productCode:item.productCode,
        warehouseAreaCode:item.warehouseAreaCode
      }
      sameArr.push(obj)
    })
    const uniqueItems = new Map();
    for(let i = 0;i<sameArr.length;i++){
      const key = JSON.stringify(sameArr[i]); // 将对象转换为字符串
      if (!uniqueItems.has(key)) {
        uniqueItems.set(key, sameArr[i]); // 如果尚未存在，则添加到Map中
      }else {
        loading.value = false
        return  ElMessage.error(t('quickOutbound.message.sameProduct'));
      }
    }
    let arr = form.warehouseOutboundDetailVOList.filter(function (item) {
      return (item.isDiscreteUnit && Number(item.availableQty) < 0) || (!item.isDiscreteUnit && Number(item.availableWeight) < 0)
    })
    if(arr.length > 0){
      loading.value = false
      return  ElMessage.error(t('quickOutbound.message.availableTip'));
    }
    ElMessageBox.confirm(t("quickOutbound.message.submitWarning"), t("common.tipTitle"), {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }).then(() => {
      let fastOutboundProductInfoDTOList = []
      let arr = form.warehouseOutboundDetailVOList.filter(function (list) {
        return list.isParent
      })
      arr.forEach(item => {
        let fastOutboundProductDetailDTOList = []
        let obj = {
          actualPickThisTime:item.outboundQty,
          warehouseAreaActualPickWeight:item.outboundWeight,
          productPackage:item.productPackage,
          warehouseAreaCode:item.warehouseAreaCode,
          warehouseAreaName:item.warehouseAreaName,
        }
        if(form.outboundType == 7){
          obj.salePrice = item.salePrice
          obj.saleAmount = item.saleAmount
        }
        fastOutboundProductDetailDTOList.push(obj)
        form.warehouseOutboundDetailVOList.forEach(list=>{
          if(item.productCode == list.productCode && !list.isParent){
            let obj1 = {
              actualPickThisTime:list.outboundQty,
              warehouseAreaActualPickWeight:list.outboundWeight,
              productPackage:list.productPackage,
              warehouseAreaCode:list.warehouseAreaCode,
              warehouseAreaName:list.warehouseAreaName,
            }
            if(form.outboundType == 7){
              obj1.salePrice = list.salePrice
              obj1.saleAmount = list.saleAmount
            }
            fastOutboundProductDetailDTOList.push(obj1)
          }
        })
        item.fastOutboundProductDetailDTOList = fastOutboundProductDetailDTOList
      })
      arr.forEach(item=>{
        let obj = {
          outboundProductId:item.id,
          productCode:item.productCode,
          productName:item.productName,
          firstCategoryId:item.firstCategoryId,
          firstCategoryName:item.firstCategoryName,
          productSpecs:item.productSpecs,
          productUnitId:item.productUnitId,
          productUnitName:item.productUnitName,
          conversionRelSecondUnitId:item.conversionRelSecondUnitId,
          conversionRelSecondUnitName:item.conversionRelSecondUnitName,
          secondCategoryId:item.secondCategoryId,
          secondCategoryName:item.secondCategoryName,
          thirdCategoryId:item.thirdCategoryId,
          thirdCategoryName:item.thirdCategoryName,
          totalPlanedPick:item.planProductQty,
          totalPlanedPickWeight:item.planProductWeight,
          pricingScheme:item.pricingScheme,
          isDiscreteUnit:item.isDiscreteUnit,
          fastOutboundProductDetailDTOList:item.fastOutboundProductDetailDTOList
        }
        fastOutboundProductInfoDTOList.push(obj)
      })
      let params = {
        deliveryNoticeId:route.query.id,
        deliveryNoticeCode:form.outboundNoticeCode,
        carNumber:form.carNumber,
        carrier:form.carrier,
        deliveryType:form.actualDeliveryType,
        deliveryName:form.actualDeliveryName,
        planDeliveryTime:new Date(form.plannedDeliveryTime).getTime(),
        outboundTime:new Date(form.outboundTime).getTime(),
        poundCode:form.poundCode,
        remark:form.outboundRemark,
        poundAttachmentFiles:form.poundAttachmentFiles,
        fastOutboundProductInfoDTOList:fastOutboundProductInfoDTOList
      }
      QuickOutboundApi.submitOutbound(params).then((res)=>{
        ElMessage.success(t("quickOutbound.message.operationSuccess"))
        handleCancel()
      }).finally(()=>{
        loading.value = false
      })
    }, () => {
        loading.value = false
        ElMessage.info(t("quickOutbound.message.cancelTip"));
    });
  })
}

function queryDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  QuickOutboundApi.queryFastDetailEncrypt(params)
    .then((data: any) => {
      Object.assign(form, data);
      form.mobilePhoneShow = true;
      form.supplierMobilePhoneShow = true;
      form.fullAddress = ` ${form.countryName}${form.provinceName}${form.cityName}${form.districtName}${form.address}`;
      form.supplierFullAddress = (form.supplierCountryName && form.supplierProvinceName && form.supplierCityName && form.supplierDistrictName && form.supplierAddress) ?`${form.supplierCountryName}${form.supplierProvinceName}${form.supplierCityName}${form.supplierDistrictName}${form.supplierAddress}` : '-';
      if (form.contactPerson?.length > 1) {
        form.nameShow = false;
      } else {
        form.nameShow = true;
      }
      if(form.supplierContactPerson?.length > 1){
        form.supplierNameShow = false;
      }else {
        form.supplierNameShow = true;
      }

      // 地址包含数字
      if (form.fullAddress && containsNumber(form.fullAddress)) {
        form.addressShow = true;
        form.addressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      } else {
        // 不包含数字
        form.addressShow = false;
      }
      if(form.supplierFullAddress  && containsNumber(form.supplierFullAddress)) {
        form.supplierAddressShow = true;
        form.supplierAddressFormat = replaceNumbersWithAsterisk(form.fullAddress);
      }else {
        form.supplierAddressShow = false;
      }
      if(form.outboundNoticeStatus == 1){
        form.plannedDeliveryTime = parseDateTime(new Date().getTime(),'dateTime')
      }
      if(form.warehouseOutboundDetailVOList.length > 0){
        form.warehouseOutboundDetailVOList.forEach((list,index)=>{
          list.isParent = true
          list.index = index+1
          list.surplusQty = list.alreadyOutboundQty ? (parseFloat(list.planProductQty) - parseFloat(list.alreadyOutboundQty)).toFixed(3) : list.planProductQty
          list.surplusWeight = list.alreadyOutboundWeight ? (parseFloat(list.planProductWeight) - parseFloat(list.alreadyOutboundWeight)).toFixed(3) : list.planProductWeight
          list.outboundQty = list.surplusQty
          list.outboundWeight = list.surplusWeight
        })
        getTotalOutboundQty()
        getTotalOutboundWeight()
      }
      form.outboundTime = parseDateTime(new Date().getTime(),'dateTime')
      form.actualDeliveryType = form.deliveryType
      form.actualDeliveryName = form.deliveryName
    })
    .finally(() => {
      formLoading.value = false;
    });
}
//计算金额
function calculateAmount(index) {
  if(form.warehouseOutboundDetailVOList[index].outboundQty && form.warehouseOutboundDetailVOList[index].outboundWeight && form.warehouseOutboundDetailVOList[index].salePrice) {
    let params = {
      qty: form.warehouseOutboundDetailVOList[index].outboundQty,
      convertedQty: form.warehouseOutboundDetailVOList[index].outboundWeight,
      productCode: form.warehouseOutboundDetailVOList[index].productCode,
      unitPrice: form.warehouseOutboundDetailVOList[index].salePrice
    }
    CommonAPI.calculateAmount(params).then((res) => {
      form.warehouseOutboundDetailVOList[index].saleAmount = res.amount
    })
  }
}
function setSaleAmount(row){
  if(row.salePrice){
    form.warehouseOutboundDetailVOList.forEach((item,index) =>{
      if(item.productCode == row.productCode){
        item.salePrice = row.salePrice
        calculateAmount(index)
      }
    })

  }
}
function replaceNumbersWithAsterisk(str: string) {
  // 使用正则表达式匹配数字，并用 * 替换
  return str.replace(/\d+/g, (match) =>
    match.length > 4 ? "****" : "*".repeat(match.length)
  );
}
function containsNumber(str: any) {
  for (let i = 0; i < str.length; i++) {
    if (!isNaN(str[i]) && str[i] !== " ") {
      return true;
    }
  }
  return false;
}
function queryDeliveryMethodList(){
  deliveryTypeList.value = []
  let data = {
    page:1,
    limit:100,
    enableStatus:1
  }
  QuickOutboundApi.queryDeliveryMethodList(data).then((res)=>{
    if(res.records && res.records.length > 0){
      res.records.forEach(item=>{
        let obj = {
          code:item.id,
          name:item.deliveryMethodsCategoryVO.deliveryMethodsCategoryName + '/' + item.methodName,
          methodName:item.methodName
        }
        deliveryTypeList.value.push(obj)
      })
    }else {
      deliveryTypeList.value = []
    }
  })
}
function setName(){
  if(form.actualDeliveryType){
    deliveryTypeList.value.forEach(list=>{
      if(list.id == form.actualDeliveryType){
        form.actualDeliveryName = list.name
      }
    })
  }else{
    form.actualDeliveryName = ''
  }
}

onMounted(() => {
  queryDeliveryMethodList()
  queryDetail();
});
</script>
<style scoped lang="scss">
.addPage {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 20px 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 30px 24px 30px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 22px 30px;
    border-top: 1px solid #e5e7f3;
  }
  .mt20 {
    margin-top: 20px;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .product-code {
    color: #90979e;
  }
  .product-name {
    color: #151719;
  }
  .mt15px {
    margin-bottom: 15px !important;
  }
}
</style>
<style lang="scss">
::v-deep .custom-select .el-select__wrapper {
  background: #f8f9fc !important;
}
</style>
