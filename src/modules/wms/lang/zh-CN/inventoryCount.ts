export default {
  inventoryCount: {
    label: {
      countNo: "盘点单号",
      countType: "盘点类型",
      countStatus: "盘点状态",
      warehouseCode: "仓库编码",
      warehouseName: "仓库名称",
      warehouseAreaCode: "库区编码",
      warehouseAreaName: "库区名称",
      warehouseAreaLocationName: "库位名称",
      countBy: "盘点人",
      countByName: "盘点人名称",
      countTime: "盘点时间",
      remark: "备注",
      createBy: "创建人",
      createName: "创建人名称",
      createTime: "创建时间",
      updateBy: "更新人",
      updateTime: "更新时间",
      productCode: "商品编码",
      productName: "商品名称",
      productSpec: "规格",
      productUnit: "单位",
      locationCode: "库位编码",
      locationName: "库位名称",
      systemQty: "系统库存数量",
      countQty: "盘点数量",
      diffQty: "差异数量",
      batchNo: "批次号",
      productionDate: "生产日期",
      expiryDate: "过期日期",
      isAssigned: "是否领单",
      operation: "操作"
    },
    placeholder: {
      countNo: "请输入盘点单号",
      productKeyword: "请输入商品编码/名称",
      warehouseArea: "请选择库区",
      countStatus: "请选择盘点状态",
      isAssigned: "请选择是否领单"
    },
    button: {
      add: "新增盘点单",
      edit: "编辑",
      detail: "查看",
      delete: "删除",
      submit: "提交",
      cancel: "取消",
      complete: "处理",
      export: "导出"
    },
    countType: {
      visible: "明盘",
      blind: "盲盘"
    },
    countStatus: {
      draft: "草稿",
      pending: "待盘点",
      counting: "盘点中",
      completed: "完结",
      cancelled: "已取消",
      checked: "已盘点"
    },
    isAssigned: {
      yes: "是",
      no: "否"
    },
    message: {
      deleteConfirm: "是否确认删除所选盘点单?",
      deleteSuccess: "删除成功",
      submitConfirm: "是否确认提交该盘点单?",
      submitSuccess: "提交成功",
      cancelConfirm: "是否确认取消该盘点单?",
      cancelSuccess: "取消成功",
      completeConfirm: "是否确认完成该盘点单?",
      completeSuccess: "完成盘点成功",
      operationSuccess: "操作成功"
    },
    title: {
      list: "盘点单列表",
      add: "新增盘点单",
      edit: "编辑盘点单",
      detail: "盘点单详情",
      basicInfo: "基本信息",
      detailList: "盘点明细"
    }
  }
}
