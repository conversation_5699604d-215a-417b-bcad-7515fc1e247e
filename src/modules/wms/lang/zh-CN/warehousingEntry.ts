export default {
  warehouseEntry: {
    label: {
      entryOrderCode: "入库单号",
      entryOperator: "入库人",
      entryTime: "入库时间",
      productType: "商品种类",
      createUserName: "创建人",
      createTime: "创建时间",
      status: "状态",
      alreadyStock: "已入库",
      stocking: "入库中",
      initStock: "初始",
      orderDetails: "入库列表明细",
      receiptNoticeCode: "入库通知单号",
      receiptType: "单据类型", // "入库类型",
      purchaseInventory: "采购入库",
      returnStorage: "退货入库",
      receivingOrderCode: "收运单号",
      orderType: "入库类型",
      sortingOrderEntry: "按分拣单入库",
      productEntry: "按商品入库",
      receiptNoticeEntry: "按收运单入库",
      actualWeightCopy: "收运重量",
      initial: "初始",
      receivedShipped: "已收运",
      sourceOrderCode: "来源单号",
      plannedDeliveryTime: "计划交货时间",
      documentSynchronizationTime: "单据同步时间",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      startTime: "开始时间",
      endTime: "结束时间",
      to: "至",
      name: "客户/供应商",
      customerName: "客户",
      supplierName: "供应商",
      address: "地址",
      contactPerson: "联系人",
      mobile: "联系电话",
      productExpectQty: "商品数量",
      productNameCopy: "商品",
      purchaseSalesPerson: "采购/销售员",
      remark: "备注",
      manuallyCreate: "手动创建",
      synchronous: "同步",
      source: "来源",
      sourceSystem: "来源系统",

      updateUserName: "最后修改人",
      updateTime: "最后修改时间",
      basicInformation: "基本信息",
      warehouse: "仓库",
      areaCode: "区号",
      country: "国家",
      area: "地区",
      detailAddress: "详细地址",
      goodsDetails: "商品明细",

      unitName: "单位",
      plannedQuantity: "计划数量",
      specifications: "规格",
      actualQuantity: "(实际）收运数量",
      actualQuantityCopy: "收运数量",
      goodsInfor: "商品信息",
      productCode: "商品编码",
      productName: "商品名称",
      goodsCategory: "商品分类",
      productSpecs: "规格",
      productPlanQty: "计划数量",
      productInventoryQty: "已入库数量",
      inventoryQty: "入库数量",
      warehouseAreaName: "库区",
      productInventoryQtyCopy: "本次入库数量",
      sortingOrderCode: "分拣单号",
      sortingCode: "分拣单号",
      productUnit: "单位",
      sortingAfterQty: "分拣后数量",
      sortingAfterWeight: "分拣后重量",
      inWarehouseQty: "待入库数量",
      inWarehouseWeight: "待入库重量(Kg)",
      receiptType2: "单据类型"
    },
    button: {
      addBtn: "去入库",
      syncBtn: "同步",
      addNextLevel: "新增下级分类",
      back: "返回",
      goodsAdd: "添加",
      cancel: "取消",
      submit: "保存",
      comfirm: "确定",
      confirmsubmit: "确认入库",
    },
    title: {
      addTitle: "去入库",
      addProduct: "选择商品",
      editTitle: "编辑入库通知单",
    },
    message: {
      deleteTips: "确定删除已创建的入库通知单？",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      deleteCancel: "取消删除！",
      addOrEditGoodsTips: "请选择商品",
      warehousingEntryTips: "确认入库后，数量不可更改，是否继续？",
      cancelTip: "已取消",
      warehouseEntrySucess: "入库成功",
    },
    placeholder: {
      productInputTips: "请输入商品名称或商品编码",
    },
    rules: {
      receiptTypeTip: "请选择入库类型",
      plannedDeliveryTimeTip: "请选择计划交货时间",
      customerNameTip: "请输入客户名称",
      supplierNameTip: "请输入供应商名称",

      name: "请输入分类名称",
      nameFomart: "可输入1到5位中文、英文或数字",
      sortFomart: "请输入正确的格式，0和正整数",
      sort: "请输入排序",
      warehouseAreaCode: "请选择库区",
      productInventoryQtyCopy: "请输入本次入库数量",
      productInventoryQtyCopyFomart:
        "请输入大于0的数字，支持小数点前8位，小数点后3位",
      productInventoryQtyNum: "本次入库数量不能大于收运数量减去已入库数量",
      productInventoryWeight: "请输入入库重量",
      productInventoryWeightFomart: "请输入大于0的数字，支持小数点前8位，小数点后3位",
      productInventoryQtyNumberFomart: "请输入大于0的整数"
    },
  },
};
