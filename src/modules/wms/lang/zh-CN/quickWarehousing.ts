export default {
  quickWarehousing: {
    label: {
      receiptNoticeCode: "入库通知单",
      themeDesc: "主题描述", 
      receiptType: "入库类型",
      status: "状态",
      sourceOrderCode: "来源单号",
      queryType: "查询类型",
      plannedDeliveryTime: "计划交货时间",
      orderSyncTime: "单据同步时间",
      createTime: "创建时间",
      createUserName: "创建人",
      warehouseName: "仓库",
      entryOperator: "入库操作员",
      entryTime: "入库时间",
      to: "至",
      startTime: "开始时间",
      endTime: "结束时间",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      initial: "初始",
      received: "已收运",
      draft: "草稿",
      purchaseInventory: "采购入库",
      returnStorage: "退货入库",
      transferInventory: "调拨入库",
      directStorage: "直接入库",
      groundStorage: "地头入库",
      cancel: "取消",
      processing: "入库中",
      week: "近七天",
      month: "近30天",
      year: "近一年",
      all: "全部",
      completed: "完结",
      canceled: "已取消",
      planQty: "计划量",
      inQty: "入库量",
      entryOperatorList: "入库人",
      salesmanName: "采购员/销售员",
      customerName: "客户",
      supplierName: "供应商",
      remark: "备注",
      sourceList: "来源",
      manualCreate: "手动创建",
      sync: "同步",
    },
    button: {
      addBtn: "新增",
      goWarehousing: "去入库",
      complete: "完结",
      search: "搜索",
      reset: "重置",
      edit: "编辑",
      delete: "删除",
      print: "打印",
      detail: "详情",
    },
    title: {
      quickWarehousingManagement: "快速入库管理",
      addQuickWarehousing: "新增快速入库",
      editQuickWarehousing: "编辑快速入库",
      quickWarehousingDetail: "快速入库详情",
    },
    message: {
      deleteTips: "确定删除已创建的快速入库单？",
      deleteSuccess: "删除成功！",
      deleteFail: "删除失败！",
      deleteCancel: "取消删除！",
      completeTips: "完结后不可再对当前单据进行入库",
      completeSuccess: "操作成功",
      completeFail: "操作失败",
      addSuccess: "添加成功",
      editSuccess: "编辑成功",
    },
    placeholder: {
      inputTips: "请输入",
      selectTips: "请选择",
    },
  },
}; 