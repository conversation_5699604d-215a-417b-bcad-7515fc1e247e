export default {
  outboundNotice: {
    label: {
      outboundNoticeCode: "出库通知单号",
      outboundType: "出库类型",
      status: "状态",
      sourceNumber: "来源单号",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      customerMobile: "联系电话",
      supplierName: "供应商",
      createTime: "创建时间",
      remark: "备注",
      basicInformation: "基本信息",
      sourceOrderCode: "提货单号",
      approveStatus: "审核状态",
      to: "至",
      startTime: "开始时间",
      endTime: "结束时间",
      plannedDeliveryTime:'计划发货时间',
      plannedReceivedTime: "要求到货时间",
      plannedQty:'计划量',
      count:'个数',
      plannedTotalQty:'计划总数',
      plannedTotalWeight:'计划总量(kg)',
      deliveryType: "配送方式",
      delivery: "配送",
      selfPickup: "自提",
      customerName: "客户",
      approveUserName: "审核人",
      address: "地址",
      detailAddress: "详细地址",
      contactPerson: "联系人",
      orderCreateTime: "客户下单时间",
      purchaseSalesPerson: "采购/销售员",
      source: "来源",
      sourceSystem: "来源系统",
      createUserName: "创建人",
      updateUserName: "最后操作人",
      updateTime: "最后操作时间",
      productQty: "商品个数",
      areaCode: "区号",
      country: "国家",
      area: "地区",
      goodsDetails: "商品明细",
      goodsInfor: "商品信息",
      productCode: "商品编码",
      goodsCategory: "商品分类",
      productSpecs: "规格",
      productUnit:'单位',
      productPlanQty: "计划数量",
      productPlanWeight: "计划重量",
      productName: "商品名称",
      productNameCopy: "商品",
      approveTime: "审核时间",
      success: "成功",
      fail: "失败",
      approveResultUnit: "条",
      failReason: "失败原因",
      presenter: "提交人",
      submissionTime: "提交时间",
      totalPlanProductQty:'商品计划总数量',
      totalPlanProductWeight:'商品计划总重量(kg)',
      productMiniWeight:"最小发货重量",
    },
    outboundTypeList: {
      procurementOutbound: "销售出库",
      returnOutbound: "采购退货",
    },
    sourceList: {
      creat: "创建",
      synchronous: "同步",
    },
    approveStatusList: {
      Unaudited: "未审核",
      success: "成功",
      fail: "失败",
    },
    outboundNoticeStatusList: {
      draft: "草稿",
      initial: "初始",
      picking: "拣货中",
      finish: "完成",
      cancel: "取消",
    },
    dateTypeList: {
      createDate: "创建时间",
      plannedDate: "要求到货时间",
    },
    button: {
      close: "关闭",
      cancel: "取消",
      submit: "提交",
      saveDraft: "保存草稿",
      addOutboundNotice: "新建通知单",
      approve: "审核",
      exApprove: "反审",
      goodsAdd: "添加",
      comfirm: "确定",
      weightEdit:'重量调整',
    },
    title: {
      outboundNoticeCode: "出库通知单",
      editTitle: "编辑出库通知单",
      addTitle: "新增出库通知单",
      addProduct: "选择商品",
      approve: '审核',
    },
    message: {
      cancelTips:
        "请确认客户或供应商取消出库通知单，若取消后不能对出库通知单再次操作，是否继续操作？",
      cancelConcel: "已取消操作！",
      cancelSucess: "取消成功！",
      cancelFail: "删除失败！",
      submitCancel: "已取消提交！",
      selectApproveTips: "请选择需要审核的单据",
      selectExApproveTips: "请选择需要反审的单据",
      ApproveCancel: "已取消审核！",
      generateNotApproveTips: "只有初始状态的出库通知单可以进行审核",
      generateNotExApproveTips: "只有拣货中状态的出库通知单可以进行反审",
      addSucess: "添加成功！",
      editSucess: "编辑成功！",
      addOrEditGoodsTips: "请选择商品",
      deleteTips: "是否确认删除已创建的出库通知单",
      deleteSucess: "删除成功！",
      deleteCancel: "已取消删除！",
      exApproveSucess: "反审成功！",
      exApproveCancel: "已取消反审！",
      haveTip: "有",
      approveTips: "个出库通知单生成发货任务，确定要生成吗？",
      submitTips:
        "提交后系统自动生成拣货方式为按单拣货的拣货单，生成后不可修改出库通知单相关内容，是否继续？",
      exApproveTips:
        "入库通知单已生成拣货单并锁定库存，反审后取消对应生成的拣货单，是否继续反审？",
      changeSuccess:'调整成功',
    },
    placeholder: {
      productInputTips: "请输入商品名称或商品编码",
      inputLimtTips: "请输入大于等于4位的单号",
      defaultNowDate:'默认当天日期',
      chooseTime:'选择时间',
    },
    rules: {
      productPlanQty: "请输入计划数量",
      productPlanQtyFomart: "请输入大于0的数字，支持4位整数",
      productPlanWeight: "请输入计划重量",
      productPlanWeightFomart: "请输入大于0的数字，支持小数点前8位，小数点后3位",
      productMiniWeight: '请输入最小发货重量',
      productMiniWeightFomart:'请输入大于0的数字，支持小数点前8位，小数点后3位',
      outboundTypeTip: "请选择出库类型",
      plannedReceivedTimeTip: "请选择要求到货时间",
      customerNameTip: "请输入客户",
      supplierNameTip: "请输入供应商",
      contactPersonTip: "请输入联系人",
      customerAreaCodeTip: "请选择区号",
      customerMobileTip: "请输入联系电话",
      deliveryTypeTip: "请选择配送方式",
      countryTip: "请选择国家",
      areaTip: "请选择地区",
      addressTip: "请输入详细地址",
      nameFomart: "可输入中文、英文和数字",
      plannedDeliveryTimeRules:'请完善计划发货时间',
    },
  },
};
