export default {
  warehouseEntryNotice: {
    label: {
      receiptNoticeCode: "入库通知单号",
      receivingOrderCode: "收运单号",
      receivingOrderCodeCopy: "收运单",
      receiptType: "单据类型",
      purchaseInventory: "采购入库",
      returnStorage: "退货入库",
      status: "入库状态",
      statusCopy: "状态",
      receivingStatus:'收运状态',
      initial: "初始",
      receivingShipped:"收运中",
      receivedShipped: "已收运",
      finished: "完结",
      draft: "草稿",
      sourceOrderCode: "来源单号",
      isReceived: "是否领单",
      not:"否",
      yes:"是",
      plannedDeliveryTime: "计划交货时间",
      documentSynchronizationTime: "单据同步时间",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      startTime: "开始时间",
      endTime: "结束时间",
      to: "至",
      name: "客户/供应商",
      customerName: "客户",
      supplierName: "供应商",
      address: "地址",
      contactPerson: "联系人",
      mobile: "联系电话",
      expectedQty: "商品计划总数量",
      expectedWeight: "商品计划总重量",
      productExpectQty: "商品数量",
      purchaseSalesPerson: "采购/销售员",
      remark: "备注",
      manuallyCreate: "手动创建",
      synchronous: "同步",
      source: "来源",
      sourceSystem: "来源系统",
      createUserName: "创建人",
      createTime: "创建时间",
      updateUserName: "最后修改人",
      updateTime: "最后修改时间",
      basicInformation: "基本信息",
      warehouse: "仓库",
      areaCode: "区号",
      country: "国家",
      area: "地区",
      detailAddress: "详细地址",
      goodsDetails: "商品明细",
      productType: "商品种类",
      unitName: "单位",
      plannedQuantity: "计划数量",
      specifications: "规格",
      actualQuantity: "(实际）收运数量",
      actualQuantityCopy: "收运数量",
      actualWeight: "收运重量",
      actualWeightCopy: "收运重量(kg)",
      productPlanWeightCopy: "计划重量(kg)",
      goodsInfor: "商品信息",
      productCode: "商品编码",
      productName: "商品名称",
      productNameCopy: "商品",
      goodsCategory: "商品分类",
      productSpecs: "规格",
      productPlanQty: "计划数量",
      productPlanWeight: "计划重量",
      productPlanWeightUnit: "kg",
      partialStorage: "部分入库",
      allStorage: "全部入库",
      receivingTime: "收运时间",
      receivingDate: "收运日期",
      receiver: "收运人",
      documentSelection: "单据选择",
      goodsReceiving: "商品收运",
      barcode:"条形码",
      receivedQty: "收运数量",
      receivedWeight: "收运重量",
      inventoryQuantity: "入库数量",
      inventoryRealQuantity: "实际入库数量",
      inventoryRealWeight: "实际入库重量",
      attachment: "附件",
      documentRemark: "单据备注",
      printPerson: "打印人",
      printTime: "打印时间",
      storagePerson: "入库人",
      storageDate: "入库日期",
      receivingRemark: "收运备注",
      receiptNoticeRemark:'入库通知单备注',
      storage: "入库",
      storageCode: "入库单号",
      useMessage:'领用信息',
      useTime:'领用时间',
      useUserName:'领用人',
      plannedQty:'计划量',
      receivedQuantity:'收运量',
      productQty:'商品个数',
      quantity:'数量',
      weight:'重量(kg)',
      isAllReceived:'是否全部收运',
      isSorting:'是否分拣',
      productTotalPlanQty:'商品计划总数量',
      productTotalPlanWeight:'商品计划总重量(kg)',
      productTotalReceiveQty:'商品收运总数量',
      productTotalReceiveWeight:'商品收运总重量(kg)',
      storageWeight:'入库重量(kg)',
      receiveDetail:'收运明细',
      boxCode: '箱码',
      scanTime:'扫描时间',
    },
    dateTypeList:{
      receivingTime:'收运时间',
      useTime:'领用时间',
      lastUpdateTime:'最后修改时间',
    },
    button: {
      addBtn: "新增",
      syncBtn: "同步",
      addNextLevel: "新增下级分类",
      back: "返回",
      goodsAdd: "添加",
      cancel: "取消",
      submit: "保存",
      comfirm: "确定",
      receivingOrderBtn: "按单收运",
      upload: "上传",
      confirmBtn: "确认收运",
      toReceive:'去收运',
      saveDraft: "保存草稿",
      openDetail:'查看明细',
      closeBtn:'关闭',
    },
    title: {
      receiptNoticeCode: "入库通知单",
      addTitle: "新增入库通知单",
      editTitle: "编辑入库通知单",
      addProduct: "选择商品",
      addReceivingOrderTitle: "收运",
      receivedProductDetailTitle:'收运商品明细',
    },
    message: {
      deleteTips: "确定删除已创建的入库通知单？",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      deleteCancel: "取消删除！",
      addOrEditGoodsTips: "请选择商品",
      shippingReceiptTips:
        "一个通知单只能收运一次，确认收运后不可更改，是否确认？",
      cancelTip: "已取消",
      productActualQtyTips1: "收运数量不能大于计划数量",
      shippingReceiptSucess: "收运成功",
      productActualQtyTips: "至少有一条收运数量大于0",
      qtyAndWeightTip: "数量或重量不完整",
      codeValideTips: "查询单号必须大于等于4位",
      cancelOrderTips:'确认取消领取单据',
      cancelSuccess: '取消领单成功！',
      pickSuccess: '领单成功！',
    },
    placeholder: {
      productInputTips: "请输入商品名称或商品编码",
      inputLimtTips: "请输入大于等于4位的单号",
    },
    rules: {
      receiptTypeTip: "请选择入库类型",
      plannedDeliveryTimeTip: "请选择计划交货时间",
      customerNameTip: "请输入客户名称",
      supplierNameTip: "请输入供应商名称",

      name: "请输入分类名称",
      nameFomart: "可输入1到5位中文、英文或数字",
      sortFomart: "请输入正确的格式，0和正整数",
      sort: "请输入排序",
      productPlanQty: "请输入计划数量",
      // productPlanQtyFomart: "请输入大于0的数字，支持小数点前8位，小数点后3位",
      productPlanQtyFomart: "计划数量支持大于0的整数",
      expectedWeight: "请输入计划重量",
      expectedWeightFomart: "请输入大于0的数字，支持小数点前8位，小数点后3位",
      productActualQty: "请输入收运数量",
      productActualQtyFomart: "请输入大于0的数字，支持4位整数",
      productActualWeight:'请输入收运重量',
      productActualWeightFomart: "请输入大于0的数字，支持小数点前8位，小数点后3位",
      isSorting:'请选择是否分拣',
    },
  },
};
