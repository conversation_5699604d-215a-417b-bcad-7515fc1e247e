export default {
  reportLossOrder: {
    label: {
      lossOrderCode: "报损单号",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      orderType: "报损类型",
      orderStatus: "状态",
      sourceOrderCode: '拆装单号',
      reporterName: '报损人',
      reportTime: '报损时间',
      createUserName: '创建人',
      createTime: '创建时间',
      remark: '备注',
      basicInformation: "基本信息",
      reportLossInformation: "报损明细",
      productInformation: "商品信息",
      productCode: "商品编码",
      productSpec: "规格",
      warehouseArea: "库区",
      disassemblyOutQty: "数量",
      lossQty: "报损数量",
      activeBtn: "报损",
      inactiveBtn: "不报损",
      receivingStatus: "是否领单",
      receivingUserName: "领单人",
      receivingTime: "领单时间",
      pickingOrderCode: "分拣单号",


      sourceOrderCodeNew: '单号'
    },
    placeholder: {
      lossOrderCode: '请输入大于等于4位的单号'
    },
    dateTypeList: {
      createDate: "创建时间",
      approvalDate: "领用时间",
    },
    reportLossOrderTypeList: {
      productDisassembleInstall: "拆装报损",
      sortingOrder: "分拣报损",
    },
    statusList: {
      draft: "草稿",
      finish: "完结",
    },
    shelfLifeUnitList: {
      day: "天",
      month: "月",
      year: "年",
    },
    button: {
      addReportLossOrder: "新建报损单",
      editReportLossOrder: "编辑报损单",
      saveDraft: "保存草稿",
      confirm: "提交",
    },
    title: {
    },
    message: {
      deleteTips: "是否确认删除已创建的报损单？",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功！",
      saveSucess: "保存草稿成功！",
      submitTips: "一个拆装单只能报损一次，提交后不可修改，是否继续？",
      submitSucess: "提交成功！",
      submitConcel: "已取消提交！",
      lossQtyTips: "报损数量不能大于出库数量！",
      reportLossOrderTips: "报损明细不能为空！",
      reportLossOrderLessTips: "报损明细中最少有一条报损数量！",
      codeValideTips: "查询单号必须大于等于4位",
    },
    rules: {
      orderType: "请选择报损类型",
      sourceOrderCode: '请选择拆装单号',
      lossQty: '请输入报损数量',
      lossQtyFormat: '请输入大于0的数字，支持小数点前8位后3位',
      lossWeight: '请输入报损转换量',
      lossWeightFormat: '请输入大于0的数字，支持小数点前8位后3位'
    },
    receivingStatusList: {
      notReceived: "未领单",
      received: "已领单",
    },


  },
};
