export default {
  disassemblyAssembleTemplate: {
        label: {
            templateCode: "模板编码",
            templateName: "模板名称",
            applyName: "申请人",
            apply:  "申请",
            applyTime: "申请时间",
            templateStatus: "状态",
            remark:  "备注",
            approve: '审核',
            approveTime: "审核时间",
            approveName: "审核人",
            approveResult:  "审核结果",
            createUserName:  "创建人",
            createTime:  "创建时间",
            draft : "草稿",
            unApprovePass : '驳回',
            unApprove : '未审核',
            approvePass : '审核通过',
            stop : '停用',
            isApprove: '是否审核',
            approveUser:  '审核人',
            basicInformation: "基本信息",
            templateDetail: '拆装模板明细',
            productInformation:"商品信息",
            productCode:"商品编码",
            productSpec: "规格",
            productUnit: " 单位",
            allUnitIncreaseAndDecrease: "一级单位增减",
            productQty: "数量",
            productTransformQty: "转换量",
            today: "今天",
            yesterday: "昨天",
            weekday: "近七天",
            approveDesc: "审核意见",
            pass: "通过",
            reject: "驳回",
            sourceProduct: "源商品",
            targetProduct: "目标商品",

        },
        placeholder:{
            templateCode :'请输入大于等于4位的模板编码',
            templateName :'请输入模板名称',
            applyName :'请输入申请人',
            templateStatus: '请选择状态',
            systemGenerated: "系统生成",
            approveDesc: '请输入审核意见',

            startTime: '开始时间',
            endTime: '结束时间',
        },
        dateTypeList: {
            applyTime: "申请时间",
            approveTime: "审批时间",
            createTime: "创建时间",
            updateTime: "更新时间",
        },

        templateStatusList: {
            0 : "草稿",
            1 : '未审核',
            2 : '审核通过 ',
            3 : '驳回 ',
            4 : '停用'
        },
        approveStatus: {
            1: "待审核",
            2: "已同意",
            3: "以驳回",
        },
        whetherOption: {
          yes: "是",
          no: "否",
        },
        shelfLifeUnitList:{
            day: "天",
            month: "月",
            year: "年",
        },
        button: {
          addTemplate: '新建模板',
          deactivate: '停用',
          addProduct:  "添加商品",
          detail: "详情",
          stop: "停用",
          approve: "审核",
          edit: "编辑",
          delete: "删除",
          submit: "提交",
          draft: "保存草稿",
          back: "返回",

          editDisassemblyAssembleTemplate: '编辑拆装模板',
          addDisassemblyAssembleTemplate: '新建拆装模板',
          disassemblyAssembleTemplateCode: '拆装模板编码',
        },
        title: {
            addProduct: "添加商品",
            addSourceProduct: "添加源商品",
            addTargetProduct: "添加目标商品",
        },
        message: {
            deleteTips: "是否确认删除已创建的拆装模板？",
            stopTips: "停用后，模板不可使用，是否确认停用审核通过的拆装模板",
            deleteCancel: "已取消删除！",
            deleteSucess: "删除成功！",
            addOrEditDisassemblyAssembleTemplateTips: "拆装模板明细不能为空！",
            approveUser:  '请选择审核人',
            deleteConcel: "已取消删除！",
            saveDraftSucess: "保存草稿成功！",
            actionSucess:'操作成功',
            submitSucess:  "提交成功！",
            stopSuccess: "停用成功！",
            stopCancel: "已取消停用！",
            submitTip: "提交后不可修改，是否继续",
            submitCancel: "已取消提交！",
            approveSuccess: "审核成功！",


            codeValideTips: "查询编码必须大于等于4位",
            selectNumTips1: "已选",
            selectNumTips2: "个商品",
            confirmSucess: "确认成功！",
            confirmConcel: "已取消确认！",
            changeReceiptNoticeCodeConcel: "已取消切换！",
            targetProductReapetTips: "目标商品重复(商品+库区为唯一标识)！",
            pleaseSelect: "请选择要停用的模板",
        },
        rules: {
           isApprove: '请选择是否审核',
            productQty: '请输入数量',
            productTransformQty: '请输入转换量',

            disassemblyQtyFormat: '请输入大于0的数字，支持小数点前8位后3位',
            disassemblyConvertedQtyFormat: '请输入大于0的数字，支持小数点前8位后3位',
            productTransformQtyFormat: '请输入大于0的数字，支持小数点前8位后3位',


          disassemblyOutWeightFormat: '请输入大于0的数字，支持小数点前8位后3位',
          disassemblyInWeightFormat: '请输入大于0的数字，支持小数点前8位后3位',

        },
    },
};
