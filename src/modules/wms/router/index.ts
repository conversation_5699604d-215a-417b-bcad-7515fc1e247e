import { RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/core/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/wms/goods",
    component: Layout,
    children: [
      {
        path: "add",
        component: () =>
          import("@wms/views/fileManagement/goodsManagement/add.vue"),
        meta: {
          title: "新增商品",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/wms/fileManagement",
    component: Layout,
    children: [
      {
        path: "category",
        component: () =>
          import("@wms/views/fileManagement/categoryManagement/index.vue"),
        meta: {
          title: "文件上传",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/wms/insideWarehouseManagement",
    component: Layout,
    children: [
      {
        path: "addInventoryTransfer",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/inventoryTransfer/addInventoryTransfer.vue"
          ),
        meta: {
          title: "新建库存转移",
          hidden: true,
        },
      },
      {
        path: "addReportLossOrder",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/reportLossOrder/addReportLossOrder.vue"
          ),
        meta: {
          title: "新建报损单",
          hidden: true,
        },
      },
      {
        path: "editReportLossOrder",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/reportLossOrder/editReportLossOrder.vue"
            ),
        meta: {
          title: "编辑报损单",
          hidden: true,
        },
      },

      {
        path: "detailReportLossOrder",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/reportLossOrder/detailReportLossOrder.vue"
            ),
        meta: {
          title: "报损单详情",
          hidden: true,
        },
      },
      {
        path: "addProductDisassemblyAssembleOrder",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/productDisassemblyAssembleOrder/addProductDisassemblyAssembleOrder.vue"
          ),
        meta: {
          title: "新建商品拆装单",
          hidden: true,
        },
      },
      {
        path: "addQualityInspectionOrder",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/qualityInspectionOrder/addQualityInspectionOrder.vue"
          ),
        meta: {
          title: "新建质检单",
          hidden: true,
        },
      },
      {
        path: "addDisassemblyAssembleTemplate",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/disassemblyAssembleTemplate/addTemplate.vue"
            ),
        meta: {
          title: "新建拆装模板",
          hidden: true,
        },
      },
      {
        path: "detailDisassemblyAssembleTemplate",
        component: () =>
          import(
            "@wms/views/insideWarehouseManagement/disassemblyAssembleTemplate/detail.vue"
            ),
        meta: {
          title: "拆装模板详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/wms/inventory",
    component: Layout,
    children: [
      {
        path: "addProfitAndLoss",
        component: () =>
          import(
            "@wms/views/inventory/profitAndLossManagement/addProfitAndLoss.vue"
          ),
        meta: {
          title: "新建损益",
          hidden: true,
        },
      },
      {
        path: "detailProfitAndLoss",
        component: () =>
          import(
            "@wms/views/inventory/profitAndLossManagement/detailProfitAndLoss.vue"
          ),
        meta: {
          title: "损益详情",
          hidden: true,
        },
      },
    ],
  },
  {
    // /wms/inventory/inventoryOrder/add
    path: "/wms/inventory",
    component: Layout,
    children: [
      {
        path: "inventoryOrder/add",
        component: () =>
          import(
            "@wms/views/inventory/inventoryOrder/add.vue"
          ),
        meta: {
          title: "新建损益",
          hidden: true,
        },
      },
    ],
  },
  {
    // /wms/inventory/inventoryOrder/add
    path: "/wms/inventory",
    component: Layout,
    children: [
      {
        path: "inventoryOrder/detail",
        component: () =>
          import(
            "@wms/views/inventory/inventoryOrder/detail.vue"
          ),
        meta: {
          title: "盘点单详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/wms/transferManagement",
    component: Layout,
    children: [
      {
        path: "transferOrder/add",
        component: () =>
          import("@wms/views/transferManagement/transferOrder/add.vue"),
        meta: {
          title: "新增调拨单",
          hidden: true,
        },
      },
      {
        path: "transferOrder/edit/:transferOrderCode",
        component: () =>
          import("@wms/views/transferManagement/transferOrder/add.vue"),
        meta: {
          title: "编辑调拨单",
          hidden: true,
        },
      },
      {
        path: "transferOrder/detail/:transferOrderCode",
        component: () =>
          import("@wms/views/transferManagement/transferOrder/detail.vue"),
        meta: {
          title: "调拨单详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/wms/storeManagement",
    component: Layout,
    children: [
      {
        path: "addWarehouseEntryNotice",
        component: () =>
          import(
            "@wms/views/storeManagement/warehouseEntryNotice/addWarehouseEntryNotice.vue"
          ),
        meta: {
          title: "新增入库通知单",
          hidden: true,
        },
      },
      {
        path: "warehouseEntryNoticeDetail",
        component: () =>
          import(
            "@wms/views/storeManagement/warehouseEntryNotice/warehouseEntryNoticeDetail.vue"
          ),
        meta: {
          title: "入库通知单详情",
          hidden: true,
        },
      },
      {
        path: "addReceivingOrders",
        component: () =>
          import(
            "@wms/views/storeManagement/shippingReceipt/addReceivingOrders.vue"
          ),
        meta: {
          title: "收运",
          hidden: true,
        },
      },
      {
        path: "toReceivingOrders",
        component: () =>
          import(
            "@wms/views/storeManagement/shippingReceipt/toReceivingOrders.vue"
            ),
        meta: {
          title: "收运",
          hidden: true,
        },
      },
      {
        path: "receivingOrderDetail",
        component: () =>
          import(
            "@wms/views/storeManagement/shippingReceipt/receivingOrderDetail.vue"
          ),
        meta: {
          title: "收运单详情",
          hidden: true,
        },
      },
      {
        path: "addWarehousingEntryOrders",
        component: () =>
          import(
            "@wms/views/storeManagement/warehousingEntry/addWarehousingEntryOrders.vue"
          ),
        meta: {
          title: "去入库",
          hidden: true,
        },
      },
      {
        path: "editWarehousingEntryOrders",
        component: () =>
          import(
            "@wms/views/storeManagement/warehousingEntry/editWarehousingEntryOrders.vue"
          ),
        meta: {
          title: "去入库",
          hidden: true,
        },
      },
      {
        path: "warehousingEntryOrderDetail",
        component: () =>
          import(
            "@wms/views/storeManagement/warehousingEntry/warehousingEntryOrderDetail.vue"
          ),
        meta: {
          title: "入库单详情",
          hidden: true,
        },
      },
      {
        path: "editPickOrder",
        component: () =>
          import(
            "@wms/views/storeManagement/pickOrder/editPickOrder.vue"
            ),
        meta: {
          title: "去分拣",
          hidden: true,
        },
      },
      // 快速入库相关路由
      {
        path: "quickWarehousing/add",
        component: () =>
          import("@wms/views/storeManagement/quickWarehousing/add.vue"),
        meta: {
          title: "新增快速入库",
          hidden: true,
        },
      },
      {
        path: "quickWarehousing/edit",
        component: () =>
          import("@wms/views/storeManagement/quickWarehousing/add.vue"),
        meta: {
          title: "编辑快速入库",
          hidden: true,
        },
      },
      {
        path: "quickWarehousing/detail",
        component: () =>
          import("@wms/views/storeManagement/quickWarehousing/detail.vue"),
        meta: {
          title: "快速入库详情",
          hidden: true,
        },
      },
      {
        path: "quickWarehousing/warehousing",
        component: () =>
          import("@wms/views/storeManagement/quickWarehousing/warehousing.vue"),
        meta: {
          title: "去入库",
          hidden: true,
        },
      },
      {
        path: "editQuickPickOrder",
        component: () =>
          import(
            "@wms/views/storeManagement/quickPickOrder/editPickOrder.vue"
            ),
        meta: {
          title: "去分拣",
          hidden: true,
        },
      },
    ],
  },
  {
    path: "/wms/outboundManagement",
    component: Layout,
    children: [
      {
        path: "addOutboundNotice",
        component: () =>
          import(
            "@wms/views/outboundManagement/outboundNotice/addOutboundNotice.vue"
          ),
        meta: {
          title: "新增出库通知单",
          hidden: true,
        },
      },
      {
        path: "detailOutboundNotice",
        component: () =>
          import(
            "@wms/views/outboundManagement/outboundNotice/detailOutboundNotice.vue"
          ),
        meta: {
          title: "出库通知单详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path: '/wms',
    component: Layout,
    children: [
      {
        path: "pickingOrder/add",
        component: () =>
          import("@wms/views/outboundManagement/pickingOrder/add.vue"),
        meta: {
          title: "拣货",
          hidden: true,
        },
      },
      {
        path: "pickingOrder/detail",
        component: () =>
          import("@wms/views/outboundManagement/pickingOrder/detail.vue"),
        meta: {
          title: "拣货单详情",
          hidden: true,
        },
      },
    ],
  },
  {
    path:'/wms',
    component: Layout,
    children: [
      {
        path:'quickOutbound/confirmOutbound',
        component: () =>
          import("@wms/views/outboundManagement/quickOutbound/confirmOutbound.vue"),
        meta: {
          title: "确认出库",
          hidden: true,
        },
      },
      {
        path:'quickOutbound/detailQuickOutbound',
        component: () =>
          import("@wms/views/outboundManagement/quickOutbound/detailQuickOutbound.vue"),
        meta: {
          title: "出库详情",
          hidden: true,
        },
      },
    ],
  },
];
