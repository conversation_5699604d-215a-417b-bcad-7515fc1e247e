import request from "@/core/utils/request";
const PURCHASE_BASE_URL = "/supply-wms/warehouseOutboundNotice";

class QuickOutboundApi{

  //分页列表
  static queryPageList(queryParams?: QuickOutboundQueryPage){
    return request<any, PageResult<QuickOutboundResponse[]>>({
      url: `${PURCHASE_BASE_URL}/queryFastPageList`,
      method: "post",
      data: queryParams,
    })
  }

  //排班
  static doScheduling(data:any){
    return request({
      url:`${PURCHASE_BASE_URL}/doScheduling`,
      method:'post',
      data:data
    })
  }
  //取消排班
  static cancelScheduling(data:any){
    return request({
      url:`${PURCHASE_BASE_URL}/cancelScheduling`,
      method:'post',
      data:data
    })
  }
  //确认出库
  static submitOutbound(data:any){
    return request({
      url:`${PURCHASE_BASE_URL}/submitOutbound`,
      method:'post',
      data:data
    })
  }
  //详情查询
  static queryFastDetail(data:any){
    return request({
      url:`${PURCHASE_BASE_URL}/queryFastDetail`,
      method:'post',
      data:data
    })
  }
  //详情查询加密
  static queryFastDetailEncrypt(data:any){
    return request({
      url:`${PURCHASE_BASE_URL}/queryFastDetailEncrypt`,
      method:'post',
      data:data
    })
  }
  //配送方式
  static queryDeliveryMethodList(data?: any){
    return request({
      url: `/supply-biz-common/deliveryMethods/queryPageList`,
      method: "post",
      data: data
    })
  }
  //完结
  static completedOutbound(data?: any){
    return request({
      url:`${PURCHASE_BASE_URL}/completedOutbound`,
      method:'post',
      data:data
    })
  }
  //导出
  static exportList(data?: any){
    return request({
      url:`${PURCHASE_BASE_URL}/export`,
      method:'post',
      data:data
    })
  }
};

export default QuickOutboundApi;

//分页查询请求参数
export interface QuickOutboundQueryPage extends PageQuery{
  /** 出库通知单号 */
  outboundNoticeCode?: string;
  /** 主题描述 */
  orderTheme?: string;
  /** 出库类型 */
  outboundType?: any;
  /** 状态 */
  outboundNoticeStatus?: any;
  /** 来源单号 */
  sourceOrderCode?: string;
  /** 是否预售单 */
  isPresale?: number;
  /** 时间类型(1->创建时间，2->计划交货时间，3->出库时间，4->要求到货时间)*/
  dateType?: number;
}

//分页查询响应参数
export interface QuickOutboundResponse {
  /** ID */
  id?: string;
  /** 主题描述 */
  orderTheme?: string;
  /** 是否预售单 */
  isPresale?: number;
  /** 出库通知单号 */
  outboundNoticeCode?: string;
  /** 出库类型 */
  outboundType?: number;
  /** 计划配送方式 */
  sourceDeliveryType?: number;
  sourceDeliveryName?: string;
  /** 计划总量 */
  totalPlanProductQty?: string;
  /** 计划转换量 */
  totalPlanProductWeight?: string;
  /** 出库总量 */
  totalOutboundQty?: string;
  /** 出库转换量 */
  totalOutboundWeight?: string;
  /** 计划发货时间 */
  plannedDeliveryTime?: string;
  /** 要求到货时间 */
  plannedReceivedTime?: string;
  /** 来源单号 */
  sourceOrderCode?: string;
  /** 采购/销售员 */
  purchaseSalesPerson?: string;
  /** 申请人 */
  createUserName?: string;
  /** 申请时间 */
  createTime?: string;
  /** 客户 */
  customerName?: string;
  /** 供应商 */
  supplierName?: string;
  /** 结算方式 */
  paymentType?: number;
  /** 合同编码 */
  contractCode?: string;
  /** 合同分类 */
  contractType?: number;
  /** 备注 */
  remark?: string;
  /** 状态 */
  outboundNoticeStatus?: number;
}
//详情参数
export interface detailResponse {
  /** ID */
  id?: string;
  /** 出库通知单号 */
  outboundNoticeCode?: string;
  /** 出库类型 */
  outboundType?: number;
  /** 状态 */
  outboundNoticeStatus?: number;
  /** 主题描述 */
  orderTheme?: string;
  /** 来源单号 */
  sourceOrderCode?: string;
  /** 业务员 */
  purchaseSalesPerson?: string;
  /** 原单号 */
  sourceCode?: string;
  /** 申请人 */
  createUserName?: string;
  /** 申请时间 */
  createTime?: string;
  /** 客户 */
  customerName?: string;
  /** 客户地址 */
  address?: string;
  /** 客户联系人 */
  contactPerson?: string;
  /** 客户联系电话 */
  customerMobile?: string;
  /** 供应商 */
  supplierName?: string;
  /** 供应商地址 */
  supplierAddress?: string;
  /** 供应商联系人 */
  supplierContactPerson?: string;
  /** 供应商联系电话 */
  supplierContactMobile?: string;
  /** 合同名称 */
  contractName?:string;
  /** 合同编码 */
  contractCode?: string;
  /** 合同分类 */
  contractType?: number;
  /** 结算方式 */
  paymentType?: number;
  /** 要求到货时间 */
  plannedReceivedTime?: string;
  /** 计划发货时间 */
  plannedDeliveryTime?: string;
  /** 计划配送方式 */
  deliveryType?: number;
  deliveryName?: string;
  /** 来源单备注 */
  remark?: string;
}
//确认出库参数
export interface confirmOutboundRequest {
  /** ID */
  id?: string;
  /** 出库通知单号 */
  outboundNoticeCode?: string;
  /** 出库类型 */
  outboundType?: number;
  /** 状态 */
  outboundNoticeStatus?: number;
  /** 主题描述 */
  orderTheme?: string;
  /** 来源单号 */
  sourceOrderCode?: string;
  /** 业务员 */
  purchaseSalesPerson?: string;
  /** 原单号 */
  sourceCode?: string;
  /** 申请人 */
  createUserName?: string;
  /** 申请时间 */
  createTime?: string;
  /** 客户 */
  customerName?: string;
  /** 地址 */
  address?: string;
  /** 联系人 */
  address?: string;
  /** 联系电话 */
  customerMobile?: string;
  /** 供应商 */
  supplierName?: string;
  /** 供应商地址 */
  supplierAddress?: string;
  /** 供应商联系人 */
  supplierContactPerson?: string;
  /** 供应商联系电话 */
  supplierContactMobile?: string;
  /** 合同名称 */
  contractName?:string;
  /** 合同编码 */
  contractCode?: string;
  /** 合同分类 */
  contractType?: number;
  /** 结算方式 */
  paymentType?: number;
  /** 要求到货时间 */
  plannedReceivedTime?: string;
  /** 计划发货时间 */
  plannedDeliveryTime?: string;
  /** 计划配送方式 */
  deliveryType?: string;
  /** 计划配送方式名称 */
  deliveryName?: string;
  /** 来源单备注 */
  remark?: string;
  /** 承运商 */
  carrier?: string;
  /** 车号 */
  carNumber?: string;
  /** 磅单编号 */
  poundCode?: string;
  /** 磅单附件 */
  poundAttachmentFiles?: string;
  /** 实际配送方式 */
  actualDeliveryType?: number;
  /** 实际配送方式名称 */
  actualDeliveryName?: number;
  /** 实际出库时间 */
  outboundTime?: string;
  /** 出库备注 */
  outboundRemark?: string;
}
