import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "/supply-wms/disassemblyTemplate";

class DisassemblyAssembleTemplateAPI {

    /** 获取拆装模板分页数据 */
    static getDisassemblyAssembleTemplatePage(queryParams?: DisassemblyAssembleTemplatePageQuery) {
        return request<any, PageResult<DisassemblyAssembleTemplatePageVO[]>>({
            url: `${PURCHASE_BASE_URL}/queryPageList`,
            method: "post",
            data: queryParams,
        });
    }

    /**删除拆装单 */
    static deleteDisassemblyAssembleTemplate(data: { id?:string }) {
        return request({
            url: `${PURCHASE_BASE_URL}/delete`,
            method: "post",
            data: data,
        });
    }

    /**新增拆装模板 */
    static addDisassemblyAssembleTemplate(data: DisassemblyAssembleTemplate) {
        return request({
            url: `${PURCHASE_BASE_URL}/add`,
            method: "post",
            data: data,
        });
    }

    /**提交拆装模板 */
    static submitDisassemblyAssembleTemplate(data: DisassemblyAssembleTemplate) {
        return request({
            url: `${PURCHASE_BASE_URL}/edit`,
            method: "post",
            data: data,
        });
    }

    /** 拆装模板详情 */
    static getDisassemblyAssembleTemplateDetail(data:{id?:string}) {
        return request({
            url: `${PURCHASE_BASE_URL}/queryDetail`,
            method: "post",
            data: data,
        });
    }


  /** 拆装模板审核 */
  static approveDisassemblyAssembleTemplateDetail(data:approveTemplate) {
    return request({
      url: `${PURCHASE_BASE_URL}/approve`,
      method: "post",
      data: data,
    });
  }

  /** 拆装模板停用 */
  static stopDisassemblyAssembleTemplateDetail(data:{id?:string}) {
    return request({
      url: `${PURCHASE_BASE_URL}/disable`,
      method: "post",
      data: data,
    });
  }

  /** 拆装模板批量停用 */
  static batchStopDisassemblyAssembleTemplateDetail(data: string[]) {
    return request({
      url: `${PURCHASE_BASE_URL}/disableBatch`,
      method: "post",
      data: data,
    });
  }
}




export default DisassemblyAssembleTemplateAPI;

/** 拆装模板分页查询参数 */
export interface DisassemblyAssembleTemplatePageQuery extends PageQuery {
    /** 模板编码 */
    disassemblyTemplateCode?: string;
    /** 模板名称 */
    disassemblyTemplateName?: string;
    /** 申请人*/
    applyUserName?: string;
    /** 状态*/
    statusList?: number[];
    /** 时间类型(1->创建时间，2->拆装时间)*/
    queryTimeType?: number;
    /** 时间范围 */
    dateRange?: string[];
}

/** 拆装单模板分页对象 */
export interface DisassemblyAssembleTemplatePageVO {
    /** ID */
    id?: string;
    /** 模板编码 */
    templateCode?: string;
    /** 模板名称 */
    templateName?: string;
    /** 申请人 */
    applyName?: string;
    /** 申请时间 */
    applyDate?: string;
    /** 备注 */
    remark?: string;
    /** 创建人 */
    createUserName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 状态 (0->草稿，1->驳回,2->停用,3->待审核,4->审核通过,)*/
    orderStatus?: number;
}

/**根据拆装单号查询报损商品对象(报损单创建页面) */
export interface ProductVO {
  /** ID */
  id?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品规格 */
  productSpec?: string;
  /** 商品单位 */
  productUnitName?: string;
  /** 数量 */
  totalStockQty?: number;
  /** 可用库存 */
  availableStockQty?: number;
  /** 源出库库区编码 */
  sourceWarehouseAreaId?: string;
  /** 源出库库区名称 */
  sourceWarehouseAreaName?: string;
  /** 源出库数量 */
  disassemblyInQty?: number;
  /** 目标入库库区编码 */
  targetWarehouseAreaId?: string;
  /** 目标入库库区名称 */
  targetWarehouseAreaName?: string;
  /** 入库数量 */
  disassemblyOutQty?: number;
}

/** 拆装单模板对象 */
export interface DisassemblyAssembleTemplateFrom {
  /** ID */
  id?: string;
  /** 模板编码 */
  templateCode?: string;
  /** 模板名称 */
  disassemblyTemplateName?: string;
  /** 是否审核 */
  isApprove?: number;
  /** 备注 */
  remark?: string;
  /** 审核人 */
  approveUser?: number;
  /** 审核人名称 */
  approveUserName?: string;
  /** 拆装时间 */
  disassemblyTime?: string;
  /** 创建人 */
  createUserName?: string;
  /** 创建时间 */
  createTime?: string;

  /** 模板源数据明细 */
  sourceList?: ProductList[];
  /** 模板目标数据明细 */
  targetList?: ProductList[];
}


export interface approveTemplate {
  id?:string,
  approveStatus?: number,
  approveRemark?: string
}
export interface DisassemblyAssembleTemplate {
  disassemblyTemplateName?: string;
  disassemblyTemplateProductSourceList?: templateProductList[];
  disassemblyTemplateProductTargetList?: templateProductList[];
  isApprove:  number;
  remark: string,
  // saveType=1  草稿  saveType=2 提交
  saveType: number
  approveUser: number
  approveUserName: string
  [key: string]: any;
}

export interface templateProductList {
  belongType?: number;
  convertedQty?: number;
  convertedQtyUnitName?: string;
  isDiscreteUnit?: number;
  productCode?: string;
  productName?: string;
  productSpec?: string;
  qty?: number;
  qtyUnitName?: string;
}


/** 拆装单对象(产品对象) */
export interface ProductList{
  /** ID */
  id?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品规格 */
  productSpec?: string;
  /** 商品单位 */
  productUnitName?: string;
  /** 数量 */
  totalStockQty?: number;
  /** 可用库存 */
  availableStockQty?: number;
  /** 源出库库区编码 */
  sourceWarehouseAreaId?: string;
  /** 源出库库区名称 */
  sourceWarehouseAreaName?: string;
  /** 源出库数量 */
  disassemblyOutQty?: number;
  /** 目标入库库区编码 */
  targetWarehouseAreaId?: string;
  /** 目标入库库区名称 */
  targetWarehouseAreaName?: string;
  /** 入库数量 */
  disassemblyInQty?: number;
  /** 入库通知单 */
  receiptNoticeCode?: string;
  /** 入库类型（入库通知单-16） */
  sourceOrderType?: number;
}
