import request from "@/core/utils/request";

const WAREHOUSR_BASE_URL = "/supply-wms/receivingOrders";

class shippingReceiptAPI {
  /** 获取分页数据 */
  static queryPageList(queryParams?: PageQuery) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取国际国家列表-含港澳台
   *
   */
  static getAllCountry() {
    return request({
      url: `/supply-base/country/all`,
      method: "get",
    });
  }

  /** 添加 */
  static addReceivingOrders(data: addFormData) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

  /** 详情 */
  static queryDetail(data: any) {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryDetail`,
      method: "post",
      data: data,
    });
  }

  /** 获取收运单下拉数据 (质检单创建页面)*/
  static getReceivingOrderList() {
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryInspectionListByOrderCode`,
      method: "post",
      data: {},
    });
  }

  /**根据收运单查询质检商品列表(质检单创建页面) */
  static queryInspectionProductListByOrderCode(data: {
    receivingOrderCode?: string;
  }) {
    return request<any, PageResult<ProductVO[]>>({
      url: `${WAREHOUSR_BASE_URL}/queryInspectionProductListByOrderCode`,
      method: "post",
      data: data,
    });
  }

  /** 领单-取消领单 */
  static receivingOrder(data: {receivingOrderId?: number; isCancel?: boolean}){
    return request({
      url: `${WAREHOUSR_BASE_URL}/receivingOrder`,
      method: 'post',
      data: data
    })
  }

  /** 收运明细 */
  static queryScanPageInfo(data ?: queryDetailDto){
    return request({
      url: `${WAREHOUSR_BASE_URL}/queryScanPageInfo`,
      method: 'post',
      data: data
    })
  }

  /** 去收运-收运确认 */
  static submitReceiving(data ?: addFormData){
    return request({
      url: `${WAREHOUSR_BASE_URL}/submitReceiving`,
      method: 'post',
      data: data
    })
  }
}

export interface addFormData {
  receiptNoticeCode?: string;
  receiptNoticeId?: string;
  sourceOrderCode?: string;
  queryType?: number;
  queryCode?: any;
  plannedDeliveryTime?: string;
  receiptType?: number;
  purchaseSalesPerson?: string;
  customerName?: string;
  supplierName?: string;
  address?: string;
  contactPerson?: string;
  countryAreaCode?: string;
  mobile?: string;
  productList?: any;
  remark?: string;
  goodsRemark?: string;
  receiptNoticeId?: string;
  receiver?: string;
  receivingTime?: string;
  status?: number;
  countryName?: string;
  cityName?: string;
  districtName?: string;
  provinceName?: string;
  fullAddress?: string;
  isSorting?: number;
  useStatus?: number;
  useUserName?: string;
  useTime?: number;
  receivedStatus?: number;
  expectedQty?: number;
  expectedWeight?: number;
  receivedQty?: number;
  receivedWeight?: number;
}

export interface shippingReceiptPageQuery extends PageQuery {
  /** 入库通知单号 */
  receiptNoticeCode?: string;
  /** 来源单号 */
  sourceOrderCode?: number;
  /** 收运单号 */
  receivingOrderCode?: string;
  /** 开始时间 */
  queryStartTime?: any;
  /** 结束时间 */
  queryEndTime?: any;
  /** 入库状态 */
  statusList?: any;
  /** 收运状态 */
  isReceivedStatusList?: any;
  /** 是否领单 */
  useStatus?: number;
  /** 时间类型 */
  queryType?: number;
}

/** 根据拆装单号查询拆装商品对象(拆装单创建页面) */
export interface ProductVO {
  /** ID */
  id?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品规格 */
  productSpecs?: string;
  /** 商品单位 */
  productUnitName?: string;
  /** 数量 */
  productExpectQty?: number;
  /** 可用库存 */
  productInventoryQty?: number;
  /** 收运数量 */
  productActualQty?: number;
  /* /!** 源出库库区编码 *!/
    sourceWarehouseAreaId?: string;
    /!** 源出库库区名称 *!/
    sourceWarehouseAreaName?: string;
    /!** 源出库数量 *!/
    disassemblyOutQty?: number;
    /!** 目标入库库区编码 *!/
    targetWarehouseAreaId?: string;
    /!** 目标入库库区名称 *!/
    targetWarehouseAreaName?: string;
    /!** 入库数量 *!/
    disassemblyInQty?: number;*/
}
/** 收运明细查询参数 */
export interface queryDetailDto {
  businessId ?: string
}

export default shippingReceiptAPI;
