import request from "@/core/utils/request";
const BASE_URL = '/supply-wms/productStockYsn'

// 查询参数类型定义
export interface QueryAbnormalYsnParams {
  limit: number;
  page: number;
  productSearch?: string; // 商品查询条件
  profitLossCode?: string; // 损益单号
  ysnCode?: string; // ysn编码
}

// 返回数据项定义
export interface AbnormalYsnItem {
  actualWeight: number;
  createTime: string;
  createUser: number;
  id: number;
  orgCode: string;
  originalWeight: number;
  productCode: string;
  productName: string;
  productStockId: number;
  tenantId: string;
  updateTime: string;
  updateUser: number;
  version: number;
  warehouseAreaCode: string;
  warehouseAreaName: string;
  warehouseCode: string;
  warehouseLocationCode: string;
  ysnCode: string;
  ysnStatus: number;
  ysnStatusDesc: string;
  ysnStrategyCode: string;
  profitLossCode?: string; // 损益单号
}

// 响应类型定义
export interface AbnormalYsnPageResponse {
  code: number;
  data: {
    current: number;
    pages: number;
    records: AbnormalYsnItem[];
    size: number;
    total: number;
  };
  extra: Record<string, any>;
  message: string;
  path: string;
  timestamp: number;
}

class YsnManagementApi{
  /** 分页查询异常YSN */
  static queryPage(params: QueryAbnormalYsnParams) {
    return request<AbnormalYsnPageResponse>({
      url: `${BASE_URL}/queryAbnormalYsnPageList`,
      method: 'post',
      data: params,
    });
  }
  
  /** 查询异常YSN列表 */
  static queryAbnormalYsnPageList(params: QueryAbnormalYsnParams) {
    return request<AbnormalYsnPageResponse>({
      url: '/supply-wms/productStockYsn/queryAbnormalYsnPageList',
      method: 'post',
      data: params,
    });
  }
}

export default YsnManagementApi
/**
 * 分页查询请求实体
 */
export interface queryPageDto extends PageQuery{
  productSearch?: string;
  profitLossCode?: string;
  ysnCode?: string;
}
/**
 * 分页查询响应实体
 */
export interface queryPageResponse	{
  actualWeight: number;
  createTime: string;
  createUser: number;
  id: number;
  orgCode: string;
  originalWeight: number;
  productCode: string;
  productName: string;
  productStockId: number;
  tenantId: string;
  updateTime: string;
  updateUser: number;
  version: number;
  warehouseAreaCode: string;
  warehouseAreaName: string;
  warehouseCode: string;
  warehouseLocationCode: string;
  ysnCode: string;
  ysnStatus: number;
  ysnStatusDesc: string;
  ysnStrategyCode: string;
}

