import request from "@/core/utils/request";
const PRODUCTBRAND_BASE_URL = "/supply-wms";

class ProductMgAPI {
  /** 分页查询 */
  static getPageList(data?: PageQuery) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/pickingList/queryPageList`,
      method: "post",
      data,
    });
  }

  // 保存商品
  static saveProduct(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/product/product/save`,
      method: "post",
      data,
    });
  }


  // 根据id查询商品详情
  static getProductDetailById(params: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/pickingList/queryDetail`,
      method: "get",
      params,
    });
  }

  // 完成拣货
  static completePick(data: {}) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/pickingList/completePick`,
      method: "post",
      data,
    });
  }

  // 根据出货通知单查询出库通知详情
  static queryDetailByDeliveryNoticeCode(params: { deliveryNoticeCode: string }) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/pickingList/queryDetailByDeliveryNoticeCode`,
      method: "get",
      params,
    });
  }

  // 根据出货通知单查询出库通知详情
  static pickingOrderPrint(data: { pickListId: string }) {
    return request<any, PageResult<[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/pickingList/print`,
      method: "post",
      data,
    });
  }

  // 领单和取消领单
  static receiveOrCancelOrder(data: { id: number; receivingStatus: number }) {
    return request<any, any>({
      url: `${PRODUCTBRAND_BASE_URL}/pickingList/receiveOrCancelOrder`,
      method: "post",
      data,
    });
  }

  // 拣货详情查看YSN 
  static queryPageByPickingListProductDetailId(params: { 
    pickingListProductDetailId: number;
    page?: number;
    limit?: number;
  }) {
    return request<any, any>({
      url: `${PRODUCTBRAND_BASE_URL}/pickingList/queryPageByPickingListProductDetailId`,
      method: "get",
      params,
    });
  }
}

export default ProductMgAPI;
