import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "/supply-wms/qualityInspectionRecords";

class QualityInspectionOrderAPI {

  /** 获取质检单分页数据 */
  static getQualityInspectionOrderPage(queryParams?: QualityInspectionOrderPageQuery) {
    return request<any, PageResult<QualityInspectionOrderPageVO[]>>({
      url: `${PURCHASE_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /** 质检详情查看异常YSN */
  static getQualityInspectionAbnormalYsnPage(queryParams?: QualityInspectionYsnPageQuery) {
    return request<any, PageResult<QualityInspectionYsnPageVO[]>>({
      url: `${PURCHASE_BASE_URL}/queryPageByInspectionProductId`,
      method: "post",
      data: queryParams,
    });
  }

  /** 质检详情查看所有YSN */
  static getQualityInspectionYsnPage(queryParams?: QualityInspectionYsnPageQuery) {
    return request<any, PageResult<QualityInspectionYsnPageVO[]>>({
      url: `${PURCHASE_BASE_URL}/queryYsnCodePage`,
      method: "POST",
        data: queryParams,
    });
  }

  /** 质检详情查看所有箱码 */
  static getQualityInspectionBoxPage(queryParams?: QualityInspectionYsnPageQuery) {
      return request<any, PageResult<QualityInspectionYsnPageVO[]>>({
          url: `${PURCHASE_BASE_URL}/queryBoxCodePage`,
          method: "POST",
          data: queryParams,
      });
  }

  /**删除质检单 */
  static deleteQualityInspectionOrder(data: { id?:string }) {
      return request({
          url: `${PURCHASE_BASE_URL}/delete`,
          method: "post",
          data: data,
      });
  }

    /** 质检单详情 */
    static queryQualityInspectionOrderDetail( data:{id?:string}) {
        return request<any,QualityInspectionOrderFrom>({
            url: `${PURCHASE_BASE_URL}/queryDetail`,
            method: "post",
            data:data
        });
    }

    /** 暂存、提交质检单(添加) */
    static addQualityInspectionOrder( data:QualityInspectionOrderFrom) {
        return request({
            url: `${PURCHASE_BASE_URL}/add`,
            method: "post",
            data:data
        });
    }

    /**暂存，提交质检单(编辑)*/
    static editQualityInspectionOrder( data:QualityInspectionOrderFrom) {
        return request({
            url: `${PURCHASE_BASE_URL}/edit`,
            method: "post",
            data:data
        });
    }

    /**领用或取消领用质检单 */
    static receiveOrCancelQualityInspectionOrder(data: { id?:string ,receivingStatus?:number}) {
        return request({
            url: `${PURCHASE_BASE_URL}/receiveOrCancelOrder`,
            method: "post",
            data: data,
        });
    }

}

export default QualityInspectionOrderAPI;

/** 质检单分页查询参数 */
export interface QualityInspectionOrderPageQuery extends PageQuery {
  /** 质检单号 */
  inspectionCode?: string;
  /** 质检类型  (0->收运质检，1->库内巡检)*/
  // inspectionType?: number;
  inspectionTypeList?: number[];
  /** 状态 (1->草稿，2->质检完成)*/
  // status?: number;
  statusList?: number[];
  /** 领单状态 (0->未领用 1->已领用)*/
  receivingStatus?: number;
  /** 质检结果 (1->异常，0->无异常)*/
  inspectionResultType?: number;
  /** 时间类型(1->创建时间，2->计划交货时间)*/
  dateType?: number;
  /** 时间范围 */
  dateRange?: string[];
}

/** 质检单分页对象 */
export interface QualityInspectionOrderPageVO {
  /** ID */
  id?: string;
  /** 质检单号 */
  inspectionCode?: string;
  /** 质检类型  (0->收运质检，1->库内巡检) */
  inspectionType?: number;
  /** 收运单号 */
  receivingOrderCode?: string;
  /** 库区编码 */
  warehouseAreaCode?: string;
  /** 库区名称 */
  warehouseAreaName?: string;
  /** 质检结果 (1->异常，0->无异常)*/
  inspectionResultType?: number;
  /** 数量 */
  productCount?: number;
  /** 重量 */
  productWeight?: number;
  /** 质检时间 */
  inspectionTime?: string;
  /** 质检人 */
  inspector?: string;
  /** 领用人id */
  receivingUserId?: string;
  /** 领用人 */
  receivingUserName?: string;
  /** 领用时间 */
  receivingTime?: string;
  /** 备注 */
  remark?: string;
  /** 创建类型 */
  createType?: number;
  /** 工单号 */
  workOrderNum?: string;
  /** 创建人 */
  createUserName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 状态 (1->草稿，2->质检完成)*/
  status?: number;
  /** 领单状态 (0->未领用 1->已领用)*/
  receivingStatus?: number;
}

/** 质检单分页查询Ysn参数 */
export interface QualityInspectionYsnPageQuery extends PageQuery {
    /** 质检单商品主键Id */
    inspectionProductId?: string;
    /** 收运单号 */
    receivingOrderCode?: string;
}

/** 质检单Ysn分页对象 */
export interface QualityInspectionYsnPageVO {
  /** ID */
  id?: string;
  /** YSN码 */
  ysnCode?: string;
  /** 箱码 */
  boxCode?: string;
  /** 重量 */
  weight?: number;
  /** 数量 */
  quantity?: number;
  /** 扫描时间 */
  scanTime?: string;
}

/** 质检单对象 */
export interface QualityInspectionOrderFrom {
    /** ID */
    id?: string;
    /** 质检单号 */
    inspectionCode?: string;
    /** 收运单号 */
    receivingOrderCode?: string;
    /** 库区编码 */
    warehouseAreaCode?: string;
    /** 库区名称 */
    warehouseAreaName?: string;
    /** 质检结果/是否有异常 (1->异常，0->无异常) */
    inspectionResultType?: number;
    /** 质检类型  (0->收运质检，1->库内巡检) */
    inspectionType?: number;
    /** 创建人 */
    createUserName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 质检时间 */
    inspectionTime?: string;
    /** 质检人 */
    inspector?: string;
    /** 领用人id */
    receivingUserId?: string;
    /** 领用人 */
    receivingUserName?: string;
    /** 领用时间 */
    receivingTime?: string;
    /** 质检总数量 */
    productCount?: number;
    /** 质检总重量 */
    productWeight?: number;
    /** 状态 (1->草稿，2->质检完成)*/
    status?: number;
    /** 领单状态 (0->未领用 1->已领用)*/
    receivingStatus?: number;
    /** 备注 */
    remark?: string;
    /** 质检信息 */
    productList?: ProductVO[];
}
export interface ProductVO{
    /** ID */
    id?: string;
    /** 商品编码 */
    productCode?: string;
    /** 商品名称 */
    productName?: string;
    /** 商品规格 */
    productSpec?: string;
    /** 商品单位 */
    productUnitName?: string;
    /** 总库存数量 */
    totalStockQty?: number;
    /** 收运数量 */
    productActualQty?: number;
    /** 异常数量 */
    abnormalQty?: number;
    /** 异常原因编码 */
    abnormalCode?: string;
    /** 异常原因名称 */
    abnormalCause?: string;
    /** 质检结果/质检状态 (1->异常，0->无异常)*/
    inspectionStatus?: number;
    /** 图片*/
    imagesUrls?: string;
}


