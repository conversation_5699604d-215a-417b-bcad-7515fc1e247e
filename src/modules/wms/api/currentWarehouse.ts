import request from "@/core/utils/request";

const CURRENTWAREHOUSE_BASE_URL = "/supply-wms/current/warehouse";

class CurrentWarehouseAPI {
    /** 根据用户查询选择仓库列表 */
    static checkedUserById() {
        return request({
            url: `${CURRENTWAREHOUSE_BASE_URL}/listContainsChecked`,
            method: "get",
        });
    }
    /** 勾选仓库 */
    static checkedwarehouse(data: Array<string>) {
        return request({
            url: `${CURRENTWAREHOUSE_BASE_URL}/checked`,
            method: "post",
            data: data,
        });
    }
}

/** 仓库列表对象 */
export interface warehouseInfo {
    /** 仓库编码 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 状态 */
    checked: boolean;
}

export default CurrentWarehouseAPI;
