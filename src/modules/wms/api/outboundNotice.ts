import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "/supply-wms/warehouseOutboundNotice";

class OutboundNoticeAPI {
  /** 获取出库通知单分页数据 */
  static getOutboundNoticePage(queryParams?: OutboundNoticePageQuery) {
    return request<any, PageResult<OutboundNoticePageVO[]>>({
      url: `${PURCHASE_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /** 获取出库通知单手机号(小眼睛查看手机号) */
  static queryRealPhone(queryParams: any) {
    return request({
      url: `${PURCHASE_BASE_URL}/querySrcMobileInfo`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取国际国家列表-含港澳台
   *
   */
  static getAllCountry() {
    return request({
      url: `/supply-base/country/all`,
      method: "get",
    });
  }

  /** 管理端懒加载分类下拉列表 */
  static queryCategoryTreeList(data: any) {
    return request({
      url: `/supply-wms/product/category/queryManagerCategoryList`,
      method: "post",
      data: data,
    });
  }

  /** 添加 */
  static addOutboundNotice(data: addFormData) {
    return request({
      url: `${PURCHASE_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

  /** 编辑 */
  static editOutboundNotice(data: addFormData) {
    return request({
      url: `${PURCHASE_BASE_URL}/edit`,
      method: "post",
      data: data,
    });
  }

  /** 详情 */
  static queryDetail(data: any) {
    return request({
      url: `${PURCHASE_BASE_URL}/queryDetail`,
      method: "post",
      data: data,
    });
  }

  /** 详情查询加密 */
  static queryDetailMobileEncrypt(data: any) {
    return request({
      url: `${PURCHASE_BASE_URL}/queryDetailEncrypt`,
      method: "post",
      data: data,
    });
  }

  /**取消出库通知单 */
  static concelOutboundNotice(data: { id?: string }) {
    return request({
      url: `${PURCHASE_BASE_URL}/cancel`,
      method: "post",
      data: data,
    });
  }

  /** 商品选择页面分页查询 */
  static queryProductPageList(data: any) {
    return request({
      url: `/supply-wms/product/product/page`,
      method: "post",
      data: data,
    });
  }

  /**审核 */
  static approveOutboundNotice(data: { id?: string }) {
    return request({
      url: `${PURCHASE_BASE_URL}/approve`,
      method: "post",
      data: data,
    });
  }

  /**反审 */
  static exApproveOutboundNotice(data: { id?: string }) {
    return request({
      url: `${PURCHASE_BASE_URL}/exApprove`,
      method: "post",
      data: data,
    });
  }

  /**删除*/
  static delete(data: { id?: string }) {
    return request({
      url: `${PURCHASE_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 重量调整 */
  static changeWeight(data: any){
    return request({
      url: `${PURCHASE_BASE_URL}/changeWeight`,
      method: 'post',
      data: data
    })
  }
}

export default OutboundNoticeAPI;

/** 采购商品分页查询参数 */
export interface OutboundNoticePageQuery extends PageQuery {
  /** 出库通知单号 */
  outboundNoticeCode?: string;
  /** 出库类型  (1->成品销售出库，2->原料拆装出库，3->原料退货出库)*/
  outboundType?: any;
  /** 状态 (1->草稿，2->拣货中，3->完成，4->取消)*/
  outboundNoticeStatus?: any;
  /** 提货单号 */
  sourceOrderCode?: string;
  /** 时间类型(1->创建时间，2->计划交货时间)*/
  dateType?: number;
  approveStatus?: any;
}

export interface addFormData {
  id?: string;
  outboundNoticeCode?: string;
  outboundType?: number;
  orderCreateTime?: string;
  purchaseSalesPerson?: string;
  sourceOrderCode?: string;
  deliveryType?: number;
  plannedReceivedTime?: string;
  customerName?: string;
  supplierName?: string;
  contactPerson?: string;
  customerAreaCode?: string;
  customerMobile?: string;
  countryId?: string;
  provinceId?: string;
  cityId?: string;
  districtId?: string;
  address?: string;
  remark?: string;
  provinceName?: string;
  areaInfo?: any;
  cityName?: string;
  districtName?: string;
  countryName?: string;
  warehouseOutboundDetailVOList?: any;
  addressShow?: boolean;
  addressFormat?: string;
  mobilePhoneShow?: boolean;
  fullAddress?: string;
  approveStatus?: number;
  approveTime?: string;
  approveUserName?: string;
  createTime?: string;
  createUserName?: string;
  submitUserName?: string;
  submitTime?: string;
  nameShow?: boolean;
}

/** 采购商品分页对象 */
export interface OutboundNoticePageVO {
  /** ID */
  id?: string;
  /** 出库通知单号 */
  outboundNoticeCode?: string;
  /** 出库类型 */
  outboundType?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 仓库地址 */
  warehouseAddress?: string;
  /** 商品行数 */
  productLinesNumber?: number;
  /** 客户 */
  customerName?: string;
  /** 客户地址 */
  customerAddress?: string;
  /** 客户联系人 */
  customerContact?: string;
  /** 客户联系人电话 */
  customerMobile?: string;
  /** 供应商 */
  supplierName?: string;
  /** 状态 (1->草稿，2->拣货中，3->完成，4->取消)*/
  status?: number;
}

/** 设置供应商弹窗列表分页查询参数 */
export interface SetSupplierPageQuery extends PageQuery {
  /** 供应商名称 */
  supplierName?: string;
}

/** 采购商品对象 */
export interface PurchaseFrom {
  /** ID */
  id?: string;
  /** 商品分类 */
  productCategory?: string;
  /** 商品分类一级 */
  firstCategoryId?: string;
  /** 商品分类二级 */
  secondCategoryId?: string;
  /** 商品分类三级 */
  thirdCategoryId?: string;
  /** 商品名称*/
  productName?: string;
  /** 是否标品*/
  isStandard?: number;
  /** 采购单位id*/
  productUnitId?: string;
  /** 采购单位数量*/
  conversionRelFirstNum?: number;
  /** 基本单位数量*/
  conversionRelSecondNum?: number;
  /** 基本单位数量单位*/
  conversionRelSecondUnitId?: string;
  /** 长*/
  length?: number;
  /** 宽*/
  width?: number;
  /** 高*/
  height?: number;
  /** 体积*/
  volume?: number;
  /** 重量*/
  weight?: number;
  /** 商品品牌 */
  productBrandId?: string;
  /** 保质期 */
  shelfLife?: number;
  /** 保质期单位*/
  shelfLifeUnit?: string;
  /** 损耗比例 */
  lossRatio?: string;
  /** 存储条件 */
  storageCondition?: string;
  /** 备注 */
  remark?: string;
  /** 商品图片 */
  imageUrlList?: string[];
  /** 商品状态 1->上架，2->下架*/
  status?: string;
  /** 供应商列表 */
  supplierList?: supplierList[];
}

export interface supplierList {
  /** ID */
  id?: string;
  /** 供应商编码 */
  supplierCode?: string;
  /** 供应商名称*/
  supplierName?: string;
  /** 关联仓库*/
  supplierWarehouseName?: string;
  /** 是否默认（0->否，1->是） */
  isDefault?: number;
}
