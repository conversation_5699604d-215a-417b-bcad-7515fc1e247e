import request from "@/core/utils/request";

const INVENTORY_COUNT_BASE_URL = "/supply-wms/inventoryCheck";

/**
 * 盘点单API
 */
class InventoryCountApi {
  /**
   * 分页查询盘点单列表
   */
  static getInventoryCountPage(params: InventoryCountPageQuery) {
    return request<PageResult<InventoryCountVO[]>>({
      url: `${INVENTORY_COUNT_BASE_URL}/page`,
      method: "post",
      data: params,
    });
  }

  /**
   * 删除盘点单
   */
  static deleteInventoryCount(id: string) {
    return request({
      url: `${INVENTORY_COUNT_BASE_URL}/del/${id}`,
      method: "post",
    });
  }

  /**
   * 获取盘点单详情
   */
  static getInventoryCountDetail(id: string) {
    return request<InventoryCountDetailResponseVO>({
      url: `${INVENTORY_COUNT_BASE_URL}/detail/${id}`,
      method: "get",
    });
  }

  /**
   * 处理盘点单
   */
  static completeInventoryCount(data: InventoryCountCompleteForm) {
    return request({
      url: `${INVENTORY_COUNT_BASE_URL}/operation`,
      method: "post",
      data: data,
    });
  }

  static getYsnList(params: YsnListPageQuery) {
    return request<PageResult<InventoryCheckDetailYsnVO[]>>({
      url: `${INVENTORY_COUNT_BASE_URL}/ysnCode/list`,
      method: "post",
      data: params,
    });
  }

  // 损益类型
  static getLossCodeList(params: YsnListPageQuery) {
    return request<PageResult<InventoryCheckDetailYsnVO[]>>({
      url: `/supply-wms/profitLoss/lossCodeList`,
      method: "get",
    });
  }

  // 转移类型
  static getTransferCodeList(params: YsnListPageQuery) {
    return request<PageResult<InventoryCheckDetailYsnVO[]>>({
      url: `/supply-wms/inventoryTransferInfo/finish/list`,
      method: "post",
    });
  }
}

export default InventoryCountApi;

/** 分页查询基础参数 */
export interface PageQuery {
  pageNum?: number;
  pageSize?: number;
  page?: number;
  limit?: number;
}

/** 分页查询结果 */
export interface PageResult<T> {
  records: T;
  total: number;
  size: number;
  current: number;
  pages: number;
}

/** 盘点单分页查询参数 */
export interface InventoryCountPageQuery extends PageQuery {
  /**
     * 盘点单状态：0=草稿 1=已完成
     * 盘点状态：0=草稿 2=已盘点
     */
  checkStatus: number;
  /**
   * 盘点商品明细
   */
  inventoryCheckInfoDetailDTOS?: InventoryCheckInfoDetailDTO[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 仓库库区编码
   */
  warehouseAreaCode: string;
  /**
   * 仓库库区名称
   */
  warehouseAreaName: string;
  [property: string]: any;
}

/** YSN列表查询参数 */
export interface YsnListPageQuery extends PageQuery {
  /** 盘点单明细id */
  checkDetailId?: number;
  /** 盘点单号 */
  checkCode?: string;
  /** 商品编码 */
  productCode?: string;
}

/**
 * 盘点商品明细实体信息
 *
 * InventoryCheckInfoDetailDTO
 */
export interface InventoryCheckInfoDetailDTO {
  /**
   * 盘点后库存数量
   */
  afterInventoryQty: number;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 商品编码
   */
  productCode: string;
  [property: string]: any;
}

/** 盘点单VO */
export interface InventoryCountVO {
  /**
    * 盘点后商品个数
    */
  afterCheckQty?: number;
  /**
   * 盘点后商品总数量
   */
  afterCheckTotalQty?: number;
  /**
   * 盘点前商品个数
   */
  beforeCheckQty?: number;
  /**
   * 盘点前商品总数量
   */
  beforeCheckTotalQty?: number;
  /**
   * 盘点单号
   */
  checkCode?: string;
  /**
   * 盘点状态 0 草稿 1盘点中 2完成
   */
  checkStatus?: number;
  /**
   * 盘点时间
   */
  checkTime?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 处理时间
   */
  handleTime?: string;
  /**
   * 处理类型  00 未处理   10 损益处理
   */
  handleType?: string;
  /**
   * 处理人
   */
  handleUser?: number;
  /**
   * 处理人名称
   */
  handleUserName?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 操作单号
   */
  operationCode?: string;
  /**
   * 操作备注
   */
  operationRemark?: string;
  /**
   * 操作时间
   */
  operationTime?: string;
  /**
   * 操作方式：1=损益 2=移库 3=关闭
   */
  operationType?: number;
  /**
   * 操作人
   */
  operationUser?: number;
  /**
   * 操作人姓名
   */
  operationUserName?: string;
  /**
   * 领用状态 0-未领用 1-已领用
   */
  receiptStatus?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 仓库库区编码
   */
  warehouseAreaCode?: string;
  /**
   * 仓库库位编码
   */
  warehouseAreaLocationCode?: string;
  /**
   * 仓库库位名称
   */
  warehouseAreaLocationName?: string;
  /**
   * 仓库库区名称
   */
  warehouseAreaName?: string;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}

/** 盘点单详情VO */
export interface InventoryCountDetailVO {
  /** ID */
  id: string;
  /** 盘点单ID */
  countId: string;
  /** 商品编码 */
  productCode: string;
  /** 商品名称 */
  productName: string;
  /** 规格 */
  productSpec: string;
  /** 单位 */
  productUnit: string;
  /** 库位编码 */
  locationCode: string;
  /** 库位名称 */
  locationName: string;
  /** 系统库存数量 */
  systemQty: number;
  /** 盘点数量 */
  countQty: number;
  /** 差异数量 */
  diffQty: number;
  /** 批次号 */
  batchNo: string;
  /** 生产日期 */
  productionDate: string;
  /** 过期日期 */
  expiryDate: string;
  /** 备注 */
  remark: string;
}

/** 盘点单表单 */
export interface InventoryCountForm {
  /** ID */
  id?: string;
  /** 盘点单号 */
  countNo?: string;
  /** 盘点类型 1:明盘 2:盲盘 */
  countType: number;
  /** 仓库编码 */
  warehouseCode: string;
  /** 库区编码 */
  warehouseAreaCode: string;
  /** 盘点人 */
  countBy: string;
  /** 盘点时间 */
  countTime: string;
  /** 备注 */
  remark?: string;
  /** 盘点明细列表 */
  detailList: InventoryCountDetailForm[];
}

/** 盘点单详情表单 */
export interface InventoryCountDetailForm {
  /** 商品编码 */
  productCode: string;
  /** 库位编码 */
  locationCode: string;
  /** 系统库存数量 */
  systemQty: number;
  /** 盘点数量 */
  countQty: number;
  /** 批次号 */
  batchNo?: string;
  /** 生产日期 */
  productionDate?: string;
  /** 过期日期 */
  expiryDate?: string;
  /** 备注 */
  remark?: string;
}

/** 盘点单详情响应VO */
export interface InventoryCountDetailResponseVO {
  /** 主键id */
  id?: number;
  /** 仓库编码 */
  warehouseCode?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 仓库库区编码 */
  warehouseAreaCode?: string;
  /** 仓库库区名称 */
  warehouseAreaName?: string;
  /** 仓库库位编码 */
  warehouseAreaLocationCode?: string;
  /** 仓库库位名称 */
  warehouseAreaLocationName?: string;
  /** 领用状态 0-未领用 1-已领用 */
  receiptStatus?: number;
  /** 处理人 */
  handleUser?: number;
  /** 处理人名称 */
  handleUserName?: string;
  /** 处理时间 */
  handleTime?: string;
  /** 盘点状态 0 草稿 1盘点中 2完成 */
  checkStatus?: number;
  /** 盘点单号 */
  checkCode?: string;
  /** 盘点前商品个数 */
  beforeCheckQty?: number;
  /** 盘点后商品个数 */
  afterCheckQty?: number;
  /** 盘点前商品总数量 */
  beforeCheckTotalQty?: number;
  /** 盘点后商品总数量 */
  afterCheckTotalQty?: number;
  /** 处理类型 00 未处理 10 损益处理 */
  handleType?: string;
  /** 盘点时间 */
  checkTime?: string;
  /** 操作方式：1=损益 2=移库 3=关闭 */
  operationType?: number;
  /** 操作备注 */
  operationRemark?: string;
  /** 操作人 */
  operationUser?: number;
  /** 操作人姓名 */
  operationUserName?: string;
  /** 操作时间 */
  operationTime?: string;
  /** 操作单号 */
  operationCode?: string;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createTime?: string;
  /** 盘点明细 */
  detailList?: AppInventoryCheckDetailVO[];
}

/** 盘点明细VO */
export interface AppInventoryCheckDetailVO {
  /** 主键id */
  id?: number;
  /** 仓库编码 */
  warehouseCode?: string;
  /** 盘点id */
  checkId?: number;
  /** 盘点单号 */
  checkCode?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品规格 */
  productSpec?: string;
  /** 商品单位 */
  productUnit?: string;
  /** 商品采购单位id */
  productUnitId?: number;
  /** 一级分类id */
  firstCategoryId?: number;
  /** 二级分类id */
  secondCategoryId?: number;
  /** 三级分类id */
  thirdCategoryId?: number;
  /** 一级分类名称 */
  firstCategoryName?: string;
  /** 二级分类名称 */
  secondCategoryName?: string;
  /** 三级分类名称 */
  thirdCategoryName?: string;
  /** 盘点前商品总数量 */
  beforeCheckTotalQty?: number;
  /** 盘点后商品总数量 */
  afterCheckTotalQty?: number;
  /** 盘点商品明细ysn */
  ysnList?: InventoryCheckDetailYsnVO[];
}

/** YSN明细VO */
export interface InventoryCheckDetailYsnVO {
  /** 仓库编码 */
  warehouseCode?: string;
  /** 盘点id */
  checkId?: number;
  /** 盘点单号 */
  checkCode?: string;
  /** 盘点明细id */
  checkDetailId?: number;
  /** 重量 */
  weight?: number;
  /** YSN编码 */
  ysnCode?: string;
  /** 盘点状态 0待盘 1盘亏 2正常 3 盘盈 */
  ysnCheckStatus?: number;
  /** 扫描时间 */
  scanTime?: string;
}
