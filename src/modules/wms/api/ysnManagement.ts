import request from "@/core/utils/request";
const BASE_URL = '/supply-wms/ysnManage'
class YsnManagementApi{
  /** 分页查询 */
  static queryPage(queryParams ?: queryPageDto){
    return request<PageResult<queryPageResponse[]>>({
      url: `${BASE_URL}/queryPageList`,
      method: 'post',
      data: queryParams,
    })
  }
  /** 数据明细 */
  static queryDetail(queryParams ?: queryDetailPageDto){
    return request({
      url: `${BASE_URL}/queryDetail`,
      method: 'post',
      data: queryParams
    })
  }
  /** 新生成保存 */
  static add(data ?: any){
    return request({
      url: `${BASE_URL}/add`,
      method: 'post',
      data: data
    })
  }

  /** 更换保存 */
  static ysnChange(data ?: any){
    return request({
      url: `${BASE_URL}/ysnChange`,
      method: 'post',
      data: data
    })
  }

  /** 根据ysn码查信息 */
  static queryYsnDetailByObj(data: {ysnCode?: string}){
    return request({
      url: `${BASE_URL}/queryYsnDetailByObj`,
      method: 'post',
      data: data
    })
  }

  /** 根据商品编码或商品名称远程搜索下拉 */
  static queryProductListByKeyword(data : {keyword ?: string}){
    return request({
      url: `${BASE_URL}/queryProductListByKeyword`,
      method: 'get',
      params: data
    })
  }
}
export default YsnManagementApi
/**
 * 分页查询请求实体
 */
export interface queryPageDto extends PageQuery{
  /** 商品 */
  productQueryStr ?: string;
  /** 操作类型 */
  optTypeList ?: number[];
  /** 打印状态 */
  printStatusList?: number[];
  /** 原YSN码 */
  oldYsnCode ?: string;
  /** 新YSN码 */
  newYsnCode ?: string;
}
/**
 * 分页查询响应实体
 */
export interface queryPageResponse {
  /** 商品编码 */
  productCode ?: string;
  /** 商品名称 */
  productName ?: string;
  /** 操作类型 */
  optType ?: number;
  /** 生成重量 */
  quantity ?: number;
  /** 原YSN码 */
  oldYsnCode ?: string;
  /** 新YSN码 */
  newYsnCode ?: string;
  /** 重量 */
  newWeight ?: number;
  /** 生成/更新人 */
  updateUserName ?: string;
  /** 生成/更新时间 */
  updateTime ?: string;
  /** 打印人 */
  printUserName ?: string;
  /** 打印时间 */
  printTime ?: string;
}
/** 数量明细请求实体 */
export interface queryDetailPageDto extends PageQuery{
  /** ID */
  id ?: string
}
/** 数量明细响应实体 */
export interface queryDetailPageResponse {
  /** ysn码 */
  ysnCode ?: string;
  /** 重量 */
  weight ?: number;
}
/** 新增YSN */
export interface saveYSNDto {
  /** 操作类型 */
  optType ?: number;
  /** 原YSN */
  oldYsnCode ?: string;
  /** 商品信息 */
  productMessage ?: string;
  /** 商品编码 */
  productCode ?: string;
  /** 商品名称 */
  productName ?: string;
  /** 商品规格 */
  productSpec ?: string;
  /** 一级分类ID */
  firstCategoryId ?: number;
  /** 一级分类名称 */
  firstCategoryName ?: string;
  /** 二级分类ID */
  secondCategoryId ?: number;
  /** 二级分类名称 */
  secondCategoryName ?: string;
  /** 三级分类ID */
  thirdCategoryId ?: number;
  /** 三级分类名称 */
  thirdCategoryName ?: string;
  /** 商品重量 */
  productWeight ?: number;
  /** 生成数量 */
  quantity ?: number;
}
