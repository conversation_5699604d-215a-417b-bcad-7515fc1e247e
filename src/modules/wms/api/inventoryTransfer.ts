import request from "@/core/utils/request";

const INVENTORYTRANSFER_BASE_URL = "/supply-wms/inventoryTransferInfo";

class InventoryTransferAPI {

    /** 获取库存转移分页数据 */
    static getInventoryTransferPage(queryParams?: {}) {
        return request<any, PageResult<InventoryTransferPageVO[]>>({
            url: `${INVENTORYTRANSFER_BASE_URL}/list`,
            method: "post",
            data: queryParams,
        });
    }

    /**删除库存转移 */
    static deleteInventoryTransfer(data: { id?:string }) {
        return request({
            url: `${INVENTORYTRANSFER_BASE_URL}/del/${data.id}`,
            method: "post",
            data: data,
        });
    }

    /**查询移库原因列表 */
    static getInventoryTransferReasonList(data:{params?:string}) {
        return request({
            url: `supply-base/dict/findItemListByDictKey`,
            method: "get",
            params: data,
        });
    }

    /** 查询库存转移详情*/
    static queryInventoryTransferDetail(data: { id?:string }) {
        return request<any, InventoryTransferFrom>({
            url: `${INVENTORYTRANSFER_BASE_URL}/detail/${data.id}`,
            method: "get",
        });
    }


    /** 库存转移添加、编辑 */
    static addAndEditInventoryTransfer(data: InventoryTransferFrom) {
        return request({
            url: `${INVENTORYTRANSFER_BASE_URL}/save`,
            method: "post",
            data: data,
        });
    }

  /**
   * 领单
   */
  static pickOrder(transferOrderCode: string) {
    return request<any>({
      url: `${INVENTORYTRANSFER_BASE_URL}/receipt/${transferOrderCode}`,
      method: "get",
    });
  }

  /**
   *  取消领单
   */
  static releaseOrder(transferOrderCode: string) {
    return request<any>({
      url: `${INVENTORYTRANSFER_BASE_URL}/cancel/receipt/${transferOrderCode}`,
      method: "get",
    });
  }
  /**
   * ysn列表
   */
  static queryYsnList(data: any) {
    return request<any>({
      url: `${INVENTORYTRANSFER_BASE_URL}/ysnCode/list`,
      method: "post",
      data: data,
    });
  }
}

export default InventoryTransferAPI;

/** 库存转移分页查询参数 */
export interface InventoryTransferPageQuery extends PageQuery {
    /** 移库单号 */
    transferOrderCode?: string;
    /** 时间类型(1->创建时间，2->移库时间)*/
    dateType?: number;
    /** 时间范围 */
    dateRange?: string[];
   /* /!** 移库类型  (1->普通移库) *!/
    transferType?: number[];*/
    /** 状态 (0->草稿，1->完成)*/
    transferStatus?: number[];
}

/** 库存转移分页对象 */
export interface InventoryTransferPageVO {
    /** ID */
    id?: string;
    /** 移库单号 */
    transferOrderCode?: string;
    /** 移库类型  (1->普通移库) */
    transferType?: number;
    /** 来源库区 */
    sourceWarehouseArea?: string;
    /** 移库原因 */
    transferReasonName?: string;
    /** 备注 */
    remark?: string;
    /** 创建人 */
    createUserName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 移库人 */
    transferUserName?: string;
    /** 移库时间 */
    transferTime?: string;
    /** 状态 (1->草稿，2->完成)*/
    transferStatus?: number;
}

/** 库存转移对象 */
export interface InventoryTransferFrom{
    /** ID */
    id?: string;
    /** 库存转移单号 */
    transferOrderCode?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建用户名 */
    createUserName?: string;
    /** 仓库库区编码 */
    sourceWarehouseAreaCode?: string;
    /** 仓库库区名称 */
    sourceWarehouseAreaName?: string;
    /** 移库原因id */
    transferReasonId?: number;
    /** 移库原因名称 */
    transferReasonName?: string;
    /** 库存转移类型 */
    transferType?: number|boolean;
    /** 备注 */
    remark?: string;
    /** 库区转移明细 */
    inventoryTransferInfoDetailList?: InventoryTransferInfoDetailList[];
    /** 库存转移状态 (0->草稿，1->完成) */
    transferStatus?:number;
    /** 仓库编码 */
    warehouseCode?: string;
}

export interface InventoryTransferInfoDetailList{
    /** ID */
    id?: string;
    /** 商品编码 */
    productCode?: string;
    /** 商品名称 */
    productName?: string;
    /** 商品规格 */
    productSpec?: string;
    /** 商品单位 */
    productUnit?: string;
    /** 仓库库区编码 */
    targetWarehouseAreaCode?: string;
    /** 仓库库区名称 */
    targetWarehouseAreaName?: string;
    /** 转移数量 */
    transferQty?: number;
    /** 转移后可用库存数量 */
    afterAvailableQty?: number;
    /** 转移后总库存数量*/
    afterInventoryQty?: number;
    /** 转移前可用库存数量 */
    beforeAvailableQty?: number;
    /** 转移前总库存数量 */
    beforeInventoryQty?: number;
}
