import request from "@/core/utils/request";

const SYSTEM_PARAMETERS_BASE_URL = "/supply-biz-common/systemParameters";

class SystemParametersAPI {
  /**
   * 分页查询系统参数
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageList(queryParams?: SystemParametersPageQuery) {
    return request<any, SystemParametersPageResult>({
      url: `${SYSTEM_PARAMETERS_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取系统参数详情
   * @param id 参数ID
   * @returns 参数详情
   */
  static getDetail(id: number) {
    return request<any, SystemParametersVO>({
      url: `${SYSTEM_PARAMETERS_BASE_URL}/queryDetail`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 新增系统参数
   * @param data 参数表单数据
   * @returns 操作结果
   */
  static add(data: SystemParametersForm) {
    return request({
      url: `${SYSTEM_PARAMETERS_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

  /**
   * 编辑系统参数
   * @param data 参数表单数据
   * @returns 操作结果
   */
  static update(data: SystemParametersForm) {
    return request({
      url: `${SYSTEM_PARAMETERS_BASE_URL}/edit`,
      method: "post",
      data: data,
    });
  }

  /**
   * 删除系统参数
   * @param id 参数ID
   * @returns 操作结果
   */
  static deleteById(id: number) {
    return request({
      url: `${SYSTEM_PARAMETERS_BASE_URL}/delete`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 批量更新系统参数
   * @param data 参数数组
   * @returns 操作结果
   */
  static updateBatch(data: SystemParametersForm[]) {
    return request({
      url: `${SYSTEM_PARAMETERS_BASE_URL}/updateBatch`,
      method: "post",
      data: data,
    });
  }

  /**
   * 批量删除系统参数
   * @param ids 参数ID数组
   * @returns 操作结果
   */
  static deleteBatch(ids: number[]) {
    return request({
      url: `${SYSTEM_PARAMETERS_BASE_URL}/deleteBatch`,
      method: "post",
      data: ids,
    });
  }
}

export default SystemParametersAPI;

/**
 * 系统参数分页查询对象
 */
export interface SystemParametersPageQuery {
  /** 当前页 */
  page?: number;
  /** 每页条数 */
  limit?: number;
  /** 功能编码 */
  functionCode?: string;
  /** 功能名称 */
  functionName?: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: number;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 系统参数表单对象
 */
export interface SystemParametersForm {
  /** 主键ID */
  id?: number;
  /** 功能编码 */
  functionCode?: string;
  /** 功能名称，描述系统参数对应的功能 */
  functionName?: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: number;
  /** 说明，对系统参数功能的详细描述 */
  description?: string;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 系统参数视图对象
 */
export interface SystemParametersVO {
  /** 主键ID */
  id?: number;
  /** 功能编码 */
  functionCode?: string;
  /** 功能名称，描述系统参数对应的功能 */
  functionName?: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: number;
  /** 说明，对系统参数功能的详细描述 */
  description?: string;
  /** 租户ID */
  tenantId?: string;
  /** 组织编码 */
  orgCode?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 创建用户ID */
  createUser?: number;
  /** 更新用户ID */
  updateUser?: number;
}

/**
 * 系统参数分页结果对象（匹配 Apifox 响应结构）
 */
export interface SystemParametersPageResult {
  /** 当前页 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: SystemParametersVO[];
}

/**
 * 启禁用状态枚举
 */
export enum EnableStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
} 