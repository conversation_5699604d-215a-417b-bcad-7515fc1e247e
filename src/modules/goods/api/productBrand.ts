import request from "@/core/utils/request";
const PRODUCTBRAND_BASE_URL = "/supply-biz-common/product/brand";

class productBrandAPI {
  /** 分页查询 */
  static getPageList(queryParams?: PageQuery) {
    return request<any, PageResult<productBrandInfo[]>>({
      url: `${PRODUCTBRAND_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }
  /** 添加品牌 */
  static saveBrand(data: productBrandForm) {
    return request({
      url: `${PRODUCTBRAND_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }
  /** 编辑品牌 */
  static updateBrand(data: productBrandForm) {
    return request({
      url: `${PRODUCTBRAND_BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }
  /** 删除品牌 */
  static deleteBrand(data: { id?: string }) {
    return request({
      url: `${PRODUCTBRAND_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 获取商品品牌下拉数据源 */
  static queryBrandList() {
    return request({
      url: `${PRODUCTBRAND_BASE_URL}/select`,
      method: "post",
      data: {},
    });
  }
}
export interface productBrandForm {
  id?: string;
  brandName?: string;
  sort?: number;
}
export interface productBrandInfo {
  brandName: string;
  updateTime: string;
  sort: number;
  id: string;
}

export default productBrandAPI;
