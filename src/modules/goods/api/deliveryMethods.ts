import request from "@/core/utils/request";

const DELIVERY_METHODS_BASE_URL = "/supply-biz-common/deliveryMethods";

class DeliveryMethodsAPI {
  /**
   * 分页查询配送方式
   * @param queryParams 查询参数
   * @returns 分页结果
   */
  static getPageList(queryParams?: DeliveryMethodsPageQuery) {
    return request<any, DeliveryMethodsPageResult>({
      url: `${DELIVERY_METHODS_BASE_URL}/queryPageList`,
      method: "post",
      data: queryParams,
    });
  }

  /**
   * 获取配送方式详情
   * @param id 配送方式ID
   * @returns 配送方式详情
   */
  static getDetail(id: number) {
    return request<any, DeliveryMethodsVO>({
      url: `${DELIVERY_METHODS_BASE_URL}/queryDetail`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 新增配送方式
   * @param data 配送方式表单数据
   * @returns 操作结果
   */
  static add(data: DeliveryMethodsForm) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_BASE_URL}/add`,
      method: "post",
      data,
    });
  }

  /**
   * 编辑配送方式
   * @param data 配送方式表单数据（包含ID）
   * @returns 操作结果
   */
  static update(data: DeliveryMethodsForm) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_BASE_URL}/edit`,
      method: "post",
      data,
    });
  }

  /**
   * 删除配送方式
   * @param id 配送方式ID
   * @returns 操作结果
   */
  static deleteById(id: number) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_BASE_URL}/delete`,
      method: "post",
      params: { id },
    });
  }

  /**
   * 批量删除配送方式
   * @param ids ID数组
   * @returns 操作结果
   */
  static deleteBatch(ids: number[]) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_BASE_URL}/deleteBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 批量启用配送方式
   * @param ids ID数组
   * @returns 操作结果
   */
  static enableBatch(ids: number[]) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_BASE_URL}/enableStatusBatch`,
      method: "post",
      data: ids,
    });
  }

  /**
   * 批量禁用配送方式
   * @param ids ID数组
   * @returns 操作结果
   */
  static disableBatch(ids: number[]) {
    return request<any, string>({
      url: `${DELIVERY_METHODS_BASE_URL}/disableStatusBatch`,
      method: "post",
      data: ids,
    });
  }
}

/**
 * 配送方式分页查询参数
 */
export interface DeliveryMethodsPageQuery {
  /** 当前页 */
  page?: number;
  /** 每页条数（最大1000） */
  limit?: number;
  /** 主键ID */
  id?: number;
  /** 分类ID，配送方式所属的分类id */
  deliveryMethodsCategoryId?: number;
  /** 配送方式名称 */
  methodName?: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 配送方式表单数据
 */
export interface DeliveryMethodsForm {
  /** 主键ID（编辑时必填） */
  id?: number;
  /** 分类ID，配送方式所属的分类id */
  deliveryMethodsCategoryId?: number;
  /** 配送方式名称 */
  methodName?: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
}

/**
 * 配送方式视图对象
 */
export interface DeliveryMethodsVO {
  /** 组织编码 */
  orgCode?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 创建用户ID */
  createUser?: number;
  /** 更新用户ID */
  updateUser?: number;
  /** 主键ID */
  id?: number;
  /** 分类ID，配送方式所属的分类id */
  deliveryMethodsCategoryId?: number;
  /** 配送方式名称 */
  methodName?: string;
  /** 启禁用状态 0->禁用，1->启用 */
  enableStatus?: EnableStatus;
  /** 租户ID */
  tenantId?: string;
  /** 配送方式分类信息 */
  deliveryMethodsCategoryVO?: any;
}

/**
 * 配送方式分页结果对象
 */
export interface DeliveryMethodsPageResult {
  /** 当前页 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: DeliveryMethodsVO[];
}

/**
 * 启禁用状态枚举
 */
export enum EnableStatus {
  /** 禁用 */
  DISABLED = 0,
  /** 启用 */
  ENABLED = 1,
}

export default DeliveryMethodsAPI; 