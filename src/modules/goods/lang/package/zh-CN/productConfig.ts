export default {
  productConfig: {
    label: {
      field: "字段",
      typeName: "名称",
      sort: "排序",
      enableStatus: "状态",
      updateTime: "更新时间",
      operation: "操作"
    },
    button: {
      search: "搜索",
      reset: "重置",
      disable: "禁用",
      enable: "启用",
      add: "新增",
      edit: "编辑",
      delete: "删除",
      deleteConfirm: "确定要删除这个配置吗？",
      deleteSuccess: "删除成功",
      addProductConfig: "新增配置",
      editProductConfig: "编辑配置",
      productConfigName: "配置名称",
      cancel: "取消",
      confirm: "确定",
      save: "保存",
    },
    status: {
      enabled: "启用",
      disabled: "禁用"
    },
    message: {
      deleteConfirm: "确定要删除这个配置吗？",
      deleteSuccess: "删除成功",
      addProductConfig: "新增配置",
      editProductConfig: "编辑配置",
      productConfigName: "配置名称",
      saveSuccess: "保存成功",
      saveFailed: "保存失败",
      loadFailed: "数据加载失败",
      confirmSave: "确定要保存当前配置吗？",
      editSuccess: "编辑成功",
      addSuccess: "新增成功",
      batchEnableSuccess: "批量启用成功",
      batchDisableSuccess: "批量禁用成功",
      selectTip: "请选择要操作的配置",
      enableSuccess: "启用成功",
      disableSuccess: "禁用成功",
      operationFailed: "操作失败",
      enableBatchConfirm: "确定要启用这些配置吗？",
      disableBatchConfirm: "确定要禁用选中的商品配置/配送方式吗"
    },
    title: {
      edit: "编辑配置",
      add: "新增配置"
    },
    placeholder: {
      productConfigNamePlaceholder: "请输入配置名称",
      productConfigNameRequired: "请输入配置名称",
      fieldPlaceholder: "请选择",
      typeNamePlaceholder: "请输入名称",
    },
    rules: {
      fieldName: "请输入字段名称",
      typeName: "请输入名称",
      sort: "请输入排序",
      sortNumber: "排序必须是数字"
    }
  }
}; 