import { ref } from "vue";
import WarehouseAPI from "@pms/api/warehouse";

export function useWarehouse() {
  const warehouseList = ref([]);
  const loading = ref(false);

  // 获取仓库列表
  const fetchWarehouseList = async () => {
    try {
      loading.value = true;
      const res = await WarehouseAPI.getStorehouseSelect();
      warehouseList.value = res;
    } catch (error) {
      console.error("获取仓库列表失败:", error);
    } finally {
      loading.value = false;
    }
  };

  fetchWarehouseList();
  return {
    warehouseList,
    loading,
    fetchWarehouseList,
  };
}
