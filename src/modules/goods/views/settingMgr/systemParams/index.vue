<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="80px">
        <el-row>
          <el-form-item :label="$t('systemParams.label.functionName')" prop="functionName">
            <el-input v-model="queryParams.functionName"
              :placeholder="$t('systemParams.placeholder.functionNamePlaceholder')" clearable style="width: 240px"
              @keyup.enter="handleQuery" />
          </el-form-item>
         <!--  <el-form-item :label="$t('systemParams.label.functionCode')" prop="functionCode">
            <el-input v-model="queryParams.functionCode"
              :placeholder="$t('systemParams.placeholder.functionCodePlaceholder')" clearable style="width: 240px"
              @keyup.enter="handleQuery" />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" @click="handleQuery" v-hasPerm="['wms:system:params:search']">
              {{ $t("systemParams.button.search") }}
            </el-button>
            <el-button @click="handleResetQuery" v-hasPerm="['wms:system:params:search']">
              {{ $t("systemParams.button.reset") }}
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <!-- 数据表格 -->
    <el-card shadow="never" class="table-container">
      <template #header>

          <!-- 有数据时批量更新 -->
          <el-button v-if="!hasNoData" type="primary" :loading="saveLoading" :disabled="modifiedParams.size === 0" @click="handleSave" v-hasPerm="['wms:system:params:save']">
            {{ $t("systemParams.button.save") }}
          </el-button>
          <!-- 无数据时新增操作 -->
          <el-button v-else type="primary" :loading="saveLoading" :disabled="modifiedParams.size === 0" @click="handleAdd" v-hasPerm="['wms:system:params:save']">
            {{ $t("systemParams.button.save") }}
          </el-button>
      </template>

      <el-table ref="dataTableRef" v-loading="loading" :data="systemParamsList" stripe highlight-current-row>
        <template #empty>
          <Empty />
        </template>

        <el-table-column type="index" :label="$t('common.sort')" width="60" align="center" />

        <el-table-column :label="$t('systemParams.label.functionName')" prop="functionName" width="300"
          show-overflow-tooltip />

        <el-table-column :label="$t('systemParams.label.operation')" width="150" align="center">
          <template #default="scope">
            <el-checkbox 
              v-model="scope.row.enableStatus" 
              :true-label="1" 
              :false-label="0"
              @change="handleStatusChange(scope.row)"
            >
              {{ scope.row.enableStatus === 1 ? $t('systemParams.status.enabled') : $t('systemParams.status.disabled') }}
            </el-checkbox>
          </template>
        </el-table-column>

        <el-table-column :label="$t('systemParams.label.description')" min-width="300">
          <template #default="scope">
            <div class="description-text">
              {{ scope.row.description || '-' }}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination  v-model:total="total" v-model:page="queryParams.page"
        v-model:limit="queryParams.limit" :page-sizes="[20, 50, 100, 200]" @pagination="handleQuery" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "SystemParams",
  inheritAttrs: false,
});

import SystemParametersAPI, {
  SystemParametersPageQuery,
  SystemParametersVO,
  SystemParametersPageResult
} from "@/modules/goods/api/systemParameters";
import type { FormInstance, TableInstance } from "element-plus";

const { t } = useI18n();
const queryFormRef = ref<FormInstance>();
const dataTableRef = ref<TableInstance>();
const loading = ref(false);
const saveLoading = ref(false);
const total = ref(0);

// 查询参数
const queryParams = reactive<SystemParametersPageQuery>({
  page: 1,
  limit: 20,
  functionName: "",
});

// 默认数据
const defaultData: SystemParametersVO[] = [
  {
    functionName: '启用采购单匹配询价定价商品',
    functionCode: 'enable_pricing_products',
    enableStatus: 0,
    description: `勾选此选项后，新增采购单选择「供应商直供」类型时，商品资料仅显示对应询价周期内的商品(即当前有效询价覆盖的商品)。未勾选时，商品
资料默认展示所有已上架商品(不受询价周期限制)。`,
  }
]
// 系统参数列表
const systemParamsList = ref<SystemParametersVO[]>([]);

// 修改状态记录
const modifiedParams = ref<Map<number, SystemParametersVO>>(new Map());
const hasNoData = ref(false);
/** 查询系统参数列表 */
function handleQuery() {
  loading.value = true;
  SystemParametersAPI.getPageList(queryParams)
    .then((data: SystemParametersPageResult) => {
      if(data?.records?.length === 0){ // 无数据时使用默认数据
        systemParamsList.value = defaultData;
        hasNoData.value = true;
      }else{
        systemParamsList.value = data.records;
        hasNoData.value = false;
      }
      total.value = data.total;
      // 清空修改记录
      modifiedParams.value.clear();
    })
    .catch(() => {
      ElMessage.error(t("systemParams.message.loadFailed"));
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}

/** 处理状态变更 */
function handleStatusChange(row: SystemParametersVO) {
  // 记录修改的参数
  if (row.functionCode) {
    modifiedParams.value.set(row.functionCode, { ...row });
  }
}

/** 新增 */
function handleAdd() {
  ElMessageBox.confirm(
    t("systemParams.message.confirmSave"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(async () => {
    saveLoading.value = true;
    try {
      await SystemParametersAPI.add(defaultData[0]);
      ElMessage.success(t("systemParams.message.saveSuccess"));
      modifiedParams.value.clear();
      handleQuery(); // 重新加载数据
    } catch (error) {
      ElMessage.error(error?.message || t("systemParams.message.saveFailed"));
    } finally {
      saveLoading.value = false;
    }
  }).catch(() => {
    ElMessage.info(t("common.cancel"));
  })
}

/** 批量更新状态 */
function handleSave() {
  ElMessageBox.confirm(
    t("systemParams.message.confirmSave"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(async () => {
    saveLoading.value = true;

    try {
      // 批量更新所有修改的参数，只提交id和enableStatus
      const updateData = Array.from(modifiedParams.value.values()).map(param => ({
        id: param.id!,
        enableStatus: param.enableStatus!
      }));

      await SystemParametersAPI.updateBatch(updateData);

      ElMessage.success(t("systemParams.message.saveSuccess"));
      modifiedParams.value.clear();
      handleQuery(); // 重新加载数据
    } catch (error) {
      ElMessage.error(error?.message || t("systemParams.message.saveFailed"));
    } finally {
      saveLoading.value = false;
    }
  }).catch(() => {
    ElMessage.info(t("common.cancel"));
  });
}

// 页面加载时查询数据
onMounted(() => {
  handleQuery();
});
</script>

<style scoped lang="scss">
.app-container {
  .search-container {
    background: #ffffff;
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .table-container {
    margin-bottom: 20px;

    /* .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 16px;
    } */
  }

  .footer-container {
    display: flex;
    justify-content: center;
    padding: 20px;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}

// 开关组件样式调整
:deep(.el-switch__label) {
  font-size: 12px;
}

:deep(.el-switch__label.is-active) {
  color: #13ce66;
}

:deep(.el-switch__label:not(.is-active)) {
  color: #ff4949;
}

.description-text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: 2.8em; /* 2行的高度 */
  word-break: break-word;
}
:deep(.el-popper){
  width: 50vw;
}
</style>