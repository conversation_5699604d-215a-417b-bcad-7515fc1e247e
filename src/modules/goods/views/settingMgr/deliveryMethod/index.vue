<template>
  <div class="delivery-method-container-block">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-input v-model="queryParams.methodName" placeholder="配送方式" clearable style="width: 240px"
        @keyup.enter="handleQuery" @clear="handleQuery">
        <template #suffix>
          <el-icon @click="handleQuery" style="cursor: pointer;">
            <Search />
          </el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="handleQuery" style="margin-left: 12px;"
        v-hasPerm="['wms:delivery:method:search']">
        搜索
      </el-button>
    </div>
    <section class="delivery-method-container">
      <!-- 左侧分类管理 -->
      <div class="left-panel">
        <div class="category-header">
          <h3>配送方式分类</h3>
          <el-button type="primary" size="small" @click="handleAddCategory"
            v-hasPerm="['wms:delivery:method:type:add']">
            新增
          </el-button>
        </div>

        <div class="category-list">
          <!-- 全部分类 -->
          <div class="category-item" :class="{ active: selectedCategoryId === null }" @click="selectCategory(null)">
            <span>全部</span>
          </div>

          <!-- 分类列表 -->
          <div v-for="category in categoryList" :key="category.id" class="category-item"
            :class="{ active: selectedCategoryId === category.id }" @click="selectCategory(category.id || null)">
            <span>{{ category.deliveryMethodsCategoryName }}</span>
            <div class="category-actions">
              <el-button type="primary" link @click.stop="handleEditCategory(category)"
                v-hasPerm="['wms:delivery:method:type:edit']">
                重命名
              </el-button>
              <el-button type="danger" link @click.stop="handleDeleteCategory(category)"
                v-hasPerm="['wms:delivery:method:type:del']">
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧配送方式管理 -->
      <div class="right-panel">

        <!-- 操作按钮区域 -->
        <div class="toolbar-container">
          <div class="toolbar-left">
            <!--  <el-button type="primary" @click="handleAddCategory" v-hasPerm="['wms:delivery:method:add']">
              新增
            </el-button> -->

          </div>
          <div class="toolbar-right">

            <el-button type="primary" @click="handleAdd" v-hasPerm="['wms:delivery:method:add']">
              新增配送方式
            </el-button>
            <el-button type="success" :disabled="!multipleSelection.length" @click="handleBatchEnable"
              v-hasPerm="['wms:delivery:method:enable']">
              启用
            </el-button>
            <el-button type="danger" :disabled="!multipleSelection.length" @click="handleBatchDisable"
              v-hasPerm="['wms:delivery:method:disable']">
              禁用
            </el-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
          <el-table ref="tableRef" v-loading="loading" :data="methodList" @selection-change="handleSelectionChange"
            stripe border height="100%">
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column label="配送方式" prop="methodName" min-width="150" show-overflow-tooltip />

            <el-table-column label="分类" min-width="120" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.deliveryMethodsCategoryVO?.deliveryMethodsCategoryName || '-' }}
              </template>
            </el-table-column>

            <el-table-column label="状态" prop="enableStatus" width="100" align="center">
              <template #default="scope">
                <el-switch v-model="scope.row.enableStatus" :active-value="1" :inactive-value="0"
                  @change="handleStatusChange(scope.row)" :active-text="'启用'" :inactive-text="'禁用'"
                  inline-prompt style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"/>
              </template>
            </el-table-column>

            <el-table-column label="更新时间" prop="updateTime" width="180" align="center" show-overflow-tooltip>
              <template #default="scope">
                {{formatDateTime(scope.row.updateTime)}}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="150" align="center" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleUpdate(scope.row)"
                  v-hasPerm="['wms:delivery:method:edit']">
                  编辑
                </el-button>
                <el-button type="danger" link @click="handleDelete(scope.row)" v-hasPerm="['wms:delivery:method:del']">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <pagination v-model:total="total" v-model:page="queryParams.page" v-model:limit="queryParams.limit"
              @pagination="handleQuery" />
          </div>
        </div>
      </div>
    </section>


    <!-- 分类编辑抽屉 -->
    <el-drawer v-model="categoryDialogVisible" :title="categoryForm.id ? '编辑配送方式分类' : '新增配送方式分类'" 
      size="500px" direction="rtl">
      <div class="drawer-content">
        <el-form ref="categoryFormRef" :model="categoryForm" :rules="categoryRules" label-width="80px" label-position="top">
          <el-form-item label="分类" prop="deliveryMethodsCategoryName">
            <el-input v-model="categoryForm.deliveryMethodsCategoryName" placeholder="请输入" maxlength="20"
              show-word-limit />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="categoryDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitCategoryForm" :loading="categoryLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 配送方式编辑抽屉 -->
    <el-drawer v-model="methodDialogVisible" :title="methodForm.id ? '编辑配送方式' : '新增配送方式'" 
      size="500px" direction="rtl">
      <div class="drawer-content">
        <el-form ref="methodFormRef" :model="methodForm" :rules="methodRules" label-width="80px" label-position="top">
          <el-form-item label="配送方式" prop="methodName">
            <el-input v-model="methodForm.methodName" placeholder="请输入" maxlength="30" show-word-limit />
          </el-form-item>
          <el-form-item label="分类" prop="deliveryMethodsCategoryId">
            <el-select v-model="methodForm.deliveryMethodsCategoryId" placeholder="请选择" style="width: 100%">
              <el-option v-for="category in categoryList" :key="category.id" :label="category.deliveryMethodsCategoryName"
                :value="category.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="enableStatus">
            <el-radio-group v-model="methodForm.enableStatus">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="methodDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitMethodForm" :loading="methodLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, type Ref } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type TableInstance } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// API imports
import DeliveryMethodsAPI, {
  type DeliveryMethodsPageQuery,
  type DeliveryMethodsVO,
  type DeliveryMethodsForm,
  EnableStatus
} from '@/modules/goods/api/deliveryMethods'
import DeliveryMethodsCategoryAPI, {
  type DeliveryMethodsCategoryVO,
  type DeliveryMethodsCategoryForm
} from '@/modules/goods/api/deliveryMethodsCategory'
import { parseDateTime } from "@/core/utils";
defineOptions({
  name: "DeliveryMethod",
  inheritAttrs: false,
})

// 表单引用
const categoryFormRef: Ref<FormInstance | undefined> = ref()
const methodFormRef: Ref<FormInstance | undefined> = ref()
const tableRef: Ref<TableInstance | undefined> = ref()

// 数据状态
const loading = ref(false)
const categoryLoading = ref(false)
const methodLoading = ref(false)
const total = ref(0)
const selectedCategoryId = ref<number | null>(null)
const multipleSelection = ref<DeliveryMethodsVO[]>([])

// 列表数据
const categoryList = ref<DeliveryMethodsCategoryVO[]>([])
const methodList = ref<DeliveryMethodsVO[]>([])

// 查询参数
const queryParams = reactive<DeliveryMethodsPageQuery>({
  page: 1,
  limit: 20,
  methodName: '',
  deliveryMethodsCategoryId: undefined,
  enableStatus: undefined
})
/** 格式化时间 */
function formatDateTime(time: string) {
  return parseDateTime(time, "dateTime");
}
// 分类弹窗
const categoryDialogVisible = ref(false)
const categoryForm = reactive<DeliveryMethodsCategoryForm>({
  id: undefined,
  deliveryMethodsCategoryName: '',
  sort: 0
})

// 配送方式弹窗
const methodDialogVisible = ref(false)
const methodForm = reactive<DeliveryMethodsForm>({
  id: undefined,
  methodName: '',
  deliveryMethodsCategoryId: undefined,
  enableStatus: EnableStatus.ENABLED
})

// 表单验证规则
const categoryRules = reactive({
  deliveryMethodsCategoryName: [
    { required: true, message: '分类名称不能为空', trigger: 'blur' },
    { max: 20, message: '分类名称不能超过20个字符', trigger: 'blur' }
  ]
})

const methodRules = reactive({
  methodName: [
    { required: true, message: '配送方式名称不能为空', trigger: 'blur' },
    { max: 30, message: '配送方式名称不能超过30个字符', trigger: 'blur' }
  ],
  deliveryMethodsCategoryId: [
    { required: true, message: '请选择所属分类', trigger: 'change' }
  ]
})

// 加载分类列表
const loadCategoryList = async () => {
  try {
    const response = await DeliveryMethodsCategoryAPI.getPageList({ page: 1, limit: 1000 })
    categoryList.value = response.records || []
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

// 加载配送方式列表
const loadMethodList = async () => {
  loading.value = true
  try {
    // 设置分类筛选
    if (selectedCategoryId.value) {
      queryParams.deliveryMethodsCategoryId = selectedCategoryId.value
    } else {
      queryParams.deliveryMethodsCategoryId = undefined
    }

    const response = await DeliveryMethodsAPI.getPageList(queryParams)
    methodList.value = response.records || []
    total.value = parseInt(response.total || '0')
  } catch (error) {
    console.error('加载配送方式列表失败:', error)
    methodList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.page = 1
  loadMethodList()
}

// 重置搜索
const resetQuery = () => {
  Object.assign(queryParams, {
    page: 1,
    limit: 20,
    methodName: '',
    enableStatus: undefined
  })
  handleQuery()
}

// 选择分类
const selectCategory = (categoryId: number | null) => {
  selectedCategoryId.value = categoryId
  handleQuery()
}

// 分类管理
const handleAddCategory = () => {
  resetCategoryForm()
  categoryDialogVisible.value = true
}

const handleEditCategory = (category: DeliveryMethodsCategoryVO) => {
  resetCategoryForm()
  Object.assign(categoryForm, category)
  delete categoryForm.tenantId;
  categoryDialogVisible.value = true
}

const handleDeleteCategory = async (category: DeliveryMethodsCategoryVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除${category.deliveryMethodsCategoryName}吗？删除后不可恢复`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await DeliveryMethodsCategoryAPI.deleteById(category.id!)
    ElMessage.success('删除成功')

    // 如果删除的是当前选中的分类，切换到全部
    if (selectedCategoryId.value === id) {
      selectedCategoryId.value = null
    }

    loadCategoryList()
    loadMethodList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
    }
  }
}

const resetCategoryForm = () => {
  Object.assign(categoryForm, {
    id: undefined,
    deliveryMethodsCategoryName: '',
    sort: 0
  })
  categoryFormRef.value?.resetFields()
}

const submitCategoryForm = async () => {
  if (!categoryFormRef.value) return

  try {
    const valid = await categoryFormRef.value.validate()
    if (!valid) return

    if (categoryForm.id) {
      await DeliveryMethodsCategoryAPI.update(categoryForm)
    } else {
      await DeliveryMethodsCategoryAPI.add(categoryForm)
    }

    ElMessage.success(t('deliveryMethod.category.saveSuccess'))
    categoryDialogVisible.value = false
    loadCategoryList()
  } catch (error) {
    console.error('保存分类失败:', error)
  }
}

// 配送方式管理
const handleAdd = () => {
  resetMethodForm()
  methodDialogVisible.value = true
}

const handleUpdate = (row: DeliveryMethodsVO) => {
  resetMethodForm()
  Object.assign(methodForm, {
    id: row.id,
    methodName: row.methodName,
    deliveryMethodsCategoryId: row.deliveryMethodsCategoryId,
    enableStatus: row.enableStatus
  })
  delete methodForm.tenantId;
  methodDialogVisible.value = true
}

const handleDelete = async (row: DeliveryMethodsVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除${row.methodName}吗？删除后不可恢复`,
      t('common.tips'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )

    await DeliveryMethodsAPI.deleteById(row.id!)
    ElMessage.success(t('deliveryMethod.method.deleteSuccess'))
    queryParams.page = 1;
    loadMethodList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配送方式失败:', error)
    }
  }
}

const resetMethodForm = () => {
  Object.assign(methodForm, {
    id: undefined,
    methodName: '',
    deliveryMethodsCategoryId: undefined,
    enableStatus: EnableStatus.ENABLED
  })
  methodFormRef.value?.resetFields()
}

const submitMethodForm = async () => {
  if (!methodFormRef.value) return

  try {
    const valid = await methodFormRef.value.validate()
    if (!valid) return

    if (methodForm.id) {
      await DeliveryMethodsAPI.update(methodForm)
    } else {
      await DeliveryMethodsAPI.add(methodForm)
    }

    ElMessage.success('保存成功')
    methodDialogVisible.value = false
    loadMethodList()
  } catch (error) {
    console.error('保存配送方式失败:', error)
  }
}

// 状态切换
const handleStatusChange = async (row: DeliveryMethodsVO) => {
  try {
    const message = row.enableStatus ? '启用成功' : '禁用成功'

    if (row.enableStatus === 1) {
      // 启用
      await DeliveryMethodsAPI.enableBatch([row.id!])
      ElMessage.success(message)
      loadMethodList()

    } else {
      // 禁用
      ElMessageBox.confirm(
        t("productConfig.message.disableBatchConfirm"),
        t("common.tipTitle"),
        {
          confirmButtonText: t("common.confirm"),
          cancelButtonText: t("common.cancel"),
          type: "warning",
        }
      ).then(async () => {
        await DeliveryMethodsAPI.disableBatch([row.id!])
        ElMessage.success(message)
        loadMethodList()

      }).catch(() => {
        // ElMessage.error(t("productConfig.message.operationFailed"));
      })

    }

  } catch (error) {
    // 恢复原状态
    row.enableStatus = row.enableStatus ? 0 : 1
    console.error('状态切换失败:', error)
  }
}

// 批量操作
const handleSelectionChange = (selection: DeliveryMethodsVO[]) => {
  multipleSelection.value = selection
}

const handleBatchEnable = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要启用的配送方式')
    return
  }

  try {
    /*  await ElMessageBox.confirm(
       '确认要启用选中配送方式？',
       '提示',
       {
         confirmButtonText: '确定',
         cancelButtonText: '取消',
         type: 'warning'
       }
     ) */
    const ids = multipleSelection.value.map(item => item.id!)
    await DeliveryMethodsAPI.enableBatch(ids)
    ElMessage.success('批量启用成功')
    loadMethodList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量启用失败:', error)
      ElMessage.error('批量启用失败')
    }
  }
}

const handleBatchDisable = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要禁用的配送方式')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确认要禁用选中配送方式？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = multipleSelection.value.map(item => item.id!)
    await DeliveryMethodsAPI.disableBatch(ids)
    ElMessage.success('批量禁用成功')
    loadMethodList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量禁用失败:', error)
      ElMessage.error('批量禁用失败')
    }
  }
}

// 初始化
onMounted(() => {
  loadCategoryList()
  loadMethodList()
})
</script>

<style lang="scss" scoped>
.search-container {
  padding: 20px;
}

.delivery-method-container {
  display: flex;
  height: calc(100vh - 185px);
  gap: 10px;

  .left-panel {
    width: 300px;
    background: white;
    border-radius: 2px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .category-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .category-list {
      .category-item {
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 8px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        border: 1px solid transparent;

        &:hover {
          background: #F8F9FC;
          border-radius: 4px;
        }

        &.active {
          background: rgba(118, 42, 219, 0.06);
          border-radius: 4px;
          color: #762ADB;
        }

        .category-actions {
          display: none;
          gap: 8px;
        }

        &:hover .category-actions {
          display: flex;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    // gap: 16px;
    background-color: #fff;
    border-radius: 2px;

    .search-card,
    .toolbar-card {
      :deep(.el-card__body) {
        padding: 16px;
      }
    }

    .search-card {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 16px;
        margin-bottom: 0;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 20px;
    }
  }
}

:deep(.el-table) {
  .el-switch {
    --el-switch-on-color: #67c23a;
    --el-switch-off-color: #dcdfe6;
  }
}

.drawer-content {
  padding: 20px;
  flex: 1;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
}
</style>
