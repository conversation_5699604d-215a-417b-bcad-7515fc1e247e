<template>
  <div class="app-container">
    <div class="container-wrapper">
      <div class="container-main">
        <!-- 左侧信息 -->
        <div class="container-left">
          <div class="menu-list">
            <div
              v-for="(item, index) in menuList"
              :key="index"
              class="item"
              :class="{ active: item.value === activeTab }"
              @click="handleMenu(item)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>

        <!-- 右侧表单 -->
        <div class="container-right">
          <!--        <div class="content-head">-->
          <!--          {{ detailData.contentName }}-->
          <!--        </div>-->
          <div class="content-body">
            <div v-html="detailData.contentDesc"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/core/store";

import UserAPI from "@/core/api/accountManagement";

const userStore = useUserStore();
const { user, getUserInfo } = userStore;

const state = reactive({
  loading: false,
  menuList: [
    {
      label: "隐私政策",
      value: "qygj_privacy_agreement",
    },
    {
      label: "隐私政策摘要",
      value: "qygj_privacy_agreement_abstract",
    },
    {
      label: "个人信息收集清单",
      value: "qygj_personal_information_collection",
    },
  ],
  activeTab: "qygj_privacy_agreement",
  detailData: "",
}) as any;

const { loading, menuList, userInfo, activeTab, detailData } = toRefs(state);

function handleMenu(item: any) {
  activeTab.value = item.value;
  getDetail();
}

/**
 * 查询详情
 */
function getDetail() {
  loading.value = true;

  UserAPI.queryProvisionInfo(activeTab.value)
    .then((res: any) => {
      detailData.value = res || "";
    })
    .catch((err: any) => {
      detailData.value = "";
      console.warn(err);
    })
    .finally(() => {
      loading.value = false;
    });
}

onMounted(() => {
  getDetail();
});
</script>

<style scoped lang="scss">
.container-wrapper {
  padding: 0 20px;
  background: #fff;

  .container-main {
    width: 100%;
    display: flex;

    .container-left {
      width: 210px;
      border-right: 1px solid #eef0f5;

      .menu-list {
        margin-top: 20px;

        .item {
          cursor: pointer;
          margin: 30px 0;
          font-weight: 400;
          font-size: 12px;
          color: #151719;

          &.active {
            font-weight: 500;
            color: var(--el-color-primary);
              position: relative;
              &::before {
                content: "";
                position: absolute;
                left: -10px;
                top: 50%;
                transform: translateY(-50%);
                width: 2px;
                height: 10px;
                background: var(--el-color-primary);
                  border-radius: 2px;
              }
          }
        }
      }
    }

    .container-right {
      margin-left: 20px;
      flex: 1;

      .content-body {
        padding: 50px;
        height: calc(100vh - 350px);
        overflow: auto;

        :deep(table) {
          margin: 0 auto;
        }
      }
    }
  }
}
</style>
