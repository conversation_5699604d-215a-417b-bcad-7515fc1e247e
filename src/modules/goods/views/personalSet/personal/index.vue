<template>
  <div class="app-container">
    <div class="container-wrapper">
      <div class="container-main">
        <div class="container-top">
          <div class="info">
            <img class="avatar" src="@/core/assets/images/default_avatar.png" />
            <div class="info-text">
              <div class="nick-name">{{ user.nickName }}</div>
              <div class="dept-name">{{ user.deptName || "-" }}</div>
            </div>
          </div>
        </div>

        <el-tabs v-model="activeTab" class="menu-list">
          <el-tab-pane
            v-for="(item, index) in menuList"
            :key="index"
            :label="item.label"
            :name="item.value"
          ></el-tab-pane>
        </el-tabs>
        <div class="container-body" v-if="activeTab === '1'">
          <div class="form-wrapper">
            <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
              <el-form-item :label="$t('personal.label.account')" prop="account">
                <el-input v-model="formData.account" disabled></el-input>
              </el-form-item>
              <el-form-item :label="$t('personal.label.nickName')" prop="nickName" maxlength="20">
                <el-input v-model="formData.nickName"></el-input>
              </el-form-item>
              <el-form-item :label="$t('personal.label.mobile')" prop="mobile" maxlength="30">
                <el-input v-model="formData.mobile">
                  <template #prepend>
                    <el-select
                      class="custom-select"
                      v-model="formData.countryAreaCode"
                      style="width: 80px; background: white"
                    >
                      <el-option
                        v-for="item in countryNumCodeList"
                        :key="item.id"
                        :label="item.internationalCode"
                        :value="item.internationalCode"
                      />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item
                :label="$t('personal.label.password')"
                prop="password"
                style="display: flex; justify-content: space-between"
              >
                <el-input
                  v-model="formData.password"
                  type="password"
                  disabled
                  style="width: calc(100% - 70px)"
                ></el-input>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  @click="handlePassword"
                >
                  {{$t('personal.button.edit')}}
                </el-button>
              </el-form-item>
              <el-form-item>
                <span class="text-gray">*   {{$t('personal.message.friendlyTips')}}</span>
              </el-form-item>
            </el-form>
            <el-form-item>
              <el-button
                class="submit-button"
                type="primary"
                @click="handleSubmit"
              >
                  {{$t('personal.button.save')}}
              </el-button>
            </el-form-item>
          </div>
        </div>
        <div class="container-right" v-if="activeTab === '2'">
          <Policy></Policy>
        </div>
      </div>
    </div>
    <UpdatePassword v-model:dialog-visible="dialog.visible"></UpdatePassword>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/core/store";

import Policy from "./components/policy.vue";

import UserAPI from "@/core/api/accountManagement";
import API from "@/modules/goods/api/personal";

const userStore = useUserStore();
const { user, getUserInfo } = userStore;
const { t } = useI18n();

const state = reactive({
  loading: false,
    formRef:null,
  formData: JSON.parse(JSON.stringify(user)),
  menuList: [
    {
      label: "基本信息",
      value: "1",
    },
    {
      label: "隐私政策",
      value: "2",
    },
  ],
  activeTab: "1",
  dialog: {
    visible: false,
  },
  countryNumCodeList: "",
  rules: {
    account: [
      {
        required: true,
        message: t("common.placeholder.inputTips"),
        trigger: "blur",
      },
    ],
    nickName: [
      {
        required: true,
        message: t("common.placeholder.inputTips"),
        trigger: "blur",
      },
    ],
    mobile: [
      {
        required: true,
        message: t("common.placeholder.inputTips"),
        trigger: "blur",
      },
    ],
    password: [
      {
        required: true,
        message: t("common.placeholder.inputTips"),
        trigger: "blur",
      },
    ],
  },
}) as any;

const {
  loading,
    formRef,
  formData,
  menuList,
  activeTab,
  dialog,
  countryNumCodeList,
  rules,
} = toRefs(state);

function handleMenu(item: any) {
  activeTab.value = item.value;
}

function handlePassword() {
  dialog.value.visible = true;
}

function handleSubmit() {
    formRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      let params = {
        nickName: formData.value.nickName,
        mobile: formData.value.mobile,
        countryAreaCode: formData.value.countryAreaCode,
      };
      API.updateCurrentUser(params)
        .then((data) => {
          ElMessage.success(t("personal.message.handelSuccess"));
          getUserInfo();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

// 获取区号
function getAreaList() {
  UserAPI.getAllCountry()
    .then((data) => {
      countryNumCodeList.value = data;
    })
    .finally(() => {});
}

onMounted(() => {
  getAreaList();
});
</script>

<style scoped lang="scss">
.container-wrapper {
  padding: 20px;
  background: #fff;
  min-height: calc(100vh - 110px);

  .container-main {
    .container-top {
      padding: 0 40px;

      .info {
        display: flex;
        align-items: center;

        .avatar {
          width: 72px;
          height: 72px;
          border-radius: 72px;
        }

        .info-text {
          flex: 1;
          margin-left: 20px;

          .nick-name {
            font-weight: 500;
            font-size: 20px;
            color: #151719;
          }

          .dept-name {
            ont-weight: 400;
            font-size: 14px;
            color: #90979e;
          }
        }
      }
    }

    .menu-list {
      margin-top: 40px;
    }

    .container-body {
      padding-top: 50px;

      .form-wrapper {
        width: 400px;
        margin: 0 auto;

        .submit-button {
          margin: 50px 0 0 100px;
          width: 340px;
          height: 40px;
        }
      }
    }
  }
}
</style>
