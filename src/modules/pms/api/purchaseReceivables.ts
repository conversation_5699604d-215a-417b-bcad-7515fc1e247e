import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "supply-pms";
// List purchase receivables 获取收款单列表
export function getPurchaseReceivablesList(params: {
  createTimeEnd: string; // 生成账单结束时间
  createTimeStart: string; // 生成账单开始时间
  limit: number; // 每页条数
  page: number; // 当前页
  purchaseOrderCode: string; // 采购单号
  purchaseReceivableCode: string; // 应收账单编号
  purchaseUser: string; // 采购员名称
  receivableStatus: number; // 收款状态：1->待收款，2->已结清状态
  supplierName: string; // 供应商名称
  workOrderCode: string; // 工单号
}) {
  return request({
    url: `${PURCHASE_BASE_URL}/purchaseReceivable/page`,
    method: "post",
    data: params,
  });
}

// Get purchase receivable details 获取收款单详情
export function getPurchaseReceivableDetail(id: string) {
  return request({
    url: `${PURCHASE_BASE_URL}/purchaseReceivable/detail/${id}`,
    method: "get",
  });
}

// Submit payment 收款
export function submitPayment(data: {
  currencyCode: string; // 交易币种：CNY->人民币，USD->美元
  exchangeRate: number; // 汇率
  id: number; // 收款单id
  receivableMethod: number; // 收款方式：1->转账，2->现金，3->线下支付宝, 4->线下微信
  receivableRemark: string; // 收款备注
  receivableTime: string; // 收款时间
  receivableVoucher: string; // 收款凭证
}) {
  return request({
    url: `${PURCHASE_BASE_URL}/purchaseReceivable/receive`,
    method: "post",
    data,
  });
}

// Confirm settlement 结清归档
export function confirmSettlement(id: string) {
  return request({
    url: `${PURCHASE_BASE_URL}/purchaseReceivable/close/${id}`,
    method: "post",
  });
}
