import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "/supply-pms/product/category";

class CategoryAPI {

  /** 获取商品分类下拉数据源 */
  static queryManagerCategoryList(data?: { id?:any }) {
      return request<any, CategoryVO[] >({
          url: `${PURCHASE_BASE_URL}/queryManagerCategoryList`,
          method: "post",
          data:data
      });
  }

}

export default CategoryAPI;

/** 分类对象 */
export interface CategoryVO {
    /** 分类ID */
    id?: string;
    /** 分类名称 */
    categoryName?: string;
    /** 上级分类编号 */
    parentId?: string;
    /** 级别 */
    level?: number;
}
