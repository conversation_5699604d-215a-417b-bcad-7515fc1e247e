import request from "@/core/utils/request";

const PURCHASEPERSONNEL_BASE_URL = "/supply-pms/purchaseTask";

class PurchaerTasksAPI {
  /** 获取采购员分页数据 */
  static getPurchaerTaskPage(queryParams?: PurchaseTasksPageQuery) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

  /** 修改采购任务 */
  static editPurchaseTask(data: PurchasePersonnelForm) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }

  /** 添加采购任务 */
  static addPurchaseTask(data: PurchasePersonnelForm) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }
  /** 获取指定仓库和收货日期供应商下拉数据源 */
  static getSupListByParam(data: any) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/querySupListByParam`,
      method: "post",
      data: data,
    });
  }

  /**
   * 删除采购任务
   *
   */
  static delete(data: { id?: string }) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 生成采购单 */
  static batchCreateOrder(data?: any) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/batchCreateOrder`,
      method: "post",
      data: data,
    });
  }
}

export default PurchaerTasksAPI;

/** 采购任务分页查询参数 */
export interface PurchaseTasksPageQuery extends PageQuery {
  supplierId?: number;
  purchaseUserId?: number;
  taskStatus?: number;
  firstCategoryId?: number;
  secondCategoryId?: number;
  thirdCategoryId?: number;
  dateType?: number;
}

/** 添加采购任务参数 */
export interface PurchaseFrom {
  expectedDeliveryDate?: number;
  id?: string;
  purchaseTaskProductInfoDTOList?: any;
  warehouseId?: string;
  warehouseName?: string;
}

/** 商品分页查询参数 */
export interface goodsPageQuery {
  page: number;
  limit: number;
  productName?: string;
}

/** 采购员分页对象 */
export interface PurchasePersonnelPageVO {
  /** ID */
  id?: string;
  /** 账号 */
  account?: number;
  /** 姓名 */
  purchaserName?: string;
  /** 手机号 */
  purchaserMobile?: string;
  /** 角色 */
  roles?: string;
  /** 部门 */
  department?: string;
  /** 创建时间 */
  createTime?: Date;
}

/** 采购员表单对象 */
export interface PurchasePersonnelForm {
  /** 人员编码 */
  userId?: string;
}

export interface GoodsListResponse {
  list: Array<{
    productCode: string;
    productName: string;
    warehouseName: string;
    roles: string;
  }>;
  total: number;
}

// Get goods list
export function getGoodsList(query: goodsPageQuery) {
  return request({
    url: "/pms/goods/list",
    method: "get",
    params: query,
  });
}
