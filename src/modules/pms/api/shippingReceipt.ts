import request from "@/core/utils/request";

const PURCHASEPERSONNEL_BASE_URL = "/supply-pms/purchaser";

class ShippingReceiptAPI {
  /** 分页数据 */
  static getShippingReceiptPage(queryParams?: ReceiveTransportPageQuery) {
    return request({
      url: `/supply-pms/receiveTransport/listPage`,
      method: "post",
      data: queryParams,
    });
  }

  /** 添加 */
  static addReceiveTransport(data: any) {
    return request({
      url: `/supply-pms/receiveTransport/save`,
      method: "post",
      data: data,
    });
  }
  /** 收运单确认 */
  static confirmReceiveTransport(data: any) {
    return request({
      url: `/supply-pms/receiveTransport/confirm`,
      method: "post",
      data: data,
    });
  }

  /** 详情 */
  static receiveTransportDetails(data: any) {
    return request({
      url: `/supply-pms/receiveTransport/detail`,
      method: "get",
      params: data,
    });
  }
  /** 收运单下查询质检单 */
  static queryInspectionByReceiveTransportCode(data: any) {
    return request({
      url: `/supply-pms/receiveTransport/qtyDetail?receiveTransportId=${data?.receiveTransportId}`,
      method: "get",
    });
  }
  /** 添加收运单-采购列表点击收运详情数据 */
  static detailForAddReceiveTransport(data: any) {
    return request<any, PageResult<SRPurchaseOrderData>>({
      url: `/supply-pms/receiveTransport/detailForAddReceiveTransport`,
      method: "get",
      params: data,
    });
  }
  /**
   * 删除采购员
   *
   */
  static delete(data: { id?: string }) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 获取人员下拉数据源 */
  static getUserList() {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/userList`,
      method: "get",
    });
  }

  /** 下拉框列表查询 */
  static getPurchaserList(data: any) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/select`,
      method: "post",
      data: data,
    });
  }
}

export default ShippingReceiptAPI;

/** 采购员分页查询参数 */
export interface ReceiveTransportPageQuery extends PageQuery {
  receiveTransportCode?: string;
  purchaseCode?: string;
  supplierId?: string;
  buyerId?: string;
}

/** 采购员分页对象 */
export interface PurchasePersonnelPageVO {
  /** ID */
  id?: string;
  /** 账号 */
  account?: number;
  /** 姓名 */
  purchaserName?: string;
  /** 手机号 */
  purchaserMobile?: string;
  /** 角色 */
  roles?: string;
  /** 部门 */
  department?: string;
  /** 创建时间 */
  createTime?: Date;
}

/** 采购员表单对象 */
export interface PurchasePersonnelForm {
  /** 人员编码 */
  userId?: string;
}
export interface SRPurchaseOrderData {
  /** 采购员ID */
  buyerId?: string;
  /** 采购单号 */
  purchaseCode?: string;
  /** 收运类型 */
  receiveTransportType?: number;
  /** 供应商ID*/
  supplierId?: string;
  /** 供应商名称*/
  supplierName?: string;
  /** 采购类型*/
  purchaseOrderType?: string;
  /** 采购来源*/
  purchaseOrderSource?: string;
  /** 采购员*/
  buyerName?: string;
  /** 采购员加密*/
  encipherBuyerName?: string;
  /** 计划交货日期 */
  planDeliveryDate?: string;
  /** 制单人 */
  createUserName?: string;
  /** 制单人加密 */
  encipherCreateUserName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 备注 */
  remark?: string;
  /** 本次收货量合计 */
  totalReceivedCount?: number;
  /** 本次收货金额合计 */
  totalReceivedAmount?: number;


  receiveTransportDetailVOList?: SRPurchaseProductData[];
}
export interface SRPurchaseProductData {
  /** 币种 */
  amountCurrency?: string;
  /** 商品品牌 */
  brandName?: string;
  firstCategoryId?: string;
  secondCategoryId?: string;
  thirdCategoryId?: string;
  /** 计划采购量 */
  planPurchaseCount?: number;
  /** 商品分类 */
  productCategoryFullName?: string;
  /** 商品编码 */
  productCode?: string;
  /** 商品ID */
  productId?: string;
  /** 商品图片 */
  productImg?: string;
  /** 商品名称 */
  productName?: string;
  /** 已收货金额 */
  receivedAmount?: string;
  /** 本次收货金额=本次收货量*本次收货单价 */
  receivedAmountThisTime: number;
  /** 已收货量 */
  receivedCount?: number;
  /** 本次收货量 */
  receivedThisTime: string;
  /** 备注 */
  remark?: string;
  /** 采购单位Id */
  unitId?: string;
  /** 采购单位 */
  unitName?: string;
  /** 本次收货单价 */
  unitPriceReceivedThisTime: string;
  //收运单明细主键id
  receiveTransportDetailId: string;
}
