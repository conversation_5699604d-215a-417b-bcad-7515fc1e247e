import request from "@/core/utils/request";

const PURCHASEPERSONNEL_BASE_URL = "/supply-pms/purchaser";

class PurchasePersonnelAPI {
  /** 获取采购员下拉列表 */
  static getPerchasePersonnelList() {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/select`,
        method: "post",
        data: {},
    });
  }

  /** 获取采购员分页数据 */
  static getPurchaerPersonnelPage(queryParams?: PurchasePersonnelPageQuery) {
    return request<any, PageResult<PurchasePersonnelPageVO[]>>({
      url: `${PURCHASEPERSONNEL_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

  /** 添加采购员 */
  static addPurchasePersonnel(data: { userId?: string; }) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/save`,
      method: "post",
      data: data,
    });
  }

  /**
   * 删除采购员
   *
   */
  static delete(data: { id?: string }) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/delete`,
      method: "post",
      data: data,
    });
  }

  /** 获取人员下拉数据源 */
  static getUserList() {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/user/select`,
      method: "post",
      data: {},
    });
  }

  /** 采购员下拉数据源 */
  static getPurchaerPersonnelList(data?:any) {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/searchList`,
      method: "post",
      data: data,
    });
  }

  /** 采购员列表 */
  static getPurchaerPersonlList() {
    return request({
      url: `${PURCHASEPERSONNEL_BASE_URL}/select`,
      method: "post",
      data: {},
    });
  }
}

export default PurchasePersonnelAPI;

/** 采购员分页查询参数 */
export interface PurchasePersonnelPageQuery extends PageQuery {
  /** 姓名 */
  purchaserName?: string;
  /** 手机号 */
  purchaserMobile?: string;
}

/** 采购员分页对象 */
export interface PurchasePersonnelPageVO {
  /** ID */
  id?: string;
  /** 账号 */
  purchaserAccount?: number;
  /** 姓名 */
  purchaserName?: string;
  /** 手机号 */
  purchaserMobile?: string;
  /** 区号 */
  countryAreaCode?: string;
  /** 角色 */
  roleNames?: string;
  /** 部门 */
  deptName?: string;
  /** 创建时间 */
  createTime?: Date;
}
