import request from "@/core/utils/request";

const PURCHASE_BASE_URL = "/supply-pms/reconciliationBill";

class PurchaseReconcileAPI {

    /** 获取采购对账单分页数据 */
  static getPurchaseReconcilePage(queryParams?: PurchaseReconcilePageQuery) {
    return request<any, PageResult<PurchaseReconcilePageVO[]>>({
      url: `${PURCHASE_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

    /** 导出采购对账单 */
  static exportPurchaseReconcile(queryParams?: PurchaseReconcilePageQuery) {
    return request({
      url: `/supply-pms/export`,
      method: "post",
      data: queryParams,
    });
  }

    /**生成账单 */
    static generatePayableBill(data: {ids?:[string]}) {
        return request({
            url: `${PURCHASE_BASE_URL}/generatePayableBill`,
            method: "post",
            data: data,
        });
    }

    /**反对账 */
    static objection(data: {id?:string}) {
        return request({
            url: `${PURCHASE_BASE_URL}/cancel/${data.id}`,
            method: "get",
        });
    }


    /** 添加编辑采购单 */
    static check( data:CheckFrom) {
        return request({
            url: `${PURCHASE_BASE_URL}/check`,
            method: "post",
            data:data
        });
    }

    /**采购对账单详情查询 */
    static queryPurchaseReconcileDetail( data:{id?:string}) {
        return request<any,PurchaseReconcileDetailFrom>({
            url: `${PURCHASE_BASE_URL}/detail/${data.id}`,
            method: "get",
        });
    }

    /**采购对账单打印详情查询 */
    static queryPurchaseReconcilePrint( data:{id?:string}) {
        return request<any,PurchaseReconcilePrintFrom>({
            url: `${PURCHASE_BASE_URL}/print/${data.id}`,
            method: "get",
        });
    }

}

export default PurchaseReconcileAPI;

/** 采购对账单分页查询参数 */
export interface PurchaseReconcilePageQuery extends PageQuery {
    /** 供应商 */
    supplierName?: string;
    /** 对账状态 */
    reconciliationBillStatus?:number;
    /** 采购单号 */
    purchaseCode?: string;
    /** 对账单号 */
    reconciliationBillCode?: string;
    /** 对账时间类型 */
    timeType?: string;
    /** 对账时间/下单时间 */
    reconciliationBillDate?: [string, string];
    startTime?: string;
    endTime?: string;
}

/** 采购对账单分页对象 */
export interface PurchaseReconcilePageVO {
  /** ID */
  id?: string;
  /** 对账单号 */
  reconciliationBillCode?: string;
  /** 采购单号 */
  purchaseCode?: string;
  /** 下单时间 */
  orderTime?: string;
  /** 供应商名称 */
  supplierName?: string;
  /** 采购员 */
  purchaser?: string;
  /** 应付金额 */
  amount?: string;
  /** 抹零金额 */
  changeAmount?: string;
  /**币种 */
  currencyCode?: string;
  /** 对账状态（1->未对账，2->已对账） */
  reconciliationBillStatus?: number;
  /** 对账时间 */
  checkTime?: string;
  /** 生成账单（1->未生成，2->已生成） */
  generateStatus?: number;
}

/** 采购对账单打印对象 */
export interface PurchaseReconcilePrintFrom {
    /** ID */
    id?: string;
    /** 对账单号 */
    reconciliationBillCode?: string;
    /** 采购类型 */
    purchaseType?: string;
    /** 采购员*/
    purchaser?: string;
    /** 采购单号*/
    purchaseCode?: string;
    /** 创建时间*/
    createTime?: string;
    /** 计划交货日期*/
    planDeliveryTime?: string;
    /** 对账人*/
    checkerUser?: string;
    /** 对账时间*/
    checkTime?: string;
    /** 备注 */
    remark?: string;
    /** 商品列表 */
    purchaseOrderDetailList?: ReceiveProductVO[];
    /** 收货货品总金额（已收货金额） */
    totalAmount?: string;
    /** 对账抹零金额 */
    changeAmount?: string;
    /** 应付总金额 */
    amount?: string;
    /**币种 */
    currencyCode?: string;
}

/** 采购对账单对象 */
export interface PurchaseReconcileDetailFrom {
    /** ID */
    id?: string;
    /** 对账单号 */
    reconciliationBillCode?: string;
    /** 采购员*/
    purchaser?: string;
    /** 计划交货日期*/
    planDeliveryTime?: string;
    /** 采购单号*/
    purchaseCode?: string;
    /** 单据来源 */
    source?: string;
    /** 采购类型 */
    purchaseType?: string;
    /** 对账状态 */
    reconciliationBillStatus?: string;
    /** 收货商品清单 */
    receiveTransportList?:ProductVO[];
    /** 已收货量*/
    totalReceiveNum?: string;
    /** 收货货品总金额（已收货金额） */
    totalAmount?: string;
    /** 对账抹零金额 */
    reconciliationZeroAmount?: string;
    /** 应付总金额 */
    amountTotal?: string;
    /** 备注 */
    remark?: string;
    /**币种 */
    currencyCode?: string;
}

/** 收货商品清单list */
export interface ReceiveProductVO {
    /** 收运单 */
    receiveTransportCode?: number;
    /** 本次收货数量 */
    totalReceivedCount?: string;
    /** 本次收货金额 */
    totalReceivedAmount?: string;
    /** 收运时间 */
    receiveTransportDate?: number;
    purchaseOrderDetailVOList?:ProductVO[];
}

/** 收货商品清单对象 */
export interface ProductVO {
    /** ID */
    id?: string;
    /** 商品图片 */
    productImg?: string;
    /** 商品名称 */
    productName?: string;
    /** 基本单位 */
    unitName?: string;
    /** 收货数量 */
    receivedThisTime?: string;
    /** 收货金额 */
    receivedAmountThisTime?: string;
    /** 备注 */
    remark?: string;
}


/** 对账入参对象 */
export interface CheckFrom {
    /** ID */
    id?: string;
    /** 对账抹零金额 */
    changeAmount?: string;
    /** 备注 */
    remark?: string;
}







