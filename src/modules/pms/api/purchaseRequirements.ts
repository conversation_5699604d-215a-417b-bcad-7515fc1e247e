import request from "@/core/utils/request";

// Types
export interface PurchaseRequirement {
  purchaseReqNo?: string;
  orderNo?: string;
  customerName?: string;
  customerAddress?: string;
  customerContact?: string;
  customerPhone?: string;
  goodsQuantity?: number;
  createTime?: string;
  receivingTime?: string;
  remark?: string;
  status?: number;
}

export interface QueryParams {
  customerInfo: string; // 客户信息
  endCreateTime: string; // 创建截至时间 date-time
  limit: number; // 每页条数
  orderCode: string; // 订购单号
  page: number; // 当前页码
  reqCode: string; // 需求编号
  startCreateTime: string; // 创建开始时间 date-time
}

const USER_BASE_URL = "/supply-pms";
// Get purchase requirements list
export function getPurchaseRequirementsList(params: QueryParams) {
  return request({
    url: `${USER_BASE_URL}/purchaseReq/page`,
    method: "post",
    data: params,
  });
}

// Get purchase requirement detail
export function getPurchaseRequirementDetail(id: number) {
  return request({
    url: `${USER_BASE_URL}/purchaseReq/detail`,
    method: "post",
    data: {
      id,
    },
  });
}

// Create new purchase requirement
export function createPurchaseRequirement(data: PurchaseRequirement) {
  return request({
    url: `${USER_BASE_URL}/purchaseReq/save`,
    method: "post",
    data,
  });
}

// Delete purchase requirement
export function deletePurchaseRequirement(id: number) {
  return request({
    url: `${USER_BASE_URL}/purchaseReq/delete`,
    method: "post",
    data: {
      id,
    },
  });
}

export function querySrcInfo(data: any) {
  /*{
    "businessCode": "",
    "id": 0,
    "querySrcType": 0
  }
  MOBILE(1,"电话"),
    ADDRESS(2,"地址"),
    NAME(3,"姓名"),
    ALL_INFO(4,"所有");
  */
  return request({
    url: `${USER_BASE_URL}/purchaseReq/querySrcInfo`,
    method: "post",
    data,
  });
}


