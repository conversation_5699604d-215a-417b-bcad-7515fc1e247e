import request from "@/core/utils/request";
import {convertToTimestamp} from "@/core/utils";

const PURCHASE_BASE_URL = "/supply-pms/workOrder";

class AbnormalManagementAPI {

    /** 获取异常工单分页数据 */
  static getAbnormalManagementPage(queryParams?:AbnormalManagementPageQuery) {
    return request<any, PageResult<PurchaseReconcilePageVO[]>>({
      url: `${PURCHASE_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

    /**异常工单详情查询 */
    static queryAbnormalManagementDetail( data:{id?:string}) {
        return request<any,AbnormalManagementDetailFrom>({
            url: `${PURCHASE_BASE_URL}/detail/${data.id}`,
            method: "get",
        });
    }

    /** 异常工单处理 */
    static doDealWith( data?:DealWithFrom) {
        return request({
            url: `${PURCHASE_BASE_URL}/handle`,
            method: "post",
            data:data
        });
    }

    /** 工单详情手机号解密 */
    static desensitization( data:{id?:string,name?:string}) {
        return request({
            url: `${PURCHASE_BASE_URL}/desensitization/${data.name}/${data.id}`,
            method: "get",
        });
    }

}

export default AbnormalManagementAPI;

/** 异常工单分页查询参数 */
export interface AbnormalManagementPageQuery extends PageQuery {
    /** 工单编号 */
    workOrderCode?: string;
    /** 供应商 */
    supplierId?: string;
    /** 采购员 */
    purchaserUserId?: string;
    /** 工单类型 */
    workOrderType?:number;
    /** 工单状态 */
    workOrderStatus?:number;
    /** 采购单号 */
    purchaseCode?: string;
    /** 申请日期 */
    applyDate?: [string, string];
    startApplyDate?: string;
    endApplyDate?: string;
    /** 处理日期 */
    handleDate?: [string, string];
    startHandleDate?: string;
    endHandleDate?: string;
}

/** 采购对账单分页对象 */
export interface PurchaseReconcilePageVO {
  /** ID */
  id?: string;
  /** 工单编号 */
  workOrderCode?: string;
  /** 采购单号 */
  purchaseCode?: string;
  /** 收运单号 */
  receiptCode?: string;
  /** 供应商 */
  supplierName?: string;
  /** 采购员 */
  purchaseUser?: string;
  /** 商品图片 */
  imagesUrls?: string;
  /** 商品 */
  productName?: number;
  /** 商品编码 */
  productCode?: number;
  /** 采购金额 */
  purchaseAmount?: string;
  /** 工单总金额 */
  workOrderAmount?: string;
  /** 退款金额 */
  returnAmount?: string;
  /** 申请人 */
  createUserName?: string;
  /** 申请时间 */
  createTime?: string;
  /** 工单类型 （1->采购退货）*/
  workOrderType?: number;
  /** 采购类型 （1-供应商直供 2-市场采购）*/
  orderType?: number;
  /** 工单状态 （1->待处理，2->处理中，3->已完成，4->已取消）*/
  workOrderStatus?: number;
  /**币种 */
  currencyCode?: string;
}

/** 采购对账单对象 */
export interface AbnormalManagementDetailFrom {
    /** ID */
    id?: string;
    /** 工单状态 （1->待处理，2->处理中，3->已完成，4->已取消）*/
    workOrderStatus?: number;
    /** 工单编号 */
    workOrderCode?: string;
    /** 收运单号 */
    receiptCode?: string;
    /** 供应商 */
    supplierName?: string;
    /** 采购员 */
    purchaseUser?: string;
    /** 工单类型 （1->采购退货）*/
    workOrderType?: number;
    /** 采购单号 */
    purchaseCode?: string;
    /** 申请人 */
    createUserName?: string;
    /** 联系方式 */
    createUserPhone?: string;
    /** 申请时间 */
    createTime?: string;
    /** 工单总金额 */
    workOrderAmount?: string;
    /** 退款金额 */
    returnAmount?: string;
    /** 商品信息 */
    workOrderProductDetailList?: ProductVO[];
    /** 处理意见 */
    handleOpinion?:string,
    mobilePhoneShow?:boolean,
    createUserNameShow?:boolean,
    /**币种 */
    currencyCode?: string;
}

/** 商品信息 */
export interface ProductVO {
    /** 商品图片 */
    productImage?: string;
    /** 图片 */
    attachmentInfo?: string;
    /** 商品编码 */
    productCode?: string;
    /** 商品名称 */
    productName?: string;
    /** 基本单位 */
    productUnit?: string;
    /** 数量 */
    productQuantity?: string;
    /** 金额 */
    workOrderAmount?: string;
    /** 备注 */
    workOrderRemark?: string;
    /** 处理方式 */
    handleType?: string;
}


/** 异常工单处理入参对象 */
export interface DealWithFrom {
    /** ID */
    id?: string;
    // /** 处理方式 */
    // handleType?:number,
    /** 商品信息 */
    workOrderProductDetailList?: ProductVO[];
    /** 处理意见 */
    handleOpinion?:string,
}







