import request from "@/core/utils/request";

const PURCHASEPAYABLE_BASE_URL = "/supply-pms/payableBill";

class PurchasePayableAPI {
    /** 分页查询 */
    static getPageList(queryParams?: PurchasePayablePageQuery) {
        return request<any, PageResult<PurchasePayableInfo[]>>({
            url: `${PURCHASEPAYABLE_BASE_URL}/page`,
            method: "post",
            data: queryParams,
        });
    }
    /** 详情查询 */
    static detailPayableBill(id?: string) {
        return request<any, PurchasePayableInfo>({
            url: `${PURCHASEPAYABLE_BASE_URL}/detail/${id}`,
            method: "get",
        });
    }
    /** 可冲账列表 */
    static rechargeableList(payableBillId?: string) {
        return request({
            url: `/supply-pms/purchaseReceivable/rechargeableList/${payableBillId}`,
            method: "get",
        });
    }
    /** 结清归档 */
    static closebill(payableBillId?: string) {
        return request({
            url: `${PURCHASEPAYABLE_BASE_URL}/close/${payableBillId}`,
            method: "get",
        });
    }
    /** 应付账单付款 */
    static payableBill(data: PurchasePayableForm) {
        return request({
            url: `${PURCHASEPAYABLE_BASE_URL}/pay`,
            method: "post",
            data: data
        });
    }
}

export interface PurchasePayablePageQuery extends PageQuery {
    supplierName?: string;
    startCreateTime?: string;
    endCreateTime?: string;
    payableBillCode?: string;
    purchaseCode?: string;
    creatTime?: string[],
    payableBillStatus?: number,
}
export interface PurchasePayableInfo {
    id?: string,
    payableBillCode?: string,
    purchaseCode?: string,
    supplierName?: string,
    purchaserUser?: string,
    amount?: number,
    paidAmount?: number,
    changeAmount?: number,
    checkCode?: string,
    createUserName?: string,
    createTime?: string,
    planDeliveryDate?: string,
    closeTime?: string,
    payableBillStatus?: number,
    orderSource?: number,
    purchaseType?: number,
    refundAmount?: number,
    remark?: string,
    totalAmount?: number,
    paymentTime?: string,
    currencyCode?: string;
    operationLogList?: PayableOperaInfo[],
    payableBillItemList?: PayableProductInfo[],
    payableBillPaymentList?: PayableBillPaymentInfo[],
}
/** 商品信息 */
export interface PayableProductInfo {
    productName?: string;
    productUnit?: string;
    productQuantity?: string;
    productAmount?: number;
}
/** 操作类型 */
export interface PayableOperaInfo {
    operationTime?: string;
    operationType?: string;
    operationUserName?: string;
}

/** 采购员表单对象 */
export interface PurchasePayableForm {
    payableBillId?: string;
    receiverUserName?: string;
    receiverUserId?: string;
    transactionType?: number;
    paymentMethod?: string;
    currencyCode?: string;
    paymentVoucher?: any;
    paymentTime?: string;
    paymentAmount?: string | null;
    exchangeRate?: string;
    remark?: string;
    purchaseReceivableCode?: string;
}
export interface PayableBillPaymentInfo {
    receiverUserName?: string;
    transactionType?: number;
    paymentMethod?: number;
    currencyCode?: string;
    paymentVoucher?: any;
    paymentTime?: string;
    paymentAmount?: number;
    exchangeRate?: string;
    paymentRemark?: string;
    operatorUserName?: string;
}
export default PurchasePayableAPI;
