<template>
  <el-drawer
    v-model="props.visible"
    :title="props.title"
    :close-on-click-modal="false"
    size="500px"
    @close="close"
  >
    <el-form ref="goodsRef">
      <el-form-item>
        <el-tree
          ref="treeRef"
          style="width: 600px"
          :data="goodsList"
          :props="defaultProps"
          node-key="id"
          highlight-current
          :default-checked-keys="defaultCheckedKeys"
          @check-change="handleCheckChange"
          show-checkbox
        />
        <!-- <el-tree
          ref="treeRef"
          style="width: 600px"
          :data="goodsList"
          :props="defaultProps"
          :load="handleLoad"
          node-key="id"
          highlight-current
          :default-checked-keys="defaultCheckedKeys"
          show-checkbox
          lazy
        /> -->
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submitForm"
          :disabled="
            allCheckedNodes.length < 1 && defaultCheckedKeys.length < 1
          "
        >
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { ElMessage } from "element-plus";
import supplierAPI from "@/modules/pms/api/supplier";
import productCategoryAPI from "@/modules/pms/api/productCategory";
import { isLeaf } from "element-plus/es/utils";
import { dataType } from "element-plus/es/components/table-v2/src/common";

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();
// const defaultProps = {
//   label: "name",
//   children: "children",
//   isLeaf: "leaf",
// };
const defaultProps = {
  label: "categoryName",
  children: "children",
};

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

const defaultCheckedKeys = ref([]);
const menuIds = ref([]);
const submitLoading = ref(false);
const goodsList = ref([]);
const treeRef = ref();
const allCheckedNodes = ref([]);
// const isVisible = computed({
//   get: () => props.visible,
//   set: (val: any) => {
//     emit("update:modelValue", val);
//   },
// });
let goodsRef = ref();

function getGoodsList() {
  productCategoryAPI
    .queryCategoryTreeList({})
    .then((data: any) => {
      goodsList.value = data;
    })
    .finally(() => {});
}

// function handleLoad(node: any, resolve: any) {
//   if (node.level === 0) {
//     //如果是第一级，调取接口resolve出对应数据的一级数据
//     supplierAPI.getManagerCategoryList({}).then((data: any) => {
//       if (data) {
//         //通过map重组数据结构
//         let res = data.map((item: any, index: any) => {
//           return {
//             name: item.categoryName,
//             id: item.id,
//             leaf: !item.hasChildren,
//           };
//         });
//         resolve(res);
//       }
//     });
//   }
//   if (node.level === 1) {
//     //点击一级，触发调用第二级数据
//     supplierAPI
//       .getManagerCategoryList({ id: node.data.id })
//       .then((data: any) => {
//         if (data) {
//           let children = data.map((item: any, index: any) => ({
//             name: item.categoryName,
//             id: item.id,
//             leaf: !item.hasChildren,
//           }));
//           resolve(children);
//         }
//       });
//   }
//   if (node.level === 2) {
//     supplierAPI
//       .getManagerCategoryList({ id: node.data.id })
//       .then((data: any) => {
//         if (data) {
//           let children = data.map((item: any, index: any) => ({
//             name: item.categoryName,
//             id: item.id,
//             leaf: !item.hasChildren,
//           }));
//           resolve(children);
//         }
//       });
//   }

//   //没有第三级，点击第二级不会再有新的数据
//   if (node.level > 2) return resolve([]);
// }

function reset() {
  defaultCheckedKeys.value = [];
  menuIds.value = [];
}

// const getCheckedKeys = () => {
//   return treeRef.value?.getCheckedKeys(false);
// };

const getCheckedNodes = () => {
  return treeRef.value.getCheckedNodes();
};
const handleCheckChange = (data: any, checked: any, indeterminate: any) => {
  allCheckedNodes.value = getCheckedNodes();
  // const checkedNodes = getCheckedNodes();
  // let count = checkedNodes.filter((item: any) => item.level == 3).length;
  // if (count > 5) {
  //   goodsList.value.forEach(disableOtherNodes);
  // } else {
  //   goodsList.value.forEach(enableAllNodes);
  // }

  // allCheckedNodes.value = getCheckedNodes();
};

// const disableOtherNodes = (node: any) => {
//   // if (!treeRef.value?.isChecked(node)) {
//   node.disabled = true;
//   // }
// };

// const enableAllNodes = (node: any) => {
//   node.disabled = false;
//   allCheckedNodes.value = getCheckedNodes();
// };

function getThirdLevelCheckedNodes() {
  if (treeRef.value) {
    const checkedNodes = getCheckedNodes(); // 获取所有选中的节点
    const thirdLevelNodes = checkedNodes.filter((node: any) => {
      // 判断节点是否为第三级（即 children 不为空且没有子节点的 children）
      return node.level == 3;
    });
    // 提取 label 和 value
    const result = thirdLevelNodes.map((node: any) => ({
      thirdCategoryName: node.categoryName,
      value: node.id,
    }));
    console.log(result); // 输出结果数组，包含所有第三级选中节点的 label 和 value
    return result;
  }
}

function setFormData(data: any) {
  let ids: any = [];
  if (data && data.length > 0) {
    data.forEach((item: any) => {
      console.log(item);
      ids.push(item.thirdCategoryId ? item.thirdCategoryId : item.value);
    });
  }
  defaultCheckedKeys.value = ids;
  console.log(defaultCheckedKeys.value);
}

function submitForm() {
  menuIds.value = getThirdLevelCheckedNodes();
  if (menuIds.value && menuIds.value.length > 30) {
    let mess =
      t("supplierManagement.message.goodsMountTip") + menuIds.value.length;
    ElMessage.warning(mess);
    return false;
  }
  emit("onSubmit", menuIds.value);
  close();
}

function close() {
  emit("update:visible", false);
  reset();
}

defineExpose({
  getGoodsList,
  setFormData,
});
</script>
