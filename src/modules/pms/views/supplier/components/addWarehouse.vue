<template>
  <el-drawer
    v-model="props.visible"
    :title="props.title"
    :close-on-click-modal="false"
    size="800px"
    @close="close"
  >
    <el-form ref="warehouseRef" :inline="true" :model="queryParams">
      <div class="flex-center-but">
        <div class="supplier-div">
          <el-form-item
            prop="warehouseName"
            :label="$t('supplierManagement.label.warehouseName')"
          >
            <el-input
              v-model="queryParams.warehouseName"
              :placeholder="$t('common.placeholder.inputTips')"
              clearable
            />
          </el-form-item>
        </div>
        <div class="btn">
          <el-form-item>
            <el-button type="primary" @click="getWarehouseList">
              {{ $t("common.search") }}
            </el-button>
            <el-button @click="handleResetQuery">
              {{ $t("common.reset") }}
            </el-button>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <div>
      <el-table
        ref="dataTableRef"
        :data="dataList"
        v-loading="loading"
        highlight-current-row
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          :label="$t('supplierManagement.label.warehouseName')"
          prop="warehouseName"
        />
        <el-table-column
          :label="$t('supplierManagement.label.addressW')"
          prop="warehouseAddress"
        />
        <el-table-column
          prop="contactPerson"
          :label="$t('supplierManagement.label.contactsPerson')"
        >
          <template #default="scope">
            {{ scope.row.contactPerson }}
            <!-- <EncryptPhone :nameType="true" :name="scope.row.contactPerson" /> -->
          </template>
        </el-table-column>
        <el-table-column
          prop="mobile"
          :label="$t('supplierManagement.label.contactInformation')"
          min-width="70"
        >
          <template #default="scope">
            <!-- <EncryptPhone :phone="scope.row.mobile" /> -->
            <span class="encryptBox">
              {{ scope.row.countryAreaCode }}
              <span v-if="scope.row.mobile && scope.row.mobile.length <= 4">
                {{ scope.row.mobile }}
              </span>
              <span v-else>
                <span v-if="scope.row.mobilePhoneShow">
                  {{ scope.row.mobile }}
                </span>
                <span v-if="!scope.row.mobilePhoneShow">
                  {{ scope.row.realMobile }}
                </span>
                <el-icon
                  v-if="scope.row.mobile"
                  @click="
                    scope.row.mobilePhoneShow
                      ? getRealPhone(scope.row.id, scope.$index)
                      : ''
                  "
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                >
                  <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                </el-icon>
              </span>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="getWarehouseList"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          :disabled="multipleSelection.length < 1"
        >
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import supplierAPI from "@/modules/pms/api/supplier";

const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

const dataList = ref([]);
const multipleSelection = ref([]);
const total = ref(0);
const loading = ref(false);

const queryParams = reactive({
  page: 1,
  limit: 20,
  warehouseName: "",
  excludeWarehouseIds: [],
});

let warehouseRef = ref();

function getWarehouseList() {
  loading.value = true;
  let params = {
    ...queryParams,
  };
  // if(data && data.length > 0){
  //   params.excludeWarehouseIds = data
  // }
  supplierAPI
    .getWarehouse(params)
    .then((data: any) => {
      dataList.value = data.records;
      if (dataList.value && dataList.value.length > 0) {
        dataList.value.forEach((item: any) => {
          item.mobilePhoneShow = true;
        });
      }
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

function getRealPhone(id: any, index: any) {
  supplierAPI
    .queryWarehouseRealPhone({ id: id })
    .then((data: any) => {
      dataList.value[index].realMobile = data;
      dataList.value[index].mobilePhoneShow = false;
    })
    .finally(() => {});
}

function handleResetQuery() {
  queryParams.warehouseName = "";
  queryParams.page = 1;
  queryParams.limit = 20;
  getWarehouseList();
}

function reset() {
  multipleSelection.value = [];
}

function submitForm() {
  multipleSelection.value.forEach((item: any) => {
    item.warehouseId = item.id;
  });
  emit("onSubmit", multipleSelection.value);
  close();
}

function close() {
  emit("update:visible", false);
  reset();
}

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
}

function setFormData(data: any) {
  queryParams.excludeWarehouseIds = data;
}

defineExpose({
  setFormData,
  getWarehouseList,
});
</script>

<style scoped lang="scss">
.supplier-div {
  width: calc(100% - 180px);
  .el-form-item {
    width: 100%;
  }
}
.encryptBox {
  // display: inline-flex;
  // justify-content: space-between;
  // align-items: center;
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  // align-self: flex-start;
  vertical-align: text-top;
}
</style>
