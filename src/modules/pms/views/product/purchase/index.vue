<template>
    <div class="app-container">
        <div class="purchaseProcuct">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
                    <el-form-item prop="productName" :label="$t('purchase.label.productName')">
                        <el-input
                                v-model="queryParams.productName"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="productCode" :label="$t('purchase.label.productCode')">
                        <el-input
                                v-model="queryParams.productCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('purchase.label.productCategory')" prop="productCategory">
<!--                        <el-cascader v-model="queryParams.productCategory"-->
<!--                                     class="!w-[256px]"-->
<!--                                     :props="props"-->
<!--                                     filterable-->
<!--                                     :placeholder="$t('common.placeholder.selectTips')"-->
<!--                                     clearable />-->
                        <el-cascader v-model="queryParams.productCategory"
                                     :options="categoryList"
                                     :props="propsCategory"
                                     class="!w-[256px]"
                                     filterable
                                     :placeholder="$t('common.placeholder.selectTips')"
                                     clearable />
<!--                        <el-cascader v-model="productFrom.productCategory"-->
<!--                                     :options="categoryList"-->
<!--                                     :props="propsCategory"-->
<!--                                     @change="handleChange"-->
<!--                                     ref="cascaderRef"-->
<!--                                     filterable-->
<!--                                     :placeholder="$t('purchase.placeholder.productCategory')"-->
<!--                                     clearable />-->
                    </el-form-item>
                    <el-form-item :label="$t('purchase.label.status')" prop="status">
                        <el-select
                                v-model="queryParams.status"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in statusList" :key="item.statusId" :label="item.statusName" :value="item.statusId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('purchase.label.supplier')" prop="supplierId">
                        <el-select
                                v-model="queryParams.supplierId"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                filterable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['pms:product:purchase:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['pms:product:purchase:reset']" @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card shadow="never" class="table-container">
                <template #header>
                    <div class="flex-center-but">
                        <div>
                            <el-button  v-hasPerm="['pms:product:purchase:add']" type="primary" @click="addPurchaseMaterial(null,'add')">
                                {{$t('purchase.button.addPurchaseMaterial')}}
                            </el-button>
                            <el-button type="primary" plain v-hasPerm="['pms:product:purchase:batchOnShelf']" @click="batchOnShelf()" :disabled="multipleSelection.length===0">
                                {{$t('purchase.button.putOnShelves')}}
                            </el-button>
                            <el-button type="primary" plain v-hasPerm="['pms:product:purchase:batchOffShelf']" @click="batchOffShelf()" :disabled="multipleSelection.length===0">
                                {{$t('purchase.button.getOffShelves')}}
                            </el-button>
                            <el-button v-hasPerm="['pms:product:purchase:setSuppliers']" @click="setSuppliers(null,1)" :disabled="multipleSelection.length===0">
                                {{$t('purchase.button.setSuppliers')}}
                            </el-button>
                            <el-button  v-hasPerm="['pms:product:purchase:setDefaultSuppliers']" @click="setSuppliers(null,2)" :disabled="multipleSelection.length===0">
                                {{$t('purchase.button.setDefaultSuppliers')}}
                            </el-button>
                            <el-button v-hasPerm="['pms:product:purchase:exportSequence']" @click="exportSequencePurchase()">
                                {{$t('common.exportSequence')}}
                            </el-button>
                        </div>
                        <div>
                            <el-button v-hasPerm="['pms:product:purchase:export']" @click="exportPurchase()">
                                {{$t('purchase.button.exportProductInformation')}}
                            </el-button>
                        </div>
                    </div>
                </template>

                <el-table
                        v-loading="loading"
                        :data="purchaseList"
                        @selection-change="handleSelectionChange"
                        highlight-current-row
                        stripe
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="selection" width="60" align="center"/>
                    <el-table-column :label="$t('purchase.label.product')" min-width="150" show-overflow-tooltip>
                        <template #default="scope">
                            <div class="product-div">
                               <!-- <div class="picture">
                                    <img :src="scope.row.mainImageUrl" alt="">
                                </div>-->
                                <div class="product">
                                    <div>
<!--                                        <span class="product-key">{{$t('purchase.label.productCode')}}：</span>-->
                                        <span class="product-value">{{scope.row.productCode}}</span>
                                    </div>
                                    <div class="product-name">{{scope.row.productName}}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column :label="$t('purchase.label.productSpecName')" prop="productSpecName" show-overflow-tooltip/> -->
<!--                    <el-table-column :label="$t('purchase.label.conversionRelSecondUnitName')" prop="conversionRelSecondUnitName" show-overflow-tooltip></el-table-column>-->
                    <el-table-column :label="$t('purchase.label.conversionRelSecondUnitNameCopy')" prop="productUnitName" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('purchase.label.productCategory')" prop="fullCategoryName" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('purchase.label.isStandard')" prop="isStandard" show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.isStandard==1">{{$t('common.statusYesOrNo.yes')}}</span>
                            <span v-else>{{$t('common.statusYesOrNo.no')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchase.label.supplier')" prop="supplier" show-overflow-tooltip>
                        <template #default="scope">
                            <el-button
                                    v-hasPerm="['pms:product:purchase:selectDetail']"
                                    :disabled="!scope.row.supplierList || scope.row.supplierList.length==0"
                                    type="primary"
                                    link
                                    @click="setSuppliers(scope.row,3)"
                            >
                                {{$t('purchase.button.selectDetail')}}
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchase.label.createTime')" show-overflow-tooltip>
                        <template #default="scope">
                            <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchase.label.status')"  min-width="100" show-overflow-tooltip>
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color1" v-if="scope.row.status === 1" type="success">{{$t('purchase.statusList.haveBeenPutOnShelves')}}</div>
                                <div class="purchase-status purchase-status-color3" v-else type="info">{{$t('purchase.statusList.haveBeenGetOffShelves')}}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="160">
                        <template #default="scope">
                            <el-button
                                    v-hasPerm="['pms:product:purchase:update']"
                                    v-if="scope.row.status === 2"
                                    type="primary"
                                    link
                                    @click="addPurchaseMaterial(scope.row.id,'edit')"
                            >
                                {{$t('common.edit')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['pms:product:purchase:onShelf']"
                                    v-if="scope.row.status === 2"
                                    type="primary"
                                    link
                                    @click="handleShelves(scope.row,1)"
                            >
                                {{$t('purchase.button.putOnShelves')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['pms:product:purchase:offShelf']"
                                    v-if="scope.row.status === 1"
                                    type="primary"
                                    link
                                    @click="handleShelves(scope.row,2)"
                            >
                                {{$t('purchase.button.getOffShelves')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['pms:product:purchase:delete']"
                                    v-if="scope.row.status === 2"
                                    type="danger"
                                    link
                                    @click="handleDelete(scope.row.id)"
                            >
                                {{$t('common.delete')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
            </el-card>

            <SetSupplier
                ref="setSuppliersRef"
                v-model:visible="dialog.visible"
                :title="dialog.title"
                @onSubmit="handleQuery"
            />

        </div>
        <ExportSequence
                ref="exportSequenceRef"
                v-model:dialog-visible="dialogVisible"
                :path="`purchase:product:export`">
        </ExportSequence>
    </div>
</template>

<script setup lang="ts">
    import PurchaseOrderAPI from "@/modules/pms/api/purchaseOrder";

    defineOptions({
        name: "Purchase",
        inheritAttrs: false,
    });

    import type { CascaderProps } from 'element-plus'
    import { parseDateTime } from "@/core/utils/index.js";
    // import CategoryAPI from "@/modules/pms/api/category";
    import supplierAPI from "@/modules/pms/api/supplier";
    import PurchaseAPI, { PurchasePageVO, PurchasePageQuery} from "@/modules/pms/api/purchase";
    import {IObject} from "@/core/components/CURD/types";
    import {useRouter} from "vue-router";
    import SetSupplier from "./components/setSupplier.vue";
    import productCategoryAPI from "@/modules/pms/api/productCategory";

    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const loading = ref(false);
    const total = ref(0);
    const dialogVisible = ref(false);
    const exportSequenceRef= ref()
    const multipleSelection = ref([]);
    const supplierList = ref([])
    const statusList = ref([
        {
            statusId: 1,
            statusName: t('purchase.statusList.haveBeenPutOnShelves')
        },
        {
            statusId: 2,
            statusName:t('purchase.statusList.haveBeenGetOffShelves')
        }
    ])
   /* const props: CascaderProps = {
        lazy: true,
        checkStrictly : true,
        async lazyLoad(node, resolve) {
            const {level,data} = node
            let arr: any = []
            if (level == 0) {
                arr = await queryManagerCategoryList(null)
            } else{
                arr =  await queryManagerCategoryList(data.value)
            }
            const nodes = arr.map((item) => ({
                value: item.id,
                label: item.categoryName,
                parentId: item.parentId,
                leaf: level >= 2,
            }))
            console.log(nodes)
            resolve(nodes)
        },
    }*/
    const categoryList = ref([])
    const propsCategory: CascaderProps = {
        checkStrictly : true,
        value: 'id',
        label: 'categoryName',
        children: 'children',
    }
    const queryParams = reactive<PurchasePageQuery>({
        page: 1,
        limit: 20,
    });

    const purchaseList = ref<PurchasePageVO[]>();

    const setSuppliersRef = ref();

    const dialog = reactive({
        title: "",
        visible: false,
    });

    /** 查询供应商列表 */
    function getSupplierList() {
        supplierAPI.getSupplierListAll()
            .then((data) => {
                supplierList.value = data;
            })
    }

    /** 查询商品分类列表 */
    function queryManagerCategoryList(id?:any) {
      /*  return new Promise((resolve, reject) => {
            let params={}
            if(id){
                params.id=id
            }
            CategoryAPI.queryManagerCategoryList(params).then((data) => {
                resolve(data);
            }).catch((error) => {
                reject(error);
            })
        });*/

        productCategoryAPI.queryCategoryTreeList({}).then((data: any) => {
            categoryList.value = data;
        })
    }

    /** 查询 */
    function handleQuery() {
        loading.value = true;
        let params = {
            ...queryParams,
        }
        if(queryParams.productCategory && queryParams.productCategory.length==1){
            params.firstCategoryId=queryParams.productCategory[0]
        }
        if(queryParams.productCategory && queryParams.productCategory.length==2){
            params.firstCategoryId=queryParams.productCategory[0]
            params.secondCategoryId=queryParams.productCategory[1]
        }
        if(queryParams.productCategory && queryParams.productCategory.length==3){
            params.firstCategoryId=queryParams.productCategory[0]
            params.secondCategoryId=queryParams.productCategory[1]
            params.thirdCategoryId=queryParams.productCategory[2]
        }
        delete params.productCategory
        PurchaseAPI.getPurchaerPage(params)
            .then((data) => {
                purchaseList.value = data.records;
                total.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    /** 行复选框选中记录选中ID集合 */
    function handleSelectionChange(selection: any[]) {
        multipleSelection.value = selection;
    }

    /** 批量上架采购商品*/
    function batchOnShelf() {
        ElMessageBox.confirm(t('purchase.message.putOnShelvesTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                let ids = multipleSelection.value.map((item) => item.id);
                let data = {
                    productIds: ids,
                }
                PurchaseAPI.batchOnShelf(data).then(res => {
                    ElMessage.success(t('purchase.message.putOnShelvesSucess'))
                    handleQuery()
                })
            },
            () => {
                ElMessage.info(t('purchase.message.putOnShelvesConcel'));
            }
        );
    }

    /** 批量下架采购商品*/
    function batchOffShelf() {
        ElMessageBox.confirm(t('purchase.message.getOffShelvesTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                let ids = multipleSelection.value.map((item) => item.id);
                let data = {
                    productIds: ids,
                }
                PurchaseAPI.batchOffShelf(data).then(res => {
                    ElMessage.success(t('purchase.message.getOffShelvesSucess'))
                    handleQuery()
                })
            },
            () => {
                ElMessage.info(t('purchase.message.getOffShelvesConcel'));
            }
        );
    }

    /** 上架/下架采购商品*/
    function handleShelves(row?: IObject,status?: number) {
        ElMessageBox.confirm(status==1?t('purchase.message.putOnShelvesTips'):t('purchase.message.getOffShelvesTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
            let  data = {
                id: row.id,
                status: status
            }
            PurchaseAPI.updateStatus(data).then(res => {
                ElMessage.success(status==1?t('purchase.message.putOnShelvesSucess'):t('purchase.message.getOffShelvesSucess'))
                handleQuery()
            })
        },
        () => {
            ElMessage.info(status==1?t('purchase.message.putOnShelvesConcel'):t('purchase.message.getOffShelvesConcel'));
        }
       );
    }

    /** 打开设置（默认）供应商弹窗 */
    function setSuppliers(row,val) {
        let ids = [];
        if(row){
            dialog.title = t('purchase.title.suppliers') ;
            ids = [row.id];
        }else{
            dialog.title = val===1? t('purchase.title.setSuppliers') : t('purchase.title.setDefaultSuppliers') ;
            ids = multipleSelection.value.map((item) => item.id);
        }
        setSuppliersRef.value.setFormData({supplierType:val,productIds:ids,supplierList:row && row.supplierList?row.supplierList:[]});
        dialog.visible = true;
    }

    /** 删除采购商品 */
    function handleDelete(id?: string) {
        ElMessageBox.confirm(t('purchase.message.deleteTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                loading.value = true;
                let data = {
                    id:id
                }
                PurchaseAPI.delete(data)
                    .then(() => {
                        ElMessage.success(t('purchase.message.deleteSucess'));
                        handleResetQuery();
                    })
                    .finally(() => (loading.value = false));
            },
            () => {
                ElMessage.info(t('purchase.message.deleteConcel'));
            }
        );
    }

    /** 新增/编辑采购商品*/
    function addPurchaseMaterial(id?:string,type?:string){
        router.push({
            path: "/pms/product/addPurchase",
            query: {id:id,type:type,title:type=='edit'?t("purchase.button.editPurchaseMaterial"):t("purchase.button.addPurchaseMaterial")}
        });
    }

    /** 导出商品*/
    function exportPurchase() {
        ElMessageBox.confirm(t('common.exportTips'), t('common.export'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                loading.value = true;
                let params = {
                    ...queryParams,
                }
                if(queryParams.productCategory && queryParams.productCategory.length==1){
                    params.firstCategoryId=queryParams.productCategory[0]
                }
                if(queryParams.productCategory && queryParams.productCategory.length==2){
                    params.firstCategoryId=queryParams.productCategory[0]
                    params.secondCategoryId=queryParams.productCategory[1]
                }
                if(queryParams.productCategory && queryParams.productCategory.length==3){
                    params.firstCategoryId=queryParams.productCategory[0]
                    params.secondCategoryId=queryParams.productCategory[1]
                    params.thirdCategoryId=queryParams.productCategory[2]
                }
                delete params.productCategory
                delete params.page
                delete params.limit
                PurchaseAPI.exportPurchaer(params)
                    .then((data) => {
                        exportSequencePurchase()
                    })
                    .finally(() => {
                        loading.value = false;
                    });
            },
            () => {
                ElMessage.info(t('common.exportConcel'));
            }
        );
    }

    /** 导出序列*/
    function exportSequencePurchase(){
        exportSequenceRef.value.exportSequenceListPage()
        dialogVisible.value = true;
    }

    onActivated(() => {
        getSupplierList();
        queryManagerCategoryList();
        handleQuery();
    });

</script>

<style lang="scss" scoped>
    .purchaseProcuct{}
</style>
