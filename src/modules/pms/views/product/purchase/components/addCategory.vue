<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" width="700px" @close="close">
        <el-form :model="contentForm" :rules="contentFormRules" ref="contentFormRef" label-position="top">
            <el-form-item :label="$t('product.label.categoryImage')" prop="productImagesUrls">
<!--                <image-upload v-model="contentForm.imagesUrl" :fileSize="1" :formRef="formRef" name="frontPhoto" :width="96"-->
<!--                              :height="96" />-->
                <upload-multiple
                        :tips="`800*800像素`"
                        :isPrivate="`public-read`"
                        @update:model-value="onChangeMultiple"
                        ref="detailPicsRef"
                        :limit="1"
                        :formRef="formUpdateRef"
                        class="modify-multipleUpload"
                        name="detailPic">
                    <template #default="{ file }">
                        点击上传
                    </template>
                </upload-multiple>
            </el-form-item>
            <el-form-item :label="$t('product.label.level')" prop="level">
                <el-radio-group v-model="contentForm.level" @change="changeCategory">
                    <el-radio v-for="(item, i) in cateLevelList" :key="i" :value="item.id">{{item.content}}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('product.label.categoryName')" prop="categoryName">
                <el-input type="text" :placeholder="$t('common.placeholder.inputTips')" v-model="contentForm.categoryName" :maxlength="20" clearable />
            </el-form-item>
            <el-form-item :label="$t('product.label.parentName')" prop="parentId" v-if="contentForm.level !== 1">
                <template v-if="contentForm.level==2">
                    <el-cascader v-model="contentForm.parentId"
                                 :props="propsCategory1"
                                 @change="handleChange"
                                 ref="cascaderRef"
                                 filterable
                                 :placeholder="$t('purchase.placeholder.productCategory')"
                                 clearable/>
                </template>
                <template v-if="contentForm.level==3">
                    <el-cascader v-model="contentForm.parentId"
                                 :props="propsCategory2"
                                 @change="handleChange"
                                 ref="cascaderRef"
                                 filterable
                                 :placeholder="$t('purchase.placeholder.productCategory')"
                                 clearable/>
                </template>
            </el-form-item>
            <el-form-item :label="$t('product.label.categorySequence')" prop="sort">
                <el-input type="number" :placeholder="$t('common.placeholder.inputTips')"  oninput="if(value){value=value.replace(/[^\d]/g,'')} if(value<=0){value=''} if(value>999999999){value=999999999}" v-model="contentForm.sort" :maxlength="20" clearable />
            </el-form-item>
        </el-form>
        <template #footer>
        <span class="dialog-footer">
          <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitForm">{{ $t("common.confirm")}}</el-button>
        </span>
        </template>
    </el-drawer>
</template>
<script setup lang="ts">
    import productCategoryAPI, { productCategoryForm } from '@/modules/pms/api/productCategory'
    import {CascaderProps, FormRules} from "element-plus";
    import CategoryAPI from "@/modules/pms/api/category";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();

    const  submitLoading= ref(false)
    const loading = ref(false);
    const contentFormRef = ref();
    const cascaderRef = ref();
    // const propsCategory:CascaderProps = {}
    const propsCategory1: CascaderProps = {
        lazy: true,
        async lazyLoad(node, resolve) {
            const {level,data} = node
            let arr: any = []
            arr = await queryManagerCategoryList(null)
            const nodes = arr.map((item) => ({
                value: item.id,
                label: item.categoryName,
                parentId: item.parentId,
                leaf:level >= 0,
            }))
            console.log(nodes)
            resolve(nodes)
        },
    }
    const propsCategory2: CascaderProps = {
        lazy: true,
        async lazyLoad(node, resolve) {
            const {level,data} = node
            let arr: any = []
            if (level == 0) {
                arr = await queryManagerCategoryList(null)
            } else{
                arr =  await queryManagerCategoryList(data.value)
            }
            const nodes = arr.map((item) => ({
                value: item.id,
                label: item.categoryName,
                parentId: item.parentId,
                leaf:level >= 1,
            }))
            console.log(nodes)
            resolve(nodes)
        },
    }
    const contentForm = reactive<productCategoryForm>({
        parentId:0,
        level: 1,
    })
    const cateLevelList = reactive([
        { id: 1, disabled: true, content: t("product.label.firstCategory") },
        { id: 2, disabled: true, content: t("product.label.secondCategory") },
        { id: 3, disabled: true, content: t("product.label.thirdCategory") },
    ]);
    const contentFormRules = reactive<FormRules>({
        categoryName: [{ required: true, message: t("product.rules.categoryNameRule"), trigger: 'blur' }],
        parentId: [{ required: true, message: t("product.rules.parentIdRule"), trigger: ['blur','change'] }],
        sort: [{ required: true, message:t("product.rules.sort"), trigger: 'blur' }],
    })

    function close() {
        contentFormRef.value.clearValidate();
        contentFormRef.value.resetFields();
        emit("update:visible", false);
    }

    function onChangeMultiple(val) {
        contentForm.imagesUrl=val?val:''
    }

    /** 查询商品分类列表 */
    function queryManagerCategoryList(id?:any) {
        return new Promise((resolve, reject) => {
            let params={}
            if(id){
                params.id=id
            }
            CategoryAPI.queryManagerCategoryList(params).then((data) => {
                resolve(data);
            }).catch((error) => {
                reject(error);
            })
        });
    }
    function changeCategory() {
        contentForm.categoryName = ''
        contentForm.parentId = ''
        contentForm.sort = ''
    }

    function handleChange(){
        if(cascaderRef.value.getCheckedNodes()){
            let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues
            contentForm.parentId = valueArr && valueArr.length>1?valueArr[valueArr.length-1]:valueArr[0];
        }
    }

    function submitForm() {
        contentFormRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value = true;
            let params = {
                ...contentForm
            };
            productCategoryAPI.saveCate(params)
                .then((res) => {
                    ElMessage.success(t("product.message.addCategorySucess"));
                    close();
                    emit("onSubmit");
                })
                .finally(() => {
                    submitLoading.value = false;
                });
        })
    }
</script>

<style scoped lang="scss">
    .supplier-div{
       width: calc(100% - 150px);
    }
</style>
<style lang="scss">
</style>
