<template>
  <el-drawer
    :value="isVisible"
    :title="title"
    :close-on-click-modal="false"
    size="800px"
    @close="close"
  >
    <div class="receiveTransportDialog" v-loading="loading">
      <div class="receive-transport-content" v-if="pageContentVisible">
        <div class="purchase-info">
          <el-descriptions :column="3" style="width: 100%">
            <el-descriptions-item
              :label="$t('shippingReceipt.label.purchaseCode')"
            >
              {{ purchaseOrderInfo.purchaseCode }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.purchaseType')"
            >
              {{ purchaseOrderInfo.purchaseOrderType }}
            </el-descriptions-item>

            <el-descriptions-item
              v-if="purchaseOrderInfo.purchaseOrderType !== '市场自采'"
              :label="$t('shippingReceipt.label.supplier')"
            >
              {{ purchaseOrderInfo.supplierName }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.source')">
              {{ purchaseOrderInfo.purchaseOrderSource }}
            </el-descriptions-item>

            <el-descriptions-item
              :label="$t('shippingReceipt.label.plannedDeliveryDate')"
            >
              {{ parseDateTime(purchaseOrderInfo.planDeliveryDate, "date") }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.buyerName')"
            >
              {{ purchaseOrderInfo.buyerName }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.createTime')"
            >
              {{ parseDateTime(purchaseOrderInfo.createTime, "dateTime") }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.creator')">
              {{ purchaseOrderInfo.createUserName }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('shippingReceipt.label.remark')">
              {{ purchaseOrderInfo?.remark  ?  purchaseOrderInfo.remark : '-'}}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="header-line"></div>
        <div class="product-content-table">
          <el-descriptions :column="2" style="width: 100%">
            <el-descriptions-item
              :label="$t('shippingReceipt.label.createUserName')"
            >
              {{ purchaseOrderInfo.createUserName }}
            </el-descriptions-item>
            <el-descriptions-item
              :label="$t('shippingReceipt.label.receiveTransportDate')"
            >
              {{ parseDateTime(purchaseOrderInfo?.receiveTransportDate, "dateTime") }}
            </el-descriptions-item>
          </el-descriptions>
          <el-table
            highlight-current-row
            stripe
            :data="purchaseOrderInfo.receiveTransportDetailVOList || []">
            <template #empty>
              <Empty/>
            </template>
            <el-table-column :label="$t('shippingReceipt.label.productName')" prop="productName" />
            <el-table-column :label="$t('shippingReceipt.label.productUnit')" prop="unitName" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.price')" prop="unitPriceReceivedThisTime" min-width="120">
              <template #default="scope">
                <span v-if="scope.row?.unitPriceReceivedThisTime || scope.row?.unitPriceReceivedThisTime == 0">
                  {{ scope.row.confirmStatus === "USD" ? "$" : "￥"
                  }}{{ scope.row.unitPriceReceivedThisTime }}
                </span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('shippingReceipt.label.receiverCount')" prop="receivedCount" min-width="120" />
            <el-table-column :label="$t('shippingReceipt.label.receiverCoast')" prop="receivedAmount" min-width="60">
              <template #default="scope">
<!--                confirmStatus 与后端沟通没有此字段，默认展示rmb，与产品已同步-->
                <span v-if="scope.row?.receivedAmount || scope.row?.receivedAmount == 0">
                  {{ scope.row.confirmStatus === "USD" ? "$" : "￥"
                  }}{{ scope.row.receivedAmount }}
                </span>
              </template>
            </el-table-column>
          </el-table>



         <div class="inspection-table-title">
           {{ t('shippingReceipt.label.inspectionTableTitle') }}
         </div>
          <el-table :data="inspectionTableData || []">
            <template #empty>
              <Empty/>
            </template>
            <el-table-column :label="$t('shippingReceipt.label.productName')" prop="productName" show-overflow-tooltip min-width="120"/>
            <el-table-column :label="$t('shippingReceipt.label.unitName')" prop="productUnitName" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.quantity')" prop="productActualQty" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.proportion')" prop="proportion" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.deductionAmount')" prop="deductionAmount" min-width="90" />
            <el-table-column :label="$t('shippingReceipt.label.deductionDesc')" prop="deductionDesc" show-overflow-tooltip min-width="120" />
            <el-table-column :label="$t('shippingReceipt.label.attachment')" prop="imagesUrls" min-width="90" >
              <template #default="scope">
                <span v-if="JSON.parse(scope.row.imagesUrls).length">
                  <el-button type="primary" link @click="previewAttachment(scope.row, scope.$index)">
                    {{ t('shippingReceipt.button.viewBtn') }}
                  </el-button>
                </span>
              </template>
            </el-table-column>>
          </el-table>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer" v-if="pageContentVisible">
        <div class="end">
          <div class="end-left normal14-font">
            <div>{{ $t("shippingReceipt.label.total") }}:</div>
            <div class="ml-20px">
              {{ $t("shippingReceipt.label.receivedCount") }}:
              <span class="primary14-font">
                {{ purchaseOrderInfo.totalReceivedCount }}
              </span>
            </div>
            <div class="ml-20px">
              {{ $t("shippingReceipt.label.includedTax") }}:
              <span class="primary14-font">
                {{ purchaseOrderInfo.amountCurrency === "USD" ? "$" : "￥"
                }}{{ purchaseOrderInfo.totalReceivedAmount }}
              </span>
            </div>
          </div>
          <div class="end-right">
            <el-button @click="close()">
              {{ $t("common.cancel") }}
            </el-button>
          </div>
        </div>
      </div>
    </template>

    <!--上传-->
    <UploadDialog v-model:visible="uploadDialog.visible" ref="uploadDialogRef" :ifDrag=false
                  :showUploadBtn=false />
  </el-drawer>
</template>
<script setup lang="ts">
import {preview} from "vite";

const { t } = useI18n();
import ShippingReceiptAPI, {inspectionData, SRPurchaseOrderData,} from "@/modules/pms/api/shippingReceipt";
import { parseDateTime } from "@/core/utils/index.js";
import UploadDialog
  from "@/modules/wms/views/insideWarehouseManagement/qualityInspectionOrder/components/uploadDialog.vue";

const emit = defineEmits(["onSubmit"]);

const receiveTransportId = ref();

const pageContentVisible = ref(false);
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

let purchaseOrderInfo = ref<SRPurchaseOrderData>({});
let data = reactive({
  rules: {},
  submitLoading: false,
  editType: "add",
  loading: false,
});

let { submitLoading, editType, loading } = toRefs(data);

const isVisible = computed({
  get: () => props.visible,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const inspectionTableData = ref([]);
// const mockdata = [
//   {
//     productCode: 1,
//     productName: '测试数据1231232131',
//     productSpecs: 1,
//     productActualQty: 1,
//     productUnitName: '公斤',
//     deductionDesc: '坏果qweeeeeeeeeeeeeeeeeeee',
//     deductionAmount: 100,
//     proportion: '20%',
//     imagesUrls
//       :
//       "[{\"bucket\":\"yt-oxms-uat\",\"fileName\":\"yt-oxms-uat/image/e2ecfa207bf1d30d32039cd2af6b211.png\",\"originalFileName\":\"e2ecfa207bf1d30d32039cd2af6b211.png\"}]"
//   },
// ]
// inspectionTableData.value = mockdata

function queryInspectionByReceiveTransportCode() {
  loading.value = true;
  ShippingReceiptAPI.queryInspectionByReceiveTransportCode({
    receiveTransportId: receiveTransportId.value
  }).then((data) => {
    inspectionTableData.value = data || [];
  }).finally(() => {
    loading.value = false;
  });
}

function close() {
  receiveTransportId.value = "";
  isVisible.value = false;
  pageContentVisible.value = false;
}

function setEditType(data: any) {
  editType.value = data.type;
  /** 获取收运单详情 */
  if (editType.value === "details") {
    receiveTransportId.value = data.receiveTransportId;
    let params = {
      receiveTransportId: receiveTransportId.value,
    };
    loading.value = true;
    ShippingReceiptAPI.receiveTransportDetails(params)
      .then((data) => {
        pageContentVisible.value = true;
        Object.assign(purchaseOrderInfo.value, data);
      })
      .finally(() => {
        loading.value = false;
      });
    //查询质检单列表
    // queryInspectionByReceiveTransportCode()
  }
}

const uploadDialog = reactive({
  visible: false,
});
// 上传
const uploadDialogRef = ref()
function previewAttachment(row: any, index: any) {
  uploadDialog.visible = true;
  uploadDialogRef.value.setEditType("add");
  if (row?.imagesUrls) {
    uploadDialogRef.value.setFormData(JSON.parse(row.imagesUrls));
  }
}

defineExpose({
  setEditType,
});
</script>

<style scoped lang="scss">
.receiveTransportDialog {
  padding: 5px;

  .receive-transport-content {
    margin-top: 20px;
  }
}

.end {
  display: flex;
  justify-content: space-between;

  .end-left {
    display: flex;

    div {
      align-content: center;
    }

    span {
      color: #c00c1d;
    }
  }
}
.header-line{
  border-bottom: 1px solid #ebeef5;
  margin: 22px 0;
}
.inspection-table-title {
  text-align: center;
  color: #323233;
  margin: 22px 0;
}
</style>
<style scoped>
::v-deep .el-input-group__append {
  padding: 10px;
}

::v-deep .custom-form-item {
  margin-bottom: 15px;
}
</style>
