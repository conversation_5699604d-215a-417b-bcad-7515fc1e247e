<template>
  <div class="app-container purchasePersonnel">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
        <el-form-item prop="purchaserName" :label="$t('purchasePersonnel.label.purchaserName')">
          <el-input
            v-model="queryParams.purchaserName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
          />
        </el-form-item>
        <el-form-item prop="purchaserMobile" :label="$t('purchasePersonnel.label.purchaserMobile')">
            <el-input
                    v-model="queryParams.purchaserMobile"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
            />
        </el-form-item>
        <el-form-item>
          <el-button  v-hasPerm="['pms:purchase:purchasePersonnel:search']" type="primary" @click="handleQuery">
                {{$t('common.search')}}
          </el-button>
          <el-button  v-hasPerm="['pms:purchase:purchasePersonnel:reset']" @click="handleResetQuery">
                {{$t('common.reset')}}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
         <el-button  v-hasPerm="['pms:purchase:purchasePersonnel:add']" type="primary" @click="handleOpenDialog()">
                {{$t('purchasePersonnel.button.addPurchasePersonnel')}}
          </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="purchasePersonnelList"
        highlight-current-row
        stripe
      >
          <template #empty>
              <Empty/>
          </template>
        <el-table-column type="index" :label="$t('common.sort')" width="60" align="center"/>
        <el-table-column :label="$t('purchasePersonnel.label.account')" prop="purchaserAccount" show-overflow-tooltip/>
        <el-table-column :label="$t('purchasePersonnel.label.purchaserName')" prop="purchaserName" show-overflow-tooltip/>
        <el-table-column :label="$t('purchasePersonnel.label.purchaserMobile')" show-overflow-tooltip>
            <template #default="scope">
                <span class="encryptBox">
                    {{scope.row.countryAreaCode}}
                    <span v-if="scope.row.purchaserMobile && scope.row.purchaserMobile.length <= 4">{{scope.row.purchaserMobile}}</span>
                    <span v-else>
                      {{scope.row.purchaserMobile}}
                      <el-icon
                              v-if="scope.row.purchaserMobile"
                              @click="scope.row.mobilePhoneShow ? getRealPhone(scope.row.userId,scope.$index):''"
                              class="encryptBox-icon"
                              color="#762ADB "
                              size="16"
                      >
                        <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                </span>
            </template>
        </el-table-column>
        <el-table-column :label="$t('purchasePersonnel.label.roleNames')" prop="roleNames" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('purchasePersonnel.label.deptName')" prop="deptName" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('purchasePersonnel.label.createTime')" prop="createTime" show-overflow-tooltip>
            <template #default="scope">
                <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
            </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.handle')" width="80">
          <template #default="scope">
            <el-button
              v-hasPerm="['pms:purchase:purchasePersonnel:delete']"
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
            >
                      {{$t('common.delete')}}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 采购员弹窗 -->
    <Edit
        ref="addPurchasePersonnelRef"
        v-model:visible="dialog.visible"
        :title="dialog.title"
        @onSubmit="handleResetQuery"
    />
  </div>
</template>

<script setup lang="ts">

import UserAPI from "@/core/api/accountManagement";

defineOptions({
  name: "PurchasePersonnel",
  inheritAttrs: false,
});

import { parseDateTime } from "@/core/utils/index.js";
import PurchasePersonnelAPI, { PurchasePersonnelPageVO, PurchasePersonnelForm, PurchasePersonnelPageQuery } from "@/modules/pms/api/purchasePersonnel";
import Edit from "./components/edit.vue";

const { t } = useI18n();
const queryFormRef = ref(null);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<PurchasePersonnelPageQuery>({
  page: 1,
  limit: 20,
});

const purchasePersonnelList = ref<PurchasePersonnelPageVO[]>();

const addPurchasePersonnelRef = ref();

const dialog = reactive({
  title: "",
  visible: false,
});

/** 查询 */
function handleQuery() {
  loading.value = true;
  PurchasePersonnelAPI.getPurchaerPersonnelPage(queryParams)
    .then((data) => {
      // purchasePersonnelList.value = data.records;
      purchasePersonnelList.value = data.records.map((item: any, index: any) => {
          item.mobilePhoneShow = true;
          return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  handleQuery();
}

/** 打开添加采购员弹窗 */
function handleOpenDialog() {
    dialog.title = t('purchasePersonnel.title.addPurchasePersonnelUnitTitle');
    addPurchasePersonnelRef.value.queryUserList()
    dialog.visible = true;
}

/** 删除角色 */
function handleDelete(id?: string) {
  ElMessageBox.confirm(t('purchasePersonnel.message.deleteTips'), t('common.tipTitle'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      let data = {
          id:id
      }
      PurchasePersonnelAPI.delete(data)
      .then(() => {
        ElMessage.success(t('purchasePersonnel.message.deleteSucess'));
        handleResetQuery();
      })
      .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t('purchasePersonnel.message.deleteConcel'));
    }
  );
}

function getRealPhone(id: any, index:any) {
    UserAPI.queryRealPhone({ userId: id })
        .then((data: any) => {
            purchasePersonnelList.value[index].purchaserMobile = data.mobile;
            purchasePersonnelList.value[index].mobilePhoneShow = false;
        })
        .finally(() => {});
}

onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
    .encryptBox {
        // display: inline-flex;
        // justify-content: space-between;
        // align-items: center;
        word-wrap: break-word;
        word-break: break-all;
    }

    .encryptBox-icon {
        margin-left: 4px;
        cursor: pointer;
        // align-self: flex-start;
        vertical-align: text-top;
    }
</style>
