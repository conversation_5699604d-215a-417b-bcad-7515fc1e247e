<template>
    <div class="app-container">
        <div class="addPurchaseOrder"  v-loading="loading">
            <div class="page-title">
               <div @click="handleBack()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
               <div>
                   <span v-if="type=='add' || type=='copy'">{{ $t("purchaseOrder.button.addPurchaseOrder") }}</span>
                   <span v-else>{{ $t("purchaseOrder.button.editPurchaseOrder") }}</span>
               </div>
            </div>
            <div class="page-content">
                <el-form
                        :model="form"
                        :rules="rules"
                        ref="fromRef"
                        label-width="108px"
                        label-position="right"
                >
                    <div class="title-lable">
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.basicInformation") }}
                        </div>
                    </div>
                    <div>
                        <el-row class="flex-center-start">
                            <el-col :span="8">
                            <el-form-item :label="$t('purchaseOrder.label.purchaseTheme')" prop="purchaseTheme" >
                                <el-input
                                  class="!w-[256px]"
                                  v-model="form.purchaseTheme"
                                  :placeholder="$t('common.placeholder.inputTips')"
                                  maxlength="100"
                                  clearable
                                />
                            </el-form-item>
                        </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.orderType')" prop="orderType">
                                    <el-select
                                            @change="orderTypeChange"
                                            v-model="form.orderType"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            :disabled="confirmDisabled"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in orderTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.storehouse')" prop="warehouseId">
                                    <el-select
                                            v-model="form.warehouseId"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            filterable
                                            :disabled="confirmDisabled"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in storehouseList" :key="item.warehouseId" :label="item.warehouseName" :value="item.warehouseId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                        :label="$t('purchaseOrder.label.planDeliveryDate')"
                                        prop="startExpectedDeliveryDate"
                                >
                                    <el-date-picker
                                            :editable="false"
                                            class="!w-[210px]"
                                            v-model="form.startExpectedDeliveryDate"
                                            type="date"
                                            value-format="YYYY-MM-DD"
                                            :disabled="confirmDisabled"
                                            :disabled-date="disabledDate"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                    />
                                </el-form-item>
                            </el-col>

                            <el-col :span="8" v-if="form.orderType==1"  style="display: flex">
                                <el-form-item :label="$t('purchaseOrder.label.supplierName')" prop="supplierId" >
                                    <el-select
                                            @change="supplierChange"
                                            v-model="form.supplierId"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            :disabled="confirmDisabled"
                                            filterable
                                            class="!w-[200px]"
                                    >
                                        <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId"></el-option>
                                    </el-select>

                                </el-form-item>

                                <el-form-item label-width="0" prop="contractName" v-if="form.supplierId">
                                    <el-select
                                      @change="changeContract"
                                      v-model="form.contractName"
                                      :placeholder="$t('purchaseOrder.rules.contractName')"
                                      clearable
                                      :disabled="confirmDisabled"
                                      filterable
                                      class="!w-[200px]"
                                      value-key="contractId"
                                    >
                                        <el-option v-for="(item,index) in supplierContractList" :key="index" :label="item.contractName" :value="item"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseOrder.label.purchaseUserName')" prop="purchaseUserId"  v-if="form.orderType==2">
                                    <el-select
                                            @change="purchaseUserChange"
                                            v-model="form.purchaseUserId"
                                            :placeholder="$t('common.placeholder.selectTips')"
                                            clearable
                                            filterable
                                            :disabled="confirmDisabled"
                                            class="!w-[256px]"
                                    >
                                        <el-option v-for="item in purchasePersonnelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item :label="$t('purchaseOrder.label.purchaseUserName')" prop="purchaseUserName"  v-else>
                                    <el-input
                                            class="!w-[256px]"
                                            v-model="form.purchaseUserName"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            clearable
                                            disabled
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                  :label="$t('purchaseOrder.label.paymentType')"
                                  prop="paymentType"
                                >
                                    <el-select
                                      v-model="form.paymentType"
                                      :placeholder="$t('common.placeholder.selectTips')"
                                      clearable
                                      filterable
                                      class="!w-[256px]"

                                    >
                                        <el-option v-for="(item,index) in paymentTypeOption" :key="index" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                            </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item>
                                    <el-button  :disabled="confirmDisabled" type="primary" @click="confirm" >
                                        {{$t('common.confirm')}}
                                    </el-button>
                                    <el-button :disabled="type=='edit'"  @click="handleResetQueryProductAll">
                                        {{$t('common.reset')}}
                                    </el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable"  style="justify-content:space-between;" v-if="showDisabled">
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.productListInformation") }}
                        </div>
                        <div class="button-add cursor-pointer" @click="addProduct()">
                            {{$t('purchaseOrder.button.addProduct')}}
                        </div>
                    </div>

                    <div  v-if="showDisabled">
                        <el-table
                                :data="form.purchaseOrderDetailAddDTOList"
                                highlight-current-row
                                stripe
                        >
                            <el-table-column type="index" :label="$t('common.sort')" width="60" />
                            <el-table-column :label="$t('purchaseOrder.label.productImg')" min-width="150" show-overflow-tooltip>
                                <template #default="scope">
                                    <div class="product-div">
                                        <div class="picture">
                                            <img :src="scope.row.productImg" alt="">
                                        </div>
                                        <div class="product">
                                            <div class="product-name">{{scope.row.productName}}</div>
                                            <div>
                                                <span class="product-key">{{$t('purchaseOrder.label.unitName')}}：</span>
                                                <span class="product-value">{{scope.row.unitName}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('purchaseOrder.label.onHandStock')" prop="onHandStock" show-overflow-tooltip align="right"/>
                            <el-table-column :label="'*'+$t('purchaseOrder.label.planPurchaseCount')" prop="planPurchaseCount" show-overflow-tooltip>
                                <template #default="scope">
<!--                                        <el-form-item class="mt15px" label-width="0px" :prop="'purchaseOrderDetailAddDTOList.'+scope.$index +'.planPurchaseCount'" :rules="[{required:true,message:t('purchaseOrder.rules.planPurchaseCount'),trigger:'blur'},{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/,message:t('purchaseOrder.rules.planPurchaseCountFomart'),trigger:'blur'}]">-->
                                        <el-form-item class="mt15px" label-width="0px" :prop="'purchaseOrderDetailAddDTOList.'+scope.$index +'.planPurchaseCount'" :rules="dialog.visible?[]: [{required:true,message:t('purchaseOrder.rules.planPurchaseCount'),trigger:'blur'},{pattern:/^([1-9]\d{0,7}(\.\d{1,3})?|0\.(?!0+$)\d{1,3})$/,message:t('purchaseOrder.rules.planPurchaseCountFomart'),trigger:'blur'}]">
                                            <el-input
                                                    @change="planPurchaseAmountChange(scope.$index,1)"
                                                    v-model="scope.row.planPurchaseCount"
                                                    :placeholder="$t('common.placeholder.inputTips')"
                                                    clearable
                                            />
                                        </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column :label="'*'+$t('purchaseOrder.label.purchasePrice')" show-overflow-tooltip align="right">
                                <template #default="scope">
<!--                                    <el-form-item class="mt15px" label-width="0px" :prop="'purchaseOrderDetailAddDTOList.'+scope.$index + '.purchasePrice'" v-if="form.orderType==2" :rules="[{required:true,message:t('purchaseOrder.rules.purchasePrice'),trigger:'blur'},{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/,message:t('purchaseOrder.rules.purchasePriceFomart'),trigger:'blur'}]">-->
                                    <el-form-item class="mt15px" label-width="0px" :prop="'purchaseOrderDetailAddDTOList.'+scope.$index + '.purchasePrice'" v-if="form.orderType==2 ||(form.orderType==1 && !enableRicingProducts)" :rules="dialog.visible?[]: [{required:true,message:t('purchaseOrder.rules.purchasePrice'),trigger:'blur'},{pattern:/^-?\d{1,7}(\.\d{1,4})?$/,message:t('purchaseOrder.rules.purchasePriceFomart'),trigger:'blur'}]">
                                        <el-input
                                                @change="planPurchaseAmountChange(scope.$index,1)"
                                                v-model="scope.row.purchasePrice"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                        >
                                            <template #prefix>
                                                <span v-if="form.orderType==2 || (form.orderType==1 && scope.row.currencyCode == 'CNY')">￥</span>
                                                <span v-else-if="!scope.row.currencyCode">￥</span>
                                                <span v-else>$</span>
                                            </template>
                                        </el-input>
                                    </el-form-item>
                                    <span v-else>
                                         <span v-if="form.orderType==2 || (form.orderType==1 && scope.row.currencyCode == 'CNY')">￥</span>
                                          <span v-else-if="!scope.row.currencyCode">￥</span>
                                         <span v-else>$</span>
                                        {{scope.row.purchasePrice}}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="'*'+$t('purchaseOrder.label.planPurchaseAmount')" show-overflow-tooltip align="right">
                                <template #default="scope">
<!--                                    <el-form-item class="mt15px" label-width="0px" :prop="'purchaseOrderDetailAddDTOList.'+scope.$index +'.planPurchaseAmount'" v-if="form.orderType==2" :rules="[{required:true,message:t('purchaseOrder.rules.planPurchaseAmount'),trigger:'blur'},{pattern:/(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/,message:t('purchaseOrder.rules.planPurchaseAmountFomart'),trigger:'blur'}]">-->
                                    <el-form-item class="mt15px" label-width="0px" :prop="'purchaseOrderDetailAddDTOList.'+scope.$index +'.planPurchaseAmount'" v-if="form.orderType==2 || (form.orderType==1 && !enableRicingProducts)" :rules="dialog.visible?[]: [{required:true,message:t('purchaseOrder.rules.planPurchaseAmount'),trigger:'blur'},{pattern:/^-?\d{1,9}(\.\d{1,2})?$/,message:t('purchaseOrder.rules.planPurchaseAmountFomart'),trigger:'blur'}]">
                                        <el-input
                                                @change="planPurchaseAmountChange(scope.$index)"
                                                v-model="scope.row.planPurchaseAmount"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                clearable
                                        >
                                            <template #prefix>
                                                <span v-if="form.orderType==2 || (form.orderType==1 && scope.row.currencyCode == 'CNY')">￥</span>
                                                <span v-else-if="!scope.row.currencyCode">￥</span>
                                                <span v-else>$</span>
                                            </template>
                                        </el-input>
                                    </el-form-item>
                                    <span v-else>
                                         <span v-if="scope.row.planPurchaseAmount">
                                             <span v-if="form.orderType==2 || (form.orderType==1 && scope.row.currencyCode == 'CNY')">￥</span>
                                               <span v-else-if="!scope.row.currencyCode">￥</span>
                                             <span v-else>$</span>
                                             {{scope.row.planPurchaseAmount}}
                                         </span>
                                        <span v-else>-</span>
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="$t('purchaseOrder.label.remark')" show-overflow-tooltip>
                                <template #default="scope">
                                    <el-form-item class="mt15px" label-width="0px" :prop="'purchaseOrderDetailAddDTOList.'+scope.$index +'.remark'">
                                        <el-input
                                                v-model="scope.row.remark"
                                                :placeholder="$t('common.placeholder.inputTips')"
                                                maxlength="200"
                                                clearable
                                        />
                                    </el-form-item>
                                </template>
                            </el-table-column>
                            <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                                <template #default="scope">
                                    <el-button
                                            type="danger"
                                            size="small"
                                            link
                                            @click="handleDelete(scope.$index)"
                                    >
                                        {{$t('common.delete')}}
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div  class="table-title">
                            <div>
                                <span>{{t('purchaseOrder.label.total')}}：</span>
                            </div>
                            <div>
                                <template v-if="form.orderPurchaseStatus==2 || form.orderPurchaseStatus==3">
                                    <span class="mr16px">{{t('purchaseOrder.label.receivingNumberCopy')}}：<span  style="font-size: 18px; color: #C00C1D ">{{form.totalReceivedCount}}</span></span>
                                    <span>
                                        {{t('purchaseOrder.label.receivingMoneyCopy')}}：
                                        <span  style="font-size: 18px; color: #C00C1D ">
                                            <template v-if="form.purchaseOrderDetailAddDTOList && form.purchaseOrderDetailAddDTOList.length>0">
                                                <span v-if="form.orderType==2 || (form.orderType==1 && form.purchaseOrderDetailAddDTOList[0].currencyCode == 'CNY')">￥</span>
                                                    <span v-else-if="!form.purchaseOrderDetailAddDTOList[0]?.currencyCode">￥</span>
                                                <span v-else>$</span>
                                            </template>
                                            {{form.totalReceivedAmount}}
                                        </span>
                                    </span>
                                </template>
                                <template v-else>
                                    <span class="mr16px">{{t('purchaseOrder.label.planPurchaseCount')}}：<span  style="font-size: 18px; color: var(--el-color-primary)">{{form.totalPurchaseCount}}</span></span>
                                    <span>
                                        {{t('purchaseOrder.label.planPurchaseAmount')}}：
                                        <span  style="font-size: 18px; color: var(--el-color-primary)">
                                             <template v-if="form.purchaseOrderDetailAddDTOList && form.purchaseOrderDetailAddDTOList.length>0">
                                                 <span v-if="form.orderType==2 || (form.orderType==1 && form.purchaseOrderDetailAddDTOList[0].currencyCode == 'CNY')">￥</span>
                                                   <span v-else-if="!form.purchaseOrderDetailAddDTOList[0]?.currencyCode">￥</span>
                                                 <span v-else>$</span>
                                             </template>
                                            {{form.totalPurchaseAmount}}
                                        </span>
                                    </span>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div   v-if="showDisabled" class="total-amount">
                    <el-row class="flex-center-start">
                        <el-col :span="6">
                            <el-form-item :label="$t('purchaseOrder.label.totalPurchaseAmount')" prop="totalPurchaseAmount" >
                                <el-input
                                  class="!w-[256px]"
                                  v-model="form.totalPurchaseAmount"
                                  :placeholder="$t('common.placeholder.inputTips')"
                                  maxlength="9"
                                  clearable
                                  disabled
                                />
                            </el-form-item>

                        </el-col>
                        <el-col :span="10">
                            <el-form-item :label="$t('purchaseOrder.label.discountType')" prop="discountType" >
                                <el-radio-group v-model="form.discountType" @change="handleDiscount">
                                    <el-radio :value="1">{{ $t('purchaseOrder.label.discountTypeOption[1]') }}</el-radio>
                                    <el-radio :value="2">{{ $t('purchaseOrder.label.discountTypeOption[2]') }}</el-radio>
                                    <el-radio :value="3">{{ $t('purchaseOrder.label.discountTypeOption[3]') }}</el-radio>
                                </el-radio-group>
                                <el-form-item label-width="0"  prop="discountValue" >
                                <el-input
                                  style="margin-left: 14px;"
                                  v-if="form.discountType===2 || form.discountType===3"
                                  class="!w-[128px]"
                                  v-model="form.discountValue"
                                  :placeholder="$t('common.placeholder.inputTips')"
                                  maxlength="12"
                                  clearable
                                >   <template v-if="form.discountType===3" #append>%</template></el-input>
                                </el-form-item>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item :label="$t('purchaseOrder.label.totalDiscountedAmount')" prop="totalDiscountedAmount" >
                                <el-input
                                  class="!w-[256px]"
                                  :model-value="form.totalDiscountedAmount=calcTotalDiscountedAmount"
                                  :placeholder="$t('common.placeholder.inputTips')"
                                  clearable
                                  disabled
                                />

                            </el-form-item>

                        </el-col>
                    </el-row>
            </div>
                    <div class="title-lable"  v-if="showDisabled">
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.otherInformation") }}
                        </div>
                    </div>
                    <div  v-if="showDisabled">
                        <el-row>
                            <el-col :span="24">
                                <el-form-item
                                        :label="$t('purchaseOrder.label.orderAttachmentFiles')"
                                        prop="orderAttachmentFiles"
                                >
                                    <upload-multiple
                                            :listType="`text`"
                                            :tips="''"
                                            :fileSize="10"
                                            :fileType="['rar','zip','docx','xls','xlsx','pdf','jpg','png','jpeg']"
                                            :isPrivate="`public-read`"
                                            :modelValue="form.orderAttachmentFiles"
                                            @update:model-value="onChangeMultiple"
                                            ref="detailPicsRef"
                                            :limit="10"
                                            :formRef="formUpdateRef"
                                            class="modify-multipleUpload"
                                            name="detailPic">
                                        <template #default="{ file }">
                                            点击上传
                                        </template>
                                    </upload-multiple>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item :label="$t('purchaseOrder.label.remark')" prop="remark">
                                    <el-input
                                            :rows="4"
                                            type="textarea"
                                            show-word-limit
                                            v-model="form.remark"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            maxlength="200"
                                            clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="title-lable" v-if="showDisabled">
                        <div class="title-content">
                            {{ $t("purchaseOrder.label.approve") }}
                        </div>
                    </div>
                    <div  v-if="showDisabled">
                        <el-row>
                            <el-col :span="24">
                                <el-form-item
                                  :label="$t('purchaseOrder.label.approveUserName')"
                                  prop="approveUserId"
                                >
                                    <el-select
                                      v-model="form.approveUserId"
                                      :placeholder="$t('common.placeholder.selectTips')"
                                      clearable
                                      filterable
                                      class="!w-[200px]"

                                    >
                                        <el-option v-for="(item,index) in approveUserOption" :key="index" :label="item.nickName" :value="item.userId"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </div>
                </el-form>
            </div>
            <div class="page-footer"  v-if="showDisabled">
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
                <el-button type="primary" plain v-if="form.orderType==1 && form.orderPurchaseStatus==1 && form.sendStatus==0"  :loading="submitLoading" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
                <el-button type="primary" v-else :loading="submitLoading" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
<!--                <el-button v-hasPerm="['pms:purchase:purchaseOrder:send']" v-if="(type=='edit' && form.orderType==1 && form.orderPurchaseStatus==1 && form.sendStatus==0) || (type=='add' && form.orderType==1)" type="primary" :loading="submitLoading" @click="handleSaveAndSendPurchaseOrderSubmit">{{ $t("purchaseOrder.button.saveAndSendPurchaseOrder") }}</el-button>-->
            </div>
            <AddProduct
                    ref="addProductRef"
                    v-model:visible="dialog.visible"
                    :title="dialog.title"
                    @onSubmit="onSubmit"
            />
        </div>
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "AddPurchaseOrder",
        inheritAttrs: false,
    });

    import WarehouseAPI from "@/modules/pms/api/warehouse";
    import AddProduct from "./components/addProduct.vue";
    import {parseDateTime ,convertToTimestamp} from "@/core/utils/index.js";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
    import PurchaseOrderAPI, {PurchaseOrderFrom, ProductAllPageVO, ProductAllPageQuery} from "@/modules/pms/api/purchaseOrder";
    import supplierAPI from "@/modules/pms/api/supplier";

    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const fromRef = ref()
    const submitLoading = ref(false)
    const queryFormRef = ref(ElForm);
    const loading = ref(false);
    const id = route.query.id;
    const type = route.query.type;
    const addProductRef = ref();
    const supplierList = ref([])
    const formUpdateRef = ref(null);
    const showDisabled = ref(false)
    const confirmDisabled = ref(false)
    const disabledDate = (time) => {
        // 获取今天的日期
        const today = new Date();
        today.setHours(0, 0, 0, 0); // 将时间设置为0点，确保比较时不考虑具体时间
        // 比较传入的日期是否小于今天
        return time.getTime() < today.getTime();
    };
    const dialog = reactive({
        title: "",
        visible: false,
    });
    const orderTypeList = ref([
        {
            key: 2,
            value: t('purchaseOrder.orderTypeList.marketDirectSupply')
        },
        {
            key: 1,
            value:t('purchaseOrder.orderTypeList.suppliersDirectSupply')
        }
    ])
    const storehouseList = ref([])
    const purchasePersonnelList = ref([])
    const supplierContractList = ref([])
    const approveUserOption = ref([])
    const paymentTypeOption = ref([
        {
            value: 1,
            label: t('purchaseOrder.paymentTypeOption[1]')
        },
        {
            value: 2,
            label:t('purchaseOrder.paymentTypeOption[2]')
        }
    ])

    const form = reactive<PurchaseOrderFrom>({
        discountType:1,
        purchaseOrderDetailAddDTOList:[]
    });
    const queryParams = reactive<ProductAllPageQuery>({
        page: 1,
        limit: 20,
    });
    const productTotal = ref(0);
    const productAllList = ref<ProductAllPageVO[]>();

    const enableRicingProducts = ref(false)

    const rules = reactive({
        purchaseTheme:  [{ required: true, message: t("purchaseOrder.rules.purchaseTheme"), trigger: ["blur","change"] }],
        orderType: [{ required: true, message: t("purchaseOrder.rules.orderType"), trigger: ["blur","change"] }],
        warehouseId: [{ required: true, message: t("purchaseOrder.rules.warehouseId"), trigger: ["blur","change"] }],
        startExpectedDeliveryDate: [{ required: true, message: t("purchaseOrder.rules.planDeliveryDate"), trigger: ["blur","change"] }],
        purchaseUserId: [{ required: true, message: t("purchaseOrder.rules.purchasePersonnel"), trigger: ["blur","change"]}],
        purchaseUserName: [{ required: false, message: t("purchaseOrder.rules.purchasePersonnel"), trigger: ["blur","change"] }],
        supplierId: [{ required: true, message: t("purchaseOrder.rules.supplierId"), trigger: ["blur","change"] }],
        contractName: [{ required: true, message: t("purchaseOrder.rules.contractName"), trigger: ["blur","change"] }],
        discountValue:  [
          {  validator: (rule:any, value:any, callback:any) => {
                  const regSum = new RegExp( /^-?\d{1,9}(\.\d{1,2})?$/) //金额可以是正数、负数或零，并且整数部分最长为 9 位，小数部分最多 2 位
                  const regDiscount = new RegExp( /^([1-9]\d{0,2}(\.\d{1,2})?|0\.(?!0+$)\d{1,2})$/) //折扣大于0，小数部分最多 2 位

                      if (form.discountType===2){
                          if (!regSum.test(value)) {
                              callback(new Error(t("purchaseOrder.rules.discountValue")));
                          }
                          if (Number(value)>Number(form.totalPurchaseAmount || 0)) {
                               callback(new Error(t("purchaseOrder.rules.discountValueAmount")));
                          }
                      }
                      if (form.discountType===3){
                          if (!regDiscount.test(value)) {
                              callback(new Error(t("purchaseOrder.rules.discountValuePercentage")));
                          }
                          if (Number(value)>100) {
                              callback(new Error(t("purchaseOrder.rules.discountValuePercentage")));
                          }

                      }

                      callback();


          }, trigger: ["blur"] }
        ],
        paymentType:  [{ required: true, message: t("common.placeholder.selectTips"), trigger: ["change"] }],
        approveUserId:  [{ required: true, message: t("common.placeholder.selectTips"), trigger: ["change"] }],
    });



    /**
     * 计算优惠后金额
     */
    const calcTotalDiscountedAmount = computed(() => {
        let total=Number(form.totalPurchaseAmount || 0), calc=0
        if (form.discountType===1){
            calc=total
        }
        else if (form.discountType===2){
            calc=total-Number(form.discountValue || 0)
        }
        else if (form.discountType===3){
            calc= total*Number(form.discountValue || 0)/100
        }
        return calc.toFixed(2);
    });





    function onChangeMultiple(val) {
        form.orderAttachmentFiles=val?val:''
        console.info('==form.orderAttachmentFiles=='+form)
    }

    function orderTypeChange() {
        form.supplierId=''
        form.supplierName=''
        form.supplierCode=''
        form.purchaseUserId=''
        form.purchaseUserName=''

        form.purchaseOrderDetailAddDTOList=[]

        form.contractId = '';
        form.contractName = '';
        form.contractCode = '';
        form.contractType = '';
    }

    function supplierChange(val:any) {
        if (val){
        let supplier = supplierList.value.filter(function (item) {return form.supplierId ==item.supplierId})
        form.supplierName=supplier[0].supplierName
        form.supplierCode=supplier[0].supplierCode
        form.purchaseUserId=supplier[0].purchaserId
        form.purchaseUserName=supplier[0].purchaserName

        getSupplierContractList()

        }else {
            form.supplierName=''
            form.supplierCode=''
            form.purchaseUserId=''
            form.purchaseUserName=''

        }
        form.contractId = '';
        form.contractName = '';
        form.contractCode = '';
        form.contractType = '';
    }

    function purchaseUserChange() {
        let purchaseUser = purchasePersonnelList.value.filter(function (item) {return form.purchaseUserId ==item.id})
        form.purchaseUserName=purchaseUser[0].name
    }

    function planPurchaseAmountChange(index,val) {
       if(form.purchaseOrderDetailAddDTOList[index].planPurchaseCount!==undefined && form.purchaseOrderDetailAddDTOList[index].purchasePrice!==undefined){
           if(val==1){
               let planPurchaseAmount = (form.purchaseOrderDetailAddDTOList[index].planPurchaseCount * form.purchaseOrderDetailAddDTOList[index].purchasePrice).toFixed(2)
               form.purchaseOrderDetailAddDTOList[index].planPurchaseAmount=planPurchaseAmount
           }
           let totalPurchaseCount= '0'
           let totalPurchaseAmount= '0'
           form.purchaseOrderDetailAddDTOList.forEach(item=>{
              /* item.planPurchaseCount=item.planPurchaseCount?parseFloat(item.planPurchaseCount):0
               item.planPurchaseAmount=item.planPurchaseAmount?parseFloat(item.planPurchaseAmount):0
               totalPurchaseCount= (parseFloat(totalPurchaseCount) + item.planPurchaseCount).toFixed(2)
               totalPurchaseAmount= (parseFloat(totalPurchaseAmount) + item.planPurchaseAmount).toFixed(2)*/
               let planPurchaseCount1=item.planPurchaseCount?parseFloat(item.planPurchaseCount):0
               let planPurchaseAmount1=item.planPurchaseAmount?parseFloat(item.planPurchaseAmount):0
               totalPurchaseCount= (parseFloat(totalPurchaseCount) + planPurchaseCount1).toFixed(2)
               totalPurchaseAmount= (parseFloat(totalPurchaseAmount) + planPurchaseAmount1).toFixed(2)
           })
           form.totalPurchaseCount=totalPurchaseCount
           form.totalPurchaseAmount=totalPurchaseAmount
       }
    }

    /** 查询供应商列表 */
    function getSupplierList() {
        supplierAPI.getSupplierList()
            .then((data) => {
                supplierList.value = data;
            })
    }

    /** 仓库 */
    function getStorehouseList() {
        WarehouseAPI.getStorehouseSelect()
            .then((data) => {
                storehouseList.value = data;
            })
    }

    /** 查询采购员列表 */
    function getPerchasePersonnelList() {
        PurchasePersonnelAPI.getPerchasePersonnelList()
            .then((data) => {
                purchasePersonnelList.value = data;
            })
    }

    function onSubmit(data) {
        let arr = data.concat(form.purchaseOrderDetailAddDTOList);
        let uniqueArr =[...new Map(arr.map(item => [item.productCode, item])).values()];
        form.purchaseOrderDetailAddDTOList=uniqueArr;
        form.purchaseOrderDetailAddDTOList.forEach(item=>{
            item.currency= form.orderType==1?item.currency:'CNY'
            item.currencyCode= item.currency
        })
        console.log("===purchaseOrderDetailAddDTOList==="+form.purchaseOrderDetailAddDTOList);
    }

    /** 假删除*/
    function handleDelete(index?: number) {

        let totalPurchaseCount =form.totalPurchaseCount?parseFloat(form.totalPurchaseCount):0
        let totalPurchaseAmount =form.totalPurchaseAmount?parseFloat(form.totalPurchaseAmount):0
        let planPurchaseCount =form.purchaseOrderDetailAddDTOList[index].planPurchaseCount?parseFloat(form.purchaseOrderDetailAddDTOList[index].planPurchaseCount):0
        let planPurchaseAmount=form.purchaseOrderDetailAddDTOList[index].planPurchaseAmount?parseFloat(form.purchaseOrderDetailAddDTOList[index].planPurchaseAmount):0
        form.totalPurchaseCount= (totalPurchaseCount - planPurchaseCount).toFixed(2)
        form.totalPurchaseAmount= (totalPurchaseAmount - planPurchaseAmount).toFixed(2)
        form.purchaseOrderDetailAddDTOList.splice(index, 1);
        ElMessage.success(t('purchaseOrder.message.deleteSucess'));
    }

     function handleClose(){
        ElMessageBox.confirm(t('purchaseOrder.message.cancelTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            async () => {
                await tagsViewStore.delView(route);
                router.go(-1);
            },
            () => {
            }
        );
    };

    async function handleBack() {
        await tagsViewStore.delView(route);
        router.go(-1);
    };

    /** 添加/编辑采购单 */
    function handleSubmit(){
        console.log("===form===");
        console.log(form);
        console.log(approveUserOption.value?.find((item:  any) => item.userId === form.approveUserId)?.nickName || '');
        if(form.purchaseOrderDetailAddDTOList && form.purchaseOrderDetailAddDTOList.length==0){
            return  ElMessage.error(t('purchaseOrder.message.addOrEditPurchaseTips'));
        }
        fromRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value=true
            form.orderAttachmentFiles=JSON.stringify(form.orderAttachmentFiles)
            let params = {
                orderSource: 1,
                currencyCode: undefined,
                warehouseName: undefined,
                planDeliveryDate: undefined,
                ...form
            }
            let storehouse = storehouseList.value.filter(function (item) {return form.warehouseId ==item.warehouseId})
            params.warehouseCode= storehouse[0].warehouseCode
            params.warehouseId= storehouse[0].warehouseId
            params.warehouseName= storehouse[0].warehouseName
            params.planDeliveryDate=convertToTimestamp(form.startExpectedDeliveryDate+ ' 00:00:00')

            params.approveUserName= approveUserOption.value?.find((item:  any) => item.userId === form.approveUserId)?.nickName || '';

            if(params.purchaseOrderDetailAddDTOList && params.purchaseOrderDetailAddDTOList.length>0){
                params.purchaseOrderDetailAddDTOList.forEach((item)=>{
                    item.planPurchasePrice=item.purchasePrice
                    item.currency=''
                    item.currencyCode=item.currencyCode
                    item.inventoryCount=item.onHandStock
                    item.imagesUrls=item.productImg
                })
                params.currencyCode=params.purchaseOrderDetailAddDTOList[0].currencyCode
            }
            delete params.sendStatus
            if(type=='add' || type=='copy'){
                delete params.id
                PurchaseOrderAPI.addPurchaseOrder(params)
                    .then((data) => {
                        ElMessage.success(t('purchaseOrder.message.addSucess'));
                        handleBack()
                    })
                    .finally(() => {
                        submitLoading.value = false;
                    });

            }else{
                PurchaseOrderAPI.editPurchaseOrder(params)
                    .then((data) => {
                        ElMessage.success(t('purchaseOrder.message.editSucess'));
                        handleBack()
                    })
                    .finally(() => {
                        submitLoading.value = false;
                    });
            }
        })
    }

    /**保存并发送采购单 */
    function handleSaveAndSendPurchaseOrderSubmit(){
        if(form.purchaseOrderDetailAddDTOList && form.purchaseOrderDetailAddDTOList.length==0){
            return  ElMessage.error(t('purchaseOrder.message.addOrEditPurchaseTips'));
        }
        fromRef.value.validate((valid) => {
            if (!valid) return;
            submitLoading.value=true
            form.orderAttachmentFiles=JSON.stringify(form.orderAttachmentFiles)
            let params = {
                orderCode: undefined,
                ...form,
                sendStatus: 1
            }
            if(type=='add' || type=='copy'){
                params.orderSource=1
                let storehouse = storehouseList.value.filter(function (item) {return form.warehouseId ==item.warehouseId})
                params.warehouseCode= storehouse[0].warehouseCode
                params.warehouseId= storehouse[0].warehouseId
                params.warehouseName= storehouse[0].warehouseName
                params.planDeliveryDate=convertToTimestamp(form.startExpectedDeliveryDate+ ' 00:00:00')
                if(params.purchaseOrderDetailAddDTOList && params.purchaseOrderDetailAddDTOList.length>0){
                    params.purchaseOrderDetailAddDTOList.forEach((item)=>{
                        item.planPurchasePrice=item.purchasePrice
                        item.currency=''
                        item.currencyCode=item.currencyCode
                        item.inventoryCount=item.onHandStock
                        item.imagesUrls=item.productImg
                    })
                    params.currencyCode=params.purchaseOrderDetailAddDTOList[0].currencyCode
                }
                delete params.id
                PurchaseOrderAPI.addPurchaseOrder(params)
                    .then((data) => {
                        ElMessage.success(t('purchaseOrder.message.saveAndSendPurchaseOrderSucess'));
                        handleBack()
                    })
                    .finally(() => {
                        submitLoading.value = false;
                    });

            }else{
                params.orderCode=form.orderCode
                PurchaseOrderAPI.editPurchaseOrder(params)
                    .then((data) => {
                        ElMessage.success(t('purchaseOrder.message.saveAndSendPurchaseOrderSucess'));
                        handleBack()
                    })
                    .finally(() => {
                        submitLoading.value = false;
                    });
            }
        })
    }

    /** 查询采购单详情 */
    function queryPurchaseOrderDetail(){
        loading.value = true;
        let params = {
            id:id
        }
        PurchaseOrderAPI.queryPurchaseOrderDetail(params)
            .then((data) => {
                Object.assign(form,data)
                form.startExpectedDeliveryDate=parseDateTime(form.planDeliveryDate, "dateTime")
                form.purchaseOrderDetailAddDTOList=form.purchaseOrderDetailVOList
                if(form.purchaseOrderDetailVOList && form.purchaseOrderDetailVOList.length>0){
                    form.purchaseOrderDetailVOList.forEach((item)=>{
                        item.purchasePrice=item.planPurchasePrice
                        item.currency=item.currencyCode
                        item.onHandStock=item.inventoryCount
                        item.productImg=item.imagesUrls
                    })
                }
                if(form.orderAttachmentFiles  && typeof form.orderAttachmentFiles ==='string' && form.orderAttachmentFiles!=='null'){
                    form.orderAttachmentFiles =JSON.parse(form.orderAttachmentFiles);
                }else {
                    form.orderAttachmentFiles= [];
                }
                queryProductAll(null)
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 查询所有可选的商品列表 */
    function queryProductAll(type){
        fromRef.value.validate((valid) => {
            if (!valid) return;
            loading.value = true;
            let params = {
                supplierId:undefined,
                supplierCode:undefined,
                chooseType:2,
                orderType:form.orderType,
                warehouseId:form.warehouseId,
                warehouseCode:form.warehouseCode,
                startExpectedDeliveryDate:convertToTimestamp(form.startExpectedDeliveryDate+ ' 00:00:00'),
                limit: 20,
                page: 1,
            }
            if(form.orderType==1){
                params.supplierId=form.supplierId
                  params.supplierCode=form.supplierCode
                  params.contractId=form.contractId
                  params.contractCode=form.contractCode
                  params.contractType=form.contractType
            }
            PurchaseOrderAPI.queryProductAll(params)
                .then((data) => {
                    productAllList.value = data.records;
                    if(type==1 && productAllList.value && productAllList.value.length>0){
                        confirmDisabled.value = true
                    }
                    productTotal.value = parseInt(data.total);
                })
                .finally(() => {
                    loading.value = false;
                });
        })
    }

    function confirm() {
        fromRef.value.validate((valid) => {
            if (!valid) return;
            showDisabled.value = true
            queryProductAll(1)
        })
    }

    function handleResetQueryProductAll() {
        ElMessageBox.confirm(t('purchaseOrder.message.resetQueryPurchaseOrder'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                fromRef.value.resetFields();
                queryParams.page = 1;
                queryParams.limit = 20;
                productAllList.value = [];
                productTotal.value = 0;
                form.purchaseOrderDetailAddDTOList = [];
                form.purchaseUserId = '';
                form.purchaseUserName = '';
                form.totalPurchaseCount=''
                form.totalPurchaseAmount=''
                showDisabled.value = false
                confirmDisabled.value = false
            },
            () => {
                ElMessage.info(t("purchaseOrder.message.resetConcel"));
            }
        );
    }

    /** 添加商品 */
   async function addProduct() {
        await queryProductAll(null)
        dialog.title = t('purchaseOrder.title.addProduct');
        let data = {
            supplierId:undefined,
            supplierCode:undefined,
            chooseType:2,
            orderType:form.orderType,
            warehouseId:form.warehouseId,
            startExpectedDeliveryDate:convertToTimestamp(form.startExpectedDeliveryDate+ ' 00:00:00'),
        }
        let storehouse = storehouseList.value.filter(function (item) {return form.warehouseId ==item.warehouseId})
        data.warehouseCode= storehouse[0].warehouseCode

        if(form.orderType==1){
            data.supplierId=form.supplierId
              data.supplierCode=form.supplierCode
              data.contractId=form.contractId
              data.contractCode=form.contractCode
              data.contractType=form.contractType
        }


        addProductRef.value.setFormData({productAllList:productAllList.value,productTotal:productTotal.value,queryParams:data });
        addProductRef.value.queryManagerCategoryList();
        dialog.visible = true;
    }
   function handleDiscount(){
       form.discountValue=''
       fromRef.value?.validateField('discountValue');
   }

   function getSupplierContractList() {
       let params = {
           supplierId:form.supplierId
       }
       PurchaseOrderAPI.querySupplierContractList(params)
       .then((data:any) => {
           supplierContractList.value = data;

       })

   }

   function changeContract(value: any) {
       if (value){
           form.contractId = value.contractId;
           form.contractName = value.contractName;
           form.contractCode =  value.contractCode;
           form.contractType =  value.contractType;


       }else{
           form.contractId = '';
           form.contractName = '';
           form.contractCode = '';
           form.contractType = '';
       }

   }


    function getApproveUser() {
        PurchaseOrderAPI.queryApproveUser().then(res => {
            approveUserOption.value = res
        })

    }
    /** 查询询价配置是否启用 */
     async function getSystemParameters() {

        let res  =await  PurchaseOrderAPI.querySystemParameters('enable_pricing_products')
        enableRicingProducts.value=res

    }

    onMounted(() => {
        getSystemParameters()
        getSupplierList();
        getStorehouseList();
        getPerchasePersonnelList();
        getApproveUser()
        if(type=='edit' || type=='copy'){
            showDisabled.value=true
            confirmDisabled.value=true
            queryPurchaseOrderDetail();
        }
    });
</script>
<style scoped lang="scss">
    .addPurchaseOrder {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .button-add{
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                font-size: 14px;
                color: var(--el-color-primary)
            }
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;
            }
            .total-amount{
                margin-top: 20px;
            }
        }
    }
</style>
