<template>
    <el-drawer v-model="props.visible" :title="props.title" :close-on-click-modal="false" size="850px" @close="close" class="add-product">
        <div>
            <el-form ref="productFromRef" :model="productFrom" :inline="true">
                       <!-- <el-form-item prop="keywords" :label="$t('purchaseOrder.label.productName')">
                            <el-input
                                    v-model="productFrom.keywords"
                                    :placeholder="$t('purchaseOrder.placeholder.keywords')"
                                    clearable
                            />
                        </el-form-item>-->
                         <el-form-item prop="productCode" :label="$t('purchaseOrder.label.productCode')">
                             <el-input
                                     v-model="productFrom.productCode"
                                     :placeholder="$t('purchaseOrder.placeholder.productCode')"
                                     clearable
                                     class="!w-[256px]"
                             />
                         </el-form-item>
                        <el-form-item prop="productName" :label="$t('purchaseOrder.label.productNameCopy')">
                            <el-input
                                    v-model="productFrom.productName"
                                    :placeholder="$t('purchaseOrder.placeholder.productName')"
                                    clearable
                                    class="!w-[256px]"
                            />
                        </el-form-item>
                        <el-form-item prop="productCategory" :label="$t('purchaseOrder.label.productCategory')">
<!--                            <el-cascader v-model="productFrom.productCategory"-->
<!--                                         :props="propsCategory"-->
<!--                                         @change="handleChange"-->
<!--                                         ref="cascaderRef"-->
<!--                                         filterable-->
<!--                                         :placeholder="$t('purchaseOrder.placeholder.productCategory')"-->
<!--                                         clearable />-->
                            <el-cascader v-model="productFrom.productCategory"
                                         :options="categoryList"
                                         :props="propsCategory"
                                         @change="handleChange"
                                         ref="cascaderRef"
                                         filterable
                                         class="!w-[256px]"
                                         :placeholder="$t('purchaseOrder.placeholder.productCategory')"
                                         clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="queryProductAll">
                                {{$t('common.search')}}
                            </el-button>
                            <el-button  @click="reset">
                                {{$t('common.reset')}}
                            </el-button>
                        </el-form-item>
            </el-form>
            <el-table
                    v-loading="loading"
                    :data="productTable"
                    highlight-current-row
                    stripe
                    @selection-change="handleSelectionSupplierChange"
            >
                <el-table-column type="selection" width="60" align="center"/>
                <el-table-column :label="$t('purchaseOrder.label.productName')" min-width="150" show-overflow-tooltip>
                    <template #default="scope">
                        <div class="product-div">
                            <div class="picture">
                                <img :src="scope.row.productImg" alt="">
                            </div>
                            <div class="product">
                                <div class="product-code">
                                    <span class="product-key">{{$t('purchaseOrder.label.productCode')}}：</span>
                                    <span class="product-value">{{scope.row.productCode}}</span>
                                </div>
                                <div class="product-name">{{scope.row.productName}}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
<!--                <el-table-column :label="$t('purchaseOrder.label.productSpecName')" prop="productSpecName" show-overflow-tooltip/>-->
                <el-table-column :label="$t('purchaseOrder.label.unitName')" prop="unitName" show-overflow-tooltip/>
                <el-table-column :label="$t('purchaseOrder.label.productCategory')" prop="productCategoryFullName" show-overflow-tooltip/>
            </el-table>
            <pagination
                    v-if="productTotal > 0"
                    v-model:total="productTotal"
                    v-model:page="productFrom.page"
                    v-model:limit="productFrom.limit"
                    @pagination="queryProductAll"
            />
            <div>{{ $t("purchaseOrder.message.selectNumTips")}}<span class="select-num">{{multipleSelectionSupplier.length}}</span></div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" :loading="submitLoading" @click="submitForm" :disabled="multipleSelectionSupplier.length==0">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-drawer>
</template>
<script setup lang="ts">
    // import CategoryAPI from "@/modules/pms/api/category";
    import PurchaseOrderAPI, { ProductAllPageQuery,ProductAllPageVO} from "@/modules/pms/api/purchaseOrder";
    import type { CascaderProps } from 'element-plus';
    import productCategoryAPI from "@/modules/pms/api/productCategory";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();

    const  supplierType= ref()
    const  ids = ref([])
    const  submitLoading= ref(false)
    const loading = ref(false);
    const productTotal = ref(0);
    const productFromRef = ref();
    const multipleSelectionSupplier = ref([]);
    const productTable = ref<ProductAllPageVO[]>()
    const cascaderRef = ref();
    // const propsCategory: CascaderProps = {
    //     lazy: true,
    //     checkStrictly : true,
    //     async lazyLoad(node, resolve) {
    //         const {level,data} = node
    //         let arr: any = []
    //         if (level == 0) {
    //             arr = await queryManagerCategoryList(null)
    //         } else{
    //             arr =  await queryManagerCategoryList(data.value)
    //         }
    //         const nodes = arr.map((item) => ({
    //             value: item.id,
    //             label: item.categoryName,
    //             parentId: item.parentId,
    //             leaf: level >= 2,
    //         }))
    //         console.log(nodes)
    //         resolve(nodes)
    //     },
    // }
    const categoryList = ref([])
    const propsCategory: CascaderProps = {
        checkStrictly : true,
        value: 'id',
        label: 'categoryName',
        children: 'children',
    }
    const enableRicingProducts = ref(false)


    let productFrom = reactive<ProductAllPageQuery>({
        page: 1,
        limit: 20,
    });

    function close() {
        emit("update:visible", false);
        // productFrom.keywords = '';
        productFrom.productCode = '';
        productFrom.productName = '';
        productFrom.productCategory = [];
        productFrom.firstCategoryId = '';
        productFrom.secondCategoryId = '';
        productFrom.thirdCategoryId = '';
        productFrom.page = 1;
        productFrom.limit = 20;
    }

    function reset() {
        // productFrom.keywords = '';
        productFrom.productCode = '';
        productFrom.productName = '';
        productFrom.productCategory = [];
        productFrom.firstCategoryId = '';
        productFrom.secondCategoryId = '';
        productFrom.thirdCategoryId = '';
        productFrom.page = 1;
        productFrom.limit = 20;
        queryProductAll()
    }

    function handleChange(){
        if(cascaderRef.value.getCheckedNodes()[0]){
            let valueArr = cascaderRef.value.getCheckedNodes()[0]?.pathValues
            productFrom.firstCategoryId = valueArr[0];
            productFrom.secondCategoryId = valueArr[1];
            productFrom.thirdCategoryId = valueArr[2];
            productFrom.productCategory = valueArr;
        }else {
            productFrom.productCategory = [];
            productFrom.firstCategoryId = '';
            productFrom.secondCategoryId = '';
            productFrom.thirdCategoryId = '';
        }
    }

    function handleSelectionSupplierChange(val) {
        multipleSelectionSupplier.value = val;
    }

    function submitForm() {
        submitLoading.value = true;
        const  collection = multipleSelectionSupplier.value
        // const removeField = (collection, field) => collection.map(item => {
        //     const { [field]: removed, ...rest } = item;
        //     return rest;
        // });
        // const newCollection = removeField(collection, 'productCode');
        close();
        submitLoading.value = false;
        emit("onSubmit",collection);
    }

    function queryProductAll(){
        loading.value = true;
        submitLoading.value=true
        let data = {
            ...productFrom
        }
        delete data.productCategory

        if (!enableRicingProducts.value){
            delete data.startExpectedDeliveryDate
        }

        PurchaseOrderAPI.queryProductAll(data)
            .then((data) => {
                productTable.value = data.records;
                productTotal.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
                submitLoading.value=false
            });
    }

  async  function setFormData(data) {
        productTable.value = data.productAllList;
        productTotal.value = data.productTotal;
        productFrom.chooseType = data.queryParams.chooseType;
        productFrom.orderType = data.queryParams.orderType;
        productFrom.warehouseId = data.queryParams.warehouseId;
        productFrom.warehouseCode = data.queryParams.warehouseCode;
        productFrom.startExpectedDeliveryDate = data.queryParams.startExpectedDeliveryDate;
        if(productFrom.orderType==1){
            productFrom.supplierId = data.queryParams.supplierId;
        }
        // productFrom.keywords = '';
        productFrom.productCode = '';
        productFrom.productName = '';
        productFrom.productCategory = [];
        productFrom.firstCategoryId = '';
        productFrom.secondCategoryId = '';
        productFrom.thirdCategoryId = '';
       await getSystemParameters()
      await queryProductAll()

    }


    /** 查询商品分类列表 */
    function queryManagerCategoryList(id?:any) {
       /* return new Promise((resolve, reject) => {
            let params={}
            if(id){
                params.id=id
            }
            CategoryAPI.queryManagerCategoryList(params).then((data) => {
                resolve(data);
            }).catch((error) => {
                reject(error);
            })
        });*/

        productCategoryAPI.queryCategoryTreeList({}).then((data: any) => {
            categoryList.value = data;
         })
    }
    /** 查询询价配置是否启用 */
  async  function getSystemParameters() {

      let res  =await  PurchaseOrderAPI.querySystemParameters('enable_pricing_products')
        enableRicingProducts.value=res

    }




    defineExpose({
        setFormData,
        queryManagerCategoryList
    });
</script>

<style scoped lang="scss">
    .add-product{
        .supplier-div{
            width: calc(100% - 170px);
        }
        .product-div{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .picture{
                margin-right: 16px;
                img{
                    width: 80px;
                    height: 80px;
                }
            }
            .product{
                font-family: PingFangSC, PingFang SC;
                font-style: normal;
                .product-code{
                    font-weight: 400;
                    font-size: 14px;
                    color: #90979E;
                }
                .product-name{
                    font-weight: 500;
                    font-size: 14px;
                    color: #52585F;
                }
            }
        }
        .select-num{
            margin-left: 8px;
            font-size: 18px;
            color:var(--el-color-primary)
        }

    }
</style>
<style lang="scss">
</style>
