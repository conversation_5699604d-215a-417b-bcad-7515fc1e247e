<template>
  <el-drawer
    v-model="props.visible"
    :title="props.title"
    :close-on-click-modal="false"
    size="850px"
    @close="close"
    class="add-product"
  >
    <div>
      <el-form ref="productFromRef" :model="productFrom" :inline="true">
        <!-- <div class="flex-center-but"> -->
        <!-- <div class="supplier-div"> -->
        <el-form-item
          prop="productCode"
          :label="$t('purchaseTasks.label.productCode')"
        >
          <el-input
            v-model="productFrom.productCode"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            class="!w-[256px]"
          />
        </el-form-item>
        <el-form-item
          prop="productName"
          :label="$t('purchaseTasks.label.productNameCopy')"
        >
          <el-input
            v-model="productFrom.productName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            class="!w-[256px]"
          />
        </el-form-item>
        <el-form-item
          prop="productCategory"
          :label="$t('purchaseTasks.label.goodsClassification')"
        >
          <el-cascader
            v-model="productFrom.productCategory"
            :props="propsCategory"
            @change="handleChange"
            ref="cascaderRef"
            filterable
            :placeholder="$t('purchase.placeholder.productCategory')"
            clearable
          />
        </el-form-item>
        <!-- </div> -->
        <!-- <div> -->
        <el-form-item>
          <el-button type="primary" @click="queryProductAll">
            {{ $t("common.search") }}
          </el-button>
          <el-button @click="reset">
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
        <!-- </div> -->
        <!-- </div> -->
      </el-form>
      <el-table
        v-loading="loading"
        :data="productTable"
        highlight-current-row
        stripe
        @selection-change="handleSelectionSupplierChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column
          :label="$t('purchaseTasks.label.productName')"
          min-width="100"
        >
          <template #default="scope">
            <!-- <div class="product-div">
                <div class="picture">
                  <img :src="scope.row.productImg" alt="" />
                </div>
                <div class="product">
                  <div class="product-code">
                    商品编码：{{ scope.row.productCode }}
                  </div>
                  <div class="product-name">{{ scope.row.productName }}</div>
                </div>
              </div> -->
            <div class="product-div">
              <div class="picture">
                <img :src="scope.row.productImg" alt="" />
              </div>
              <div class="product">
                <div>
                  <span class="product-key">
                    {{ $t("purchaseTasks.label.productCode") }}：
                  </span>
                  <span class="product-value">{{ scope.row.productCode }}</span>
                </div>
                <div class="product-name">{{ scope.row.productName }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseTasks.label.unitCopy')"
          prop="unitName"
        />
        <el-table-column
          :label="$t('purchaseTasks.label.goodsClassification')"
          prop="productCategoryFullName"
          min-width="100"
        />
      </el-table>
      <pagination
        v-if="productTotal > 0"
        v-model:total="productTotal"
        v-model:page="productFrom.page"
        v-model:limit="productFrom.limit"
        @pagination="queryProductAll"
      />
      <div>
        {{ $t("purchaseTasks.message.selectNumTips") }}
        <span class="select-num">{{ multipleSelectionSupplier.length }}</span>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submitForm"
          :disabled="multipleSelectionSupplier.length == 0"
        >
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import CategoryAPI from "@/modules/pms/api/category";
import PurchaseOrderAPI, {
  ProductAllPageQuery,
  ProductAllPageVO,
} from "@/modules/pms/api/purchaseOrder";
import type { CascaderProps } from "element-plus";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible", "onSubmit"]);
const { t } = useI18n();

const supplierType = ref();
const ids = ref([]);
const submitLoading = ref(false);
const loading = ref(false);
const productTotal = ref(0);
const productFromRef = ref();
const multipleSelectionSupplier = ref([]);
const productTable = ref<ProductAllPageVO[]>();
const cascaderRef = ref();
const propsCategory: CascaderProps = {
  lazy: true,
  checkStrictly: true,
  async lazyLoad(node, resolve) {
    const { level, data } = node;
    let arr: any = [];
    if (level == 0) {
      arr = await queryManagerCategoryList(null);
    } else {
      arr = await queryManagerCategoryList(data.value);
    }
    const nodes = arr.map((item: any) => ({
      value: item.id,
      label: item.categoryName,
      parentId: item.parentId,
      leaf: level >= 2,
    }));
    console.log(nodes);
    resolve(nodes);
  },
};
let productFrom = reactive<ProductAllPageQuery>({
  page: 1,
  limit: 20,
});

function close() {
  emit("update:visible", false);
  productFrom.productName = "";
  productFrom.productCode = "";
  productFrom.productCategory = [];
  productFrom.firstCategoryId = "";
  productFrom.secondCategoryId = "";
  productFrom.thirdCategoryId = "";
  productFrom.page = 1;
  productFrom.limit = 20;
}

function reset() {
  // productFromRef.value.resetFields();
  productFrom.productName = "";
  productFrom.productCode = "";
  productFrom.productCategory = [];
  productFrom.firstCategoryId = "";
  productFrom.secondCategoryId = "";
  productFrom.thirdCategoryId = "";
  productFrom.page = 1;
  productFrom.limit = 20;
  queryProductAll();
}

function handleChange() {
  if (cascaderRef.value.getCheckedNodes()) {
    let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues;
    productFrom.firstCategoryId = valueArr[0];
    productFrom.secondCategoryId = valueArr[1];
    productFrom.thirdCategoryId = valueArr[2];
    productFrom.productCategory = valueArr;
  }
}

function handleSelectionSupplierChange(val) {
  multipleSelectionSupplier.value = val;
}

function submitForm() {
  submitLoading.value = true;
  const collection = multipleSelectionSupplier.value;
  // const removeField = (collection, field) => collection.map(item => {
  //     const { [field]: removed, ...rest } = item;
  //     return rest;
  // });
  // const newCollection = removeField(collection, 'productCode');
  close();
  submitLoading.value = false;
  emit("onSubmit", collection);
}

function queryProductAll() {
  loading.value = true;
  submitLoading.value = true;
  let data = {
    ...productFrom,
  };
  delete data.productCategory;
  PurchaseOrderAPI.queryProductAll(data)
    .then((data: any) => {
      productTable.value = data.records;
      productTotal.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
      submitLoading.value = false;
    });
}

function setFormData(data: any) {
  productTable.value = data.productAllList;
  productTotal.value = parseInt(data.productTotal);
  productFrom.chooseType = data.queryParams.chooseType;
  productFrom.warehouseId = data.queryParams.warehouseId;
  productFrom.startExpectedDeliveryDate =
    data.queryParams.startExpectedDeliveryDate;
  productFrom.productName = "";
  productFrom.productCode = "";
  productFrom.productCategory = [];
  productFrom.firstCategoryId = "";
  productFrom.secondCategoryId = "";
  productFrom.thirdCategoryId = "";
  multipleSelectionSupplier.value = [];
}

/** 查询商品分类列表 */
function queryManagerCategoryList(id?: any) {
  return new Promise((resolve, reject) => {
    let params = {};
    if (id) {
      params.id = id;
    }
    CategoryAPI.queryManagerCategoryList(params)
      .then((data) => {
        resolve(data);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

defineExpose({
  setFormData,
  queryManagerCategoryList,
});
</script>

<style scoped lang="scss">
.add-product {
  .supplier-div {
    width: calc(100% - 170px);
  }
  .product-div {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .picture {
      margin-right: 16px;
      img {
        width: 80px;
        height: 80px;
      }
    }
    .product {
      font-family:
        PingFangSC,
        PingFang SC;
      font-style: normal;
      .product-code {
        font-weight: 400;
        font-size: 14px;
        color: #90979e;
      }
      .product-name {
        font-weight: 500;
        font-size: 14px;
        color: #52585f;
      }
    }
  }
  .select-num {
    margin-left: 8px;
    font-size: 18px;
    color: var(--el-color-primary);
  }
}
</style>
<style lang="scss"></style>
