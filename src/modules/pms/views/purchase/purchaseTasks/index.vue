<template>
  <div class="app-container purchaseTasks">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item prop="dateRange">
          <el-select
            v-model="queryParams.dateType"
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[180px]"
          >
            <el-option
              v-for="item in dateTypeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
          <el-date-picker
            :editable="false"
            class="!w-[370px]"
            v-model="dateRange"
            type="daterange"
            :range-separator="$t('purchaseTasks.label.to')"
            :start-placeholder="$t('purchaseTasks.label.startTime')"
            :end-placeholder="$t('purchaseTasks.label.endTime')"
            value-format="YYYY-MM-DD"
            :placeholder="$t('common.placeholder.selectTips')"
          />
          <span
            class="ml16px mr14px cursor-pointer"
            style="color: var(--el-color-primary)"
            @click="changeDateRange(1)"
          >
            {{ $t("purchaseTasks.label.today") }}
          </span>
          <span
            class="mr14px cursor-pointer"
            style="color: var(--el-color-primary)"
            @click="changeDateRange(2)"
          >
            {{ $t("purchaseTasks.label.yesterday") }}
          </span>
          <span
            class="mr16px cursor-pointer"
            style="color: var(--el-color-primary)"
            @click="changeDateRange(3)"
          >
            {{ $t("purchaseTasks.label.weekday") }}
          </span>
        </el-form-item>
        <!-- 期望收货日期 -->
        <!-- <el-form-item :label="$t('purchaseTasks.label.expectedDeliveryDate')">
          <el-date-picker
            v-model="expectedDeliveryDateRange"
            type="daterange"
            value-format="YYYY-MM-DD"
            :range-separator="$t('purchaseTasks.label.to')"
            :start-placeholder="$t('purchaseTasks.label.startTime')"
            :end-placeholder="$t('purchaseTasks.label.endTime')"
          />
        </el-form-item> -->
        <!-- 创建日期 -->
        <!-- <el-form-item :label="$t('purchaseTasks.label.createTime')">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            value-format="YYYY-MM-DD"
            :range-separator="$t('purchaseTasks.label.to')"
            :start-placeholder="$t('purchaseTasks.label.startTime')"
            :end-placeholder="$t('purchaseTasks.label.endTime')"
          />
        </el-form-item> -->
        <!-- 供应商 -->
        <el-form-item
          prop="supplierId"
          :label="$t('purchaseTasks.label.supplier')"
        >
          <el-select
            v-model="queryParams.supplierId"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[260px]"
          >
            <el-option
              v-for="item in supplierList"
              :key="item.supplierId"
              :label="item.supplierName"
              :value="item.supplierId"
            />
          </el-select>
        </el-form-item>
        <!-- 采购员 -->
        <el-form-item
          prop="purchaseUserId"
          :label="$t('purchaseTasks.label.purchaser')"
        >
          <el-select
            v-model="queryParams.purchaseUserId"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[260px]"
          >
            <el-option
              v-for="item in purchaserList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <!-- 状态 -->
        <el-form-item
          prop="taskStatus"
          :label="$t('purchaseTasks.label.status')"
        >
          <el-select
            v-model="queryParams.taskStatus"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[260px]"
          >
            <el-option
              v-for="item in statusList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            />
          </el-select>
        </el-form-item>
        <!-- 商品分类 -->
        <el-form-item :label="$t('purchaseTasks.label.goodsClassification')">
          <el-cascader
            v-model="categoryIds"
            :props="propsCategory"
            @change="handleChange"
            ref="cascaderRef"
            filterable
            :placeholder="$t('purchase.placeholder.productCategory')"
            clearable
          />
          <!-- <el-cascader
            v-model="categoryIds"
            :options="classificationList"
            clearable
            :props="{
              value: 'id',
              label: 'categoryName',
              children: 'children',
            }"
          /> -->
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleQuery"
            v-hasPerm="['pms:purchase:purchaseTask:search']"
          >
            <i-ep-search />
            {{ $t("common.search") }}
          </el-button>
          <el-button
            @click="handleResetQuery"
            v-hasPerm="['pms:purchase:purchaseTask:reset']"
          >
            <i-ep-refresh />
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          type="primary"
          @click="handleAddDialog()"
          v-hasPerm="['pms:purchase:purchaseTask:add']"
        >
          {{ $t("purchaseTasks.button.addPurchaseTask") }}
        </el-button>
        <el-button
          @click="handleBatchCreateOrder()"
          v-hasPerm="['pms:purchase:purchaseTask:generatePurchaseOrder']"
        >
          {{ $t("purchaseTasks.button.generatePurchaseOrder") }}
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="purchaseTaskList"
        highlight-current-row
        stripe
        @selection-change="handleSelectionChange"
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          :label="$t('purchaseTasks.label.purchaseGoods')"
          min-width="200"
        >
          <template #default="scope">
            <div class="product-div">
              <div class="product">
                <div>
<!--                  <span class="product-key">
                    {{ $t("purchaseTasks.label.productCode") }}：
                  </span>-->
                  <span class="product-value">{{ scope.row.productCode }}</span>
                </div>
                <div class="product-name">{{ scope.row.productName }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseTasks.label.procurementReqCode')"
          prop="reqCode"
          min-width="150"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          :label="$t('purchaseTasks.label.procurementReqCode')"
          prop="reqCode"
          min-width="150"
        >
          <template #default="scope">
            <div class="ellipsis">
              {{ scope.row.reqCode ? scope.row.reqCode : "-" }}
            </div>
          </template>
        </el-table-column> -->
        <el-table-column
          :label="$t('purchaseTasks.label.warehouseName')"
          prop="warehouseName"
          min-width="100"
        />
        <!-- 分类 -->
        <el-table-column
          :label="$t('purchaseTasks.label.classification')"
          prop="productCategoryFullName"
          width="150"
        />
        <!-- 供应商 -->
        <el-table-column
          :label="$t('purchaseTasks.label.supplier')"
          prop="supplierName"
          min-width="180"
        >
          <template #default="scope">
            <el-select
              v-if="
                scope.row.taskStatus != 1 &&
                scope.row.completedPricingSupList &&
                scope.row.completedPricingSupList.length != 0
              "
              v-model="scope.row.supplierId"
              :placeholder="$t('common.placeholder.selectTips')"
              class="!w-[140px]"
              @change="
                (e: any) => purchaseTasksChange(e, scope.row, scope.$index)
              "
            >
              <el-option
                v-for="item in scope.row.completedPricingSupList"
                :key="item.supplierId"
                :label="item.supplierName"
                :value="item.supplierId"
              />
            </el-select>

            <el-select
              v-else
              v-model="scope.row.supplierName"
              :placeholder="$t('common.placeholder.selectTips')"
              class="!w-[140px]"
              @change="(e: any) => purchaseTasksChange(e, scope.row)"
              :disabled="scope.row.taskStatus == 1"
            >
              <el-option
                v-for="item in scope.row.completedPricingSupList"
                :key="item.supplierName"
                :label="item.supplierName"
                :value="item.supplierName"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseTasks.label.purchaser')"
          prop="purchaseUserName"
          min-width="100"
        />
        <el-table-column
          :label="$t('purchaseTasks.label.purchaseQuantity')"
          prop="purchaseCount"
          min-width="100"
          align="right"
        />
        <!-- 采购单位 -->
        <el-table-column
          :label="$t('purchaseTasks.label.unitCopy')"
          prop="unitName"
          min-width="100"
        />
        <el-table-column
          :label="$t('purchaseTasks.label.purchaseUnitPrice')"
          prop="purchasePrice"
          width="100"
          align="right"
        >
          <template #default="scope">
            <span v-if="scope.row.purchasePrice">
              <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
              <span v-if="scope.row.currencyCode == 'USD'">$</span>
            </span>

            {{ scope.row.purchasePrice ? scope.row.purchasePrice : "-" }}
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('purchaseTasks.label.purchaseAmount')"
          prop="purchaseAmount"
          min-width="100"
          align="right"
        >
          <template #default="scope">
            <span v-if="scope.row.purchaseAmount">
              <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
              <span v-if="scope.row.currencyCode == 'USD'">$</span>
            </span>

            {{ scope.row.purchaseAmount ? scope.row.purchaseAmount : "-" }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseTasks.label.createTime')"
          prop="createTime"
          min-width="120"
        >
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.createTime, "date") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseTasks.label.expectedDeliveryDate')"
          prop="expectedDeliveryDate"
          min-width="120"
        >
          <template #default="scope">
            <span>
              {{ parseDateTime(scope.row.expectedDeliveryDate, "date") }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseTasks.label.creator')"
          prop="createUserName"
          width="100"
        >
          <template #default="scope">
            <span v-if="scope.row.taskSource == 1">
              {{ scope.row.createUserName }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchaseTasks.label.status')"
          prop="taskStatus"
          width="100"
        >
          <template #default="scope">
            <!-- <el-button
              :type="scope.row.taskStatus == 1 ? 'primary' : 'info'"
              size="small"
              plain
            >
              {{ filterSaskStatus(scope.row.taskStatus) }}
            </el-button> -->
            <div class="purchase">
              <div
                class="purchase-status purchase-status-color1"
                v-if="scope.row.taskStatus === 1"
                type="success"
              >
                {{ $t("purchaseTasks.label.generated") }}
              </div>
              <div
                class="purchase-status purchase-status-color3"
                v-else
                type="info"
              >
                {{ $t("purchaseTasks.label.notGenerated") }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('common.handle')" width="150">
          <template #default="scope">
            <el-button
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
              v-if="scope.row.taskStatus != 1 && scope.row.taskSource == 1"
              v-hasPerm="['pms:purchase:purchaseTask:delete']"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "PurchaseTasks",
  inheritAttrs: false,
});

import PurchaerTasksAPI, {
  PurchaseTasksPageQuery,
} from "@/modules/pms/api/purchaseTasks";
import supplierAPI from "@/modules/pms/api/supplier";
import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
import productCategoryAPI from "@/modules/pms/api/productCategory";
import { convertToTimestamp, parseDateTime } from "@/core/utils/index.js";
import moment from "moment";
import type { CascaderProps } from "element-plus";
import CategoryAPI from "@/modules/pms/api/category";
const router = useRouter();
const { t } = useI18n();
const queryFormRef = ref(ElForm);
const statusList = ref([
  {
    statusId: 1,
    statusName: t("purchaseTasks.label.generated"),
  },
  {
    statusId: 0,
    statusName: t("purchaseTasks.label.notGenerated"),
  },
]);
const supplierList = ref([]);
const purchaserList = ref([]);
const classificationList = ref([]);
const categoryIds = ref([]);
const expectedDeliveryDateRange = ref([]);
const dateRange = ref([]);
const loading = ref(false);
const total = ref(0);
const multipleSelection = ref([]);
const cascaderRef = ref();
const queryParams = reactive<PurchaseTasksPageQuery>({
  page: 1,
  limit: 20,
  dateType: 1,
});
const dateTypeList = ref([
  {
    key: 1,
    value: t("purchaseTasks.label.expectedDeliveryDate"),
  },
  {
    key: 2,
    value: t("purchaseTasks.label.createTime"),
  },
]);

// 表格数据
const purchaseTaskList = ref([]);

const propsCategory: CascaderProps = {
  lazy: true,
  checkStrictly: true,
  async lazyLoad(node, resolve) {
    const { level, data } = node;
    let arr: any = [];
    if (level == 0) {
      arr = await queryManagerCategoryList(null);
    } else {
      arr = await queryManagerCategoryList(data.value);
    }
    const nodes = arr.map((item: any) => ({
      value: item.id,
      label: item.categoryName,
      parentId: item.parentId,
      leaf: level >= 2,
    }));
    console.log(nodes);
    resolve(nodes);
  },
};

/** 查询商品分类列表 */
function queryManagerCategoryList(id?: any) {
  return new Promise((resolve, reject) => {
    let params = {};
    if (id) {
      params.id = id;
    }
    CategoryAPI.queryManagerCategoryList(params)
      .then((data) => {
        resolve(data);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

function handleChange() {
  if (cascaderRef.value.getCheckedNodes()) {
    let valueArr = cascaderRef.value.getCheckedNodes()[0].pathValues;
    // productFrom.firstCategoryId = valueArr[0];
    // productFrom.secondCategoryId = valueArr[1];
    // productFrom.thirdCategoryId = valueArr[2];
    categoryIds.value = valueArr;
  }
}

function handleSelectionChange(val: any) {
  multipleSelection.value = val;
}

/** 时间转换 */
function changeDateRange(val: any) {
  if (val === 1) {
    // var date = moment(new Date()).format('YYYY-MM-DD')
    let date1: any = moment().subtract("days", 0).format("YYYY-MM-DD");
    dateRange.value = [date1, date1];
  } else if (val === 2) {
    // var date = moment(new Date().getTime() - 3600 * 24 * 1000).format('YYYY-MM-DD')
    let date1: any = moment().subtract("days", 1).format("YYYY-MM-DD");
    dateRange.value = [date1, date1];
  } else if (val === 3) {
    // var endDate = moment(new Date().getTime() - 3600 * 24 * 1000 * 6).format('YYYY-MM-DD')
    let endDate1: any = moment(new Date()).format("YYYY-MM-DD");
    let startDate: any = moment().subtract("days", 6).format("YYYY-MM-DD");
    dateRange.value = [startDate, endDate1];
  }
}

// 获取供应商列表
function getSupplierList() {
  supplierAPI
    .getSupplierListAll()
    .then((data: any) => {
      supplierList.value = data;
    })
    .finally(() => {});
}

// 获取采购员列表
function getPurchaserList() {
  PurchasePersonnelAPI.getPerchasePersonnelList()
    .then((data: any) => {
      purchaserList.value = data;
    })
    .finally(() => {});
}

// 获取商品分类tree
function getCategoryList() {
  productCategoryAPI
    .queryCategoryTreeList({})
    .then((data: any) => {
      classificationList.value = data;
    })
    .finally(() => {});
}

/** 查询 */
function handleQuery() {
  loading.value = true;
  let params: any = {
    ...queryParams,
  };
  if (dateRange.value && dateRange.value.length > 0) {
    if (queryParams.dateType != 1) {
      params.startCreateTime = convertToTimestamp(
        dateRange.value[0] + " 00:00:00"
      );
      params.endCreateTime = convertToTimestamp(
        dateRange.value[1] + " 23:59:59"
      );
    } else {
      params.startExpectedDeliveryDate = convertToTimestamp(
        dateRange.value[0] + " 00:00:00"
      );
      params.endExpectedDeliveryDate = convertToTimestamp(
        dateRange.value[1] + " 23:59:59"
      );
    }

    // params.startCreateTime = convertToTimestamp(
    //   dateRange.value[0] + " 00:00:00"
    // );
    // params.endCreateTime = convertToTimestamp(dateRange.value[1] + " 23:59:59");
  }
  // if (
  //   expectedDeliveryDateRange.value &&
  //   expectedDeliveryDateRange.value.length > 0
  // ) {
  //   params.startExpectedDeliveryDate = convertToTimestamp(
  //     expectedDeliveryDateRange.value[0] + " 00:00:00"
  //   );
  //   params.endExpectedDeliveryDate = convertToTimestamp(
  //     expectedDeliveryDateRange.value[1] + " 23:59:59"
  //   );
  // }
  if (categoryIds.value && categoryIds.value.length > 0) {
    params.firstCategoryId = categoryIds.value[0] ? categoryIds.value[0] : "";
    params.secondCategoryId = categoryIds.value[1] ? categoryIds.value[1] : "";
    params.thirdCategoryId = categoryIds.value[2] ? categoryIds.value[2] : "";
  }
  PurchaerTasksAPI.getPurchaerTaskPage(params)
    .then((data: any) => {
      // purchaseTaskList.value = data.records;
      if (data.records && data.records.length > 0) {
        purchaseTaskList.value = data.records.map((item: any) => {
          if (
            item.completedPricingSupList &&
            item.completedPricingSupList.length > 0
          ) {
            let canSelect = item.completedPricingSupList.some((val: any) => {
              return item.supplierId == val.supplierId;
            });
            if (!canSelect) {
              item.supplierId = "";
            }
          }
          return { ...item };
        });
        total.value = parseInt(data.total);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  expectedDeliveryDateRange.value = [];
  dateRange.value = [];
  categoryIds.value = [];
  handleQuery();
}

function filterSaskStatus(status: any) {
  const taskState: any = {
    0: t("purchaseTasks.label.notGenerated"),
    1: t("purchaseTasks.label.generated"),
  };
  return taskState[status];
}

// 修改供应商(修改)
function purchaseTasksChange(val: any, row: any, index?: any) {
  let params = {
    ...row,
  };
  if (
    params.completedPricingSupList &&
    params.completedPricingSupList.length > 0
  ) {
    params.completedPricingSupList.forEach((item: any) => {
      if (item.supplierId == val) {
        params.purchasePrice = item.purchasePrice;
        params.supplierName = item.supplierName;
        params.purchaseUserId = item.purchaseUserId;
        params.purchaseUserName = item.purchaseUserName;
        params.currencyCode = item.currency;
        params.currencyName = item.currencyName;
      }
    });
  }
  params.supplierId = val;
  PurchaerTasksAPI.editPurchaseTask(params)
    .then((data: any) => {
      ElMessage.success(t("purchaseTasks.message.editSucess"));
      handleQuery();
    })
    .finally(() => {});
}

function handleAddDialog() {
  router.push({
    path: "/pms/purchase/addPurchaseTasks",
  });
}

function handleBatchCreateOrder() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(t("purchaseTasks.message.selectPurchaseTask"));
    return false;
  }
  let flag = multipleSelection.value.some(
    (item) => item.taskStatus == 1 || !item.supplierId
  );
  if (flag) {
    return ElMessage.error(t("purchaseTasks.message.batchCreateOrderArrTips"));
  }

  ElMessageBox.confirm(
    t("purchaseTasks.message.batchCreateOrderTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      let ids: any = [];
      multipleSelection.value.forEach((item: any) => {
        ids.push(item.id);
      });
      PurchaerTasksAPI.batchCreateOrder({ ids: ids })
        .then((data: any) => {
          ElMessage.success(t("purchaseTasks.message.batchCreateOrderSucess"));
          handleQuery();
        })
        .finally(() => {});
    },
    () => {
      ElMessage.info(t("purchaseTasks.message.batchCreateOrderConcel"));
    }
  );
}

/** 删除任务 */
function handleDelete(id?: string) {
  ElMessageBox.confirm(
    t("purchaseTasks.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      let params: any = {
        id: id,
      };
      PurchaerTasksAPI.delete(params)
        .then(() => {
          ElMessage.success(t("purchaseTasks.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info(t("purchaseTasks.message.deleteConcel"));
    }
  );
}

onActivated(() => {
  getSupplierList();
  getPurchaserList();
  getCategoryList();
  handleQuery();
});
</script>
<style scoped lang="scss">
.purchaseTasks {
  :deep(.select) {
    width: 130px !important;
    .el-input {
      width: 130px !important;
    }
  }
  // .ellipsis {
  //   display: -webkit-box;
  //   -webkit-box-orient: vertical;
  //   -webkit-line-clamp: 3;
  //   overflow: hidden;
  //   text-overflow: ellipsis;
  //   cursor: pointer;
  // }

  // .ellipsis:hover {
  //   display: block; /* 或者根据需要设置为其他非盒模型值 */
  //   overflow: visible;
  //   white-space: normal; /* 允许换行 */
  //   height: auto; /* 根据内容自动调整高度 */
  // }
  // :deep(.el-table .cell) {
  //   overflow: unset !important;
  // }
}
</style>
