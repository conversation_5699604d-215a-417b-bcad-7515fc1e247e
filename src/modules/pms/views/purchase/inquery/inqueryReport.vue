<script setup lang="ts">
import InqueryAPI from "@pms/api/inquery";
import { useDetail } from "./composables/useDetail";
import { parseTime } from "@/core/utils/index";
import { useTagsViewStore } from "@/core/store";
import {useCommon} from '@pms/composables/common'
const { currencyFilter } = useCommon();
const { inqueryDetail, getInqueryDetail } = useDetail();
import { useI18n } from "vue-i18n";
const { t } = useI18n();
interface InquiryDetailVO {
  productId: number;
  productCode: number;
  productName: string;
  brandName: string;
  unitName: string;
  productImg: string;
  firstCategoryId: number;
  secondCategoryId: number;
  thirdCategoryId: number;
  productCategoryFullName: string;
  defaultSupplierId: number;
  defaultSupplierName: string;
  remark: string;
  lastPurchaseAmountCurrency: string;
  lastPurchaseAmount: number;
  inquiryProductSupplierListVOS: Array<{
    supplierId: number;
    supplierName: string;
    price?: number;
    selected?: boolean;
  }>;
}

interface InquiryReport {
  inquiryNo: string;
  title: string;
  status: string;
  deliveryStartDate: string;
  deliveryEndDate: string;
  inquiryType: string;
  inquiryDetailVOS: InquiryDetailVO[];
}

const searchKeyword = ref("");
const inquiryData = ref<InquiryReport>({
  inquiryNo: "",
  title: "",
  status: "",
  deliveryStartDate: "",
  deliveryEndDate: "",
  inquiryType: "",
  inquiryDetailVOS: [],
});

const loading = ref(false);

const searchProducts = () => {
  // Implement product search logic
  if (!searchKeyword.value) {
    return inqueryDetail.value.inquiryDetailVOS || [];
  }
  return (inqueryDetail.value.inquiryDetailVOS || []).filter((product) =>
    product.productName
      .toLowerCase()
      .includes(searchKeyword.value.toLowerCase())
  );
};

const selectSupplier = (product: InquiryDetailVO, supplierName: string) => {
  // Implement supplier selection logic
  console.log(
    "Selected supplier:",
    supplierName,
    "for product:",
    product.productName
  );
};

const submitQuotation = () => {
  ElMessageBox.confirm(t('purchaseInquiry.inqueryReport.message.confirmSubmit'), t('purchaseInquiry.inqueryReport.message.tips'), {
    confirmButtonText: t('purchaseInquiry.inqueryReport.message.confirm'),
    cancelButtonText: t('purchaseInquiry.inqueryReport.message.cancel'),
    type: "warning",
  })
    .then(() => {
      loading.value = true;
      const supplierList = inqueryDetail.value.inquiryDetailVOS.flatMap(
        (detail) =>
          detail.inquiryProductSupplierListVOS.map((supplier) => ({
            inquiryDetailId: supplier.inquiryDetailId,
            inquiryAmount: supplier.inquiryAmount,
          }))
      );

      const data = {
        inquiryId: route.query.inquiryId,
        type: 1, // 0-暂存 1-确认提交
        inquiryPurchaseListRequestDTOList: supplierList,
      };

      InqueryAPI.fillQuotation(data)
        .then((res) => {
          console.log(res);
          ElMessage.success(t('purchaseInquiry.inqueryReport.message.submitSuccess'));
          cancelQuotation();
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(() => {
      // 用户取消操作
      ElMessage.info(t('purchaseInquiry.inqueryReport.message.cancelSubmit'));
    });
};

const route = useRoute();
const saveQuotation = () => {
  loading.value = true;
  const supplierList = inqueryDetail.value.inquiryDetailVOS.flatMap((detail) =>
    detail.inquiryProductSupplierListVOS.map((supplier) => ({
      inquiryDetailId: supplier.inquiryDetailId, // Assuming this is the correct ID to use
      inquiryAmount: supplier.inquiryAmount,
    }))
  );

  const data = {
    inquiryId: route.query.inquiryId,
    type: 0, // 0-暂存 1-确认提交
    inquiryPurchaseListRequestDTOList: supplierList,
  };

  InqueryAPI.fillQuotation(data)
    .then((res) => {
      cancelQuotation();
    })
    .finally(() => {
      loading.value = false;
    });
};
const parseTimeHandle = (time: string, type: string) => {
  return parseTime(time, type);
};

const statusMapClass = {
  // 0->询价中，1->已确定，2->已完成，3->已关闭
  0: "unwokring",
  1: "executing",
  2: "cancelled",
  3: "cancelled",
};


const router = useRouter();
const cancelQuotation = () => {
  // Implement cancel logic
  /*router.push({
    path: "/pms/inquery/inquery",
  });*/
  navigateAndCloseCurrentTab("/pms/inquery/inquery");
};

const tagsViewStore = useTagsViewStore();
// const route = useRoute();

const navigateAndCloseCurrentTab = async (path: string) => {
  // 先删除当前页面的标签
  await tagsViewStore.delView(route);

  // 等待标签删除完成后再进行路由跳转
  nextTick(() => {
    router.push(path);
  });
};

// 价格输入处理
const handlePriceInput = (value: string, supplier: any) => {
  if (!value) return;

  // 移除非数字和小数点以外的字符
  let newValue = value.replace(/[^\d.]/g, "");

  // 确保只有一个小数点
  const dotIndex = newValue.indexOf(".");
  if (dotIndex !== -1) {
    newValue =
      newValue.slice(0, dotIndex + 1) +
      newValue.slice(dotIndex + 1).replace(/\./g, "");
  }

  // 限制小数位数为2位
  if (dotIndex !== -1) {
    const decimal = newValue.slice(dotIndex + 1);
    if (decimal.length > 2) {
      newValue = newValue.slice(0, dotIndex + 3);
    }
  }

  // 移除开头的0（当不是小数时）
  if (newValue.length > 1 && newValue[0] === "0" && newValue[1] !== ".") {
    newValue = newValue.slice(1);
  }

  // 更新值
  supplier.inquiryAmount = newValue;
};

// 失去焦点时的处理
const handlePriceBlur = (supplier: any) => {
  if (!supplier.inquiryAmount) {
    supplier.inquiryAmount = "";
    return;
  }

  // 转换为数字进行验证
  let numValue = parseFloat(supplier.inquiryAmount);
  
  // 如果是负数，设为0
  if (numValue < 0) {
    supplier.inquiryAmount = "0";
    return;
  }

  // 判断是否为小数
  const isDecimal = supplier.inquiryAmount.includes('.');
  if (isDecimal) {
    // 只有小数才保留两位
    supplier.inquiryAmount = numValue.toFixed(2);
  } else {
    // 整数保持原样
    supplier.inquiryAmount = String(parseInt(supplier.inquiryAmount));
  }
};

// 商品显示状态Map
const productVisibilityMap = ref(new Map<string | number, boolean>());

// 搜索处理函数
const handleSearch = () => {
  const keyword = searchKeyword.value.trim().toLowerCase();

  // 重置所有商品的显示状态
  inqueryDetail.value.inquiryDetailVOS.forEach((product) => {
    const isVisible =
      !keyword || product.productName.toLowerCase().includes(keyword);
    productVisibilityMap.value.set(product.productId, isVisible);
  });
};

// 获取商品的显示状态
const getProductVisible = (product: any): boolean => {
  return productVisibilityMap.value.get(product.productId) ?? true;
};

// 计算是否有可见的商品
const hasVisibleProducts = computed(() => {
  return inqueryDetail.value.inquiryDetailVOS.some((product) =>
    getProductVisible(product)
  );
});

// 监听搜索关键词变化
/*watch(searchKeyword, (newVal) => {
  handleSearch();
});*/

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = "";
  handleSearch();
};
</script>

<template>
  <div class="inquiry-detail" v-loading="loading">
    <el-card>
      <!-- 头部 -->
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="cancelQuotation"
        />
        <span class="code" @click="cancelQuotation">
          {{ t('purchaseInquiry.inqueryReport.title') }}：{{ inqueryDetail.inquiryCode }}
        </span>
        <span
          class="contract status"
          :class="statusMapClass[inqueryDetail.inquiryStatus]"
        >
          {{ inqueryDetail.inquiryStatusStr }}
        </span>
      </div>

      <!-- 基本信息区域 -->
      <section class="basic-info">
        <div class="section-title card-title mb-20px">{{ t('purchaseInquiry.inqueryReport.basicInfo') }}</div>
        <div class="info-grid">
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryReport.deliveryPeriod') }}</label>
            <span class="card-form-text">
              {{ parseTimeHandle(new Date(inqueryDetail.startDeliveryDate), "{y}-{m}-{d}") }}至{{ parseTimeHandle(new Date(inqueryDetail.endDeliveryDate), "{y}-{m}-{d}") }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryReport.inquiryType') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.inquiryTypeStr || '--' }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryReport.name') }}</label>
            <span class="card-form-text">{{ inqueryDetail.title || '--' }}</span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryReport.warehouse') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.warehouseName || '--' }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryReport.createTime') }}</label>
            <span class="card-form-text">
              {{
                parseTimeHandle(
                  new Date(inqueryDetail.submitTime),
                  "{y}-{m}-{d} {h}:{i}:{s}"
                )
              }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryReport.inquirySupplier') }}</label>
            <span class="card-form-text">{{ inqueryDetail.suppliers || '--' }}</span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryReport.creator') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.createUserName || '--' }}
            </span>
          </div>
          <!-- 其他基本信息项 -->
        </div>
        <div class="card-bottomm-border mt-30px mb-30px"></div>
      </section>

      <!-- 询价商品列表 -->
      <section class="product-list">
        <div class="section-title card-title mb-20px">{{ t('purchaseInquiry.inqueryReport.inquiryProducts') }}</div>
        <div class="search-bar">
          <el-input
            class="mr-12px"
            style="width: 500px"
            v-model="searchKeyword"
            :placeholder="t('purchaseInquiry.inqueryReport.searchPlaceholder')"
            clearable
            @clear="handleSearch"
          />
          <el-button type="primary" @click="handleSearch">{{ t('purchaseInquiry.inqueryReport.search') }}</el-button>
        </div>

        <!-- 商品项模板 -->
        <div
          class="product-item"
          v-for="product in inqueryDetail.inquiryDetailVOS"
          :key="product.productId"
          v-show="getProductVisible(product)"
        >
          <div class="product-info">
            <section class="flex w-100%">
              <img :src="product.productImg" class="product-image mr-12px" />
              <div class="product-details">
                <div class="product-name">{{ product.productName || '--' }}</div>
                <div class="flex w-100% gap-16px">
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryReport.purchaseUnit') }}&nbsp;</span>
                    <span class="form-text">{{ product.unitName || '--' }}</span>
                  </div>
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryReport.productBrand') }}&nbsp;</span>
                    <span class="form-text">{{ product.brandName || '--' }}</span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryReport.remarkMessage') }}&nbsp;</span>
                    <span class="form-text">{{ product.remark || '--' }}</span>
                  </div>
                </div>
              </div>
              <div>
                <span class="mr-24px">
                  <span class="form-label">{{ t('purchaseInquiry.inqueryReport.defaultSupplier') }}&nbsp;</span>

                  <el-tooltip :content="product.defaultSupplierName">
                    <span class="supplier-name">
                      {{ product.defaultSupplierName || '--' }}
                    </span>
                  </el-tooltip>
                </span>
                <span>
                  <span class="form-label">{{ t('purchaseInquiry.inqueryReport.lastPurchasePrice') }}&nbsp;</span>
                  {{ currencyFilter(product.lastPurchaseAmountCurrency)
                  }}{{ product.lastPurchaseAmount || "--" }}
                </span>
              </div>
            </section>
            <div class="custom-border mb-24px"></div>
          </div>

          <!-- 供应商报价列表 -->
<!--          <div class="supplier-quotes">
            <div
              class="quote-item"
              v-for="supplier in product.inquiryProductSupplierListVOS"
              :key="supplier.supplierId"
            >
              <el-input
                v-model="supplier.inquiryAmount"
                @input="(value) => handlePriceInput(value, supplier)"
                @blur="() => handlePriceBlur(supplier)"
              >
                <template #prepend>
                  <el-tooltip :content="supplier.supplierName">
                    <span class="supplier-name">
                      {{ supplier.supplierName }}
                    </span>
                  </el-tooltip>
                </template>
                <template #prefix>
                  {{ currencyFilter(supplier.currency) }}
                </template>
              </el-input>
            </div>
          </div>-->
          <div class="supplier-quotes">
            <div
              class="quote-item"
              v-for="supplier in product.inquiryProductSupplierListVOS"
              :key="supplier.supplierId"
            >
              <div class="supplier-name">
                <el-tooltip :content="supplier.supplierName">
                  <div class="ellipsis">{{supplier.supplierName}}</div>
                </el-tooltip>
              </div>
              <section class="price-block">
                <div class="currency">{{ currencyFilter(supplier.currency) }}</div>
                <div class="price">
                  <el-input
                    v-model="supplier.inquiryAmount"
                    @input="(value) => handlePriceInput(value, supplier)"
                    @blur="() => handlePriceBlur(supplier)"
                    :placeholder="t('purchaseInquiry.inqueryReport.pleaseInput')"
                    class="price-input"
                  />
                </div>
              </section>
            </div>
          </div>
          
        </div>
      </section>

      <!-- 底部按钮 -->
      <footer class="footer">
        <button class="cancel-btn" @click="cancelQuotation">{{ t('purchaseInquiry.inqueryReport.cancel') }}</button>
        <button class="save-btn" @click="saveQuotation">{{ t('purchaseInquiry.inqueryReport.saveQuotation') }}</button>
        <button class="submit-btn" @click="submitQuotation">{{ t('purchaseInquiry.inqueryReport.completeQuotation') }}</button>
      </footer>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.inquiry-detail {
  background: #f5f5f5;
  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;
    .code {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    .status {
      margin-left: 10px;
    }
  }
  .basic-info {
    background: #fff;
    padding: 20px 20px 0;
    border-radius: 8px;
    // margin-bottom: 20px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      .info-item {
        margin-bottom: 4px;
      }
    }
  }

  .product-list {
    background: #fff;
    padding: 0 20px;
    border-radius: 8px;

    .search-bar {
      margin-bottom: 20px;
    }

    .product-item {
      background: #f8f9fc;
      border-radius: 2px;
      border: 1px dashed #d7dbdf;
      padding: 24px;
      margin-bottom: 16px;

      .product-info {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .product-image {
          width: 80px;
          height: 80px;
          border-radius: 4px;
        }
        .product-details {
          flex: 1;
          .product-name {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #151719;
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
        }
      }
      
      .supplier-quotes {
        /*display: grid;
        grid-template-columns: repeat(5, 1fr);*/
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 24px;
        .quote-item {
          width: calc(20% - 10px);
          flex-grow: 0;
          display: flex;
          align-items: center;
          gap: 10px;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid #D7DBDF;
          justify-content: space-between;
          padding: 6px 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #151719;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          .supplier-name {
            flex: 1;
            align-items: center;
            min-width: 0;
          }
          .price-block{
            width: 120px;
            display: flex;
            align-items: center;
            .price-input{
              width: 100%;
              border: none;
              outline: none;
              background: transparent;
              height: 22px;
            }
            .currency{
            
            }
            .price{
            
            }
          }
        }
      }
    }
  }

  .footer {
    padding: 15px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

    .submit-btn {
      background: #762ADB;
      color: #fff;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }

    .save-btn {
      background: #fff;
      border: 1px solid #762ADB;
      color: #762ADB;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }

    .cancel-btn {
      background: #fff;
      border: 1px solid #ddd;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  .supplier-name {
    display: inline-block;
    //max-width: 100px;
  }
}
/* 去掉 price-input 类下 input 组件的边框 */
.price-input :deep(.el-input__wrapper) {
  box-shadow: none !important;
  border: none !important;
}

/* 去掉 hover 时的边框 */
.price-input :deep(.el-input__wrapper:hover) {
  box-shadow: none !important;
  border: none !important;
}

/* 去掉 focus 时的边框 */
.price-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: none !important;
  border: none !important;
}

/* 如果需要，可以添加底部边框 */
.price-input :deep(.el-input__wrapper) {
  border-bottom: 1px solid #dcdfe6;
}

/* 去掉输入框的内边距 */
.price-input :deep(.el-input__inner) {
  padding: 0;
}
</style>
