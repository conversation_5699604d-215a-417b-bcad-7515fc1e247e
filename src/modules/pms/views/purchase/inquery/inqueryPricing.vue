<script setup lang="ts">
import InqueryAPI from "@pms/api/inquery";
import { useDetail } from "./composables/useDetail";
import { parseTime } from "@/core/utils/index";
const { inqueryDetail, getInqueryDetail } = useDetail();
import { useCommon } from "@pms/composables/common";
const { currencyFilter } = useCommon();
import { useI18n } from "vue-i18n";
const { t } = useI18n();


import { useTagsViewStore } from "@/core/store";

interface InquiryDetailVO {
  productId: number;
  productCode: number;
  productName: string;
  brandName: string;
  unitName: string;
  productImg: string;
  firstCategoryId: number;
  secondCategoryId: number;
  thirdCategoryId: number;
  productCategoryFullName: string;
  defaultSupplierId: number;
  defaultSupplierName: string;
  remark: string;
  lastPurchaseAmountCurrency: string;
  lastPurchaseAmount: number;
  inquiryProductSupplierListVOS: Array<{
    supplierId: number;
    supplierName: string;
    price?: number;
    selected?: boolean;
  }>;
}

interface InquiryReport {
  inquiryNo: string;
  title: string;
  status: string;
  deliveryStartDate: string;
  deliveryEndDate: string;
  inquiryType: string;
  inquiryDetailVOS: InquiryDetailVO[];
}

const searchKeyword = ref("");
const inquiryData = ref<InquiryReport>({
  inquiryNo: "",
  title: "",
  status: "",
  deliveryStartDate: "",
  deliveryEndDate: "",
  inquiryType: "",
  inquiryDetailVOS: [],
});

const loading = ref(false);
const setPricingAmount = (productArr = []) => {
  if (!productArr || productArr.length === 0) return [];

  return productArr.map((productItem) => {
    if (productItem && productItem.inquiryProductSupplierListVOS) {
      productItem.inquiryProductSupplierListVOS.forEach((supplierItem) => {
        // 如果没有定价金额，则使用询价金额作为初始值
        if (!supplierItem.pricingAmount && supplierItem.inquiryAmount) {
          supplierItem.pricingAmount = supplierItem.inquiryAmount;
        }
      });
    }
    return productItem;
  });
};

const submitQuotation = () => {
  // 校验是否至少有一个定价被勾选
  const hasSelectedPricing = inqueryDetail.value.inquiryDetailVOS.some(
    (detail) =>
      detail.inquiryProductSupplierListVOS.some((supplier) => supplier.selected)
  );

  if (!hasSelectedPricing) {
    ElMessage.warning(t('purchaseInquiry.inqueryPricing.message.selectPricing'));
    return;
  }

  // 检查哪些商品未勾选定价
  const unselectedProducts = inqueryDetail.value.inquiryDetailVOS.filter(
    (detail) =>
      !detail.inquiryProductSupplierListVOS.some(
        (supplier) => supplier.selected
      )
  );

  if (unselectedProducts.length > 0) {
    const productNames = unselectedProducts
      .map((product) => product.productName)
      .join("、");
    ElMessageBox.confirm(`${productNames}${t('purchaseInquiry.inqueryPricing.message.unselectedProducts')}`, t('purchaseInquiry.inqueryPricing.message.tips'), {
      confirmButtonText: t('purchaseInquiry.inqueryPricing.message.continueSave'),
      cancelButtonText: t('purchaseInquiry.inqueryPricing.message.cancel'),
      type: "warning",
    })
      .then(() => {
        saveConfirmedQuotation();
      })
      .catch(() => {
        ElMessage.info(t('purchaseInquiry.inqueryPricing.message.cancelSubmit'));
      });
  } else {
    // 所有商品都已选择定价，直接确认保存
    ElMessageBox.confirm(t('purchaseInquiry.inqueryPricing.message.confirmSave'), t('purchaseInquiry.inqueryPricing.message.confirmSave'), {
      confirmButtonText: t('purchaseInquiry.inqueryPricing.message.confirm'),
      cancelButtonText: t('purchaseInquiry.inqueryPricing.message.cancel'),
      type: "warning",
    })
      .then(() => {
        saveConfirmedQuotation();
      })
      .catch(() => {
        ElMessage.info(t('purchaseInquiry.inqueryPricing.message.cancelSubmit'));
      });
  }
};

// 抽取保存逻辑到单独的函数
const saveConfirmedQuotation = () => {
  loading.value = true;
  const supplierList = inqueryDetail.value.inquiryDetailVOS.flatMap(
    (detail) => {
      return detail.inquiryProductSupplierListVOS
        .filter((item) => item.selected)
        .map((supplier) => ({
          inquiryDetailId: supplier.inquiryDetailId,
          pricingAmount: supplier.pricingAmount,
          inquiryAmount: supplier.inquiryAmount,
        }));
    }
  );

  const data = {
    inquiryId: route.query.inquiryId,
    type: 1, // 0-暂存 1-确认提交
    inquiryPurchaseListRequestDTOList: supplierList,
  };

  InqueryAPI.fillPrice(data)
    .then((res) => {
      ElMessage.success(t('purchaseInquiry.inqueryPricing.message.completePricingSuccess'));
      cancelQuotation();
    })
    .finally(() => {
      loading.value = false;
    });
};

const route = useRoute();
const saveQuotation = () => {
  loading.value = true;
  const supplierList = inqueryDetail.value.inquiryDetailVOS.flatMap((detail) =>
    detail.inquiryProductSupplierListVOS.map((supplier) => ({
      inquiryDetailId: supplier.inquiryDetailId, // Assuming this is the correct ID to use
      pricingAmount: supplier.pricingAmount,
    }))
  );

  const data = {
    inquiryId: route.query.inquiryId,
    type: 0, // 0-暂存 1-确认提交
    inquiryPurchaseListRequestDTOList: supplierList,
  };

  InqueryAPI.fillPrice(data)
    .then((res) => {
      /*console.log(res);
      router.push({
        path: "/pms/inquery/inquery",
      });*/
      ElMessage.success(t('purchaseInquiry.inqueryPricing.message.savePricingSuccess'));

      cancelQuotation();
    })
    .finally(() => {
      loading.value = false;
    });
};

const statusMapClass = {
  // 0->询价中，1->已确定，2->已完成，3->已关闭
  0: "unwokring",
  1: "executing",
  2: "cancelled",
  3: "cancelled",
};

const parseTimeHandle = (time: string, type: string) => {
  return parseTime(time, type);
};

const tagsViewStore = useTagsViewStore();

const navigateAndCloseCurrentTab = async (path: string) => {
  // 先删除当前页面的标签
  await tagsViewStore.delView(route);

  // 等待标签删除完成后再进行路由跳转
  nextTick(() => {
    router.push(path);
  });
};
const router = useRouter();
const cancelQuotation = () => {
  // Implement cancel logic
  /*router.push({
    path: "/pms/inquery/inquery",
  });*/
  navigateAndCloseCurrentTab("/pms/inquery/inquery");
};

const handleSupplierSelect = (product, selectedSupplier) => {
  /* if (selectedSupplier.selected) {
    // 如果选中了一个供应商，取消其他供应商的选中状态
    product.inquiryProductSupplierListVOS.forEach((supplier) => {
      if (supplier.supplierId !== selectedSupplier.supplierId) {
        supplier.selected = false;
      }
    });
  }*/
};

// 商品显示状态Map
const productVisibilityMap = ref(new Map<string | number, boolean>());

// 搜索处理函数
const handleSearch = () => {
  const keyword = searchKeyword.value.trim().toLowerCase();

  // 重置所有商品的显示状态
  inqueryDetail.value.inquiryDetailVOS.forEach((product) => {
    const isVisible =
      !keyword || product.productName.toLowerCase().includes(keyword);
    productVisibilityMap.value.set(product.productId, isVisible);
  });
};

// 获取商品的显示状态
const getProductVisible = (product: any): boolean => {
  return productVisibilityMap.value.get(product.productId) ?? true;
};

// 计算是否有可见的商品
const hasVisibleProducts = computed(() => {
  return inqueryDetail.value.inquiryDetailVOS.some((product) =>
    getProductVisible(product)
  );
});
// 判断是否为最低价格
const isLowestPrice = (product: any, currentSupplier: any) => {
  if (
    !product.inquiryProductSupplierListVOS ||
    !currentSupplier.pricingAmount
  ) {
    return false;
  }

  // 获取所有有效价格（非空且大于0）
  const validPrices = product.inquiryProductSupplierListVOS
    .filter(
      (supplier) =>
        supplier.pricingAmount && parseFloat(supplier.pricingAmount) > 0
    )
    .map((supplier) => parseFloat(supplier.pricingAmount));

  if (validPrices.length === 0) {
    return false;
  }

  // 找出最低价格
  const lowestPrice = Math.min(...validPrices);

  // 判断当前供应商价格是否等于最低价格
  return parseFloat(currentSupplier.pricingAmount) === lowestPrice;
};

// 监听搜索关键词变化
/*watch(searchKeyword, (newVal) => {
  handleSearch();
});*/

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = "";
  handleSearch();
};
// 获取排序后的供应商列表
const getSortedSuppliers = (product) => {
  /*
    供应商报价，排序规则：
    默认供应商如果有报价放在最前面，后续按照价格升序排列，并且显示低价标签，供应商后显示勾选框，可进行勾选。
    如果默认供应商没有填写报价，则不用排序放在第一个，直接按价格升序排列；
  */
  // 检查是否有供应商列表
  if (
    !product ||
    !product.inquiryProductSupplierListVOS ||
    !product.inquiryProductSupplierListVOS.length
  ) {
    return [];
  }

  // 创建供应商列表副本进行排序
  const suppliers = [...product.inquiryProductSupplierListVOS];

  // 先找出默认供应商及其是否有报价
  const defaultSupplierIndex = suppliers.findIndex(
    (supplier) => supplier.isDefault === 1
  );
  const hasDefaultSupplier = defaultSupplierIndex !== -1;

  // 如果有默认供应商，先检查它是否有报价
  const defaultSupplierHasPrice =
    hasDefaultSupplier && suppliers[defaultSupplierIndex].pricingAmount;

  // 按照价格升序排列所有供应商
  const sortedSuppliers = suppliers.sort((a, b) => {
    const priceA = a.pricingAmount ? parseFloat(a.pricingAmount) : Infinity;
    const priceB = b.pricingAmount ? parseFloat(b.pricingAmount) : Infinity;
    return priceA - priceB;
  });

  // 如果默认供应商存在且有报价，将其移到第一位
  if (hasDefaultSupplier && defaultSupplierHasPrice) {
    // 排序后，默认供应商的位置可能已经改变，需要重新查找
    const defaultSupplierInSortedList = sortedSuppliers.find(
      (supplier) => supplier.isDefault === 1
    );

    // 先从已排序的列表中移除默认供应商
    const newDefaultIndex = sortedSuppliers.findIndex(
      (supplier) => supplier.isDefault === 1
    );

    if (newDefaultIndex > -1) {
      sortedSuppliers.splice(newDefaultIndex, 1);
      // 再将默认供应商添加到列表开头
      sortedSuppliers.unshift(defaultSupplierInSortedList);
    }
  }

  return sortedSuppliers;
};
</script>

<template>
  <div class="inquiry-detail" v-loading="loading">
    <el-card>
      <div class="card-header mb-24px">
        <img
          @click="cancelQuotation"
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
        />
        <span class="code" @click="cancelQuotation">
          询价单号：{{ inqueryDetail.inquiryCode }}
        </span>
        <span
          class="contract status"
          :class="statusMapClass[inqueryDetail.inquiryStatus]"
        >
          {{ inqueryDetail.inquiryStatusStr }}
        </span>
      </div>

      <!-- 基本信息区域 -->
      <section class="basic-info">
        <div class="section-title card-title mb-20px">{{ t('purchaseInquiry.inqueryPricing.basicInfo') }}</div>
        <div class="info-grid">
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryPricing.deliveryPeriod') }}</label>
            <span class="card-form-text">
              {{
                parseTimeHandle(
                  new Date(inqueryDetail.startDeliveryDate),
                  "{y}-{m}-{d}"
                )
              }}至{{
                parseTimeHandle(
                  new Date(inqueryDetail.endDeliveryDate),
                  "{y}-{m}-{d}"
                )
              }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryPricing.inquiryType') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.inquiryTypeStr || '--' }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryPricing.name') }}</label>
            <span class="card-form-text">{{ inqueryDetail.title || '--' }}</span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryPricing.warehouse') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.warehouseName || '--' }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryPricing.createTime') }}</label>
            <span class="card-form-text">
              {{
                parseTimeHandle(
                  new Date(inqueryDetail.submitTime),
                  "{y}-{m}-{d} {h}:{i}:{s}"
                )
              }}
            </span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryPricing.inquirySupplier') }}</label>
            <span class="card-form-text">{{ inqueryDetail.suppliers || '--' }}</span>
          </div>
          <div class="info-item">
            <label class="card-form-label">{{ t('purchaseInquiry.inqueryPricing.creator') }}</label>
            <span class="card-form-text">
              {{ inqueryDetail.createUserName || '--' }}
            </span>
          </div>
          <!-- 其他基本信息项 -->
        </div>
        <div class="card-bottomm-border mt-30px mb-30px"></div>
      </section>

      <!-- 询价商品列表 -->
      <section class="product-list">
        <div class="section-title card-title mb-20px">{{ t('purchaseInquiry.inqueryPricing.inquiryProducts') }}</div>
        <div class="search-bar">
          <el-input
            style="width: 500px"
            class="mr-12px"
            v-model="searchKeyword"
            :placeholder="t('purchaseInquiry.inqueryPricing.searchPlaceholder')"
            @input="handleSearch"
            clearable
            @clear="handleSearch"
          />
          <el-button type="primary" @click="handleSearch">{{ t('purchaseInquiry.inqueryPricing.search') }}</el-button>
        </div>

        <!-- 商品项模板 -->
        <div
          class="product-item"
          v-for="product in setPricingAmount(inqueryDetail.inquiryDetailVOS)"
          :key="product.productId"
          v-show="getProductVisible(product)"
        >
          <div class="product-info">
            <section class="flex w-100%">
              <img :src="product.productImg" class="product-image mr-12px" />
              <div class="product-details">
                <div class="product-name">{{ product.productName || '--' }}</div>
                <div class="flex w-100% gap-16px">
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryPricing.purchaseUnit') }}&nbsp;</span>
                    <span class="form-text">{{ product.unitName || '--' }}</span>
                  </div>
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryPricing.productBrand') }}&nbsp;</span>
                    <span class="form-text">{{ product.brandName || '--' }}</span>
                  </div>
                </div>
                <div>
                  <div>
                    <span class="form-label">{{ t('purchaseInquiry.inqueryPricing.remarkMessage') }}&nbsp;</span>
                    <span class="form-text">{{ product.remark || '--' }}</span>
                  </div>
                </div>
              </div>
              <div class="mr-24px">
                <span class="form-label">{{ t('purchaseInquiry.inqueryPricing.defaultSupplier') }}&nbsp;</span>
                <span class="form-text">{{ product.defaultSupplierName || '--'  }}</span>
              </div>
              <div>
                <span class="form-label">{{ t('purchaseInquiry.inqueryPricing.lastPurchasePrice') }}&nbsp;</span>
                <span class="form-text">
                  {{ currencyFilter(product.lastPurchaseAmountCurrency)
                  }}{{ product.lastPurchaseAmount || "--" }}
                </span>
              </div>
            </section>
            <div class="custom-border"></div>
          </div>

          <!-- 供应商报价列表 -->
          <div class="supplier-quotes">
            <div
              class="quote-item"
              v-for="supplier in getSortedSuppliers(product)"
              :key="supplier.supplierId"
            >
              <div class="supplier-name">
                <el-checkbox
                  v-model="supplier.selected"
                  @change="handleSupplierSelect(product, supplier)"
                  class="check"
                />
                <el-tooltip :content="supplier.supplierName">
                  <div class="ellipsis">{{ supplier.supplierName }}</div>
                </el-tooltip>
              </div>
              <section class="price-block">
                <section class="price-block-left">
                  <div class="currency">
                    {{ currencyFilter(supplier.currency) }}
                  </div>
                  <div class="price">
                    {{ supplier.pricingAmount || "--" }}
                  </div>
                </section>

                <div v-if="isLowestPrice(product, supplier)" class="lowest-tag">
                  低
                </div>
              </section>
            </div>
          </div>
        </div>
      </section>

      <!-- 底部按钮 -->
      <footer class="footer">
        <button class="cancel-btn" @click="cancelQuotation">{{ t('purchaseInquiry.inqueryPricing.cancel') }}</button>
        <!-- <button class="save-btn" @click="saveQuotation">保存定价</button> -->
        <button class="submit-btn" @click="submitQuotation">{{ t('purchaseInquiry.inqueryPricing.savePricing') }}</button>
      </footer>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.inquiry-detail {
  background: #f5f5f5;
  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;
    .code {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    .status {
      /*font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;*/
      margin-left: 10px;
    }
  }

  .basic-info {
    background: #fff;
    padding: 20px 20px 0;
    border-radius: 8px;
    // margin-bottom: 20px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      .info-item {
        margin-bottom: 4px;
      }
    }
  }

  .product-list {
    background: #fff;
    padding: 0 20px;
    border-radius: 8px;

    .search-bar {
      margin-bottom: 20px;
    }

    .product-item {
      background: #f8f9fc;
      border-radius: 2px;
      border: 1px solid #d7dbdf;
      padding: 24px;
      margin-bottom: 15px;

      .product-info {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .product-image {
          width: 80px;
          height: 80px;
          border-radius: 4px;
        }
        .product-details {
          flex: 1;
          .product-name {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #151719;
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
        }
      }

      .supplier-quotes {
        /*display: grid;
        grid-template-columns: repeat(5, 1fr);*/
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 24px;
        .quote-item {
          width: calc(20% - 10px);
          flex-grow: 0;
          display: flex;
          align-items: center;
          gap: 10px;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid #d7dbdf;
          justify-content: space-between;
          padding: 6px 8px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #151719;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          .supplier-name {
            flex: 1;
            align-items: center;
            min-width: 0;
            display: flex;
            .check {
              margin-right: 6px;
              height: 22px;
            }
          }
          .price-block {
            width: 80px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .price-block-left {
              display: flex;
              align-items: center;
              .currency {
              }
              .price {
              }
            }

            .lowest-tag {
              width: 16px;
              height: 16px;
              background: #29b610;
              border-radius: 3px;

              font-family:
                PingFangSC,
                PingFang SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
              line-height: 16px;
              text-align: center;
              font-style: normal;
            }
          }
        }
      }
    }
  }

  .footer {
    padding: 15px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

    .submit-btn {
      background: #762ADB;
      color: #fff;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }

    .save-btn {
      background: #fff;
      border: 1px solid #762ADB;
      color: #762ADB;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }

    .cancel-btn {
      background: #fff;
      border: 1px solid #ddd;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}
</style>
