import type { FormInstance, FormRules } from "element-plus";

export const useInquiryForm = () => {
  const rules = {
    deliveryPeriod: [
      { required: true, message: "请选择计划交货周期", trigger: "change" },
    ],
    inquiryType: [
      { required: true, message: "请选择询价类型", trigger: "change" },
    ],
    warehouseId: [{ required: true, message: "请选择仓库", trigger: "change" }],
    title: [{ required: true, message: "请输入名称", trigger: "blur" }],
  };

  const validateForm = async (
    formEl: FormInstance | null
  ): Promise<boolean> => {
    if (!formEl) return false;
    try {
      await formEl.validate();
      return true;
    } catch (error) {
      return false;
    }
  };

  return {
    rules,
    validateForm,
  };
};
