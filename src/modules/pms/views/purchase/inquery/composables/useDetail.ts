import InqueryAPI from "@pms/api/inquery";

export const useDetail = () => {
  const route = useRoute();
  const id = route.query.inquiryId;
  const inqueryDetail = ref<any>({});

  const getInqueryDetail = async (id: number) => {
    const res = await InqueryAPI.getInqueryDetail(id);
    inqueryDetail.value = res;
  };

  onMounted(() => {
    getInqueryDetail(id);
  });

  return {
    inqueryDetail,
    getInqueryDetail
  };
};
