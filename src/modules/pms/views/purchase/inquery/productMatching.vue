<script setup lang="ts">
import InqueryAPI from "@pms/api/inquery";
import { useInquiryStore } from "@pms/store/inquiry";
import { InquiryDetailDTO } from "@pms/types/inquiry";
import { useI18n } from "vue-i18n";
const {t} = useI18n();
import { useTagsViewStore } from "@/core/store";


// inquiryStore.selectedProducts
const inquiryStore = useInquiryStore();
interface Supplier {
  id: string;
  name: string;
  products: Product[];
  matchRate: number;
}

interface Product {
  id: string;
  name: string;
  image: string;
  brand: string;
  unit: string;
  price: string;
  quantity: string;
}

const suppliers = ref<Supplier[]>([]);

const selectedSuppliers = computed(() => {
  return suppliers.value.filter((item) => item.isSelected);
});

// const selectedSuppliers = ref();

const toggleSupplier = (supplierId: string, supplier: Supplier) => {
  console.log('supplierId---', supplierId, supplier.isSelected)
};

/* const isSelected = (supplierId: string) => {
  return selectedSuppliers.value.includes(supplierId);
}; */

// 获取供应商报价信息
const getSupplierInqueryInfo = async () => {
  // 转换数据为 InquiryDetailDTO 格式
  const inquiryDetails = inquiryStore.selectedProducts.map((product) => ({
    brandName: product.brandName,
    currency: product.currency,
    defaultSupplierId: product.defaultSupplierId,
    defaultSupplierName: product.defaultSupplierName,
    firstCategoryId: product.firstCategoryId,
    inquiryProductSupplierListDTOList:
      product.completedPricingSupplierListVOList,
    productCategoryFullName: product.productCategoryFullName,
    productCode: product.productCode,
    productId: product.productId,
    productImg: product.productImg,
    productName: product.productName,
    purchasePrice: product.purchasePrice,
    remark: product.remark,
    secondCategoryId: product.secondCategoryId,
    thirdCategoryId: product.thirdCategoryId,
    unitId: product.unitId,
    unitName: product.unitName,
  }));

  const data = await InqueryAPI.getSupplierInqueryInfo({
    startDeliveryDate: getTime(`${inquiryStore.inquiryForm.startDeliveryDate}`),
    endDeliveryDate: getTime(`${inquiryStore.inquiryForm.endDeliveryDate}`),
    warehouseId: inquiryStore.inquiryForm.warehouseId,
    inquiryType: inquiryStore.inquiryForm.inquiryType,
    inquiryDetailDTOS: inquiryDetails,
  });
  suppliers.value = data;
};

onMounted(async () => {
  await getSupplierInqueryInfo();
});

const getTime = (date) => {
  return new Date(date).getTime();
};

const tagsViewStore = useTagsViewStore();
const route = useRoute();

const navigateAndCloseCurrentTab = async (path: string) => {
  // 先删除当前页面的标签
  await tagsViewStore.delView(route);

  // 等待标签删除完成后再进行路由跳转
  nextTick(() => {
    router.push(path);
  });
};

// 覆盖标识 0-未覆盖 1-已覆盖
const coverFilter = (coverFlag: Number) => {
  const coverMap = {
    0: {
      text: t('purchaseInquiry.productMatching.coverStatus.notCovered'),
      className: "not-cover",
    },
    1: {
      text: t('purchaseInquiry.productMatching.coverStatus.covered'),
      className: "cover",
    },
  };
  return coverMap[coverFlag];
};

const router = useRouter();
const createInquiry = async () => {
  // 实现创建询价单的逻辑
  if (selectedSuppliers.value.length === 0) {
    ElMessage.error(t('purchaseInquiry.productMatching.message.selectSupplier'));
    return;
  }
  try {
    const res = await InqueryAPI.saveInquery({
      startDeliveryDate: getTime(`${inquiryStore.inquiryForm.startDeliveryDate}`),
      endDeliveryDate: getTime(`${inquiryStore.inquiryForm.endDeliveryDate}`),
      warehouseName: inquiryStore.inquiryForm.warehouseName,
      warehouseId: inquiryStore.inquiryForm.warehouseId,
      inquiryType: inquiryStore.inquiryForm.inquiryType,
      title: inquiryStore.inquiryForm.title,
      saveInquirySecondStepOfSupplierInfoDTOS: suppliers.value
        .filter((supplier) => supplier.isSelected)
        .map((supplier) => ({
          supplierId: supplier.supplierId,
          supplierName: supplier.supplierName,
          matchingProductIds: supplier.matchingProductIds,
          productListDTOS: supplier.productListVOS,
        })),
    });
      ElMessage.success(t('purchaseInquiry.productMatching.message.createSuccess'));
      navigateAndCloseCurrentTab("/pms/inquery/inquery");
      // router.push({ path: "/pms/inquery/inquery" });
  } catch (error) {
    // ElMessage.error("创建询价单失败");
    console.error(error);
  }
};

const goBack = () => {
  // router.go(-1);
  navigateAndCloseCurrentTab("/pms/purchase/createInquery?from=next-page");
  // router.push({
  //   path: "/pms/purchase/createInquery",
  //   query: {
  //     from: 'next-page'
  //   }
  // });
};


const handleCancel = () => {
  router.push({
    path: "/pms/inquery/inquery",
  });
};
const nextStep = () => {
  // 实现下一步逻辑
};
</script>

<template>
  <div>
    <el-card>
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="goBack"
        />
        <span class="code" @click="goBack" >{{ t('purchaseInquiry.productMatching.title') }}</span>
      </div>
      <div class="flex items-center justify-between mb-5">
        <div class="text-16px font-bold card-title">{{ t('purchaseInquiry.productMatching.supplierSelection') }}</div>
        <div class="text-14px text-gray-400">
          {{ t('purchaseInquiry.productMatching.supplierCount') }} <span class="num">{{ selectedSuppliers.length }}</span> {{ t('purchaseInquiry.productMatching.supplierUnit') }}
        </div>
      </div>
      <div class="grid grid-cols-3 gap-4">
        <div
          v-for="supplier in suppliers"
          :key="supplier.id"
          class="product-block"
        >
          <div class="">
            <div class="flex items-center justify-between mb-2">
              <el-checkbox
                v-model="supplier.isSelected"
                :label="supplier.supplierId"
                @change="toggleSupplier(supplier.supplierId, supplier)"
              >
                <span class="supplier-name">{{ supplier.supplierName }}</span>
              </el-checkbox>
            </div>
          </div>
          <el-divider class="mb-21px mt-21px" />
          <div>
            <div
              v-for="product in supplier.productListVOS"
              :key="product.id"
              class="mb-4 last:mb-0"
            >
              <div class="flex">
                <div class="img-cover w-48px h-48px object-cover rounded mr-8px">
                  <el-image
                    :src="product.productImg"
                    class="w-48px h-48px object-cover rounded mr-8px"
                  />
                  <span class="cover-wrap" :class="coverFilter(product.coverFlag).className">{{ coverFilter(product.coverFlag).text }}</span>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="text-14px font-medium mb-6px truncate">
                    {{ product.productName }}
                  </div>
                  <div class="product-code">
                    {{ t('purchaseInquiry.productMatching.productCode') }}：{{ product.productCode }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <el-divider class="mb-21px mt-21px" />
          <div class="product-code">
            {{ t('purchaseInquiry.productMatching.matchingRate') }}：
            <span class="matching-rate">
              {{ supplier.matchingRate }}
            </span>
          </div>
        </div>
      </div>

      <div class="flex justify-end mt-8 gap-2">
        <el-button plain @click="handleCancel">{{ t('purchaseInquiry.productMatching.cancel') }}</el-button>
        <el-button type="primary" plain @click="goBack">{{ t('purchaseInquiry.productMatching.previousStep') }}</el-button>
        <el-button type="primary" @click="createInquiry">{{ t('purchaseInquiry.productMatching.confirm') }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.product-block {
  background: #f9f9fb;
  border-radius: 2px;
  border: 1px dashed #d7dbdf;
  padding: 20px;
  box-sizing: border-box;
}
.product-code {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #90979e;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
.matching-rate {
  color: #762ADB ;
}
.card-header {
  padding: 0px 10px 20px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #e5e7f3;
  border-radius: 4px 4px 0px 0px;
  cursor: pointer;
  .code {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .back-btn {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .status {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
}
.supplier-name {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #151719;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
.num{
  color: #762ADB ;
}
.img-cover{
  position: relative;
  .cover-wrap{
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 12px;
    text-align: center;
    font-style: normal;
    padding: 6px 0 2px;
    &.not-cover{
      background: #90979E;
      border-radius: 0px 0px 2px 2px;
    }
    &.cover{
      background: #762ADB ;
      border-radius: 0px 0px 2px 2px;
    }
  }
}
</style>
