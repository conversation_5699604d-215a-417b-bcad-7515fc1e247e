<template>
  <div class="app-container">
    <el-card class="box-card" v-loading="submitLoading">
      <div class="top-card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="handleCancel"
        />
        <span class="code" @click="handleCancel">
          {{ t("purchaseRequirements.title.create") }}
        </span>
      </div>

      <div class="card-header">
        <span class="card-title">
          {{ $t("purchaseRequirements.title.basicInfo") }}
        </span>
      </div>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <!-- 基础信息部分 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item
              :label="$t('purchaseRequirements.label.clientName')"
              prop="customerName"
            >
              <el-input
                v-model="formData.customerName"
                :disabled="formDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('purchaseRequirements.label.deliveryAddress')"
              prop="deliveryAddress"
            >
              <el-input
                v-model="formData.deliveryAddress"
                :disabled="formDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('purchaseRequirements.label.name')"
              prop="receiveName"
            >
              <el-input
                v-model="formData.receiveName"
                :disabled="formDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('purchaseRequirements.label.phone')"
              prop="receiveMobile"
            >
              <el-input
                v-model="formData.receiveMobile"
                oninput="value=value.replace(/^0|[^0-9]/g, '')"
                tabindex="1"
                auto-complete="on"
                maxlength="30"
                :disabled="formDisabled"
              >
                <template #prepend>
                  <el-select
                    class="custom-select"
                    v-model="formData.receiveMobileArea"
                    style="width: 80px; background: white"
                    :placeholder="
                      $t('purchaseRequirements.placeholder.areaCode')
                    "
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in countryNumCodeList"
                      :key="item.id"
                      :label="item.internationalCode"
                      :value="item.internationalCode"
                    />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              :label="$t('purchaseRequirements.label.expectedDeliveryTime')"
              prop="expectedDeliveryTimeStar"
              :rules="[
                {
                  required: true,
                  message: t('purchaseRequirements.rules.expectedDeliveryTime'),
                  trigger: 'change',
                },
                {
                  validator: (rule, value, callback) => {
                    if (
                      !formData.expectedDeliveryTimeStar ||
                      !formData.expectedDeliveryTimeEnd
                    ) {
                      callback(new Error(t('purchaseRequirements.message.pleaseSelectDeliveryTimeRange')));
                    } else {
                      callback();
                    }
                  },
                  trigger: 'change',
                },
              ]"
            >
              <div class="flex items-center">
                <el-date-picker
                  v-model="deliveryDate"
                  type="date"
                  :range-separator="t('purchaseRequirements.label.to')"
                  :start-placeholder="t('purchaseRequirements.label.startDate')"
                  :end-placeholder="t('purchaseRequirements.label.endDate')"
                  value-format="YYYY-MM-DD"
                  @change="handleDeliveryDateChange"
                  :disabled="formDisabled"
                  :disabledDate="disabledDate"
                />
                <el-time-picker
                  v-model="deliveryTime"
                  is-range
                  :range-separator="t('purchaseRequirements.label.to')"
                  :start-placeholder="t('purchaseRequirements.label.startTime')"
                  :end-placeholder="t('purchaseRequirements.label.endTime')"
                  :disabled="!deliveryDate || formDisabled"
                  format="HH:mm"
                  value-format="HH:mm"
                  @change="handleDeliveryTimeChange"
                  class="ml-2"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="$t('purchaseRequirements.label.warehouse')"
              prop="warehouseId"
            >
              <el-select
                v-model="formData.warehouseId"
                @change="handleWarehouseSelect"
                :disabled="formDisabled"
              >
                <el-option
                  v-for="item in warehouseList"
                  :key="item.warehouseId"
                  :label="item.warehouseName"
                  :value="item.warehouseId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="align-right">
            <el-button type="primary" @click="handleAdd">
              {{ $t("purchaseRequirements.button.confirm") }}
            </el-button>
            <el-button @click="handleReset">
              {{ $t("purchaseRequirements.button.reset") }}
            </el-button>
          </el-col>
        </el-row>

        <!-- 商品信息部分 -->
        <div class="flex-x-between mb-20px" v-show="isFormValid">
          <span class="card-title">
            {{ $t("purchaseRequirements.title.productInfo") }}
          </span>
          <el-button
            type="primary"
            link
            :disabled="!isFormValid"
            @click="handleAddProduct"
          >
            {{ $t("purchaseRequirements.button.addProduct") }}
          </el-button>
        </div>
        <section v-show="isFormValid">
          <el-table :data="formData.purchaseReqDetailDTOList" border>
            <!-- <el-table-column type="index" width="50" /> -->
            <el-table-column
              :label="$t('purchaseRequirements.table.productInfo')"
              prop="productInfo"
            >
              <template #default="scope">
                <div class="product-info">
                  <img
                    :src="scope.row.imagesUrls"
                    alt="{{ $t('purchaseRequirements.label.productImg') }}"
                  />
                  <div class="product-info-content">
                    <div class="product-info-content-item code">
                      {{ $t("purchaseRequirements.label.productCode") }}:{{
                        scope.row.productCode
                      }}
                    </div>
                    <div class="product-info-content-item name">
                      {{ $t("purchaseRequirements.label.productName") }}:{{
                        scope.row.productName
                      }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchaseRequirements.table.count')"
              prop="reqCount"
            >
              <template #default="scope">
                <el-input
                  v-model="scope.row.reqCount"
                  @input="validateReqCount($event, scope.row)"
                  @blur="handleReqCountBlur(scope.row)"
                  :placeholder="$t('purchaseRequirements.placeholder.enterQuantity')"
                />
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchaseRequirements.table.unit')"
              prop="unitName"
            />
            <el-table-column
              :label="$t('purchaseRequirements.table.operation')"
              width="120"
            >
              <template #default="scope">
                <el-button
                  type="danger"
                  link
                  @click="handleRemoveProduct(scope.$index)"
                >
                  {{ $t("common.delete") }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 其他信息部分 -->
          <div class="other-info">
            <div class="mb-20px">
              <span class="card-title">
                {{ $t("purchaseRequirements.title.otherInfo") }}
              </span>
            </div>
            <el-form-item :label="$t('purchaseRequirements.label.attachment')">
              <UploadMultiple
                @update:model-value="onChangeMultiple"
                ref="detailPicsRef"
                v-model="formData.attachmentFiles"
                :limit="10"
                :formRef="formRef"
                listType="text"
                :fileType="filtType"
                :fileSize="10"
                class="modify-multipleUpload"
                name="detailPic"
              />
            </el-form-item>

            <el-form-item :label="$t('purchaseRequirements.label.remarks')">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="4"
                :maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- 表单操作按钮 -->
          <div class="form-buttons align-right">
            <el-button @click="handleCancel">
              {{ $t("common.cancel") }}
            </el-button>
            <el-button type="primary" @click="handleSubmit">
              {{ $t("common.confirm") }}
            </el-button>
          </div>
        </section>
      </el-form>
    </el-card>

    <!-- Add EditDialog component -->
    <EditDialog
      v-if="showEditDialog"
      v-model:visible="showEditDialog"
      @close="handleDialogClose"
      :form-data="formData"
      :deliveryDate="deliveryDate"
      title=""
      @on-submit="addGoods"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import EditDialog from "./components/goods.vue";
import { useWarehouse } from "@pms/composables/warehouse";
import { createPurchaseRequirement } from "@pms/api/purchaseRequirements";
import UserAPI from "@/core/api/accountManagement";
import InqueryAPI from "@pms/api/inquery";
import { parseTime } from "@/core/utils/index";
import commonUpload, { previewSingle } from "@/core/utils/commonUpload";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const router = useRouter();
const formRef = ref();
const fileList = ref([]);
const { warehouseList } = useWarehouse();
const deliveryDate = ref();
const deliveryTime = ref();
const formValidated = ref(false);
const filtType = [
  "png",
  "jpg",
  "jpeg",
  "pdf",
  "xlsx",
  "xls",
  "docx",
  "zip",
  "rar",
];
/* const attachmentFiles = [ // 私密上传
  '{"bucket":"yt-oxms-uat","fileName":"yt-oxms-uat/image/dbd53eb3-38c3-4bfb-a527-f3135c982ddd.png","originalFileName":"Fondo-de-Ubuntu-22.04.png"}',
  '{"bucket":"yt-oxms-uat","fileName":"yt-oxms-uat/image/d6f99270-a301-4177-82f9-f6e7ee776e7d.docx","originalFileName":"New DOCX 文档.docx"}',
  '{"bucket":"yt-oxms-uat","fileName":"yt-oxms-uat/image/94d0e672-cd8c-42c6-bde7-2d2bd960f28c.pdf","originalFileName":"New DOCX 文档.pdf"}'
] */

/* const attachmentFiles = [ // 公共上传
  '{"bucket":"yt-oxms-read-uat","fileName":"https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.com/yt-oxms-read-uat/image/New DOCX 文档 (1).docx","originalFileName":"New DOCX 文档 (1).docx"}',
  '{"bucket":"yt-oxms-read-uat","fileName":"https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.com/yt-oxms-read-uat/image/b44e8b7e-b5f4-4d08-84b6-1d0757f1619b.png","originalFileName":"b44e8b7e-b5f4-4d08-84b6-1d0757f1619b.png"}',
  '{"bucket":"yt-oxms-read-uat","fileName":"https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.com/yt-oxms-read-uat/image/New DOCX 文档 (1).docx","originalFileName":"New DOCX 文档 (1).docx"}'
] */

// const attachmentFiles = ref([])

// 禁用当前日期之前的日期
const disabledDate = (time: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime();
};

function onChangeMultiple(val: any) {
  console.log("onChangeMultiple val------------", val);
  // formData.attachmentFiles = JSON.stringify(val); // 同步更新formData中的附件信息
  formData.attachmentFiles = val; // 同步更新formData中的附件信息
}

// 验证商品数量输入
const validateReqCount = (value: string, row: any) => {
  // 只允许输入数字和小数点
  if (!/^[\d.]*$/.test(value)) {
    ElMessage.warning(t("purchaseRequirements.message.onlyNumbersAndDecimals"));
    const newValue = value.replace(/[^\d.]/g, "");
    row.reqCount = newValue;
    return;
  }

  // 处理小数点
  const parts = value.split(".");
  if (parts.length > 2) {
    ElMessage.warning(t("purchaseRequirements.message.onlyOneDecimalPoint"));
    row.reqCount = parts[0] + "." + parts.slice(1).join("");
    return;
  }

  // 检查整数位长度
  if (parts[0].length > 11) {
    ElMessage.warning(t("purchaseRequirements.message.integerPartTooLong"));
    row.reqCount = parts[0].slice(0, 11) + (parts[1] ? "." + parts[1] : "");
    return;
  }

  // 检查小数位长度
  if (parts.length === 2 && parts[1].length > 2) {
    ElMessage.warning(t("purchaseRequirements.message.decimalPartTooLong"));
    row.reqCount = parts[0] + "." + parts[1].slice(0, 2);
    return;
  }

  // 如果以小数点开头，自动补0
  if (row.reqCount.startsWith(".")) {
    row.reqCount = "0" + row.reqCount;
  }
};

// 失去焦点时的处理
const handleReqCountBlur = (row: any) => {
  if (!row.reqCount) {
    ElMessage.warning(t("purchaseRequirements.rules.quantityRequired"));
    return;
  }

  const num = parseFloat(row.reqCount);
  if (isNaN(num)) {
    ElMessage.warning(t("purchaseRequirements.message.enterValidNumber"));
    row.reqCount = "";
    return;
  }

  if (num <= 0) {
    ElMessage.warning(t("purchaseRequirements.rules.quantityPositive"));
    row.reqCount = "";
    return;
  }

  // 格式化数字，保留两位小数
  row.reqCount = num.toFixed(2);
};

const countryNumCodeList = ref([]);
// 获取区号
function getAreaList() {
  UserAPI.getAllCountry()
    .then((data) => {
      countryNumCodeList.value = data;
    })
    .finally(() => {});
}

onMounted(() => {
  getAreaList();
});

// 表单数据
const formData = reactive({
  attachmentFiles: "", // 附件
  customerCode: "", // 客户编码
  customerName: "", // 客户名称
  deliveryAddress: "", // 收货地址
  expectedDeliveryTimeEnd: null, // 期望配送时间结束 时间戳需要传long
  expectedDeliveryTimeStar: null, // 期望配送时间开始 时间戳需要传long
  expectedDeliveryTime: "", // 期望收货日期
  purchaseReqDetailDTOList: [
    // 明细列表
    /* {
        "productId": "1888098605532114946",
        "productCode": "P02084580163729",
        "productName": "小茄子",
        "brandName": "海天",
        "unitName": "斤",
        "unitId": "1879773320969637890",
        "unitCode":"123",
        "firstCategoryId": "4",
        "secondCategoryId": "5",
        "thirdCategoryId": "6",
        "productCategoryFullName": "蔬菜/茄瓜/圆茄",
        "imagesUrls": "www.pic.com",
        "createUser": 0,
        "reqCount": 10
    }, */
  ],
  receiveMobile: "", // 收货人电话
  receiveMobileArea: "+86", // 收货人电话区号
  receiveName: "", // 收货人姓名
  remark: "", // 备注
  reqSource: 1, // 需求来源：1-手动创建 2-外部接口
  warehouseId: "", // 仓库ID
  warehouseName: "", // 仓库名称
});

// 表单验证状态
const isFormValid = computed(() => {
  return (
    formValidated.value &&
    formData.customerName &&
    formData.deliveryAddress &&
    formData.receiveName &&
    formData.receiveMobile &&
    formData.expectedDeliveryTimeStar &&
    formData.expectedDeliveryTimeEnd &&
    formData.warehouseId
  );
});
// 表单验证规则
const rules = {
  customerName: [
    {
      required: true,
      message: t("purchaseRequirements.rules.clientName"),
      trigger: "blur",
    },
  ],
  deliveryAddress: [
    {
      required: true,
      message: t("purchaseRequirements.rules.deliveryAddress"),
      trigger: "blur",
    },
  ],
  receiveName: [
    {
      required: true,
      message: t("purchaseRequirements.rules.name"),
      trigger: "blur",
    },
  ],
  receiveMobile: [
    {
      required: true,
      message: t("purchaseRequirements.rules.phone"),
      trigger: "blur",
    },
  ],
  warehouseId: [
    {
      required: true,
      message: t("purchaseRequirements.rules.warehouse"),
      trigger: "change",
    },
  ],
  expectedDeliveryTime: [
    {
      required: true,
      message: t("purchaseRequirements.rules.expectedDeliveryTime"),
      trigger: "change",
    },
    {
      validator: (rule, value, callback) => {
        if (
          !formData.expectedDeliveryTimeStar ||
          !formData.expectedDeliveryTimeEnd
        ) {
          callback(
            new Error(
              t("purchaseRequirements.message.pleaseSelectDeliveryTimeRange")
            )
          );
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
};

const handleWarehouseSelect = (val) => {
  formData.warehouseName =
    warehouseList.value.find((item) => item.warehouseId == val)
      ?.warehouseName || "";
};

const formDisabled = ref(false);
const handleReset = () => {
  ElMessageBox.confirm(
    t("purchaseRequirements.message.resetConfirm"),
    t("common.tips"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      formRef.value?.resetFields();
      formValidated.value = false;
      // 重置所有表单字段
      formData.attachmentFiles = "";
      formData.customerCode = "";
      formData.customerName = "";
      formData.deliveryAddress = "";
      formData.expectedDeliveryTimeEnd = null;
      formData.expectedDeliveryTimeStar = null;
      formData.purchaseReqDetailDTOList = [];
      formData.receiveMobile = "";
      formData.receiveMobileArea = "+86";
      formData.receiveName = "";
      formData.remark = "";
      formData.warehouseId = "";
      formData.warehouseName = "";
      // 重置日期和时间选择器
      deliveryDate.value = null;
      deliveryTime.value = null;
      // 重置文件相关字段
      fileList.value = [];
      formDisabled.value = false;
    })
    .catch(() => {
      // 用户点击取消按钮，不做任何操作
    });
};

// 搜索并验证表单
const handleAdd = async () => {
  const valid = await formRef.value?.validate();
  // queryGoodList
  if (valid) {
    formValidated.value = true;
    try {
      const res = await InqueryAPI.getGoods({
        chooseType: 3,
        warehouseId: formData.warehouseId,
        startExpectedDeliveryDate: new Date(deliveryDate.value).getTime(),
        inquiryType: 1,
        warehouseName: formData.warehouseName,
      });

      // 如果有商品数据，禁用表单
      if (res.records && res.records.length > 0) {
        formDisabled.value = true;
      } else {
        ElMessage.warning(t("purchaseRequirements.message.noProductsFound"));
        formDisabled.value = false;
      }
    } catch (error) {
      ElMessage.error(t("purchaseRequirements.message.queryProductsFailed"));
      formDisabled.value = false;
    }
  }
};

// Add ref for dialog visibility
const showEditDialog = ref(false);

// Update handleAddProduct function
const handleAddProduct = () => {
  showEditDialog.value = true;
};

// Add handler for dialog close
const handleDialogClose = (product?: any) => {
  showEditDialog.value = false;
  if (product) {
    formData.products.push(product);
  }
};

const addGoods = (goods: any) => {
  formData.purchaseReqDetailDTOList = formData.purchaseReqDetailDTOList.concat((goods || [])
    .filter((item: any) => {
      return (
        formData.purchaseReqDetailDTOList.findIndex(
          (item2: any) => item2.productId == item.productId
        ) === -1
      );
    })
    .map((item: any) => ({
      productId: item.productId,
      productCode: item.productCode,
      productName: item.productName,
      brandName: item.brandName,
      unitName: item.unitName,
      unitId: item.unitId,
      unitCode: item.unitCode,
      firstCategoryId: item.firstCategoryId,
      secondCategoryId: item.secondCategoryId,
      thirdCategoryId: item.thirdCategoryId,
      productCategoryFullName: item.productCategoryFullName,
      imagesUrls: item.productImg,
      reqCount: null,
    })));
};

const handleDeliveryDateChange = (dates) => {
  if (!dates) {
    formData.expectedDeliveryTimeStar = "";
    formData.expectedDeliveryTimeEnd = "";
    return;
  }
  updateDeliveryTime();
};

const handleDeliveryTimeChange = () => {
  updateDeliveryTime();
};

const updateDeliveryTime = () => {
  if (!deliveryDate.value || !deliveryTime.value) return;

  const [startTime, endTime] = deliveryTime.value;

  formData.expectedDeliveryTime = new Date(
    `${deliveryDate.value} 00:00:00`
  ).getTime();
  const startDateTime = `${deliveryDate.value} ${startTime}`;
  const endDateTime = `${deliveryDate.value} ${endTime}`;

  formData.expectedDeliveryTimeStar = new Date(startDateTime).getTime();
  formData.expectedDeliveryTimeEnd = new Date(endDateTime).getTime();
};

// 移除商品
const handleRemoveProduct = (index: number) => {
  ElMessageBox.confirm(
    t("purchaseRequirements.message.deleteConfirm"),
    t("purchaseRequirements.message.warning"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      formData.purchaseReqDetailDTOList.splice(index, 1);
      ElMessage.success(t("purchaseRequirements.message.deleteSuccess"));
    })
    .catch(() => {});
};

import { useTagsViewStore } from "@/core/store";
const tagsViewStore = useTagsViewStore();
const route = useRoute();

const navigateAndCloseCurrentTab = async (path: string) => {
  // 先删除当前页面的标签
  await tagsViewStore.delView(route);

  // 等待标签删除完成后再进行路由跳转
  nextTick(() => {
    router.push(path);
  });
};

// 取消
const handleCancel = () => {
  ElMessageBox.confirm(
    t("purchaseRequirements.message.exitConfirm"),
    t("common.tips"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      navigateAndCloseCurrentTab("/pms/purchase/purchaseRequirements");
      // router.back();
    })
    .catch(() => {
      // 用户点击取消按钮，不做任何操作
    });
};
const submitLoading = ref(false);
// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    // 校验商品列表是否为空
    if (!formData.purchaseReqDetailDTOList.length) {
      ElMessage.error(t("purchaseRequirements.rules.productRequired"));
      return;
    }

    // 校验商品数量
    const invalidProducts = formData.purchaseReqDetailDTOList.filter(
      (item) => !item.reqCount || parseFloat(item.reqCount) < 0
    );
    if (invalidProducts.length > 0) {
      ElMessage.error(t("purchaseRequirements.rules.quantityPositive"));
      return;
    }

    submitLoading.value = true;
    
    const formDataCopy = JSON.parse(JSON.stringify(formData));
    formDataCopy.attachmentFiles = JSON.stringify(formDataCopy.attachmentFiles);
    await createPurchaseRequirement(formDataCopy);
    ElMessage.success(t("purchaseRequirements.message.submitSuccess"));
    router.push({
      path: "/pms/purchase/purchaseRequirements",
    });
  } catch (error) {
    console.error(error);
    ElMessage.error(t("purchaseRequirements.message.submitFailed"));
  } finally {
    submitLoading.value = false;
  }
};
</script>

<style scoped lang="scss">
.app-container {
  .sub-title {
    margin: 20px 0;
    font-weight: bold;
    font-size: 16px;
  }

  .table-operations {
    margin: 20px 0;
  }

  .other-info {
    margin-top: 20px;
  }

  .form-buttons {
    margin-top: 20px;
    text-align: right;
  }

  .upload-tip {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }
}

.product-info {
  display: flex;
  align-items: center;

  img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }

  .product-info-content {
    margin-left: 10px;

    .product-info-content-item {
      &.code {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #90979e;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }

      &.name {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #151719;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
.card-header {
  margin-bottom: 20px;
  .title {
    font-weight: bold;
    position: relative;
    padding-left: 10px;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #409eff;
    }
  }
}
.top-card-header {
  padding: 0px 10px 20px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #e5e7f3;
  border-radius: 4px 4px 0px 0px;
  cursor: pointer;
  .back-btn {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .status {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
}
</style>
