<template>
  <div class="dashboard-container">
    <div class="search-container">
      <el-form ref="headFormRef" :model="searchForm" :inline="true">
        <el-form-item
          prop="supplierName"
          :label="$t('purchasePayable.label.supplierName')"
        >
          <el-input
            v-model="searchForm.supplierName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="onSearchHandler"
            class="!w-[256px]"
          />
        </el-form-item>
        <el-form-item
          prop="creatTime"
          :label="$t('purchasePayable.label.creatTime')"
        >
          <el-date-picker
            :editable="false"
            class="!w-[370px]"
            v-model="searchForm.creatTime"
            type="daterange"
            :range-separator="t('purchasePayable.label.to')"
            :start-placeholder="$t('purchasePayable.label.startTime')"
            :end-placeholder="$t('purchasePayable.label.endTime')"
            value-format="YYYY-MM-DD"
            :placeholder="$t('common.placeholder.selectTips')"
          />
        </el-form-item>
        <el-form-item
          prop="billCode"
          :label="$t('purchasePayable.label.billCode')"
        >
          <el-input
            v-model="searchForm.billCode"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="onSearchHandler"
            class="!w-[256px]"
          />
        </el-form-item>
        <el-form-item
          prop="purchaseCode"
          :label="$t('purchasePayable.label.purchaseCode')"
        >
          <el-input
            v-model="searchForm.purchaseCode"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            @keyup.enter="onSearchHandler"
            class="!w-[256px]"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="onSearchHandler"
            v-hasPerm="['pms:finance:payableManagement:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button @click="onResetHandler">
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-card shadow="never" class="table-container">
      <div class="panelContent">
        <div
          class="panelItem"
          @click="changePanel(item)"
          :class="{ active: item.active }"
          v-for="(item, index) in panelList"
          :key="index"
        >
          {{ item.label }}
        </div>
      </div>
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="tableData"
        highlight-current-row
        stripe
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          :label="$t('purchasePayable.label.billCode')"
          prop="payableBillCode"
          width="180"
          show-overflow-tooltip
        />
        <el-table-column
          :label="$t('purchasePayable.label.purchaseCode')"
          prop="purchaseCode"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column
          :label="$t('purchasePayable.label.supplierName')"
          prop="supplierName"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          :label="$t('purchasePayable.label.purchaserUser')"
          prop="purchaserUser"
          show-overflow-tooltip
          min-width="120"
        />
        <!-- <el-table-column
          v-if="searchForm.payableBillStatus === 2"
          :label="$t('purchasePayable.label.paymentMethod')"
          prop="paymentMethod"
          show-overflow-tooltip
          min-width="120"
        /> -->
        <el-table-column
          :label="$t('purchasePayable.label.amount')"
          prop="amount"
          show-overflow-tooltip
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.currencyCode === "USD" ? "$" : "￥"
            }}{{ scope.row.amount }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchasePayable.label.paidAmount')"
          prop="paidAmount"
          show-overflow-tooltip
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.currencyCode === "USD" ? "$" : "￥"
            }}{{ scope.row.paidAmount }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchasePayable.label.changeAmount')"
          prop="changeAmount"
          show-overflow-tooltip
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.currencyCode === "USD" ? "$" : "￥"
            }}{{ scope.row.changeAmount }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchasePayable.label.checkCode')"
          prop="checkCode"
          show-overflow-tooltip
          min-width="180"
        />
        <!-- 生成账单时间 -->
        <el-table-column
          :label="$t('purchasePayable.label.creatTime')"
          prop="createTime"
          show-overflow-tooltip
          min-width="180"
          v-if="searchForm.payableBillStatus === 1"
        >
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
          </template>
        </el-table-column>
        <!-- 结清归档时间 -->
        <el-table-column
          :label="$t('purchasePayable.label.clearingArchivingTime')"
          prop="createTime"
          show-overflow-tooltip
          min-width="180"
          v-if="searchForm.payableBillStatus === 2"
        >
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.closeTime, "dateTime") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('purchasePayable.label.createUserName')"
          prop="createUserName"
          show-overflow-tooltip
          min-width="120"
        />
        <!-- 操作 -->
        <el-table-column
          :label="$t('common.handle')"
          fixed="right"
          min-width="230"
        >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              @click="checkDetail(scope.row.id)"
              v-hasPerm="['pms:finance:payableManagement:detail']"
            >
              {{ $t("purchasePayable.button.detail") }}
            </el-button>
            <el-button
              v-if="searchForm.payableBillStatus === 1"
              type="primary"
              size="small"
              link
              @click="selectPayment(scope.row)"
              v-hasPerm="['pms:finance:payableManagement:pay']"
            >
              {{ $t("purchasePayable.button.pay") }}
            </el-button>
            <el-button
              v-if="searchForm.payableBillStatus === 1"
              type="primary"
              size="small"
              link
              @click="closingfiling(scope.row.id)"
              v-hasPerm="['pms:finance:payableManagement:close']"
            >
              {{ $t("purchasePayable.button.closingfiling") }}
            </el-button>
            <el-button
              type="primary"
              size="small"
              link
              @click="printAction(scope.row)"
            >
              {{ $t("purchasePayable.button.print") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="paginationInfo.pageNo"
        v-model:limit="paginationInfo.pageSize"
        @pagination="onPaginationChangeHandler"
      />
    </el-card>
    <!-- 打印 -->
    <div id="printDom" v-print="printObj"></div>
    <div style="display: none">
      <div id="printHtml" class="printpage">
        <div class="print-nb">
          <div class="header-title mt-20">
            {{ $t("purchasePayable.label.printTitle") }}
          </div>
          <el-form class="print_form mt-10" inline label-position="right">
            <el-row>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.billCode') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{ printData.payableBillCode }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.purchaseCode') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{ printData.purchaseCode }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.purchaserUser') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{ printData.purchaserUser }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.billingStatus') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{
                      printData.payableBillStatus === 1
                        ? $t("purchasePayable.label.due")
                        : $t("purchasePayable.label.closedAccount")
                    }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.checkCode') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{ printData.checkCode }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.supplierName') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{ printData.supplierName }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.creatTime') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{ parseDateTime(printData.createTime, "dateTime") }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="$t('purchasePayable.label.closedTime') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    <span v-if="printData.closeTime">
                      {{ parseDateTime(printData.closeTime, "dateTime") }}
                    </span>
                    <span v-else>-</span>
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item
                  :label="$t('purchasePayable.label.remark') + `:`"
                  class="item—form"
                >
                  <span class="detail" style="width: auto">
                    {{ printData.remark || "-" }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div>
            <div class="primary18-font">
              {{ $t("purchasePayable.label.purchaseOrderDetail") }}
            </div>
            <table
              border="1"
              class="custom-table"
              cellpadding="5"
              cellspacing="0"
            >
              <thead>
                <tr>
                  <td>{{ $t("purchasePayable.label.sort") }}</td>
                  <td>{{ $t("purchasePayable.label.productName") }}</td>
                  <td>{{ $t("purchasePayable.label.productCount") }}</td>
                  <td>{{ $t("purchasePayable.label.productAmount") }}</td>
                </tr>
              </thead>
              <tbody class="paging">
                <tr
                  v-for="(item, index) in printData.payableBillItemList"
                  :key="`akl-${index}`"
                >
                  <td>{{ index + 1 }}</td>
                  <td>{{ item.productName }}</td>
                  <td>{{ item.productQuantity }}</td>
                  <td>
                    {{ printData.currencyCode === "USD" ? "$" : "￥"
                    }}{{ item.productAmount }}
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="mb20px mt20px total-row">
              <span>{{ t("purchasePayable.label.total") }}</span>
              <span class="ml10px">
                {{ t("purchasePayable.label.productTotalAmount") }}:
              </span>
              <span class="total-amount">
                {{ printData.currencyCode === "USD" ? "$" : "￥"
                }}{{ printData.totalAmount }}
              </span>
              <span class="ml10px">
                {{ t("purchasePayable.label.changeAmount") }}:
              </span>
              <span class="total-amount">
                {{ printData.currencyCode === "USD" ? "$" : "￥"
                }}{{ printData.changeAmount }}
              </span>
              <span class="ml10px">
                {{ t("purchasePayable.label.amountTotal") }}:
              </span>
              <span class="total-amount">
                {{ printData.currencyCode === "USD" ? "$" : "￥"
                }}{{ printData.amount }}
              </span>
            </div>
          </div>
          <div v-if="printData.payableBillStatus === 2">
            <div class="primary18-font">
              {{ $t("purchasePayable.button.pay") }}
            </div>
            <div class="mt10px">
              <span>{{ t("purchasePayable.label.paymentAmount") }}:</span>
              <span class="total-amount">
                {{ printData.currencyCode === "USD" ? "$" : "￥" }}
                {{ printData.paidAmount - printData.refundAmount }}
              </span>
              <span class="ml10px">
                {{ t("purchasePayable.label.refundTAmount") }}:
              </span>
              <span class="total-amount">
                {{ printData.currencyCode === "USD" ? "$" : "￥"
                }}{{ printData.refundAmount }}
              </span>
            </div>
            <table
              border="1"
              class="custom-table"
              cellpadding="5"
              cellspacing="0"
            >
              <thead>
                <tr>
                  <td>{{ $t("purchasePayable.label.receiverUserName") }}</td>
                  <td>{{ $t("purchasePayable.label.closeType") }}</td>
                  <td>{{ $t("purchasePayable.label.payType") }}</td>
                  <td>{{ $t("purchasePayable.label.paymentAmount") }}</td>
                  <td>{{ $t("purchasePayable.label.exchangeRate") }}</td>
                  <td>{{ $t("purchasePayable.label.paymentTime") }}</td>
                </tr>
              </thead>
              <tbody class="paging">
                <tr
                  v-for="(item, index) in printData.payableBillPaymentList"
                  :key="`akl-${index}`"
                >
                  <td>{{ item.receiverUserName }}</td>
                  <td>
                    <div>
                      {{
                        item.transactionType === 1
                          ? $t("purchasePayable.button.pay")
                          : $t("purchasePayable.button.strikeBalance")
                      }}
                    </div>
                    <div>{{ item.purchaseReceivableCode }}</div>
                  </td>
                  <td>{{ getPayTypeName(item.paymentMethod) }}</td>
                  <td>
                    {{ item.currencyCode === "USD" ? "$" : "￥"
                    }}{{ item.paymentAmount }}
                  </td>
                  <td>{{ item.exchangeRate }}</td>
                  <td>{{ parseDateTime(item.paymentTime, "dateTime") }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import tableMixin from "@/modules/wms/mixins/table";
import PurchasePayableAPI, {
  PurchasePayablePageQuery,
  PurchasePayableInfo,
} from "@/modules/pms/api/purchasePayable";
defineOptions({
  name: "PayableManagement",
  inheritAttrs: false,
});
const { proxy } = getCurrentInstance();
const { t } = useI18n();
import { parseDateTime } from "@/core/utils/index.js";
const printData = ref<PurchasePayableInfo>({});
let searchForm = reactive<PurchasePayablePageQuery>({
  payableBillStatus: 1,
  billCode: "",
  creatTime: [],
  page: 1,
  limit: 20,
});
function formatParamsCallbackHandle(params) {
  if (params["creatTime"].length > 0) {
    params.startCreateTime = new Date(
      `${params.creatTime[0]} 00:00:00`
    ).getTime();
    params.endCreateTime = new Date(
      `${params.creatTime[1]} 23:59:59`
    ).getTime();
    delete params["creatTime"];
  }
}
const {
  loading,
  tableData,
  total,
  paginationInfo,
  headFormRef,
  router,
  path,
  onSearchHandler,
  onResetHandler,
  onPaginationChangeHandler,
  onDeleteHandler,
} = tableMixin({
  searchForm,
  tableGetApi: PurchasePayableAPI.getPageList,
  formatParamsCallback: formatParamsCallbackHandle,
});

const panelList = reactive([
  {
    label: proxy.$t("purchasePayable.button.tabPayment"),
    value: 1,
    active: true,
  },
  {
    label: proxy.$t("purchasePayable.button.tabClose"),
    value: 2,
    active: false,
  },
]);
const changePanel = (data) => {
  panelList.map((item) => (item.active = false));
  data.active = true;
  searchForm.payableBillStatus = data.value;
  paginationInfo.pageNo = 1;
  paginationInfo.pageSize = 15;
  onSearchHandler();
};
/**付款*/
function selectPayment(row: PurchasePayableInfo) {
  router.push({
    path: "/pms/finance/payment",
    query: { id: row.id, billCode: row.billCode },
  });
}
/**明细*/
function checkDetail(id?: string) {
  router.push({
    path: "/pms/finance/payableDetails",
    query: { id: id },
  });
}
/**结清归档*/
function closingfiling(id?: string) {
  ElMessageBox.confirm(
    t("purchasePayable.message.closeTip"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      PurchasePayableAPI.closebill(id)
        .then((data) => {
          onSearchHandler();
          loading.value = false;
        })
        .finally(() => {
          loading.value = false;
        });
    },
    () => {}
  );
}

/**打印*/
function printAction(rowData: PurchasePayableInfo) {
  loading.value = true;
  /** 查询采购单详情 */
  PurchasePayableAPI.detailPayableBill(rowData.id)
    .then((data) => {
      printData.value = data;
      setTimeout(() => {
        const printDom = document.getElementById("printDom");
        printDom && printDom.click();
      }, 1000);
    })
    .finally(() => {
      loading.value = false;
    });
}
// 打印点击
const printObj = {
  id: "printHtml",
  beforeOpenCallback(vue) {},
  openCallback(vue) {
    loading.value = false;
    // printData.value = {};
  },
  closeCallback(vue) {
    loading.value = false;
    // printData.value = {};
  },
};
function getPayTypeName(paymentMethod: any) {
  switch (paymentMethod) {
    case 1:
      return "转账";
      break;
    case 2:
      return "现金";
      break;
    case 3:
      return "线下支付宝";
      break;
    default:
      return "线下微信";
      break;
  }
}

onActivated(() => {
  onSearchHandler();
});
</script>

<style lang="scss" scoped>
.panelContent {
  display: flex;
  border-bottom: 1px solid #f2f3f4;
  width: 100%;
  margin-bottom: 16px;

  .panelItem {
    font-size: 14px;
    color: #151719;
    padding: 10px 39px;
    cursor: pointer;

    &.active {
      color: var(--el-color-primary);
      border-bottom: 2px solid var(--el-color-primary);
    }
  }
}

.print-nb {
  margin: 0;
  background-color: rgb(255, 255, 255);
  padding: 16px;
  box-sizing: border-box;

  .header-title {
    font-size: 32px;
    font-weight: 500;
    color: #000000;
    line-height: 20px;
    text-align: center;
    margin-bottom: 20px;
  }

  .image {
    width: 87px;
    height: 87px;
    border: 1px solid #dcdfe6;
    margin-top: 10px;
  }

  /* 设置表格的线条颜色 */
  .custom-table {
    border-collapse: collapse;
    /* 使表格的边框线看起来是单一的线条 */
    border: 1px solid #ff0000;
    width: 100%;
    margin-top: 20px;

    /* 表头颜色 */
    thead {
      background: rgba(240, 241, 245, 0.85);
    }

    th {
      border: 1px solid #dcdcdc;
    }

    tr {
      border: 1px solid #dcdcdc;
    }

    td {
      border: 1px solid #dcdcdc;
    }
  }

  .ui-descriptions {
    --el-border-color-lighter: #dcdcdc;

    .el-descriptions__table {
      table-layout: fixed;
      --el-descriptions-item-bordered-label-background: rgba(
        240,
        241,
        245,
        0.85
      );

      .el-descriptions__label {
        background: rgba(240, 241, 245, 0.85);
        width: 135px;
        font-weight: 400;
        color: #909090;
        line-height: 24px;
        font-size: 18px;
        padding: 2px;
      }

      .el-descriptions__content {
        font-weight: 400;
        color: #252829;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 24px;
        padding: 2px;
        font-size: 18px;
      }
    }
  }
}
</style>
<style media="print" lang="scss">
@media print {
  @page {
    size: auto;
    margin-top: 3mm;
    margin-bottom: 0mm;
  }

  body {
    height: auto;
  }

  .printpage {
    height: 100%; // 根据实际情况进行分页设置
    width: 100%;
  }
  .primary18-font {
    font-weight: 600;
  }
}
</style>
