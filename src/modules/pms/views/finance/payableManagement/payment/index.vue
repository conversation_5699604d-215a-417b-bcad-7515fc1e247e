<template>
  <div class="app-container">
    <div class="reconcile" v-loading="loading">
      <!-- <div class="page-header">
        <el-button link @click="handleClose">
          <el-icon>
            <ArrowLeft />
          </el-icon>
          {{ t("purchaseReceiveables.detail.billNumber") }}：{{
            detailData.payableBillCode
          }}
        </el-button>
        <el-tag type="success">
          {{
            detailData.payableBillStatus === 1
              ? $t("purchasePayable.label.due")
              : $t("purchasePayable.label.closedAccount")
          }}
        </el-tag>
      </div> -->
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="handleClose"
        />
        <span class="code" @click="handleClose">
          {{ t("purchasePayable.label.billNumber") }}：{{
            detailData.payableBillCode
          }}
        </span>
        <!-- 收款状态：1->待收款，2->已结清 -->
        <span
          class="contract status ml-10px"
          :class="
            detailData.payableBillStatus === 1 ? 'unwokring' : 'executing'
          "
        >
          {{
            detailData.payableBillStatus === 1
              ? $t("purchasePayable.label.due")
              : $t("purchasePayable.label.closedAccount")
          }}
        </span>
      </div>
      <div class="page-content">
        <div class="base-info">
          <div class="card-title mb-20px">
            {{ $t("purchasePayable.label.basicInformation") }}
          </div>
          <div class="base-info-desc">
            <div class="item-list">
              <div class="item">
                <span>{{ $t("purchasePayable.label.billCode") }}:</span>
                {{ detailData.payableBillCode }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.purchaseCode") }}:</span>
                {{ detailData.purchaseCode }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.purchaseType") }}:</span>
                {{
                  detailData.purchaseType === 1
                    ? $t("purchasePayable.label.supplierDirect")
                    : $t("purchasePayable.label.marketPurchase")
                }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.purchaserUser") }}:</span>
                {{ detailData.purchaserUser }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.billingStatus") }}:</span>
                {{
                  detailData.payableBillStatus === 1
                    ? $t("purchasePayable.label.due")
                    : $t("purchasePayable.label.closedAccount")
                }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.creatTime") }}:</span>
                {{ parseDateTime(detailData.createTime, "dateTime") }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.deliveryTime") }}:</span>
                {{ parseDateTime(detailData.planDeliveryDate, "dateTime") }}
              </div>
              <div class="item">
                <span>{{ $t("purchasePayable.label.receipt") }}:</span>
                {{
                  detailData.orderSource
                    ? $t("purchasePayable.label.manualAddition")
                    : $t("purchasePayable.label.procurementTask")
                }}
              </div>
            </div>
          </div>
        </div>
        <div class="product-info mt10px">
          <div class="card-title mb-20px">
            {{ $t("purchasePayable.label.productInfo") }}
          </div>
          <el-table
            :data="detailData.payableBillItemList"
            highlight-current-row
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column
              :label="$t('purchasePayable.label.productImage')"
              prop="productImage"
              min-width="100"
            >
              <template #default="scope">
                <div class="product-div">
                  <div class="picture">
                    <img :src="scope.row.productImage" alt="" />
                  </div>
                  <!-- <div class="product">
                    <div class="product-code">商品编码：{{ scope.row.productCode }}</div>
                    <div class="product-name">{{ scope.row.productName }}</div>
                  </div> -->
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('purchasePayable.label.productName')"
              prop="productName"
              min-width="100"
            />
            <el-table-column
              :label="$t('purchasePayable.label.purchaseUnit')"
              prop="productUnit"
              min-width="100"
            />
            <el-table-column
              :label="$t('purchasePayable.label.productQuantity')"
              prop="productQuantity"
              min-width="100"
            />
            <el-table-column
              :label="$t('purchasePayable.label.productAmount')"
              prop="productAmount"
            >
              <template #default="scope">
                {{ scope.row.currencyCode === "USD" ? "$" : "￥"
                }}{{ scope.row.productAmount }}
              </template>
            </el-table-column>
          </el-table>
          <div class="mb20px mt20px total-row">
            <span>{{ t("purchasePayable.label.total") }}:</span>
            <span class="ml10px">{{ t("purchasePayable.label.amount") }}:</span>
            <span class="total-amount">
              {{ detailData.currencyCode === "USD" ? "$" : "￥"
              }}{{ detailData.amount }}
            </span>
            <span class="ml10px">
              {{ t("purchasePayable.label.changeAmount") }}:
            </span>
            <span class="total-amount">
              {{ detailData.currencyCode === "USD" ? "$" : "￥"
              }}{{ detailData.changeAmount }}
            </span>
          </div>
        </div>
        <div class="settle-account">
          <div class="card-title">
            {{ $t("purchasePayable.label.settleAccount") }}
          </div>
          <div class="total-row">
            <span>{{ t("purchasePayable.label.total") }}:</span>
            <span class="ml10px">{{ t("purchasePayable.label.amount") }}:</span>
            <span class="total-amount">
              <span>
                {{ detailData.currencyCode === "USD" ? "$" : "￥" }}
              </span>
              {{ detailData.amount }}
            </span>
            <!-- 剩余应付金额start -->
            <span class="ml10px">
              {{ t("purchasePayable.label.pemainingPayableAmount") }}:
            </span>
            <span class="total-amount">
              <span>
                {{ detailData.currencyCode === "USD" ? "$" : "￥" }}
              </span>
              {{
                Number(detailData?.amount || 0) -
                Number(detailData?.paidAmount || 0)
              }}
            </span>
            <!-- 剩余应付金额end -->
            <!-- 实付金额start -->
            <span class="ml10px">
              {{ t("purchasePayable.label.paidAmount") }}:
            </span>
            <span class="total-amount">
              <span>
                {{ detailData.currencyCode === "USD" ? "$" : "￥" }}
              </span>
              {{ detailData.paidAmount || 0 }}
            </span>
            <!-- 实付金额end -->
            <!-- 待冲账金额start -->
            <span class="ml10px">
              {{ t("purchasePayable.label.refundAmount") }}:
            </span>
            <span class="total-amount">
              <span>
                {{ detailData.currencyCode === "USD" ? "$" : "￥" }}
              </span>
              {{ detailData.refundAmount > 0 ? detailData.refundAmount : "0" }}
            </span>
            <!--  待冲账金额end -->
          </div>
          <el-form
            :model="form"
            :rules="rules"
            ref="formRef"
            label-width="auto"
            label-position="left"
          >
            <div class="form-item-container">
              <el-form-item
                :label="$t('purchasePayable.label.receiverUserName')"
                prop="receiverUserId"
              >
                <el-select
                  v-model="form.receiverUserId"
                  :placeholder="$t('common.placeholder.selectTips')"
                  clearable
                >
                  <el-option
                    v-for="item in receiverUserList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <!-- 交易类型 -->
              <el-form-item
                :label="$t('purchasePayable.label.transactionType')"
                prop="transactionType"
              >
                <el-select
                  v-model="form.transactionType"
                  :placeholder="$t('common.placeholder.selectTips')"
                  @change="reconciliationZeroAmountChange"
                  clearable
                >
                  <el-option
                    v-for="item in tradingTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <!-- 冲账账单 -->
              <el-form-item
                :label="$t('purchasePayable.label.purchaseReceivabl')"
                v-if="form.transactionType === 2"
                prop="purchaseReceivableCode"
              >
                <el-select
                  v-model="form.purchaseReceivableCode"
                  :placeholder="$t('common.placeholder.selectTips')"
                  clearable
                  @change="purchaseReceivableChange"
                >
                  <el-option
                    v-for="item in purchaseReceivableList"
                    :key="item.purchaseReceivableCode"
                    :label="item.purchaseReceivableCode"
                    :value="item.purchaseReceivableCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('purchasePayable.label.paymentMethod')"
                prop="paymentMethod"
              >
                <el-select
                  v-model="form.paymentMethod"
                  :placeholder="$t('common.placeholder.selectTips')"
                  clearable
                >
                  <el-option
                    v-for="item in payTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <!-- 付款金额 -->
              <el-form-item
                :label="$t('purchasePayable.label.paymentAmount')"
                prop="paymentAmount"
              >
                <el-input
                  v-model="form.paymentAmount"
                  :placeholder="$t('common.placeholder.inputTips')"
                  clearable
                  :disabled="form.transactionType === 2"
                />
              </el-form-item>
              <el-form-item
                :label="$t('purchasePayable.label.paymentTime')"
                prop="paymentTime"
              >
                <el-date-picker
                  :editable="false"
                  class="!w-[210px]"
                  v-model="form.paymentTime"
                  type="date"
                  :placeholder="$t('common.placeholder.selectTips')"
                />
              </el-form-item>
              <el-form-item
                :label="$t('purchasePayable.label.currencyCode')"
                prop="currencyCode"
              >
                <el-select
                  v-model="form.currencyCode"
                  :placeholder="$t('common.placeholder.selectTips')"
                  clearable
                  :disabled="detailData.currencyCode !== null"
                >
                  <el-option
                    v-for="item in closeCodeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                :label="$t('purchasePayable.label.exchangeRate')"
                prop="exchangeRate"
              >
                <el-input
                  v-model="form.exchangeRate"
                  :placeholder="$t('common.placeholder.inputTips')"
                  clearable
                />
              </el-form-item>
            </div>
            <el-form-item
              :label="$t('purchasePayable.label.paymentVoucher')"
              prop="paymentVoucher"
            >
              <UploadMultiple
                @update:model-value="onChangeMultiple"
                ref="detailPicsRef"
                :limit="1"
                :formRef="formRef"
                listType="text"
                :fileType="filtType"
                :fileSize="10"
                class="modify-multipleUpload"
                name="detailPic"
              />
            </el-form-item>
            <el-form-item
              :label="$t('purchasePayable.label.remark')"
              prop="paymentRemark"
            >
              <el-input
                :rows="4"
                type="textarea"
                show-word-limit
                maxlength="100"
                v-model="form.paymentRemark"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="page-footer">
          <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="doPurchaseReconcile"
            v-hasPerm="['pms:finance:payableManagement:pay']"
          >
            {{ $t("purchasePayable.button.pay") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Reconcile",
  inheritAttrs: false,
});

import { useRoute, useRouter } from "vue-router";
import PurchasePayableAPI, {
  PurchasePayableInfo,
  PurchasePayableForm,
} from "@/modules/pms/api/purchasePayable";
import { parseDateTime } from "@/core/utils/index.js";

const { proxy } = getCurrentInstance();
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const formRef = ref();
const id = route.query.id;
const loading = ref(false);
const submitLoading = ref(false);
const filtType = ["jpg", "jpeg", "png"];
const detailData = ref<PurchasePayableInfo>({});
const form = reactive<PurchasePayableForm>({});
const rules = reactive({
  receiverUserId: [
    {
      required: true,
      message: proxy.$t("purchasePayable.rules.receiverUserName"),
      trigger: "blur",
    },
  ],
  transactionType: [
    {
      required: true,
      message: proxy.$t("purchasePayable.rules.transactionType"),
      trigger: "blur",
    },
  ],
  paymentMethod: [
    {
      required: true,
      message: proxy.$t("purchasePayable.rules.paymentMethod"),
      trigger: "blur",
    },
  ],
  paymentVoucher: [
    {
      required: true,
      message: proxy.$t("purchasePayable.rules.paymentVoucher"),
      trigger: "blur",
    },
  ],
  paymentTime: [
    {
      required: true,
      message: proxy.$t("purchasePayable.rules.paymentTime"),
      trigger: "blur",
    },
  ],
  purchaseReceivableCode: [
    {
      required: true,
      message: proxy.$t("purchasePayable.rules.purchaseReceivableCode"),
      trigger: "blur",
    },
  ],
  paymentAmount: [
    {
      required: true,
      message: proxy.$t("purchasePayable.rules.paymentAmount"),
      trigger: "blur",
    },
    {
      pattern: /(^(0*[1-9]\d{0,7}|0*[1-9]\d{0,7}\.\d{1,3}|0\.\d{1,3})$)/,
      message: t("purchasePayable.rules.paymentAmountFomart"),
      trigger: "blur",
    },
  ],
});
function onChangeMultiple(val: any) {
  form.paymentVoucher = val ? val : "";
}

function handleClose() {
  router.go(-1);
}
//领款方
const receiverUserList = ref([]);
//冲账列表
const purchaseReceivableList = ref([]);
//交易类型
const tradingTypeList = reactive([
  { label: proxy.$t("purchasePayable.button.pay"), value: 1 },
  { label: proxy.$t("purchasePayable.button.strikeBalance"), value: 2 },
]);
//结算币种
const closeCodeList = reactive([
  { label: proxy.$t("purchasePayable.button.RMB"), value: "CNY" },
  { label: proxy.$t("purchasePayable.button.dollar"), value: "USD" },
]);
//支付方式
const payTypeList = reactive([
  { label: proxy.$t("purchasePayable.button.transferMoney"), value: 1 },
  { label: proxy.$t("purchasePayable.button.cash"), value: 2 },
  { label: proxy.$t("purchasePayable.button.alipay"), value: 3 },
  { label: proxy.$t("purchasePayable.button.wechat"), value: 4 },
]);
/** 查询采购单详情 */
function queryPurchaseReconcileDetail() {
  loading.value = true;
  PurchasePayableAPI.detailPayableBill(id)
    .then((data) => {
      detailData.value = data;
      detailData.value.refundAmount = "";
      form.currencyCode = data.currencyCode;
      if (detailData.value.purchaseType == 1) {
        //直供
        receiverUserList.value.push({
          label: detailData.value.supplierName,
          value: detailData.value.supplierId,
        });
      } else {
        //自采
        receiverUserList.value.push({
          label: detailData.value.purchaserUser,
          value: detailData.value.purchaserUserId,
        });
      }
      loading.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}
function getRechargeableList() {
  PurchasePayableAPI.rechargeableList(id)
    .then((data: any) => {
      if (data && data.length > 0) {
        purchaseReceivableList.value = data.filter(
          (val: any) => val.currencyCode == detailData.value.currencyCode
        );
      }
      // purchaseReceivableList.value = data;
      const receivableAmount = computed(() => {
        return purchaseReceivableList.value.reduce(
          (sum, item: any) => sum + Number(item.pendingReceivableAmount) * 100,
          0
        );
      });
      detailData.value.refundAmount = receivableAmount.value / 100;
    })
    .finally(() => {});
}

function reconciliationZeroAmountChange() {
  // form.amountTotal = parseFloat(String(parseFloat(form.totalReceivedGoodsAmount).toFixed(2) - parseFloat(form.reconciliationZeroAmount).toFixed(2))).toFixed(2);
  form.paymentAmount = null;
  // detailData.value.refundAmount = 0;
  form.purchaseReceivableCode = "";
  form.paymentAmount = "";
}
function purchaseReceivableChange() {
  const receivableAmount = purchaseReceivableList.value.filter(
    (item: any) => item.purchaseReceivableCode === form.purchaseReceivableCode
  )[0].receivableAmount;
  // detailData.value.refundAmount = receivableAmount;
  form.paymentAmount = receivableAmount;
}
/**付款*/
function doPurchaseReconcile() {
  // let amount =
  //   Number(detailData.value?.amount || 0) -
  //   Number(detailData.value?.paidAmount || 0);
  // if (Number(form.paymentAmount || 0) > amount && form.transactionType === 2) {
  //   return ElMessage.error(t("purchasePayable.message.submitTip"));
  // }
  form.payableBillId = id;
  if (form.receiverUserId) {
    form.receiverUserName = receiverUserList.value.filter(
      (item) => item.value === form.receiverUserId
    )[0].label;
  }
  if (form.paymentTime && typeof form.paymentTime !== "number") {
    form.paymentTime = form.paymentTime?.getTime();
  }
  const dataCopy = JSON.parse(JSON.stringify(form));
  if (dataCopy && Array.isArray(dataCopy.paymentVoucher)) {
    dataCopy.paymentVoucher = JSON.stringify(dataCopy.paymentVoucher);
  }

  formRef.value.validate((valid) => {
    if (!valid) return;
    ElMessageBox.confirm(
      t("purchasePayable.message.reconciledTips"),
      t("purchasePayable.message.reconciled"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(
      () => {
        submitLoading.value = true;
        PurchasePayableAPI.payableBill(dataCopy)
          .then((res) => {
            ElMessage.success(
              t("purchasePayable.message.sendPurchaseOrderSucess")
            );
            submitLoading.value = false;
            handleClose();
          })
          .finally(() => {
            submitLoading.value = false;
          });
      },
      () => {
        submitLoading.value = false;
        ElMessage.info(t("purchasePayable.message.sendPurchaseOrderConcel"));
      }
    );
  });
}

onMounted(async () => {
  await queryPurchaseReconcileDetail();

  setTimeout(() => {
    getRechargeableList();
  }, 500);
});
</script>
<style scoped lang="scss">
.reconcile {
  background: #ffffff;
  border-radius: 4px;
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .card-header {
    padding: 0px 10px 20px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: inset 0px -1px 0px 0px #e5e7f3;
    border-radius: 4px 4px 0px 0px;
    cursor: pointer;
    .code {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #151719;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
    .back-btn {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    .status {
      /* font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal; */
    }
  }

  .page-content {
    padding: 5px;

    .base-info {
      .base-info-desc {
        .item-list {
          display: flex;
          flex-wrap: wrap;
          align-items: center;

          .item {
            width: 25%;
            padding: 10px 0;
            color: #151719;
            font-size: 14px;

            span {
              display: inline-block;
              color: #90979e;
            }
          }
        }
      }
    }

    .total-row {
      margin-top: 10px;
      margin-bottom: 20px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
      line-height: 20px;
      text-align: left;
      font-style: normal;

      .total-amount {
        color: #c00c1d;
      }
    }

    .form-item-container {
      display: flex;
      flex-wrap: wrap;
    }

    .form-item-container .el-form-item {
      margin-right: 20px;
      width: 23%;
    }

    .product-div {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .picture {
        margin-right: 16px;

        img {
          width: 80px;
          height: 80px;
        }
      }

      .product {
        font-family:
          PingFangSC,
          PingFang SC;
        font-style: normal;

        .product-code {
          font-weight: 400;
          font-size: 14px;
          color: #90979e;
        }

        .product-name {
          font-weight: 500;
          font-size: 14px;
          color: #52585f;
        }
      }
    }
  }

  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 22px 30px;
    border-top: 1px solid #e5e7f3;
  }

  :deep(.el-timeline-item__timestamp) {
    font-size: 16px;
    color: #151719;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
  }

  :deep(.el-card) {
    width: 100%;
    background: linear-gradient(180deg, #fafafa 0%, #ffffff 100%);
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
  }

  .fileName_style {
    align-items: center;
  }

  .form-item-spacing {
    margin-left: 20px;
    margin-right: 20px;
  }
}
</style>
