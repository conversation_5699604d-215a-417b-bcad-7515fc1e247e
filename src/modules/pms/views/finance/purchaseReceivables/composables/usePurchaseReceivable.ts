import { ref } from "vue";
import { getPurchaseReceivableDetail } from "@pms/api/purchaseReceivables";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";


export function usePurchaseReceivable() {
  const { t } = useI18n();
  const basicInfo = ref({
    currencyCode: "",
  });

  const refundDetails = ref([]);
  const totalAmount = ref(0);

  const loadDetailData = async (id: string) => {
    try {
      const response = await getPurchaseReceivableDetail(id);
      basicInfo.value = response;
      refundDetails.value = [
        {
          sequence: 1,
          refundOrder: response.workOrderCode, // 退款工单
          amount: response.receivableAmount, // 应收金额
          applicationDate: response.applyDate, // 申请日期
          remarks: response.applyRemark, // 备注
        },
      ];
      totalAmount.value = response.receivableAmount;
    } catch (error) {
      ElMessage.error(t("purchaseReceivables.message.getDetailFailed"));
      console.error("Failed to load detail:", error);
    }
  };

  return {
    basicInfo,
    refundDetails,
    totalAmount,
    loadDetailData,
  };
}
