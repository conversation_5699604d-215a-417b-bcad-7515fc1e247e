<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import type { FormInstance } from "element-plus";
import Print from "./components/print.vue";
import { ElMessage } from "element-plus";
import { parseTime } from "@/core/utils";
import {
  getPurchaseReceivablesList, // 获取收款单列表
  confirmSettlement, // 结清归档
  getPurchaseReceivableDetail,
} from "@pms/api/purchaseReceivables";
import SupplierAPI from "@pms/api/supplier";
import PurchasePersonnelAPI from "@pms/api/purchasePersonnel";
import { useCommon } from "@/modules/pms/composables/common";

defineOptions({
  name: "PurchaseReceivables",
  inheritAttrs: false,
});

const { currencyFilter } = useCommon();
const { t } = useI18n();
const formRef = ref<FormInstance>();
// const printRef = ref<InstanceType<typeof Print>>();
const printRef = ref();
// const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const activeTab = ref("pending");

const router = useRouter();
const searchForm = ref({
  createTimeEnd: "", // 生成账单结束时间
  createTimeStart: "", // 生成账单开始时间
  limit: 20, //
  page: 1, //
  purchaseOrderCode: "", // 采购单号
  purchaseReceivableCode: "", // 应收账单编号
  purchaseUser: "", // 采购员名称
  receivableStatus: 1, // 收款状态：1->待收款，2->已结清
  supplierName: "", // 供应商名称
  workOrderCode: "", // 工单号
  billTimeRange: [],
});

// 模拟数据接口
interface PurchaseReceivable {
  applyDate: string; // 申请日期
  applyRemark: string; // 申请备注
  currencyCode: string; // 交易币种：CNY->人民币，USD->美元
  exchangeRate: number; // 汇率
  id: number; // 主键id
  payableBillCode: string; // 应付账单编号
  purchaseBillCode: string; // 采购单号
  purchaseReceivableCode: string; // 应收单号
  purchaseUser: string; // 采购员
  purchaseUserId: number; // 采购员id
  receivableAmount: number; // 收款金额
  receivableMethod: number; // 收款方式：1->转账，2->现金，3->线下支付宝, 4->线下微信
  receivableRemark: string; // 收款备注
  receivableStatus: number; // 收款状态：1->待收款，2->已结清
  receivableTime: string; // 收款时间
  receivableVoucher: string; // 收款凭证
  settlementType: number; // 结算类型：1->付款，2->冲账结算
  supplierId: number; // 供应商id
  supplierName: string; // 供应商名称
  workOrderCode: string; // 工单号
}

const receiveMethodObj = {
  1: t("purchaseReceivables.paymentMethod.transfer"),
  2: t("purchaseReceivables.paymentMethod.cash"),
  3: t("purchaseReceivables.paymentMethod.alipay"),
  4: t("purchaseReceivables.paymentMethod.wechat"),
};
const settlementTypeObj = {
  1: t("purchaseReceivables.settlementType.payment"),
  2: t("purchaseReceivables.settlementType.offsetSettlement"),
};

const supplierList = ref([]);
const getSupplierList = async () => {
  const data = await SupplierAPI.getSupplierListAll();
  supplierList.value = data;
};

const purchasePersonnelList = ref([]);
const getPurchasePersonnelList = async () => {
  const data = await PurchasePersonnelAPI.getPerchasePersonnelList();
  purchasePersonnelList.value = data;
};

const handleBillTimeChange = (val: any) => {
  searchForm.value.billTimeRange = val;
  searchForm.value.createTimeStart = new Date(val?.[0] + " 00:00:00").getTime();
  searchForm.value.createTimeEnd = new Date(val?.[1] + " 23:59:59").getTime();
};
// 初始化表格数据
const tableData = ref<PurchaseReceivable[]>([]);

const handleSearch = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm.value,
      page: currentPage.value,
      pageSize: pageSize.value,
      // status: activeTab.value,
      // startTime: searchForm.value.billTimeRange?.[0],
      // endTime: searchForm.value.billTimeRange?.[1],
      // receivableStatus: activeTab.value,
    };

    delete params.billTimeRange;

    const response = await getPurchaseReceivablesList(params);
    tableData.value = response.records;
    total.value = parseInt(response.total);
  } catch (error) {
    ElMessage.error(t("purchaseReceivables.error.fetchListFailed"));
    console.error("Failed to fetch purchase receivables:", error);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields();
    searchForm.value.createTimeStart = "";
    searchForm.value.createTimeEnd = "";
    currentPage.value = 1;
    pageSize.value = 20;
    handleSearch();
  }
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  handleSearch();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  handleSearch();
};

// 处理函数
const handleDetails = (row: PurchaseReceivable) => {
  console.log("查看明细", row);
  router.push({
    path: "/pms/finance/purchaseReceivablesDetails",
    query: {
      id: row.id,
      payableBillCode: row.payableBillCode,
      receivableStatus: row.receivableStatus,
    },
  });
};

const handlePurchaseOrderDetails = (row: PurchaseReceivable) => {
  router.push({
    path: "/pms/purchase/purchaseOrderDetail",
    query: {
      orderCode: row.purchaseBillCode,
    },
  });
};

const handleReceipt = (row: PurchaseReceivable) => {
  router.push({
    path: "/pms/finance/purchaseCollection",
    query: {
      id: row.id,
      payableBillCode: row.payableBillCode,
    },
  });
};

const handleConfirm = (row: PurchaseReceivable) => {
  ElMessageBox.confirm(
    t('purchaseReceivables.message.confirmSettlement'),
    t('purchaseReceivables.message.tip'),
    {
      confirmButtonText: t('purchaseReceivables.button.confirm'),
      cancelButtonText: t('purchaseReceivables.button.cancel'),
      type: "warning",
    }
  )
    .then(async () => {
      try {
        await confirmSettlement(row.id);
        ElMessage.success(t('purchaseReceivables.message.settlementSuccess'));
        handleSearch(); // Refresh the list
      } catch (error) {
      }
    })
    .catch(() => {
      ElMessage.info(t('purchaseReceivables.message.operationCancelled'));
    });
};

const parseTimeHandler = (time: string, format: string) => {
  return parseTime(time, format);
};

const fetchDetailData = async (id: string) => {
  try {
    loading.value = true;
    const data = await getPurchaseReceivableDetail(id);
    return data;
  } catch (error) {
    return null;
  } finally {
    loading.value = false;
  }
};

/**
 * 处理打印功能
 * @param {PurchaseReceivable} row 当前行数据
 */
const print = ref(false);
async function handerPrint(row) {
  print.value = false;
  // 获取详细数据
  const detailData = await fetchDetailData(row.id);
  print.value = true;

  // 确定打印标题
  const title =
    searchForm.receivableStatus === 1 ? t("purchaseReceivables.title") : t("purchaseReceivables.title2");

  // 格式化收款状态
  const formatStatus = (status) => (status === 1 ? t("purchaseReceivables.pending") : t("purchaseReceivables.completed"));

  // 构建打印数据对象
  const printData = {
    // 基本信息
    title,
    billCode: detailData.purchaseReceivableCode, // 账单编号
    purchaseCode: detailData.purchaseBillCode, // 采购单号
    purchaser: detailData.purchaseUser, // 采购员
    remark: detailData.receivableRemark || "--", // 备注
    status: formatStatus(detailData.receivableStatus), // 单据状态
    supplierName: detailData.supplierName, // 供应商名称

    // 收款信息
    receiptType: settlementTypeObj[detailData.settlementType], // 收款类型 1->转账，2->现金，3->线下支付宝, 4->线下微信
    receiptMethod: receiveMethodObj[detailData.receivableMethod], // 收款方式
    receiptAmount: `${currencyFilter(detailData.currencyCode)}${detailData.receivableAmount}`, // 收款金额
    receiver: detailData.receiverUserName || "-", // 收款人

    // 时间信息
    receiptTime: parseTimeHandler(detailData.receivableTime) || "-", // 收款时间
    voucherUploadTime: parseTimeHandler(detailData.uploadTime) || "-", // 上传凭证时间

    // 商品列表
    items: [
      {
        productName: detailData.workOrderCode, // 商品名称
        specification: `${currencyFilter(detailData.currencyCode)}${detailData.receivableAmount}`, // 规格
        unit: detailData.workCreateUserName || "-", // 单位
        quantity: detailData.applyRemark || "-", // 数量
      },
    ],
  };

  // 执行打印
  nextTick(() => {
    printRef.value.handlePrint(printData);
  });
}

onActivated(() => {
  handleSearch();
  getSupplierList();
  getPurchasePersonnelList();
});
</script>

<template>
  <div class="purchase-receivables">
    <el-card class="mb-12px">
      <el-form ref="formRef" :model="searchForm" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item
              :label="t('purchaseReceivables.supplier')"
              prop="supplierName"
            >
              <!--              <el-input
                v-model="searchForm.supplierName"
                :placeholder="t('purchaseReceiveables.pleaseEnter')"
                clearable
              />-->
              <el-select
                v-model="searchForm.supplierName"
                clearable
                filterable
                :placeholder="$t('common.placeholder.selectTips')"
              >
                <el-option
                  v-for="item in supplierList"
                  :key="item.supplierName"
                  :label="item.supplierName"
                  :value="item.supplierName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="t('purchaseReceivables.billNo')"
              prop="purchaseReceivableCode"
            >
              <el-input
                v-model="searchForm.purchaseReceivableCode"
                :placeholder="t('purchaseReceivables.pleaseEnter')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="t('purchaseReceivables.billGenerationTime')"
              prop="billTimeRange"
            >
              <el-date-picker
                v-model="searchForm.billTimeRange"
                type="daterange"
                :start-placeholder="t('purchaseReceivables.startDate')"
                :end-placeholder="t('purchaseReceivables.endDate')"
                :range-separator="t('purchaseReceivables.to')"
                :value-format="'YYYY-MM-DD'"
                @change="handleBillTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item
              :label="t('purchaseReceivables.purchaseNo')"
              prop="purchaseOrderCode"
            >
              <el-input
                v-model="searchForm.purchaseOrderCode"
                :placeholder="t('purchaseReceivables.pleaseEnter')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="t('purchaseReceivables.refundNo')"
              prop="workOrderCode"
            >
              <el-input
                v-model="searchForm.workOrderCode"
                :placeholder="t('purchaseReceivables.pleaseEnter')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :label="t('purchaseReceivables.purchaseBuyer')"
              prop="purchaseUser"
            >
              <!--              <el-input
                v-model="searchForm.purchaseUser"
                :placeholder="t('purchaseReceiveables.pleaseEnter')"
                clearable
              />-->
              <el-select
                v-model="searchForm.purchaseUser"
                clearable
                filterable
                :placeholder="$t('common.placeholder.selectTips')"
              >
                <el-option
                  v-for="item in purchasePersonnelList"
                  :key="item.name"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <section class="text-right">
              <el-button
                type="primary"
                @click="handleSearch"
                v-hasPerm="['pms:purchaseReceivables:list']"
              >
                {{ t("purchaseReceivables.search") }}
              </el-button>
              <el-button
                @click="handleReset"
                v-hasPerm="['pms:purchaseReceivables:reset']"
              >
                {{ t("purchaseReceivables.reset") }}
              </el-button>
            </section>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card>
      <el-tabs v-model="searchForm.receivableStatus" @click="handleSearch">
        <el-tab-pane :label="t('purchaseReceivables.pending')" :name="1" />
        <el-tab-pane :label="t('purchaseReceivables.completed')" :name="2" />
      </el-tabs>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          prop="purchaseReceivableCode"
          :label="t('purchaseReceivables.billNo')"
          min-width="120"
        />
        <el-table-column
          prop="purchaseBillCode"
          :label="t('purchaseReceivables.purchaseNo')"
          min-width="120"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handlePurchaseOrderDetails(row)"
              class="operation-text"
            >
              {{ row.purchaseBillCode }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="workOrderCode"
          :label="t('purchaseReceivables.refundNo')"
          min-width="120"
        />
        <el-table-column
          prop="supplierName"
          :label="t('purchaseReceivables.supplyCompanyName')"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="purchaseUser"
          :label="t('purchaseReceivables.purchaseBuyer')"
          min-width="100"
        />
        <el-table-column
          prop="receivableAmount"
          :label="t('purchaseReceivables.receivableAmount')"
          min-width="120"
        >
          <template #default="{ row }">
            <span class="amount">
              {{ currencyFilter(row.currencyCode) }}{{ row.receivableAmount }}
            </span>
          </template>
        </el-table-column>
        <!--实收金额-->
        <el-table-column
          v-if="searchForm.receivableStatus === 2"
          prop="receivableAmount"
          :label="t('purchaseReceivables.receivableActuralAmount')"
          min-width="120"
        >
          <template #default="{ row }">
            <span class="amount">
              {{ currencyFilter(row.currencyCode) }}{{ row.receivableAmount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          :label="t('purchaseReceivables.billGenerationTime')"
          min-width="160"
        >
          <template #default="{ row }">
            {{ parseTimeHandler(row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </el-table-column>
        <!--结清时间-->
        <el-table-column
          v-if="searchForm.receivableStatus === 2"
          prop="settlementTime"
          :label="t('purchaseReceivables.billCompletedTime')"
          min-width="160"
        >
          <template #default="{ row }">
            {{
              parseTimeHandler(row.settlementTime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}
          </template>
        </el-table-column>
        <el-table-column
          :label="t('purchaseReceivables.operation')"
          width="240"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleDetails(row)"
              class="operation-text"
              v-hasPerm="['pms:purchaseReceivables:detail']"
            >
              {{ t("purchaseReceivables.details") }}
            </el-button>
            <!--              已收款账单（settlement_type不为空时）操作列不用再展示收款按钮-->
            <el-button
              v-if="searchForm.receivableStatus === 1 && !row.settlementType"
              type="primary"
              link
              @click="handleReceipt(row)"
              class="operation-text"
              v-hasPerm="['pms:purchaseReceivables:receive']"
            >
              {{ t("purchaseReceivables.receipt") }}
            </el-button>
            <el-button
              v-if="searchForm.receivableStatus === 1"
              type="primary"
              link
              @click="handleConfirm(row)"
              class="operation-text"
              v-hasPerm="['pms:purchaseReceivables:archive']"
            >
              {{ t("purchaseReceivables.resultConfirmation") }}
            </el-button>
            <el-button
              type="primary"
              link
              @click="() => handerPrint(row)"
              class="operation-text"
              v-hasPerm="['pms:purchaseReceivables:print']"
            >
              {{ t("purchaseReceivables.button.print") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes,prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <Print ref="printRef" v-if="print" class="display-none" />
  </div>
</template>

<style scoped lang="scss">
:deep(.el-button--primary.el-button--default.is-link) {
  color: #762ADB ;
}
:deep(.el-button--danger.el-button--default.is-link) {
  color: #c00c1d;
}
.purchase-receivables {
  :deep(.el-tabs__nav-wrap) {
    padding: 0 0 20px;
  }

  .amount {
    color: #f56c6c;
    font-weight: bold;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
