<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getPurchaseReceivableDetail } from "@pms/api/purchaseReceivables";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { parseTime } from "@/core/utils";
import { useCommon } from "@/modules/pms/composables/common";
const { t } = useI18n();

// Add route to get ID parameter
const route = useRoute();

const { currencyFilter } = useCommon();

// Add loading state
const loading = ref(false);

// Basic information
const basicInfo = ref({});
// Operation records
const operationRecords = ref([
  {
    operationTime: "--",
    operationType: "--",
    operationName: "--",
  },
]);

const parseTimeHandler = (time: string, format: string) => {
  return parseTime(time, format);
};
const file = ref([]);
// Fetch detail data
const fetchDetailData = async () => {
  loading.value = true;
  try {
    const res = await getPurchaseReceivableDetail(route.query.id as string);
    // Update ref objects with API data
    res.paymentVoucher = JSON.parse(res.receivableVoucher);
    basicInfo.value = res;
    operationRecords.value = res.operationLogList || [];
  } catch (error) {
    console.error("Failed to fetch detail:", error);
    ElMessage.error(t("common.fetchError"));
  } finally {
    loading.value = false;
  }
};

// Update refresh handler
const handleRefresh = () => {
  fetchDetailData();
};

// 在组件中引入 tagsView store
import { useTagsViewStore } from "@/core/store/modules/tagsView";

// 在组件中定义跳转方法
const tagsViewStore = useTagsViewStore();
const router = useRouter();
const navigateAndCloseCurrentTab = (path: string) => {
  // 先进行路由跳转
  router.push(path);

  // 等待路由跳转完成后关闭当前标签页
  nextTick(() => {
    // 删除当前页面的标签
    tagsViewStore.delView(route);
  });
};
const handleBack = () => {
  navigateAndCloseCurrentTab("/pms/finance/purchaseReceivables");
};
// Fetch data on component mount
onMounted(() => {
  fetchDetailData();
});

function getSettlementTypeText(settlementType, defaultText = "--") {
  // 将输入转换为数字，如果转换失败则为NaN
  const type = Number(settlementType);

  // 使用映射对象存储类型与文本的对应关系
  const typeTextMap = {
    1: t("purchaseReceivables.settlementType.payment"),
    2: t("purchaseReceivables.settlementType.offsetSettlement"),
  };

  // 如果类型存在于映射中，返回对应文本，否则返回默认文本
  return typeTextMap[type] || defaultText;
}

function getReceivableMethodText(receivableMethod, defaultText = "--") {
  // 将输入转换为数字，如果转换失败则为NaN
  const method = Number(receivableMethod);

  // 使用映射对象存储方式与文本的对应关系
  const methodTextMap = {
    1: t("purchaseReceivables.paymentMethod.transfer"),
    2: t("purchaseReceivables.paymentMethod.cash"),
    3: t("purchaseReceivables.paymentMethod.alipay"),
    4: t("purchaseReceivables.paymentMethod.wechat"),
  };

  // 如果方式存在于映射中，返回对应文本，否则返回默认文本
  return methodTextMap[method] || defaultText;
}

const handleVoucherClick = (voucher: string) => {
  // Implement voucher preview/download
};
const handleCancel = () => {
  navigateAndCloseCurrentTab("/pms/finance/purchaseReceivables");
};
</script>

<template>
  <div class="detail-container">
    <el-card v-loading="loading">
      <!-- Single Card Container -->
      <div class="card-header mb-24px">
        <img
          src="@/core/assets/images/arrow-left.png"
          alt=""
          class="back-btn"
          @click="handleBack"
        />
        <span class="code" @click="handleBack">
          {{ t("purchaseReceivables.detail.billNumber") }}：{{
            basicInfo.purchaseReceivableCode || "--"
          }}
        </span>
        <!-- 收款状态：1->待收款，2->已结清 -->
        <span
          class="contract status ml-10px"
          :class="basicInfo.receivableStatus === 1 ? 'unwokring' : 'executing'"
        >
          {{ basicInfo.receivableStatus === 1 ? t("purchaseReceivables.pending") : t("purchaseReceivables.completed") }}
        </span>
      </div>
      <!-- Basic Information -->
      <div class="section">
        <div class="card-title mb-20px">
          <span>{{ t("purchaseReceivables.detail.basicInfo") }}</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.billNumber") }}
              </span>
              <span class="form-text">
                {{ basicInfo.purchaseReceivableCode || "--" }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.refundOrder") }}
              </span>
              <span class="form-text">
                {{ basicInfo.workOrderCode || "--" }}
              </span>
            </div>
          </el-col>
          <el-col :span="8" v-if="!!basicInfo.purchaseUser">
            <!--            供应商为空时，供应商字段隐藏不展示-->
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.creator") }}
              </span>
              <span class="form-text">
                {{ basicInfo.purchaseUser || "--" }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.purchaseOrderNo") }}
              </span>
              <span class="form-text">{{ basicInfo.purchaseBillCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.settlementTime") }}
              </span>
              <span class="form-text">
                {{
                  parseTimeHandler(
                    basicInfo.settlementTime,
                    "{y}-{m}-{d} {h}:{i}:{s}"
                  ) || "--"
                }}
              </span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- Settlement Information -->
      <!--      待付款，且该账单未冲账/未收款时，应收账单明细页面，整个结算模块隐藏不展示-->
      <!--      判断付款方式是否为空，为空时，认为未收账，未付款款-->
      <div class="section" v-if="!!basicInfo.settlementType">
        <div class="card-title mb-20px">
          <span>{{ t("purchaseReceivables.detail.settlement") }}</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.settlementType") }}
              </span>
              <span class="form-text">
                {{ getSettlementTypeText(basicInfo.settlementType) || "--" }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.payablesBillNo") }}
              </span>
              <span class="form-text">
                {{ basicInfo.payableBillCode || "--" }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.paymentMethod") }}
              </span>
              <span class="form-text">
                {{
                  getReceivableMethodText(basicInfo.receivableMethod) || "--"
                }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.paymentAmount") }}
              </span>
              <span class="form-text">
                {{ currencyFilter(basicInfo.currencyCode)
                }}{{ basicInfo?.receivableAmount || "--" }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.currency") }}
              </span>
              <span class="form-text">
                {{
                  basicInfo.currencyCode === "CNY"
                    ? "人民币"
                    : basicInfo.currencyCode === "USD"
                      ? "美元"
                      : "--" || "--"
                }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.exchangeRate") }}
              </span>
              <span class="form-text">
                {{ basicInfo.exchangeRate || "--" }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.voucherUploadTime") }}
              </span>
              <span class="form-text">
                {{
                  parseTimeHandler(
                    basicInfo.uploadTime,
                    "{y}-{m}-{d} {h}:{i}:{s}"
                  ) || "--"
                }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.paymentTime") }}
              </span>
              <span class="form-text">
                {{
                  parseTimeHandler(
                    basicInfo.receivableTime,
                    "{y}-{m}-{d} {h}:{i}:{s}"
                  ) || "--"
                }}
              </span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.paymentVoucher") }}
              </span>
              <!--              <el-link
                type="primary"
                @click="handleVoucherClick(basicInfo.paymentVoucher)"
              >
                <span class="form-text">
                  {{ basicInfo.receivableVoucher }}
                </span>
              </el-link>-->
              <UploadMultiple
                :showTip="false"
                :showUploadBtn="false"
                ref="detailPicsRef"
                v-model="basicInfo.receivableVoucher"
                :tips="''"
                listType="'picture-card'"
                class="modify-multipleUpload"
                name="detailPic"
                actionType="preview"
              />
            </div>
          </el-col>
          <el-col :span="24">
            <div class="info-item">
              <span class="form-label mr-8px">
                {{ t("purchaseReceivables.detail.remarks") }}
              </span>
              <span class="form-text">
                {{ basicInfo.receivableRemark || "--" }}
              </span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- Operation Records -->
      <div class="section">
        <div class="card-title mb-20px">
          <span>{{ t("purchaseReceivables.detail.operationRecords") }}</span>
        </div>

        <el-table :data="operationRecords" style="width: 100%">
          <el-table-column
            prop="operationTime"
            :label="t('purchaseReceivables.detail.operationTime')"
          >
            <template #header>
              <span class="form-label">
                {{ t("purchaseReceivables.detail.operationTime") }}
              </span>
            </template>
            <template #default="{ row }">
              <span class="form-text">
                {{
                  parseTimeHandler(row.operationTime, "{y}-{m}-{d} {h}:{i}:{s}")
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="operationType"
            :label="t('purchaseReceivables.detail.operationType')"
          >
            <template #header>
              <span class="form-label">
                {{ t("purchaseReceivables.detail.operationType") }}
              </span>
            </template>
            <template #default="{ row }">
              <span class="form-text">{{ row.operationTypeName || "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="operator"
            :label="t('purchaseReceivables.detail.operator')"
          >
            <template #header>
              <span class="form-label">
                {{ t("purchaseReceivables.detail.operator") }}
              </span>
            </template>
            <template #default="{ row }">
              <span class="form-text">{{ row.operationName }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="footer-actions">
        <el-button @click="handleCancel">
          {{ t("purchaseReceivables.detail.cancel") }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
.card-header {
  padding: 0px 10px 20px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #e5e7f3;
  border-radius: 4px 4px 0px 0px;
  cursor: pointer;
  .code {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .back-btn {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
  .status {
    /* font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    text-align: left;
    font-style: normal; */
  }
}
.detail-container {
  background: #f5f5f5;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .section {
    & + .section {
      margin-top: 24px;
    }
  }

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
  }
  .info-item {
    display: flex;
  }
}
</style>
