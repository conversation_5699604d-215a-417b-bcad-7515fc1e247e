<template>
    <div class="app-container">
        <div class="reconcile" v-loading="loading">
            <div class="page-title">
                <div class="purchase-title">
                    <div @click="handleClose" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
                    <div>{{t('purchaseReconcile.label.reconciliationBillCode')}}：{{form.reconciliationBillCode}}</div>
                </div>
                <div class="purchase">
                    <div class="purchase">
                        <div class="purchase-status purchase-status-color1" v-if="form.reconciliationBillStatus==1">{{t('purchaseReconcile.reconciliationBillStatusList.unsatisfactoryReconciliation')}}</div>
                        <div class="purchase-status purchase-status-color3" v-if="form.reconciliationBillStatus==2">{{t('purchaseReconcile.reconciliationBillStatusList.reconciled')}}</div>
                    </div>
                </div>
            </div>
            <div class="page-content">
                <el-form :model="form"  :rules="rules" ref="formRef" label-width="110px" label-position="right">
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("purchaseReconcile.label.basicInformation") }}
                        </div>
                    </div>
                    <div class="grad-row">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.reconciliationBillCode')">
                                    {{form.reconciliationBillCode}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.purchaser')">
                                    {{form.purchaser}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.planDeliveryTime')">
                                    {{parseDateTime(form.planDeliveryTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.createTime')">
                                    {{parseDateTime(form.createTime, "dateTime")}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.purchaseCode')">
                                    {{form.purchaseCode }}
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.supplierName')">
                                    {{form.supplierName }}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.source')">
                                    <span v-if="form.source==1">{{t('purchaseOrder.orderSourceList.manuallyAddPurchaseOrder')}}</span>
                                    <span v-if="form.source==2">{{t('purchaseOrder.orderSourceList.purchaseTask')}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.purchaseType')">
                                    <span v-if="form.purchaseType==1">{{t('purchaseOrder.orderTypeList.suppliersDirectSupply')}}</span>
                                    <span v-if="form.purchaseType==2">{{t('purchaseOrder.orderTypeList.marketDirectSupply')}}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item :label="$t('purchaseReconcile.label.reconciliationBillStatus')">
                                    <span v-if="form.reconciliationBillStatus==1">{{t('purchaseReconcile.reconciliationBillStatusList.unsatisfactoryReconciliation')}}</span>
                                    <span v-if="form.reconciliationBillStatus==2">{{t('purchaseReconcile.reconciliationBillStatusList.reconciled')}}</span>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <div class="line"></div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("purchaseReconcile.label.receiveProductListInformation") }}
                        </div>
                    </div>
                    <div v-if="form.receiveTransportList && form.receiveTransportList.length>0">
                        <div v-for="(item,index) in form.receiveTransportList">
                            <div v-if="item.receiveTransportDetailVOList && item.receiveTransportDetailVOList.length>0">
                                <div  class="table-title" style=" padding: 15px 20px;">
                                    <div>
                                        <span>{{t('purchaseReconcile.label.receiveTransportCode')}}{{item.receiveTransportCode}}</span>
                                        <span class="ml20px">{{t('purchaseReconcile.label.receiveTransportTime')}}：{{parseDateTime(item.receiveTransportDate, "dateTime")}}</span>
                                    </div>
                                    <div>
                                        <span class="total">{{t('purchaseReconcile.label.total')}}</span>
                                        <span class="ml32px mr20px">{{t('purchaseReconcile.label.totalReceivedCountThisTime')}}：<span  style="color: var(--el-color-primary)">{{item.totalReceivedCount}}</span></span>
                                        <span>
                                            {{t('purchaseReconcile.label.totalReceivedAmountThisTime')}}：
                                            <span style="color: var(--el-color-primary)">
                                                 <span v-if="form.currencyCode == 'CNY'">￥</span>
                                                 <span v-else>$</span>
                                                {{item.totalReceivedAmount}}
                                            </span>
                                        </span>
                                    </div>
                                </div>
                                <el-table :data="item.receiveTransportDetailVOList" highlight-current-row :show-header="false">
                                    <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
                                    <el-table-column :label="$t('purchaseReconcile.label.productImg')" show-overflow-tooltip>
                                        <template #default="scope">
                                            <div class="product-div">
                                                <div class="picture">
                                                    <img :src="scope.row.productImg" alt="">
                                                </div>
                                                <div class="product">
                                                    <div class="product-name">{{scope.row.productName}}</div>
                                                    <div>
                                                        <span class="product-key">{{$t('purchaseReconcile.label.unitName')}}：</span>
                                                        <span class="product-value">{{scope.row.unitName}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="$t('purchaseReconcile.label.totalReceivedCountThisTime')"  prop="receivedThisTime" show-overflow-tooltip>
                                        <template #default="scope">
                                            <div class="product-div">
                                                <div class="product">
                                                    <span class="product-key">{{$t('purchaseReconcile.label.totalReceivedCountThisTime')}}：</span>
                                                    <span class="product-value">{{scope.row.receivedThisTime}}</span>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="$t('purchaseReconcile.label.totalReceivedAmountThisTime')" prop="receivedAmountThisTime" show-overflow-tooltip>
                                        <template #default="scope">
                                            <div class="product-div">
                                                <div class="product">
                                                    <span class="product-key">{{$t('purchaseReconcile.label.totalReceivedAmountThisTime')}}：</span>
                                                    <span class="product-value">
                                                         <span v-if="form.currencyCode == 'CNY'">￥</span>
                                                         <span v-else>$</span>
                                                        {{scope.row.receivedAmountThisTime}}
                                                    </span>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="$t('purchaseReconcile.label.remark')" prop="remark" show-overflow-tooltip>
                                        <template #default="scope">
                                            <div class="product-div">
                                                <div class="product">
                                                    <template v-if="scope.row.remark">
                                                         <span class="product-key">{{$t('purchaseReconcile.label.remark')}}：</span>
                                                         <span class="product-value">{{scope.row.remark}}</span>
                                                    </template>
                                                    <template v-else>
                                                        <span class="product-key">{{$t('purchaseReconcile.label.remark')}}：</span>
                                                        <span class="product-value">-</span>
                                                    </template>
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <div  class="table-title">
                            <div>
                                <span>{{t('purchaseReconcile.label.total')}}：</span>
                            </div>
                            <div>
                                <span class="mr16px">{{t('purchaseReconcile.label.totalReceivedCount')}}：<span  style="font-size: 18px; color: #C00C1D ">{{form.totalReceiveNum}}</span></span>
                                <span>
                                    {{t('purchaseReconcile.label.totalReceivedAmount')}}：
                                    <span  style="font-size: 18px; color: #C00C1D ">
                                         <span v-if="form.currencyCode == 'CNY'">￥</span>
                                         <span v-else>$</span>
                                        {{form.totalAmount}}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="title-lable">
                        <div class="title-line"></div>
                        <div class="title-content">
                            {{ $t("purchaseReconcile.label.reconcileInformation") }}
                        </div>
                    </div>
                    <div>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item
                                        :label="$t('purchaseReconcile.label.totalReceivedGoodsAmount')"
                                        prop="totalAmount"
                                >
                                    <el-input
                                            v-model="form.totalAmount"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            clearable
                                            disabled
                                    >
                                        <template #prefix>
                                            <span v-if="form.currencyCode == 'CNY'">￥</span>
                                            <span v-else>$</span>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item
                                        :label="$t('purchaseReconcile.label.reconciliationZeroAmount')"
                                        prop="reconciliationZeroAmount"
                                        @change="reconciliationZeroAmountChange"
                                >
                                    <el-input
                                            v-model="form.reconciliationZeroAmount"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            clearable
                                    >
                                        <template #prefix>
                                            <span v-if="form.currencyCode == 'CNY'">￥</span>
                                            <span v-else>$</span>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item
                                        :label="$t('purchaseReconcile.label.amountTotal')"
                                        prop="amountTotal"
                                >
                                    <el-input
                                            v-model="form.amountTotal"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            clearable
                                            disabled
                                    >
                                        <template #prefix>
                                            <span v-if="form.currencyCode == 'CNY'">￥</span>
                                            <span v-else>$</span>
                                        </template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item
                                        :label="$t('purchaseReconcile.label.remark')"
                                        prop="remark"
                                >
                                    <el-input
                                            :rows="4"
                                            type="textarea"
                                            show-word-limit
                                            maxlength="200"
                                            v-model="form.remark"
                                            :placeholder="$t('common.placeholder.inputTips')"
                                            clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </el-form>
            </div>
            <div class="page-footer">
                <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
                <el-button type="primary" plain @click="handerPrint">{{ $t("common.print") }}</el-button>
                <el-button type="primary" :loading="submitLoading" @click="doPurchaseReconcile">{{ $t("common.confirm") }}</el-button>
            </div>
        </div>
        <Print ref="printRef"/>
    </div>
</template>

<script setup lang="ts">

    defineOptions({
        name: "Reconcile",
        inheritAttrs: false,
    });

    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import PurchaseReconcileAPI, {PurchaseReconcileDetailFrom} from "@/modules/pms/api/purchaseReconcile";
    import { parseDateTime } from "@/core/utils/index.js";
    import Print from "./components/print.vue";

    const route = useRoute();
    const router = useRouter();
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const printRef = ref()
    const submitLoading = ref(false)
    const queryFormRef = ref(ElForm);
    const roleFormRef = ref(ElForm);
    const loading = ref(false);
    const formRef = ref();
    const id = route.query.id;
    const form = reactive<PurchaseReconcileDetailFrom>({
        receiveTransportList:[]
    });

    const rules = reactive({
        reconciliationZeroAmount: [
            { required: true, message: t("purchaseReconcile.rules.reconciliationZeroAmount"), trigger: "blur" },
            {pattern: /(^[0-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/, message: t("purchaseReconcile.rules.reconciliationZeroAmountFomart"), trigger: "blur"}
        ],
    });

    async function handleClose(){
        await tagsViewStore.delView(route);
        router.go(-1);
    }

    function handleEdit(){
        router.push({
            path: "/pms/purchase/addPurchaseOrder",
            query: {id:id,type:'edit'}
        });
    }

    /** 查询采购单详情 */
    function queryPurchaseReconcileDetail(){
        loading.value = true;
        let params = {
            id:id
        }
        PurchaseReconcileAPI.queryPurchaseReconcileDetail(params)
            .then((data) => {
                Object.assign(form,data)
            })
            .finally(() => {
                loading.value = false;
            });
    }

    function reconciliationZeroAmountChange() {
        form.amountTotal = parseFloat(String(parseFloat(form.totalAmount).toFixed(2) - parseFloat(form.reconciliationZeroAmount).toFixed(2))).toFixed(2);
    }

    /**对账*/
    function doPurchaseReconcile() {
        formRef.value.validate((valid) => {
            if (!valid) return;
            ElMessageBox.confirm(t('purchaseReconcile.message.reconciledTips'), t('purchaseReconcile.title.reconciled'), {
                confirmButtonText: t('common.confirm'),
                cancelButtonText: t('common.cancel'),
                type: "warning",
            }).then(
                () => {
                    let data = {
                        id: id,
                        changeAmount: form.reconciliationZeroAmount,
                        remark: form.remark,
                    }
                    submitLoading.value=true
                    PurchaseReconcileAPI.check(data).then(res => {
                        ElMessage.success(t('purchaseOrder.message.sendPurchaseOrderSucess'))
                        handleClose()
                    })
                    .finally(() => {
                        submitLoading.value = false;
                    });
                },
                () => {
                    ElMessage.info(t('purchaseOrder.message.sendPurchaseOrderConcel'));
                }
            );
        })
    }

    /**打印*/
    function handerPrint(){
        printRef.value.setFormData({id: id})
    }

    onMounted(() => {
        queryPurchaseReconcileDetail();
    });
</script>
<style scoped lang="scss">
    .reconcile {
        background: #FFFFFF;
        border-radius: 4px;
        .page-content{
            .table-title{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 50px;
                background: #F4F6FA;
                box-shadow: inset 1px 1px 0px 0px #E5E7F3, inset -1px -1px 0px 0px #E5E7F3;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 14px;
                color: #52585F;
                font-style: normal;
                padding: 15px 12px;
                .total{
                    color: #90979E;
                }
            }
        }
    }

</style>
<style lang="scss" scoped>
    .reconcile {}
</style>


