<template>
    <div id="printPurchaseReconcile" style="display:none;">
        <div class="box-title">{{$t('purchaseReconcile.label.purchaseReconcileTitle')}}</div>
        <div>
            <div class="grad-row-print">
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.reconciliationBillCode')}}：</span>
                    <span class="el-form-item__content"> {{form.reconciliationBillCode}}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.purchaseType')}}：</span>
                    <span class="el-form-item__content" v-if="form.purchaseType==1">{{$t('purchaseOrder.orderTypeList.suppliersDirectSupply')}}</span>
                    <span class="el-form-item__content" v-if="form.purchaseType==2">{{$t('purchaseOrder.orderTypeList.marketDirectSupply')}}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.purchaser')}}：</span>
                    <span class="el-form-item__content"> {{form.purchaser}}</span>
                </div>
            </div>
            <div class="grad-row-print">
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.purchaseCode')}}：</span>
                    <span class="el-form-item__content"> {{form.purchaseCode}}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.createTime')}}：</span>
                    <span class="el-form-item__content">{{parseDateTime(form.createTime, "dateTime")}}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.planDeliveryTime')}}：</span>
                    <span class="el-form-item__content">{{parseDateTime(form.planDeliveryTime, "dateTime")}}</span>
                </div>
            </div>
            <div class="grad-row-print">
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.checkerUser')}}：</span>
                    <span class="el-form-item__content" v-if="form.checkTime"> {{form.checkerUser}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
                <div class="el-form-item--div">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.checkTime')}}：</span>
                    <span class="el-form-item__content" v-if="form.checkTime">{{parseDateTime(form.checkTime, "dateTime")}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
            </div>
            <div class="grad-row-print">
                <div class="el-form-item--div100">
                    <span class="el-form-item__label">{{$t('purchaseReconcile.label.remark')}}：</span>
                    <span class="el-form-item__content" v-if="form.remark"> {{form.remark}}</span>
                    <span class="el-form-item__content" v-else>-</span>
                </div>
            </div>
            <table  class="tabs">
                <thead>
                <tr>
                    <th class="index">{{$t('common.sort')}}</th>
                    <th>{{$t('purchaseReconcile.label.productCode')}}</th>
                    <th>{{$t('purchaseReconcile.label.productName')}}</th>
                    <th>{{$t('purchaseReconcile.label.unit')}}</th>
                    <th>{{$t('purchaseReconcile.label.totalReceivedCountThisTime')}}</th>
                    <th>{{$t('purchaseReconcile.label.totalReceivedAmountThisTime')}}</th>
                    <th>{{$t('purchaseReconcile.label.remark')}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in form.purchaseOrderDetailList" :key="index">
                    <td class="index">{{index+1}}</td>
                    <td>{{ item.productCode }}</td>
                    <td>{{ item.productName }}</td>
                    <td>{{ item.unitName }}</td>
                    <td>{{ item.receivedCount }}</td>
                    <td>
                        <span v-if="form.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                        {{ item.receivedAmount }}
                    </td>
                    <td>
                        <span v-if="item.remark">{{item.remark}}</span>
                        <span v-else>-</span>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="grad-row-print">
                <div class="el-form-item--div">
                    <span class="grad-row-print-money">
                        {{$t('purchaseReconcile.label.totalReceivedGoodsAmount')+'：'}}
                        <span v-if="form.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                    </span>
                    <span class="grad-row-print-money">{{form.totalAmount }}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="grad-row-print-money">
                        {{$t('purchaseReconcile.label.reconciliationZeroAmount')+'：'}}
                        <span v-if="form.currencyCode == 'CNY'">￥</span>
                        <span v-else>$</span>
                    </span>
                    <span class="grad-row-print-money">{{form.changeAmount }}</span>
                </div>
                <div class="el-form-item--div">
                    <span class="grad-row-print-money">
                        {{$t('purchaseReconcile.label.amountTotal')+'：'}}
                         <span v-if="form.currencyCode == 'CNY'">￥</span>
                         <span v-else>$</span>
                    </span>
                    <span class="grad-row-print-money">{{form.amount }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
    import {parseDateTime} from "@/core/utils/index.js";
    import PurchaseReconcileAPI, {PurchaseReconcileDetailFrom} from "@/modules/pms/api/purchaseReconcile";
    const  id= ref('')
    const form = reactive<PurchaseReconcileDetailFrom>({
        receiveTransportList:[]
    });

    function setFormData(data) {
        id.value = data.id;
        handerPrint()
    }

    /**打印*/
    function handerPrint(){
            let params = {
                id: id.value,
            }
            PurchaseReconcileAPI.queryPurchaseReconcilePrint(params)
                .then((data) => {
                    Object.assign(form, data)
                    setDatas()
                })
    }
    function setDatas(){
        if (nextTick) {
            nextTick(() => {
                setTimeout(function () {
                    const printContent1 = document.getElementById('printPurchaseReconcile').innerHTML;
                    const printContent = `
                        <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <style lang="scss">
                                @media print {
                                    @page {
                                      @bottom-center {
                                        content: counter(pages) "/" counter(page);
                                        font-size: 10px;
                                      }
                                    }
                                    body {
                                        .tabs{
                                           margin-top: 20px;
                                           margin-bottom: 20px;
                                           border-top: 1px solid #E5E7F3 !important;
                                           border-left: 1px solid #E5E7F3 !important;
                                           border-right: 1px solid #E5E7F3 !important;
                                        }
                                        .mr10px{
                                            margin-right: 10px;
                                        }
                                        .mr5px{
                                            margin-right:5px;
                                        }
                                         .box-title {
                                              text-align: center!important;
                                              margin-top: 24px;
                                              margin-bottom: 24px;
                                              font-family: PingFangSC, PingFang SC;
                                              font-weight: 600 !important;
                                              font-size: 18px!important;
                                              color: #151719;
                                              font-style: normal;
                                          }
                                         .grad-row-print {
                                              display: flex;
                                              /*justify-content: space-around;*/
                                               justify-content: flex-start;
                                              .el-form-item--div{
                                                    width: 33%;
                                              }
                                                .el-form-item--div100{
                                                    width: 100%;
                                              }
                                              .el-form-item__label {
                                                  font-family: PingFangSC, PingFang SC;
                                                  font-weight: 400;
                                                  font-size: 12px;
                                                  color: #90979E;
                                                  font-style: normal;
                                              }
                                              .el-form-item__content {
                                                  font-family: PingFangSC, PingFang SC;
                                                  font-weight: 500;
                                                  font-size: 12px;
                                                  color: #151719;
                                                  font-style: normal;
                                                  word-break:break-word;
                                              }
                                               .grad-row-print-money {
                                                  font-family: PingFangSC, PingFang SC;
                                                  font-weight: 500;
                                                  font-size: 12px;
                                                  color: #52585F;
                                                  font-style: normal;
                                                  word-break:break-word;
                                              }
                                          }


                                          table {
                                                border-collapse: collapse;
                                                width: 100%;
                                                table-layout: fixed;
                                                -webkit-print-color-adjust: exact;
                                          }
                                          table, th, td {
                                            border: 1px solid #E5E7F3;
                                          }
                                          th,td{
                                            width: cacl((100% - 50px)/4);
                                            border-left: none;
                                            border-right: none;
                                            font-family: PingFangSC, PingFang SC;
                                            font-style: normal;
                                            text-align: left;
                                            padding: 14px 12px;
                                            color: #52585F;
                                            word-break:break-word;
                                            .index{
                                                width: 50px;
                                            }
                                          }
                                          th {
                                              background: #F4F6FA;
                                              font-weight: 600;
                                              font-size: 12px;
                                              background: #F4F6FA;
                                          }
                                           td {
                                              font-weight: 400;
                                              font-size: 12px;
                                          }
                                    }
                               }
                            </style>
                        </head>
                        <body>
                          ${printContent1}
                        </body>
                        </html>
                    `;
                    const iframe = document.createElement('iframe');
                    iframe.style.display = 'none';
                    document.body.appendChild(iframe);
                    const win = iframe.contentWindow;
                    win.document.write(printContent);
                    win.print();
                    document.body.removeChild(iframe);
                }, 500);
            })
        }
    }

    defineExpose({
        setFormData,
    });
</script>

<style scoped lang="scss">
</style>
<style lang="scss">
</style>
