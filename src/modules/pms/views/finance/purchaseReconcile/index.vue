<template>
    <div class="app-container">
        <div class="purchaseReconcile">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
                    <el-form-item :label="$t('purchaseReconcile.label.supplierName')" prop="supplierName">
<!--                        <el-select-->
<!--                                v-model="queryParams.supplierName"-->
<!--                                :placeholder="$t('common.placeholder.selectTips')"-->
<!--                                clearable-->
<!--                                filterable-->
<!--                                class="!w-[256px]"-->
<!--                        >-->
<!--                            <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId"></el-option>-->
<!--                        </el-select>-->
                        <el-input
                                v-model="queryParams.supplierName"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('purchaseReconcile.label.reconciliationBillStatus')" prop="reconciliationBillStatus">
                        <el-select
                                v-model="queryParams.reconciliationBillStatus"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in reconciliationBillStatusList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="purchaseCode" :label="$t('purchaseReconcile.label.purchaseCode')">
                        <el-input
                                v-model="queryParams.purchaseCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="reconciliationBillCode" :label="$t('purchaseReconcile.label.reconciliationBillCode')">
                        <el-input
                                v-model="queryParams.reconciliationBillCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item  class="ml10px" prop="timeType">
                        <el-select
                                v-model="queryParams.timeType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[120px]"
                        >
                            <el-option v-for="item in timeTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                        <el-date-picker
                                :editable="false"
                                class="!w-[210px]"
                                v-model="queryParams.reconciliationBillDate"
                                type="daterange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button v-hasPerm="['pms:finance:purchaseReconcile:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button v-hasPerm="['pms:finance:purchaseReconcile:reset']"  @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-card shadow="never" class="table-container">
                <template #header>
                    <div class="flex-center-but">
                        <div>
                            <el-button v-hasPerm="['pms:finance:purchaseReconcile:generate']" type="primary" @click="generateBill()" :disabled="multipleSelection.length===0">
                                {{$t('purchaseReconcile.button.generateBill')}}
                            </el-button>
                            <el-button v-hasPerm="['pms:finance:purchaseReconcile:exportSequence']" @click="exportSequencePurchaseReconcile">
                                {{$t('common.exportSequence')}}
                            </el-button>
                        </div>
                        <div>
                            <el-button v-hasPerm="['pms:finance:purchaseReconcile:export']" @click="exportPurchaseReconcile()">
                                {{$t('purchaseReconcile.button.exportPurchaseReconcile')}}
                            </el-button>
                        </div>
                    </div>
                </template>
                <el-table
                        id="prints"
                        v-loading="loading"
                        :data="purchaseReconcileList"
                        highlight-current-row
                        @selection-change="handleSelectionChange"
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="selection" width="60" align="center"/>
                    <el-table-column :label="$t('purchaseReconcile.label.reconciliationBillCode')" prop="reconciliationBillCode" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.purchaseCode')" prop="purchaseCode" min-width="100" show-overflow-tooltip>
                        <template #default="scope">
                            <span class="cursor-pointer" style="color:var(--el-color-primary)" @click="jumpPurchaseOrder(scope.row.purchaseCode)">{{scope.row.purchaseCode}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.orderTime')" prop="orderTime" show-overflow-tooltip>
                        <template #default="scope">
                            <span>{{ parseDateTime(scope.row.orderTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.supplierNameCopy')" prop="supplierName" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.purchaser')" prop="purchaser" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.amount')" prop="amount" show-overflow-tooltip align="right">
                        <template #default="scope">
                            <span v-if="scope.row.amount">
                                  <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                                  <span v-else>$</span>
                                {{scope.row.amount}}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.changeAmount')" prop="changeAmount" show-overflow-tooltip align="right">
                        <template #default="scope">
                            <span v-if="scope.row.changeAmount">
                                  <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                                  <span v-else>$</span>
                                {{scope.row.changeAmount}}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.reconciliationBillStatus')" prop="reconciliationBillStatus" min-width="100" show-overflow-tooltip>
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color1" v-if="scope.row.reconciliationBillStatus==1">{{t('purchaseReconcile.reconciliationBillStatusList.unsatisfactoryReconciliation')}}</div>
                                <div class="purchase-status purchase-status-color3" v-if="scope.row.reconciliationBillStatus==2">{{t('purchaseReconcile.reconciliationBillStatusList.reconciled')}}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.checkTime')" show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.checkTime">{{ parseDateTime(scope.row.checkTime, "dateTime") }}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('purchaseReconcile.label.generateStatus')" prop="generateStatus" show-overflow-tooltip>
                        <template #default="scope">
                            <span  v-if="scope.row.generateStatus==1">{{t('common.statusYesOrNo.no')}}</span>
                            <span  v-if="scope.row.generateStatus==2">{{t('common.statusYesOrNo.yes')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="120">
                        <template #default="scope">
                            <el-button
                                    v-hasPerm="['pms:finance:purchaseReconcile:reconciled']"
                                    v-if="scope.row.reconciliationBillStatus==1"
                                    type="primary"
                                    link
                                    @click="reconciled(scope.row.id)"
                            >
                                {{$t('purchaseReconcile.button.reconciled')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['pms:finance:purchaseReconcile:objection']"
                                    v-if="scope.row.reconciliationBillStatus==2 && scope.row.generateStatus ==1"
                                    type="primary"
                                    link
                                    @click="objection(scope.row.id)"
                            >
                                {{$t('purchaseReconcile.button.objection')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['pms:finance:purchaseReconcile:print']"
                                    type="primary"
                                    link
                                    @click="handerPrint(scope.row.id)"
                            >
                                {{$t('common.print')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
            </el-card>
        </div>
        <Print ref="printRef"/>
        <ExportSequence
                ref="exportSequenceRef"
                v-model:dialog-visible="dialogVisible"
                :path="`pms:reconciliationBill:export`">
        </ExportSequence>
    </div>
</template>

<script setup lang="ts">
    defineOptions({
        name: "PurchaseReconcile",
        inheritAttrs: false,
    });

    import {parseDateTime, convertToTimestamp} from "@/core/utils/index.js";
    import PurchaseReconcileAPI, {
        PurchaseReconcilePageVO,
        PurchaseReconcilePageQuery,
        PurchaseReconcilePrintFrom
    } from "@/modules/pms/api/purchaseReconcile";
    import supplierAPI from "@/modules/pms/api/supplier";
    import {IObject} from "@/core/components/CURD/types";
    import {useRouter} from "vue-router";
    import Print from "./components/print.vue";

    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const printRef = ref()
    const loading = ref(false);
    const total = ref(0);
    const dialogVisible = ref(false);
    const exportSequenceRef= ref()
    const multipleSelection = ref([]);
    const productCategoryList = ref([])
    const supplierList = ref([])
    const reconciliationBillStatusList = ref([
        {
            key: 1,
            value: t('purchaseReconcile.reconciliationBillStatusList.unsatisfactoryReconciliation')
        },
        {
            key: 2,
            value:t('purchaseReconcile.reconciliationBillStatusList.reconciled')
        }
    ])
    const timeTypeList = ref([
        {
            key: 1,
            value: t('purchaseReconcile.timeTypeList.reconciliationTime')
        },
        {
            key: 2,
            value:t('purchaseReconcile.timeTypeList.orderTime')
        }
    ])

    const queryParams = reactive<PurchaseReconcilePageQuery>({
        timeType:1,
        page: 1,
        limit: 20,
    });

    const purchaseReconcileList = ref<PurchaseReconcilePageVO[]>();

    const  closeRef= ref()

    const dialog = reactive({
        title: "关闭",
        visible: false,
    });

    const form = reactive<PurchaseReconcilePrintFrom>({
        purchaseOrderDetailList:[]
    });

    /** 查询供应商列表 */
    function getSupplierList() {
        supplierAPI.getSupplierListAll()
            .then((data) => {
                supplierList.value = data;
            })
    }

    /** 查询 */
    function handleQuery() {
        loading.value = true;
        let params = {
           reconciliationBillDate: undefined,
            ...queryParams
        }
        if(queryParams.reconciliationBillDate && queryParams.reconciliationBillDate.length>0){
            params.startTime= convertToTimestamp(queryParams.reconciliationBillDate[0]+' 00:00:00')
            params.endTime=convertToTimestamp(queryParams.reconciliationBillDate[1]+' 23:59:59')
        }
      /*  if(queryParams.supplierName){
            let supplier =  supplierList.value.filter(item =>queryParams.supplierName==item.supplierId)
            params.supplierName=supplier[0].supplierName
        }*/
        delete params.reconciliationBillDate
        PurchaseReconcileAPI.getPurchaseReconcilePage(params)
            .then((data) => {
                purchaseReconcileList.value = data.records;
                total.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.reconciliationBillDate=[]
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    /** 行复选框选中记录选中ID集合 */
    function handleSelectionChange(selection: any[]) {
        multipleSelection.value = selection;
    }

    /**处采购单跳转*/
    function jumpPurchaseOrder(purchaseCode?:string){
        router.push({
            path: "/pms/purchase/purchaseOrderDetail",
            query: {orderCode:purchaseCode}
        });
    }

    /** 生成账单*/
    function generateBill(row?: IObject,status?: number) {

        let flag =  multipleSelection.value.some(item =>item.reconciliationBillStatus===1 || item.generateStatus===2)
        if(flag){
            return  ElMessage.error(t('purchaseReconcile.message.generateNotBillTips'))
        }
        let ids = multipleSelection.value.map((item) => item.id);
        let data = {
            ids: ids,
        }
        ElMessageBox.confirm(t('purchaseReconcile.message.generateBillTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                PurchaseReconcileAPI.generatePayableBill(data).then(res => {
                    ElMessage.success(t('purchaseReconcile.message.generateBillSucess'))
                    handleQuery()
                })
            },
            () => {
                ElMessage.info(t('purchaseReconcile.message.generateBillConcel'));
            }
        );
    }

    /**对账*/
    function reconciled(id?:string){
        router.push({
            path: "/pms/finance/reconcile",
            query: {id:id}
        });
    }

    /** 反对账*/
    function objection(id?: string) {
        let data = {
            id: id,
        }
        ElMessageBox.confirm(t('purchaseReconcile.message.objectionTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                PurchaseReconcileAPI.objection(data).then(res => {
                    ElMessage.success(t('purchaseReconcile.message.objectionTipsSucess'))
                    handleQuery()
                })
            },
            () => {
                ElMessage.info(t('purchaseReconcile.message.objectionTipsConcel'));
            }
        );
    }


    /** 导出采购对账单*/
    function exportPurchaseReconcile() {
        ElMessageBox.confirm(t('common.exportTips'), t('common.export'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                loading.value = true;
                let params = {
                    reconciliationBillDate: undefined,
                    ...queryParams
                }
                if(queryParams.reconciliationBillDate && queryParams.reconciliationBillDate.length>0){
                    params.startTime= convertToTimestamp(queryParams.reconciliationBillDate[0]+' 00:00:00')
                    params.endTime=convertToTimestamp(queryParams.reconciliationBillDate[1]+' 23:59:59')
                }
                if(queryParams.supplierName){
                    let supplier =  supplierList.value.filter(item =>queryParams.supplierName==item.supplierId)
                    params.supplierName=supplier[0].supplierName
                }
                delete params.reconciliationBillDate
                delete params.page
                delete params.limit
                PurchaseReconcileAPI.exportPurchaseReconcile(params)
                    .then((data) => {
                        exportSequencePurchaseReconcile()
                    })
                    .finally(() => {
                        loading.value = false;
                    });
            },
            () => {
                ElMessage.info(t('common.exportConcel'));
            }
        );
    }

    /** 导出序列*/
    function exportSequencePurchaseReconcile(){
        exportSequenceRef.value.exportSequenceListPage()
        dialogVisible.value = true;
    }

    /**打印*/
    function handerPrint(id?: string){
        printRef.value.setFormData({id: id})
    }

    onActivated(() => {
        getSupplierList();
        handleQuery();
    });
</script>

<style lang="scss" scoped>
    .purchaseReconcile{

    }
</style>

<style lang="scss">
    .purchaseReconcile{

    }
</style>
