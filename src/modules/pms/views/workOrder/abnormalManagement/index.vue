<template>
    <div class="app-container">
        <div class="abnormalManagement">
            <div class="search-container">
                <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
                    <el-form-item prop="workOrderCode" :label="$t('abnormalManagement.label.workOrderCode')">
                        <el-input
                                v-model="queryParams.workOrderCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('abnormalManagement.label.supplierId')" prop="supplierId">
                        <el-select
                                v-model="queryParams.supplierId"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                filterable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in supplierList" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('abnormalManagement.label.purchaserUserId')" prop="purchaserUserId">
                        <el-select
                                v-model="queryParams.purchaserUserId"
                                :placeholder="$t('common.placeholder.selectTips')"
                                clearable
                                filterable
                                class="!w-[256px]"
                        >
                            <el-option v-for="item in purchasePersonnelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('abnormalManagement.label.workOrderType')" prop="workOrderType">
                        <el-select
                                v-model="queryParams.workOrderType"
                                :placeholder="$t('common.placeholder.selectTips')"
                                class="!w-[256px]"
                                clearable
                        >
                            <el-option v-for="item in workOrderTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="purchaseCode" :label="$t('abnormalManagement.label.purchaseCode')">
                        <el-input
                                v-model="queryParams.purchaseCode"
                                :placeholder="$t('common.placeholder.inputTips')"
                                clearable
                                class="!w-[256px]"
                        />
                    </el-form-item>
                    <el-form-item prop="applyDate" :label="$t('abnormalManagement.label.applyDate')">
                        <el-date-picker
                                :editable="false"
                                class="!w-[256px]"
                                v-model="queryParams.applyDate"
                                type="daterange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                    </el-form-item>
                    <el-form-item prop="handleDate" :label="$t('abnormalManagement.label.handleDate')">
                        <el-date-picker
                                :editable="false"
                                class="!w-[256px]"
                                v-model="queryParams.handleDate"
                                type="daterange"
                                range-separator="~"
                                start-placeholder="开始时间"
                                end-placeholder="截止时间"
                                value-format="YYYY-MM-DD"
                                :placeholder="$t('common.placeholder.selectTips')"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button  v-hasPerm="['pms:workOrder:abnormalManagement:search']" type="primary" @click="handleQuery">
                            {{$t('common.search')}}
                        </el-button>
                        <el-button  v-hasPerm="['pms:workOrder:abnormalManagement:reset']"  @click="handleResetQuery">
                            {{$t('common.reset')}}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-card shadow="never" class="table-container">
                <div class="panelContent">
                    <div class="panelItem" @click="changePanel(item)" :class="{ active: item.active }" v-for="(item, index) in tabs" :key="item.key">{{ item.value }}</div>
                </div>
                <el-table
                        v-loading="loading"
                        :data="abnormalManagementList"
                        highlight-current-row
                        stripe
                >
                    <template #empty>
                        <Empty/>
                    </template>
                    <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
                    <el-table-column :label="$t('abnormalManagement.label.workOrderCode')" prop="workOrderCode" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.purchaseCode')" prop="purchaseCode" show-overflow-tooltip>
                        <template #default="scope">
                            <span class="cursor-pointer" style="color:var(--el-color-primary)" @click="jumpPurchaseOrder(scope.row.purchaseCode)">{{scope.row.purchaseCode}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.receiptCode')" prop="receiptCode" show-overflow-tooltip></el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.supplierOrPurchaserUser')" show-overflow-tooltip>
                        <template #default="scope">
                            <span  v-if="scope.row.orderType==1">{{scope.row.supplierName}}</span>
                            <span  v-if="scope.row.orderType==2">{{scope.row.purchaseUser}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.workOrderAmountTotal')" prop="workOrderAmount" show-overflow-tooltip align="right">
                        <template #default="scope">
                            <span v-if="scope.row.workOrderAmount">
                                  <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                                  <span v-else>$</span>
                                {{scope.row.workOrderAmount}}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.returnAmount')" prop="returnAmount" show-overflow-tooltip align="right">
                        <template #default="scope">
                            <span v-if="scope.row.returnAmount">
                                  <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                                  <span v-else>$</span>
                                {{scope.row.returnAmount}}
                            </span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.createUserName')" prop="createUserName" show-overflow-tooltip>
                        <template #default="scope">
                            <span class="encryptBox">
                                <span v-if="scope.row.createUserName && scope.row.createUserName.length <= 1">{{scope.row.createUserName}}</span>
                                <span v-else>
                                  {{scope.row.createUserName}}
                                  <el-icon
                                          v-hasPermEye="['pms:workOrder:abnormalManagement:eye']"
                                          v-if="scope.row.createUserName"
                                          @click="scope.row.createUserNameShow ? desensitization(scope.row.id,'createUserName',scope.$index):''"
                                          class="encryptBox-icon"
                                          color="#762ADB "
                                          size="16"
                                  >
                                    <component :is="scope.row.createUserNameShow ? 'View' : ''" />
                                  </el-icon>
                                </span>
                            </span>

                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.createTime')" show-overflow-tooltip>
                        <template #default="scope">
                            <span  v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.workOrderType')"  show-overflow-tooltip>
                        <template #default="scope">
                            <span  v-if="scope.row.workOrderType==1">{{t('abnormalManagement.workOrderTypeList.purchaseReturn')}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.workOrderStatus')" prop="workOrderStatus"  min-width="100" show-overflow-tooltip>
                        <template #default="scope">
                            <div class="purchase">
                                <div class="purchase-status purchase-status-color1" v-if="scope.row.workOrderStatus==1">{{t('abnormalManagement.workOrderStatusList.toBeTreated')}}</div>
                                <div class="purchase-status purchase-status-color3" v-if="scope.row.workOrderStatus==3">{{t('abnormalManagement.workOrderStatusList.complete')}}</div>
                                <div class="purchase-status purchase-status-color0" v-if="scope.row.orderPurchaseStatus==4">{{t('abnormalManagement.workOrderStatusList.cancel')}}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('abnormalManagement.label.handleTime')"  show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.handleTime">{{ parseDateTime(scope.row.handleTime, "dateTime") }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                        <template #default="scope">
                            <el-button
                                    v-hasPerm="['pms:workOrder:abnormalManagement:dealWith']"
                                    v-if="scope.row.workOrderStatus==1"
                                    type="primary"
                                    link
                                    @click="dealWith(scope.row.id,'dealwith')"
                            >
                                {{$t('abnormalManagement.button.dealWith')}}
                            </el-button>
                            <el-button
                                    v-hasPerm="['pms:workOrder:abnormalManagement:select']"
                                    v-if="scope.row.workOrderStatus==3 || scope.row.workOrderStatus==4"
                                    type="primary"
                                    link
                                    @click="dealWith(scope.row.id,'select')"
                            >
                                {{$t('common.select')}}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                        v-if="total > 0"
                        v-model:total="total"
                        v-model:page="queryParams.page"
                        v-model:limit="queryParams.limit"
                        @pagination="handleQuery"
                />
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">
    import PurchasePersonnelAPI from "@/modules/pms/api/purchasePersonnel";
    import supplierAPI from "@/modules/pms/api/supplier";

    defineOptions({
        name: "AbnormalManagement",
        inheritAttrs: false,
    });

    import PurchaseOrderAPI, { PurchaseOrderPageVO, PurchaseOrderPageQuery} from "@/modules/pms/api/purchaseOrder";
    import AbnormalManagementAPI, { PurchaseReconcilePageVO, AbnormalManagementPageQuery} from "@/modules/pms/api/abnormalManagement";
    import {convertToTimestamp, parseDateTime} from "@/core/utils/index.js";
    import PurchaseAPI from "@/modules/pms/api/purchase";
    import {IObject} from "@/core/components/CURD/types";
    import {useRouter} from "vue-router";

    const router = useRouter();
    const { t } = useI18n();
    const queryFormRef = ref(null);
    const loading = ref(false);
    const total = ref(0);
    const multipleSelection = ref([]);
    const productCategoryList = ref([])
    const supplierList = ref([])
    const purchasePersonnelList = ref([])
    const tabs = ref([
        {
            key: '',
            value: t('abnormalManagement.workOrderStatusList.all'),
            active: true
        },
        {
            key: 1,
            value: t('abnormalManagement.workOrderStatusList.toBeTreated'),
            active: false
        },
        {
            key: 3,
            value: t('abnormalManagement.workOrderStatusList.complete'),
            active: false
        },
    ])
    const workOrderTypeList = ref([
        {
            key: 1,
            value: t('abnormalManagement.workOrderTypeList.purchaseReturn')
        },
    ])
    const reconciliationBillDateTypeList = ref([
        {
            key: 1,
            value: t('purchaseReconcile.reconciliationBillDateTypeList.reconciliationTime')
        },
        {
            key: 2,
            value:t('purchaseReconcile.reconciliationBillDateTypeList.orderTime')
        }
    ])

    const queryParams = reactive<AbnormalManagementPageQuery>({
        workOrderStatus:'',
        page: 1,
        limit: 20,
    });

    const abnormalManagementList = ref<PurchaseReconcilePageVO[]>();

    const  closeRef= ref()
    // const setSuppliersRef = ref();

    const dialog = reactive({
        title: "关闭",
        visible: false,
    });

    const changePanel = (data) => {
        tabs.value.map(item => item.active = false);
        data.active =true;
        queryParams.workOrderStatus = data.key;
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }

    // function handleClick(val) {
    //     queryParams.workOrderStatus = val.props.name;
    //     queryParams.page = 1;
    //     queryParams.limit = 20;
    //     handleQuery();
    // }

    /** 查询供应商列表 */
    function getSupplierList() {
        supplierAPI.getSupplierList()
            .then((data) => {
                supplierList.value = data;
            })
    }

    /** 查询采购员列表 */
    function getPerchasePersonnelList() {
        PurchasePersonnelAPI.getPerchasePersonnelList()
            .then((data) => {
                purchasePersonnelList.value = data;
            })
    }

    /** 查询 */
    function handleQuery() {
        loading.value = true;
        let params={
            startApplyDate: undefined,
            endApplyDate: undefined,
            startHandleDate: undefined,
            endHandleDate: undefined,
            ...queryParams
        }
        if(queryParams.applyDate && queryParams.applyDate.length>0){
            params.startApplyDate=convertToTimestamp(queryParams.applyDate[0] + ' 00:00:00')
            params.endApplyDate=convertToTimestamp(queryParams.applyDate[1] + ' 23:59:59')
        }
        if(queryParams.handleDate && queryParams.handleDate.length>0){
            params.startHandleDate=convertToTimestamp(queryParams.handleDate[0]+ ' 00:00:00')
            params.endHandleDate=convertToTimestamp(queryParams.handleDate[1] + ' 23:59:59')
        }
        delete params.applyDate
        delete params.handleDate
        AbnormalManagementAPI.getAbnormalManagementPage(params)
            .then((data) => {
                abnormalManagementList.value = data.records.map((item: any, index: any) => {
                    item.createUserNameShow = true;
                    return { ...item };
                });
                total.value = parseInt(data.total);
            })
            .finally(() => {
                loading.value = false;
            });
    }

    /** 重置查询 */
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }


    /**处采购单跳转*/
    function jumpPurchaseOrder(purchaseCode?:string){
        router.push({
            path: "/pms/purchase/purchaseOrderDetail",
            query: {orderCode:purchaseCode}
        });
    }


    /**处理、查看*/
    function dealWith(id?:string,type?:string){
        router.push({
            path: "/pms/workOrder/dealwith",
            query: {id:id,type:type,title:type=='select'?t("common.select"):t("abnormalManagement.button.dealWith")}
        });
    }

    function desensitization(id,name,index) {
        let params ={ id: id,name:name }
        AbnormalManagementAPI.desensitization(params)
            .then((data: any) => {
                if(name=='createUserName'){
                    abnormalManagementList.value[index].createUserName = data;
                    abnormalManagementList.value[index].createUserNameShow = false;
                }
            })
            .finally(() => {});
    }


    onActivated(() => {
        getSupplierList();
        getPerchasePersonnelList();
        handleQuery();
    });
</script>

<style lang="scss" scoped>
    .abnormalManagement{
        .panelContent {
            display: flex;
            border-bottom: 1px solid #F2F3F4;
            width: 100%;
            margin-bottom: 16px;
            .panelItem {
                font-size: 14px;
                color: #151719;
                padding: 10px 39px;
                cursor: pointer;
                &.active {
                    color: var(--el-color-primary);
                    border-bottom: 2px solid var(--el-color-primary);
                }
            }
        }
        .encryptBox {
            word-wrap: break-word;
            word-break: break-all;
        }

        .encryptBox-icon {
            margin-left: 4px;
            cursor: pointer;
            vertical-align: text-top;
        }

    }
</style>
