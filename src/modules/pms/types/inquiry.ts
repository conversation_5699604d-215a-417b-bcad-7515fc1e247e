export interface GoodParams {
  /**
   * 1-新增询价单选择产品 2-新增采购单选择商品 3-新增采购需求和采购任务选择商品
   */
  chooseType: number;
  /**
   * 计划交货日期结束时间
   */
  endDeliveryDate?: number;
  /**
   * 一级分类id
   */
  firstCategoryId?: number;
  /**
   * 询价类型：0->国内供应，1->国外直采
   */
  inquiryType?: number;
  /**
   * 关键字搜索
   */
  keywords?: string;
  /**
   * 每页条数
   */
  limit?: number;
  /**
   * 采购类型：1->供应商直供，2->市场采购
   */
  orderType?: number;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 二级分类id
   */
  secondCategoryId?: number;
  /**
   * 计划交货日期开始时间
   */
  startDeliveryDate?: number;
  /**
   * 期望收货日期
   */
  startExpectedDeliveryDate?: number;
  /**
   * 供应商Id
   */
  supplierId?: number;
  /**
   * 三级分类id
   */
  thirdCategoryId?: number;
  /**
   * 仓库Id
   */
  warehouseId: number;
}

export interface SupplierListParams {
  warehouseId: number;
  inquiryType: 0 | 1;
}

export interface InquiryDetailDTO {
  /**
   * 商品品牌
   */
  brandName?: string;
  /**
   * 最近一次采购单价金额币种
   */
  currency?: string;
  /**
   * 默认供应商id
   */
  defaultSupplierId?: number;
  /**
   * 默认供应商名称
   */
  defaultSupplierName?: string;
  /**
   * 一级分类id
   */
  firstCategoryId?: number;
  /**
   * 供应商列表
   */
  inquiryProductSupplierListDTOList?: InquiryProductSupplierListDTO[];
  /**
   * 商品分类全称
   */
  productCategoryFullName?: string;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 商品id
   */
  productId?: number;
  /**
   * 商品图片
   */
  productImg?: string;
  /**
   * 商品名称
   */
  productName?: string;
  /**
   * 最近一次采购单价
   */
  purchasePrice?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级分类id
   */
  secondCategoryId?: number;
  /**
   * 三级分类id
   */
  thirdCategoryId?: number;
  /**
   * 采购单位Id
   */
  unitId?: number;
  /**
   * 采购单位
   */
  unitName?: string;
  [property: string]: any;
}

export interface InquiryProductSupplierListDTO {
  /**
   * 金额币种
   */
  currency?: string;
  /**
   * 询价金额
   */
  inquiryAmount?: number;
  /**
   * 询价单明细主键id
   */
  inquiryDetailId?: number;
  /**
   * 定价金额
   */
  pricingAmount?: number;
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * 供应商名称
   */
  supplierName?: string;
  [property: string]: any;
}

export interface SaveInquiryProductListDTO {
  /**
   * 商品品牌
   */
  brandName?: string;
  /**
   * 最近一次采购单价金额币种
   */
  currency?: string;
  /**
   * 默认供应商id
   */
  defaultSupplierId?: number;
  /**
   * 默认供应商名称
   */
  defaultSupplierName?: string;
  /**
   * 一级分类id
   */
  firstCategoryId?: number;
  /**
   * 商品分类全称
   */
  productCategoryFullName?: string;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 商品id
   */
  productId?: number;
  /**
   * 商品图片
   */
  productImg?: string;
  /**
   * 商品名称
   */
  productName?: string;
  /**
   * 最近一次采购单价
   */
  purchasePrice?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级分类id
   */
  secondCategoryId?: number;
  /**
   * 三级分类id
   */
  thirdCategoryId?: number;
  /**
   * 采购单位Id
   */
  unitId?: number;
  /**
   * 采购单位
   */
  unitName?: string;
  [property: string]: any;
}

export interface SaveInquirySupplierListDTO {
  /**
   * 匹配到的商品id集合
   */
  matchingProductIds?: number[];
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * 供应商名称
   */
  supplierName?: string;
  [property: string]: any;
}

export interface PageInquery {
  /**
   * 计划交货日期结束时间
   */
  endDeliveryDate?: number;
  /**
   * 计划交货日期结束时间
   */
  endSubmitDate?: number;
  /**
   * 询价单号
   */
  inquiryCode?: string;
  /**
   * 询价状态
   */
  inquiryStatus?: number;
  /**
   * 每页条数
   */
  limit?: number;
  /**
   * 当前页
   */
  page?: number;
  /**
   * 计划交货日期开始时间
   */
  startDeliveryDate?: number;
  /**
   * 计划交货日期开始时间
   */
  startSubmitDate?: number;
  /**
   * 供应商Id
   */
  supplierId?: number;
}

export interface FillQuotationParams {
  /**
   * 询价单主键ID
   */
  inquiryId: number;
  /**
   * 明细
   */
  inquiryPurchaseListRequestDTOList: InquiryPurchaseListRequestDTO[];
  /**
   * 0-暂存 1-确认提交
   */
  type?: number;
}

export interface InquiryPurchaseListRequestDTO {
  /**
   * 询价金额
   */
  inquiryAmount?: number;
  /**
   * 询价单明细主键id
   */
  inquiryDetailId?: number;
  /**
   * 定价金额
   */
  pricingAmount?: number;
}
