export default {
   abnormalManagement: {
    label: {
        workOrderCode: "工单编号",
        supplierId: "供应商",
        purchaserUserId: "采购员",
        workOrderType: "工单类型",
        purchaseCode: "采购单号",
        receiptCode: "收运单号",
        applyDate: "申请日期",
        handleDate: "处理日期",
        supplierOrPurchaserUser: "供应商/采购员",
        product: "商品信息",
        purchaseAmount: "采购金额",
        workOrderAmountTotal: "工单总金额",
        workOrderAmount: "工单金额",
        returnAmount: "退货金额",
        createUserName: "申请人",
        createTime: "申请时间",
        workOrderStatus: "工单状态",
        handleTime: "处理时间",
        workOrderRemark: "备注",
        basicInformation: "基本信息",
        productInformation: "商品信息",
        createUserPhone: "联系方式",
        productUnit: "单位",
        workOrderQuantity: "工单数量",
        handleType: "处理方式",
        handleOpinion: "处理意见",
        attachmentInfo: "图片",
        productCode: "商品编码",
    },
    placeholder:{
        closeReason: "请输入关闭采购单原因",
    },
    workOrderTypeList: {
        purchaseReturn: "采购退货",
    },
    workOrderStatusList: {
        all: "全部",
        toBeTreated: "待处理",
        complete: "已完成",
        cancel: "已取消",
    },
    dealingWithProcessingList: {
        purchaseReturn: "采购退货",
        continuSell: "继续销售",
        damageIntoWarehouse: "入库破损",
    },
    button: {
        dealWith: "处理",
        return: "返回",
    },
    title: {
        reconciled: "完成对账",
    },
    message: {
      dealwithSucess: "处理成功",
    },
    rules: {
        handleType: "请选择处理方式",
        handleOpinion: "处理意见支持0到200个字符",
    },
  },
};
