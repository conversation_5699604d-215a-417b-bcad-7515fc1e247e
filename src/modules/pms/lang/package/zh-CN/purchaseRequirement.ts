export default {
  purchaseRequirements: {
    title: {
      list: "采购需求",
      basicInfo: "基础信息",
      productInfo: "商品信息",
      otherInfo: "其他信息",
      addProduct: "添加商品",
      detail: "采购需求详情",
      create: "新建采购需求",
    },
    label: {
      purchaseReqNo: "采购需求编号",
      orderNo: "订购单号",
      customerInfo: "客户信息",
      createTime: "创建时间",
      expectedDeliveryDate: "期望收货日期",
      purchaseOrderNo: "采购需求编号",
      goodsQuantity: "商品数量",
      receivingTime: "期望收货时间",
      remark: "备注",
      status: "状态",
      operation: "操作",
      to: "至",
      startTime: "开始日期",
      endTime: "结束日期",
      clientName: "客户名称",
      deliveryAddress: "收货地址",
      name: "姓名",
      phone: "电话",
      expectedDeliveryTime: "期望收货时间",
      warehouse: "仓库",
      attachment: "附件",
      remarks: "备注",
      product: "商品",
      productCategory: "商品分类",
      productCode: "商品编码",
      purchaseUnit: "采购单位",
      productName: "商品名称",
      mobileArea: "区号",
      startDate: "开始时间",
      endDate: "截止时间",
      customerName: "客户名称",
      orderCode: "订购单编号",
      receiveName: "姓名",
      receiveMobile: "手机号码",
      warehouseName: "仓库",
      selectedCount: "已选",
      productImg: "商品图片",
      unitName: "采购单位",
      productCategoryFullName: "商品分类全称",
      weekday: "近七天",
      today: "今天",
      yesterday: "昨天",
      labelName: "名称",
      address: "地址",
    },
    dateTypeList: {
      createDate: "创建时间",
      expectedDeliveryDate: "期望收货时间",
    },
    status: {
      generated: "已生成",
      notGenerated: "未生成",
      closed: "已关闭",
    },
    button: {
      search: "搜索",
      reset: "重置",
      newPurchaseRequirement: "新建采购需求",
      detail: "详情",
      addProduct: "添加商品",
      delete: "删除",
      cancel: "取消",
      confirm: "确认",
      back: "返回",
      save: "保存",
      submit: "提交",
    },
    placeholder: {
      pleaseEnter: "请输入",
      pleaseSelect: "请选择",
      productInput: "请输入商品名称/编码",
      enterProductName: "请输入商品名称",
      enterProductCode: "请输入商品编码",
      selectCategory: "请选择商品分类",
      areaCode: "区号",
      enterQuantity: "请输入数量",
    },
    message: {
      today: "今天",
      yesterday: "昨天",
      lastWeek: "近七天",
      deleteConfirm: "是否确认删除该商品？",
      warning: "确认删除",
      saveSuccess: "保存成功",
      saveFailed: "保存失败",
      deleteSuccess: "删除成功",
      deleteFailed: "删除失败",
      submitSuccess: "提交成功",
      submitFailed: "提交失败",
      fetchFailed: "获取数据失败",
      pleaseSelectDeliveryTimeRange: "请选择完整的送达时间范围",
      resetConfirm: "重置后，当前页面信息将被清空，确定重置该信息？",
      exitConfirm: "退出页面将不会保存您所做的更改，您确定退出当前页面吗？",
      noProductsFound: "未查询到可用商品",
      queryProductsFailed: "查询商品列表失败",
      onlyNumbersAndDecimals: "只能输入数字和小数点",
      onlyOneDecimalPoint: "只能包含一个小数点",
      integerPartTooLong: "整数部分不能超过11位",
      decimalPartTooLong: "小数部分不能超过2位",
      enterValidNumber: "请输入有效的数字",
    },
    table: {
      productInfo: "商品信息",
      warehouse: "仓库",
      unit: "采购单位",
      operation: "操作",
      count: "数量",
      selected: "已选",
      items: "个商品",
    },
    tips: {
      uploadLimit: "文件大小不超过10M，最多上传10个文件（支持格式：rar,zip,docx,xls,xlsx,pdf,jpg,png,jpeg）",
      phoneNumberFormat: "请输入正确的手机号码",
      requiredField: "此字段为必填项",
      invalidFormat: "格式不正确",
      viewEncryptedInfo: "点击查看加密信息",
    },
    rules: {
      clientName: "请输入客户名称",
      deliveryAddress: "请输入收货地址",
      name: "请输入姓名",
      phone: "请输入电话",
      warehouse: "请选择仓库",
      expectedDeliveryTime: "请选择期望收货时间",
      productRequired: "请添加至少一个商品",
      quantityRequired: "请填写所有商品的数量且数量必须大于0",
      quantityPositive: "商品数量必须大于0",
    },
    detail: {
      basicInfo: "基础信息",
      productInfo: "商品信息",
      attachmentInfo: "其他信息",
    },
  },
};
