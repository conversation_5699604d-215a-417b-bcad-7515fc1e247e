export default {
  purchaseInquiry: {
    label: {
      orderNo: "询价单号",
      status: "状态",
      supplier: "供应商",
      createTime: "询价时间",
      startTime: "开始时间",
      endTime: "结束时间",
      to: "至",
      submitTime: "创建时间",
      deliveryTime: "计划交货周期",
    },
    status: {
      pending: "审核中",
      completed: "已完成",
      rejected: "已拒绝",
    },
    button: {
      query: "查看",
      edit: "编辑",
      delete: "删除",
      cancel: "取消",
      confirm: "确定",
      view: "查看",
      report: "报价",
      price: "定价",
      copy: "复制",
      close: "关闭",
    },
    table: {
      orderNo: "询价单号",
      supplier: "询价供应商",
      status: "询价状态",
      amount: "商品数量",
      createTime: "创建时间",
      deliveryTime: "计划交货日期",
      operation: "操作",
      warehouseName: "仓库",
      inquirySchedule: "询价进度",
      completeTime: "完成时间",
    },
    message: {
      confirmCopy: "确认复制该询价单？",
      confirmClose: "确认关闭该询价单？",
      copySuccess: "复制成功",
      closeSuccess: "关闭成功",
      closeFailed: "关闭失败",
    },
    createInquery: {
      title: "新建询价单",
      basicInfo: "基本信息",
      deliveryPeriod: "计划交货周期",
      startDate: "开始日期",
      endDate: "结束日期",
      inquiryType: "询价类型",
      warehouse: "仓库",
      name: "名称",
      confirm: "确认",
      reset: "重置",
      inquiryProducts: "询价商品",
      productsCount: "共计",
      productsUnit: "种询价商品",
      purchaseUnit: "采购单位",
      productBrand: "商品品牌",
      lastPurchasePrice: "最近一次采购价",
      defaultSupplier: "默认供应商",
      remark: "备注",
      addProduct: "添加询价商品",
      cancel: "取消",
      nextStep: "下一步",
      domesticSupply: "国内供应",
      foreignDirectPurchase: "国外直采",
      pleaseSelect: "请选择",
      pleaseInput: "请输入",
      noProducts: "请添加询价商品",
      restoreDataFailed: "恢复数据失败"
    },
    copyInquery: {
      title: "新建询价单",
      basicInfo: "基本信息",
      deliveryPeriod: "计划交货周期",
      startDate: "开始日期",
      endDate: "结束日期",
      inquiryType: "询价类型",
      warehouse: "仓库",
      name: "名称",
      confirm: "确认",
      reset: "重置",
      inquiryProducts: "询价商品",
      productsCount: "共计",
      productsUnit: "种询价商品",
      purchaseUnit: "采购单位",
      productBrand: "商品品牌",
      lastPurchasePrice: "最近一次采购价",
      defaultSupplier: "默认供应商",
      remark: "备注",
      addProduct: "添加询价商品",
      cancel: "取消",
      nextStep: "下一步",
      domesticSupply: "国内供应",
      foreignDirectPurchase: "国外直采",
      pleaseSelect: "请选择",
      pleaseInput: "请输入",
      noProducts: "请添加询价商品"
    },
    inqueryDetail: {
      title: "询价单号",
      basicInfo: "基本信息",
      deliveryPeriod: "计划交货周期",
      inquiryType: "询价类型",
      name: "名称",
      warehouse: "仓库",
      createTime: "创建时间",
      inquirySupplier: "询价供应商",
      creator: "制单人",
      inquiryProducts: "询价商品",
      searchPlaceholder: "请输入商品名称",
      search: "搜索",
      purchaseUnit: "采购单位",
      productBrand: "商品品牌",
      remarkMessage: "备注留言",
      defaultSupplier: "默认供应商",
      lastPurchasePrice: "最近一次采购价",
      operationRecords: "单据操作记录",
      operationTime: "操作时间",
      operationType: "操作类型",
      operator: "操作人",
      cancel: "取消",
      to: "至",
      statusClass: {
        inquiring: "unwokring",
        confirmed: "executing",
        completed: "cancelled",
        closed: "cancelled"
      },
      status: {
        0: "询价中",
        1: "已确定",
        2: "已完成",
        3: "已关闭"
      }
    },
    inqueryPricing: {
      title: "询价单号",
      basicInfo: "基本信息",
      deliveryPeriod: "计划交货周期",
      inquiryType: "询价类型",
      name: "名称",
      warehouse: "仓库",
      createTime: "创建时间",
      inquirySupplier: "询价供应商",
      creator: "制单人",
      inquiryProducts: "询价商品",
      searchPlaceholder: "请输入商品名称",
      search: "搜索",
      purchaseUnit: "采购单位",
      productBrand: "商品品牌",
      remarkMessage: "备注留言",
      defaultSupplier: "默认供应商",
      lastPurchasePrice: "最近一次采购价",
      cancel: "取消",
      savePricing: "保存定价",
      to: "至",
      lowest: "低",
      statusClass: {
        inquiring: "unwokring",
        confirmed: "executing",
        completed: "cancelled",
        closed: "cancelled"
      },
      message: {
        selectPricing: "请至少选择一个商品的定价",
        unselectedProducts: "未勾选定价，是否继续保存？",
        continueSave: "继续保存",
        cancelSubmit: "已取消提交",
        confirmSave: "确定要保存当前定价吗？",
        confirm: "确定",
        cancel: "取消",
        savePricingSuccess: "保存定价成功",
        completePricingSuccess: "完成定价成功",
        tips: "提示"
      }
    },
    inqueryReport: {
      title: "询价单号",
      basicInfo: "基本信息",
      deliveryPeriod: "计划交货周期",
      inquiryType: "询价类型",
      name: "名称",
      warehouse: "仓库",
      createTime: "创建时间",
      inquirySupplier: "询价供应商",
      creator: "制单人",
      inquiryProducts: "询价商品",
      searchPlaceholder: "请输入商品名称",
      search: "搜索",
      purchaseUnit: "采购单位",
      productBrand: "商品品牌",
      remarkMessage: "备注留言",
      defaultSupplier: "默认供应商",
      lastPurchasePrice: "最近一次采购价",
      cancel: "取消",
      saveQuotation: "保存报价",
      completeQuotation: "完成报价",
      to: "至",
      pleaseInput: "请输入",
      statusClass: {
        inquiring: "unwokring",
        confirmed: "executing",
        completed: "cancelled",
        closed: "cancelled"
      },
      message: {
        confirmSubmit: "确定要完成当前报价单填写吗？",
        tip: "提示",
        confirm: "确定",
        cancel: "取消",
        submitSuccess: "报价单提交成功",
        cancelSubmit: "已取消提交",
        confirmSave: "确定要保存当前报价吗？",
        saveQuotationSuccess: "保存报价成功",
        completeQuotationSuccess: "完成报价成功",
        tips: "提示"
      }
    },
    productMatching: {
      title: "新建询价单",
      supplierSelection: "供应商选择",
      supplierCount: "共计",
      supplierUnit: "个供应商",
      productCode: "商品编码",
      matchingRate: "商品匹配率",
      coverStatus: {
        covered: "已覆盖",
        notCovered: "未覆盖"
      },
      cancel: "取消",
      previousStep: "上一步",
      confirm: "确认",
      message: {
        selectSupplier: "请选择供应商",
        createSuccess: "创建询价单成功"
      }
    }
  },
};
