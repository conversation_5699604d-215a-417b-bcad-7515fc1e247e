/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-06 19:53:31
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-03-10 14:08:13
 * @FilePath: \supply-manager-web\src\modules\pms\lang\package\zh-CN\purchasePayable.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import purchase from "./purchase";

export default {
  purchasePayable: {
    label: {
      supplierName: "供应商",
      creatTime: "生成账单时间",
      startTime: "开始时间",
      endTime: "结束时间",
      billCode: "账单编号",
      purchaseCode: "采购单号",
      purchaseType: "采购类型",
      purchaserUser: "采购员",
      purchaseOrderDetail: "采购单明细",
      amount: "应付金额",
      amountTotal: "应付总金额",
      paidAmount: "实付金额",
      changeAmount: "抹零金额",
      checkCode: "对账单号",
      createUserName: "制单人",
      operationUserName: "操作人",
      operationTime: "操作时间",
      operationType: "操作类型",
      paymentMethod: "支付类型",
      basicInformation: "基本信息",
      billingStatus: "账单状态",
      deliveryTime: "计划交货日期",
      closeTime: "结清时间",
      closeType: "结算类型",
      receipt: "单据",
      productInfo: "商品信息",
      settleAccount: "结算",
      receiverUserName: "领款方",
      transactionType: "交易类型",
      paymentAmount: "付款金额",
      remark: "备注",
      paymentTime: "付款时间",
      paymentVoucher: "付款凭证",
      currencyCode: "结算币种",
      exchangeRate: "汇率",
      productName: "商品名称",
      productUnit: "基本单位",
      purchaseUnit: "采购单位",
      productQuantity: "收货数量",
      productAmount: "收货金额",
      productCount: "商品数量",
      total: "合计",
      operationRecord: "操作记录",
      payType: "支付方式",
      sort: "序号",
      purchaseReceivabl: "冲账账单",
      supplierDirect: "供应商直供",
      marketPurchase: "市场采购",
      due: "待付款",
      closedAccount: "已结清",
      closedTime: "结清时间",
      manualAddition: "手动新增",
      procurementTask: "采购任务",
      productImage: "商品图片",
      refundAmount: "待冲账金额",
      printTitle: "采购结算账单",
      productTotalAmount: "货品总金额",
      refundTotalAmount: "应付冲账总金额",
      refundTAmount: "冲账金额",
      pemainingPayableAmount: "剩余应付金额",
      to: "至",
      billNumber: "账单编号",
      uploadVoucherTime: "上传凭证时间",
      clearingArchivingTime: "结清归档时间",
    },
    placeholder: {},

    button: {
      detail: "明细",
      pay: "付款",
      closingfiling: "结清归档",
      print: "打印",
      tabPayment: "待付款",
      tabClose: "已结清",
      strikeBalance: "冲账",
      transferMoney: "转账",
      cash: "现金",
      alipay: "线下支付宝支付",
      wechat: "线下微信支付",
      RMB: "人民币",
      dollar: "美元",
    },
    title: {},
    message: {
      reconciledTips: "确认付款？",
      reconciled: "提示",
      sendPurchaseOrderSucess: "付款成功",
      sendPurchaseOrderConcel: "已取消付款",
      closeTip: "确定当前账单已结清可进行归档，归档后不可恢复？",
      submitTip: "付款金额不能超过剩余应付金额",
    },
    rules: {
      receiverUserName: "请选择领款方",
      transactionType: "请选择交易类型",
      paymentMethod: "请选择支付类型",
      currencyCode: "请选择结算币种",
      paymentVoucher: "请选择付款凭证",
      paymentTime: "请选择付款时间",
      purchaseReceivableCode: "请选择冲账账单",
      paymentAmount: "请输入付款金额",
      paymentAmountFomart: "付款金额支持输入大于0的数字，支持最多2位小数",
    },
  },
};
