export default {
  purchaseTasks: {
    label: {
      createTime: "创建日期",
      startTime: "开始日期",
      endTime: "结束日期",
      to: "至",
      supplier: "供应商",
      purchaser: "采购员",
      status: "状态",
      goodsClassification: "商品分类",
      classification: "分类",
      purchaseGoods: "采购商品",
      procurementReqCode: "采购需求编码",
      warehouseName: "仓库名称",
      warehouse: "仓库",
      purchaseQuantity: "采购数量",
      quantity: "采购数量",
      purchaseUnitPrice: "采购单价",
      purchaseAmount: "采购金额",
      expectedDeliveryDate: "期望收货日期",
      creator: "制单人",
      generated: "已生成",
      notGenerated: "未生成",
      productCode: "商品编码",
      basicInformation: "基础信息",
      goodsInformation: "商品信息",
      unit: "单位",
      productName: "商品",
      today: "今天",
      yesterday: "昨天",
      weekday: "近七天",
      productNameCopy: "商品名称",
      unitCopy: "采购单位",
    },
    button: {
      addPurchaseTask: "新建采购任务",
      generatePurchaseOrder: "生成采购单",
      addGood: "添加商品",
    },
    title: {
      addPurchasePersonnelUnitTitle: "添加采购员",
      productSpecificationsTitle: "选择商品规格",
    },
    placeholder: {
      primaryClassification: "一级分类",
      secondaryClassification: "二级分类",
      thirdClassification: "三级分类",
    },
    message: {
      deleteTips: "确定删除此采购任务吗？",
      deleteConcel: "已取消删除！",
      deleteSucess: "删除成功！",
      deleteFail: "删除失败！",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      selectPurchaseTask: "请选择需要生成采购单的采购任务",
      batchCreateOrderTips: "确定要生成采购单吗？",
      batchCreateOrderSucess: "生成成功",
      batchCreateOrderConcel: "已取消",
      addOrEditPurchaseTips: "商品清单不能为空！",
      resetQueryProduct: "重置后，当前页面信息将被清空，确定重置该信息？",
      resetConcel: "已取消！",
      selectNumTips: "已选",
      batchCreateOrderArrTips: "请选择未生成并且有供应商的采购任务",
    },
    rules: {
      userCode: "请选择人员",
      warehouseTip: "请选择仓库",
      expectedDeliveryDateTip: "请选择期望日期",
      purchaseCount: "请输入采购数量",
      purchaseCountFomart: "采购量支持大于0的数字，支持小数点后2位",
      supplier: "请选择供应商",
    },
  },
};
