export default {
  purchaseReconcile: {
    label: {
        purchaseReconcileTitle: "采购单对账单",
        supplierName: "供应商",
        reconciliationBillStatus: "对账状态",
        purchaseCode: "采购单号",
        reconciliationBillCode: "对账单号",
        timeType: "对账时间",
        reconciliationBillDate: "对账时间",
        orderTime: "下单时间",
        supplierNameCopy: "供应商名称",
        purchaser: "采购员",
        amount: "应付金额",
        changeAmount: "抹零金额",
        checkTime: "对账时间",
        checkerUser: "对账人",
        generateStatus: "生成账单",
        planDeliveryTime: "计划交货日期",
        createTime: "创建时间",
        source: "单据来源",
        purchaseType: "采购类型",
        receiveTransportCode: "收运单",
        total: "合计",
        totalReceivedCountThisTime: "收货数量",
        totalReceivedAmountThisTime: "收货金额",
        receiveTransportTime: "收运时间",
        productImg: "图片",
        productCode: "商品编码",
        productName: "商品名称",
        unitName: "采购单位",
        remark: "备注",
        totalReceivedCount: "收货总数量",
        totalReceivedAmount: "收货总金额",
        totalReceivedGoodsAmount: "收运货品总金额",
        reconciliationZeroAmount: "对账抹零金额",
        amountTotal: "应付总金额",
        basicInformation: "基本信息",
        receiveProductListInformation: "收货商品清单",
        reconcileInformation: "对账",
        unit: "单位",
    },
    placeholder:{
        closeReason: "请输入关闭采购单原因",
    },
    reconciliationBillStatusList: {
        unsatisfactoryReconciliation: "未对账",
        reconciled: "已对账",
    },
    timeTypeList: {
        reconciliationTime: "对账时间",
        orderTime: "下单时间",
    },
    button: {
        generateBill: "生成账单",
        exportPurchaseReconcile: "导出采购对账单",
        reconciled: "对账",
        objection: "反对账",
    },
    title: {
        reconciled: "完成对账",
        exportPurchaseOrder: "导出",
        addProduct: "添加商品",
    },
    message: {
      generateNotBillTips: "不能勾选未对账或者已生成账单的数据！",
      generateBillTips: "确定生成账单?",
      generateBillConcel: "已取消生成账单！",
      generateBillSucess: "生成账单成功！",
      generateBillFail: "生成账单失败！",
      objectionTips: "反对账后，该对账单状态将会变更为未对账状态，是否确认反对账？",
      objectionTipsConcel: "已取消反对账！",
      objectionTipsSucess: "反对账成功！",
      reconciledTips: "确定当前对账单信息无误，完成对账?",
      closePurchaseOrderSucess: "关闭成功",
      addOrEditPurchaseTips:'商品清单不能为空！',
      deleteSucess: "删除成功",
      addSucess: "添加成功",
      editSucess: "编辑成功",
      saveAndSendPurchaseOrderConcel: "确认并发送成功",
    },
    rules: {
        reconciliationZeroAmount: "请输入对账抹零金额",
        reconciliationZeroAmountFomart: "对账抹零金额支持大于等于0的数字，支持小数点后2位",
    },
  },
};
