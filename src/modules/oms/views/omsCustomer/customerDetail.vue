<template>
  <div class="app-container">
    <div class="addCustomer">
      <div class="page-title">
        <div @click="handleCancel" class="display_block">
          <el-icon><Back /></el-icon>
          <span>
            {{ $t("omsCustomer.title.customerDetails") }}
          </span>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" label-width="112px" label-position="right">
          <div class="basicInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.basicInformation") }}
            </div>
            <div class="grad-row">
              <el-row justify="space-between">
                <el-col :span="6">
                  <!-- 客户名称 -->
                  <el-form-item :label="$t('omsCustomer.label.customerName')">
                    {{ form.customerName ? form.customerName : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 客户简称 -->
                  <el-form-item
                    :label="$t('omsCustomer.label.customerAbbreviation')"
                  >
                    {{ form.customerShortName ? form.customerShortName : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 客户类型 -->
                  <el-form-item :label="$t('omsCustomer.label.customerType')">
                    <span v-if="form.customerTypeCode == 1">
                      {{
                        $t("omsCustomer.customerTypeList.enterpriseCustomers")
                      }}
                    </span>
                    <span v-else-if="form.customerTypeCode == 2">
                      {{ $t("omsCustomer.customerTypeList.governmentClients") }}
                    </span>
                    <span v-else-if="form.customerTypeCode == 3">
                      {{
                        $t("omsCustomer.customerTypeList.individualCustomers")
                      }}
                    </span>
                    <span v-else>-</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6" />
              </el-row>
            </div>
          </div>
          <div class="contactInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.contacts") }}
              <span class="encryptBox">
                <el-icon
                  @click="getRealPhone(1)"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['oms:customer:eye']"
                >
                  <component :is="form.mobilePhoneShow ? 'View' : ''" />
                </el-icon>
              </span>
            </div>
            <div class="grad-row">
              <el-row>
                <el-col :span="6">
                  <!-- 联系人 -->
                  <el-form-item :label="$t('omsCustomer.label.contactsPerson')">
                    {{ form.contact ? form.contact : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 联系电话 -->
                  <el-form-item :label="$t('omsCustomer.label.mobileNumber')">
                    <span v-if="form.contactMobile" class="pr4">
                      {{ form.contactCountryAreaCode }}
                    </span>
                    {{ form.contactMobile || "-" }}
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="contactInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.receiptInformation") }}
              <span class="encryptBox">
                <el-icon
                  @click="getRealPhone(2)"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['oms:customer:eye']"
                >
                  <component :is="form.receiverMobileShow ? 'View' : ''" />
                </el-icon>
              </span>
            </div>
            <div class="grad-row">
              <el-row>
                <el-col :span="6">
                  <!-- 收货人姓名 -->
                  <el-form-item :label="$t('omsCustomer.label.consigneeName')">
                    {{ form.receiverName ? form.receiverName : "-" }}
                  </el-form-item>
                </el-col>
                <!-- 收货人手机 -->
                <el-col :span="6">
                  <el-form-item :label="$t('omsCustomer.label.mobileNumber')">
                    <span v-if="form.receiverMobile" class="pr4">
                      {{ form.receiverCountryAreaCode }}
                    </span>
                    {{ form.receiverMobile || "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <!-- 收货地址 -->
                  <el-form-item
                    :label="$t('omsCustomer.label.receivingAddress')"
                    prop="fullReceiverAddress"
                  >
                    {{ form.fullReceiverAddress || "-" }}
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="tradingAndLogistics">
            <div class="title">
              <div class="flex_style">
                <div>
                  {{ $t("omsCustomer.label.contractInformation") }}
                </div>
              </div>
            </div>
            <el-row>
              <el-col :span="24">
                <!-- 关联合同列表 -->
                <el-form-item prop="contractList" label-width="0">
                  <el-table
                    :data="form.contractList"
                    stripe
                    v-loading="formLoading"
                  >
                    <el-table-column
                      :label="$t('omsCustomer.label.contractName')"
                      prop="contractName"
                    />
                    <el-table-column
                      :label="$t('omsCustomer.label.signatory')"
                      prop="contractPartner"
                    />
                    <el-table-column
                      :label="$t('omsCustomer.label.contractCode')"
                      prop="contractCode"
                    />
                    <el-table-column
                      :label="$t('omsCustomer.label.contractPeriod')"
                    >
                      <template #default="scope">
                        {{ parseDateTime(scope.row.startDate, "date") }}
                        {{ t("omsCustomer.label.to") }}
                        {{ parseDateTime(scope.row.endDate, "date") }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('omsCustomer.label.createTime')"
                    >
                      <template #default="scope">
                        <span>
                          {{ parseDateTime(scope.row.signDate, "date") }}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="qualificationInformation item_content">
            <div class="title">
              {{ $t("omsCustomer.label.financeInformation") }}
              <span class="encryptBox">
                <el-icon
                  @click="getRealPhone(3)"
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['oms:customer:eye']"
                >
                  <component :is="form.financialMobileShow ? 'View' : ''" />
                </el-icon>
              </span>
            </div>
            <div class="grad-row">
              <el-row>
                <el-col :span="6">
                  <!-- 开户银行 -->
                  <el-form-item :label="$t('omsCustomer.label.bankName')">
                    {{ form.financialBank ? form.financialBank : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 银行账号 -->
                  <el-form-item :label="$t('omsCustomer.label.bankAccount')">
                    {{ form.financialAccount ? form.financialAccount : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 开户名 -->
                  <el-form-item :label="$t('omsCustomer.label.accountName')">
                    {{ form.financialName ? form.financialName : "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 财务联系人 -->
                  <el-form-item
                    :label="$t('omsCustomer.label.financialContactPerson')"
                  >
                    {{ form.financialContact ? form.financialContact : "-" }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <!-- 联系电话 -->
                <el-col :span="6">
                  <!-- 联系电话 -->
                  <el-form-item :label="$t('omsCustomer.label.mobileNumber')">
                    <span v-if="form.financialMobile" class="pr4">
                      {{ form.financialCountryAreaCode }}
                    </span>
                    {{ form.financialMobile || "-" }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- 结算币种 -->
                  <el-form-item
                    :label="$t('omsCustomer.label.settlementCurrency')"
                  >
                    <span v-if="form.financialSettlementCurrency == 'CNY'">
                      {{ $t("omsCustomer.label.RMB") }}
                    </span>
                    <span v-else-if="form.financialSettlementCurrency == 'USD'">
                      {{ $t("omsCustomer.label.dollar") }}
                    </span>
                    <span v-else>-</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div
            class="tradingAndLogistics item_content"
            style="border-bottom: unset !important"
          >
            <div class="title">
              {{ $t("omsCustomer.label.licenseInformation") }}
            </div>
            <div class="grad-row">
              <el-row>
                <el-col :span="24">
                  <!-- 营业执照 -->
                  <el-form-item
                    :label="$t('omsCustomer.label.businessLicense')"
                    prop="businessLicenseUrl"
                  >
                    <upload-multiple
                      :custom-tips="$t('omsCustomer.message.pictureTip')"
                      :modelValue="form.businessLicenseUrl"
                      @update:model-value="onChangeMultiple"
                      ref="detailPicsRef"
                      :limit="1"
                      :formRef="formUpdateRef"
                      class="modify-multipleUpload"
                      name="detailPic"
                      :isDelete="false"
                    >
                      <template #default="{ file }">点击上传</template>
                    </upload-multiple>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6">
                  <!-- 统一社会信用代码 -->
                  <el-form-item
                    :label="$t('supplierManagement.label.creditCode')"
                    prop="creditCode"
                    label-width="160"
                  >
                    {{ form.creditCode ? form.creditCode : "-" }}
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>

      <div class="page-footer">
        <div class="operation">
          <el-button @click="handleCancel">
            {{ $t("omsCustomer.button.back") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "CustomerDetail",
  inheritAttrs: false,
});

import CustomerApi, { customerForm } from "@/modules/oms/api/customer";
import { useRoute, useRouter } from "vue-router";
import { parseDateTime } from "@/core/utils/index.js";
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const formUpdateRef = ref();
const formRef = ref(ElForm);
const loading = ref(false);
const formLoading = ref(false);

// 角色表单
const form = reactive<customerForm>({});

function onChangeMultiple(val: any) {
  form.businessLicenseUrl = val;
}

const handleCancel = async () => {
  await tagsViewStore.delView(route);
  router.push({
    path: "/oms/omsCustomer/customerManagement",
  });
};

function getRealPhone(tip: number) {
  CustomerApi.queryDetail({ id: route.query.id })
    .then((data: any) => {
      if (tip == 1) {
        form.contactMobile = data.contactMobile;
        form.contact = data.contact;
        form.mobilePhoneShow = false;
      } else if (tip == 2) {
        form.receiverMobile = data.receiverMobile;
        form.receiverName = data.receiverName;
        form.fullReceiverAddress = data.fullReceiverAddress;
        form.receiverMobileShow = false;
      } else {
        form.financialMobile = data.financialMobile;
        form.financialContact = data.financialContact;
        form.financialMobileShow = false;
      }
    })
    .finally(() => {});
}

function queryDetail() {
  formLoading.value = true;
  let params = {
    id: route.query.id,
  };
  CustomerApi.queryDetailMobileEncrypt(params)
    .then((data: any) => {
      Object.assign(form, data);
      form.mobilePhoneShow = true;
      form.receiverMobileShow = true;
      form.financialMobileShow = true;
    })
    .finally(() => {
      formLoading.value = false;
    });
}

onMounted(() => {
  queryDetail();
});
</script>
<style scoped lang="scss">
.addCustomer {
  background: #ffffff;
  border-radius: 4px;
  .page-title {
    cursor: pointer;
    padding: 20px 30px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #151719;
    line-height: 24px;
    font-style: normal;
    border-bottom: 1px solid #e5e7f3;
    .el-icon {
      margin-right: 8px;
    }
    .display_block {
      display: inline-block;
    }
  }
  .page-content {
    padding: 0px 30px 24px 30px;
    .item_content {
      border-bottom: 1px solid #e5e7f3 !important;
    }
    .title {
      padding: 24px 0px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }
  .content {
    margin-top: 20px;
  }
  .page-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 22px 30px;
    border-top: 1px solid #e5e7f3;
  }
  .flex_style {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
  .encryptBox {
    word-wrap: break-word;
    word-break: break-all;
  }

  .encryptBox-icon {
    margin-left: 4px;
    cursor: pointer;
    vertical-align: text-top;
  }
  .pr4 {
    padding-right: 4px;
  }
}
</style>
