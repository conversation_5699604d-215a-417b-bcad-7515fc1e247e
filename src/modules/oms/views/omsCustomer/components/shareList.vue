<template>
  <el-drawer
    v-model="isVisible"
    :title="title"
    width="500px"
    @close="closeDialog"
  >
    <div class="drawer-content">
      <div class="select">
          {{ $t("omsCustomer.label.selected") }}
        <span class="num">{{ selectedIds.length }}</span>
          {{ $t("omsCustomer.label.sharingNum") }}

      </div>
        <div class="tree-title"><el-icon class="icon"><OfficeBuilding /></el-icon> <span>{{tenantName}}</span></div>
      <el-tree
        ref="treeRef"
        :data="tableData"
        show-checkbox
        node-key="id"
        :default-expanded-keys="defaultExpandedNodes"
        :expand-on-click-node="false"
        :props="defaultProps"
        @check-change="handleCheckChange"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <div class="label">{{ node.label }}</div>

            <div
              class="default"
              v-if="data.type === 'u' && data.isDefault === 1"
              @click.stop="handleDefault(node)"
            >
                {{ $t("omsCustomer.button.isDefault") }}
            </div>
            <div class="action" v-if="data.type === 'u' && !data.isDefault">
              <el-button type="primary" link @click.stop="handleDefault(node)">
                  {{ $t("omsCustomer.button.setDefault") }}
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" @click="handleConfirm">
          {{ $t("common.confirm") }}
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import CustomerApi from "@/modules/oms/api/customer";

const { t } = useI18n();
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});

const state = reactive({
  loading: false,
  total: 0,
  queryParams: {},
  treeRef: null,
  defaultProps: {
    children: "children",
    label: "name",
  },
  tableData: [
    // {
    //     "id": "1895351122733121537",
    //     "parentId": "0",
    //     "name": "研发部",
    //     "type": "d",
    //     "children": [
    //         {
    //             "id": "1930071775759368193",
    //             "parentId": "1895351122733121537",
    //             "name": "研发一组",
    //             "type": "d",
    //             "children": [
    //                 {
    //                     "id": "1930071901131309058",
    //                     "parentId": "1930071775759368193",
    //                     "name": "研发小组",
    //                     "type": "d",
    //                     "children": []
    //                 }
    //             ]
    //         },
    //         {
    //             "id": "1895351454561288194",
    //             "parentId": "1895351122733121537",
    //             "name": "bins0001",
    //             "type": "u",
    //             "children": null
    //         },
    //         {
    //             "id": "1895351582122655746",
    //             "parentId": "1895351122733121537",
    //             "name": "bins002",
    //             "type": "u",
    //             "children": null
    //         },
    //         {
    //             "id": "1902555761563934721",
    //             "parentId": "1895351122733121537",
    //             "name": "魏富刚",
    //             "type": "u",
    //             "children": null
    //         },
    //         {
    //             "id": "1911662767683973121",
    //             "parentId": "1895351122733121537",
    //             "name": "商品模块权限测试",
    //             "type": "u",
    //             "children": null
    //         },
    //         {
    //             "id": "1930179324205019137",
    //             "parentId": "1895351122733121537",
    //             "name": "易盼的账号",
    //             "type": "u",
    //             "children": null
    //         }
    //     ]
    // },
    // {
    //     "id": "1901903527587086338",
    //     "parentId": "0",
    //     "name": "仓储部",
    //     "type": "d",
    //     "children": null
    // }
  ],
  selectedIds: [],
  defaultExpandedNodes: [],
  tenantName: "",
}) as any;

const {
  loading,
  queryParams,
  treeRef,
  defaultProps,
  tableData,
  selectedIds,
  defaultExpandedNodes,
  tenantName
} = toRefs(state);

const emit = defineEmits(["update:dialogVisible", "onSubmit"]);

const isVisible = computed({
  get: () => {
    return props.dialogVisible;
  },
  set: (val: any) => {
    closeDialog();
    reset();
  },
});

/**
 * 关闭
 */

function closeDialog() {
  emit("update:dialogVisible", false);
}

/**
 * 重置
 */
function reset() {
  queryParams.value = "";
  tableData.value = [];
  defaultExpandedNodes.value = [];
}

/**
 * 查询

 */
async function handleQuery(data?: any) {
  queryParams.value = {
    ...data,
  };
  await getTableList();
  await getUserListByCustomerId();
  await getAccountInfo()
}

/**
 * 通过客户id查询共享列表
 */
function getUserListByCustomerId() {
  let params = {
    ...queryParams.value,
  };
  CustomerApi.queryUserListByCustomerId(params)
    .then((res: any) => {
      setTimeout(() => {
        const defaultUserId = res
          .filter((item: any) => item.isDefault === 1)
          .map((item: any) => item.userId)?.[0];

        const checkedKeys = res.map((item: any) => item.userId);
        console.log(checkedKeys);
        console.log("defaultUserId" + defaultUserId);
        treeRef.value!.setCheckedKeys(checkedKeys, false);

        defaultExpandedNodes.value = checkedKeys;
        setDefaultById(tableData.value, defaultUserId);
          handleCheckChange()
      }, 500);
    })
    .catch((err: any) => {
      console.warn(err);
    })
    .finally(() => {});
}

/**
 * 查询列表
 */
function getTableList() {
  loading.value = true;
  CustomerApi.getDeptUserTree()
    .then((res: any) => {
      tableData.value = res || [];
    })
    .catch((err: any) => {
      tableData.value = [];
      console.warn(err);
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 勾选 计算人数
 */
function handleCheckChange(data?: any, checked?: any) {
  let nodes = treeRef.value!.getCheckedNodes(true, false);
  selectedIds.value = filterTypeDIds(nodes);
}

/**
 * 设为默认
 */

function handleDefault(node: any) {
  // 获取当前节点的 key
  const currentNodeKey = node.key;

  // 获取当前已选中的节点 keys
  const checkedKeys = treeRef.value!.getCheckedKeys();

  // 设置当前节点为默认节点
  setDefaultById(tableData.value, currentNodeKey);

  // 如果当前节点已经是选中状态，则不处理
  if (checkedKeys.includes(currentNodeKey)) {
    return;
  }

  // 否则，选中当前节点，不影响其他节点
  treeRef.value!.setCheckedKeys([...checkedKeys, ...[currentNodeKey]], true);
}

/**
 * 确定
 */
function handleConfirm() {
  let nodes = treeRef.value!.getCheckedNodes(true, false);

  const selectedIds = filterTypeDIds(nodes);

  const defaultUserIds = selectedIds
    .filter((item: any) => item.isDefault === 1)
    .map((item: any) => item.userId);

  if (defaultUserIds.length === 0) {
    ElMessage.error(t("omsCustomer.message.noDefault"));
    return;
  }
  const userIds = selectedIds.map((item: any) => {
    return {
      userId: item.id,
      isDefault: item.isDefault,
    };
  });

  let params = {
    customerId: queryParams.value.customerId,
    userIds: userIds,
  };
  console.log(params);
  CustomerApi.updateCustomerUser(params).then((res: any) => {
    ElMessage.success(t("omsCustomer.message.actionSucess"));
    emit("onSubmit");
    closeDialog();
  });
}

/**
 * 获取员工id
 * @param data
 */
function filterTypeDIds(data: any) {
  const result: any[] = [];

  function traverse(node: any) {
    if (Array.isArray(node)) {
      for (const item of node) {
        if (item.type === "u") {
          result.push(item);
          if (item.children && item.children.length > 0) {
            traverse(item.children);
          }
        }
      }
    } else if (typeof node === "object" && node !== null) {
      if (node.type === "u") {
        result.push(node);
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      }
    }
  }

  traverse(data);
  return result;
}

/**
 * 设置默认联系人，根据ID在树形结构中查找并设置 isDefault: 1
 * @param treeData 树形结构的数据
 * @param targetId 需要设置 isDefault 的目标节点 ID
 */
function setDefaultById(treeData: any[], targetId: string) {
  function traverse(nodes: any[]) {
    for (const node of nodes) {
      if (node.id === targetId) {
        node.isDefault = 1;
      } else {
        node.isDefault = 0;
      }
      if (node.children && Array.isArray(node.children)) {
        traverse(node.children);
      }
    }
  }

  traverse(treeData);
}

/**
 * 获取主账号信息
 */
function getAccountInfo() {
    let params = {
        tenantId:queryParams.value.tenantId,
    };
    CustomerApi.getPhoneNum(params)
      .then((data) => {
          tenantName.value = data.tenantName;
      })
      .finally(() => {});
}

defineExpose({
  handleQuery,
});
</script>

<style lang="scss" scoped>
.drawer-content {
    .tree-title{
        padding: 5px 10px 5px 5px;
        display: flex;
        align-items: center;
        .icon{
            margin-right: 6px;
            color: #6b6b6b;
        }
    }
  .custom-tree-node {
    display: flex;
    width: 100%;

    .label {
    }

    .default {
      color: #ffffff;
      padding: 0 6px;
      background-color: #ff9c00;
      border-radius: 4px;
      font-size: 13px;
      margin-left: 20px;
    }

    .action {
      margin-left: 20px;
      display: none;
    }

    &:hover {
      .action {
        display: block;
      }
    }
  }

  .select {
    font-weight: 400;
    font-size: 14px;
    color: #52585f;
    margin-bottom: 16px;

    .num {
      color: var(--el-color-primary);
    }
  }
}
</style>
