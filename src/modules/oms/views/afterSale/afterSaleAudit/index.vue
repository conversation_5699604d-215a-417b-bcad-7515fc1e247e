<template>
  <div class="app-container">
    <div class="afterSaleAudit">
      <el-card class="mb-12px search-card">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="84px">
            <el-form-item prop="orderReturnCode" :label="$t('afterSaleManagement.label.orderReturnCode')">
              <el-input
                v-model="queryParams.orderReturnCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                :maxlength="50"
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item prop="dateRange" :label="$t('afterSaleManagement.label.dateType')">
              <el-select
                v-model="queryParams.dateType"
                :placeholder="$t('common.placeholder.selectTips')"
                class="!w-[160px] ml5px"
              >
                <el-option v-for="item in dateTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
              </el-select>
              <el-date-picker
                :editable="false"
                class="!w-[360px]"
                v-model="queryParams.dateRange"
                type="datetimerange"
                range-separator="~"
                :start-placeholder="$t('afterSaleManagement.label.startTime')"
                :end-placeholder="$t('afterSaleManagement.label.endTime')"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="defaultTime"
                :placeholder="$t('common.placeholder.selectTips')"
              />
              <span class="ml16px mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(1)">{{$t('afterSaleManagement.label.today')}}</span>
              <span class="mr14px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(2)">{{$t('afterSaleManagement.label.yesterday')}}</span>
              <span class="mr16px cursor-pointer" style="color:var(--el-color-primary)" @click="handleChangeDateRange(3)">{{$t('afterSaleManagement.label.weekday')}}</span>
            </el-form-item>
            <el-form-item prop="orderCode" :label="$t('afterSaleManagement.label.orderCode')">
              <el-input
                v-model="queryParams.orderCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                :maxlength="50"
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item prop="orderBatchCode" :label="$t('afterSaleManagement.label.orderBatchCode')">
              <el-input
                v-model="queryParams.orderBatchCode"
                :placeholder="$t('common.placeholder.inputTips')"
                clearable
                :maxlength="50"
                class="!w-[256px]"
              />
            </el-form-item>
            <el-form-item prop="syncStatus" :label="$t('common.syncStatus')">
              <el-select v-model="queryParams.syncStatus" clearable :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]">
                <el-option v-for="item in statusList" :key="item.key" :value="item.key" :label="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-hasPerm="['oms:afterSaleAudit:search']" type="primary" @click="handleQuery">
                {{$t('common.search')}}
              </el-button>
              <el-button v-hasPerm="['oms:afterSaleAudit:reset']" @click="handleResetQuery">
                {{$t('common.reset')}}
              </el-button>
              <el-button v-hasPerm="['oms:afterSaleAudit:sync']" @click="handleSync">
                {{$t('common.manualSynchronization')}}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="content-card">
        <div class="panelContent">
          <div class="panelItem" @click="changePanel(item)" :class="{ active: item.active }" v-for="(item, index) in tabs" :key="item.key">
            {{ item.value }}
          </div>
        </div>
        <el-table
          v-loading="loading"
          :data="afterSaleAuditList"
          highlight-current-row
          stripe
          ref="afterSaleTableRef"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <Empty/>
          </template>
          <el-table-column fixed="left" type="selection" width="60" align="center" />
          <el-table-column :label="$t('afterSaleManagement.label.orderReturnCode')" prop="orderReturnCode" show-overflow-tooltip min-width="180">
            <template #default="scope">
              <span @click="toDetail(1,scope.row.orderReturnCode)" style="color:var(--el-color-primary);cursor:pointer;">{{scope.row.orderReturnCode}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.orderBatchCode')" prop="orderBatchCode" show-overflow-tooltip min-width="180"></el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.orderCode')" prop="orderCode" show-overflow-tooltip min-width="180">
            <template #default="scope">
              <span @click="toDetail(2,scope.row.orderId)" style="color:var(--el-color-primary);cursor:pointer;">{{scope.row.orderCode}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.orderReturnStatus')" min-width="150" prop="orderReturnStatus" show-overflow-tooltip>
            <template #default="scope">
              <div class="purchase">
                <span class="purchase-status purchase-status-color1" v-if="scope.row.orderReturnStatus==1">{{$t('afterSaleManagement.statusList.toBeTreated')}}</span>
                <span class="purchase-status purchase-status-color3" v-if="scope.row.orderReturnStatus==2">{{$t('afterSaleManagement.statusList.agreed')}}</span>
                <span class="purchase-status purchase-status-color5" v-if="scope.row.orderReturnStatus==3">{{$t('afterSaleManagement.statusList.declined')}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.productNames')" prop="productNames" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.customerName')" prop="customerName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.returnReasonName')" prop="returnReasonName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.returnAmount')" prop="returnAmount" show-overflow-tooltip align="right">
            <template #default="scope">
              <span v-if="scope.row.currencyCode == 'CNY'">￥{{scope.row.returnAmount}}</span>
              <span v-else>${{scope.row.returnAmount}}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.createTime')" prop="createTime" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('afterSaleManagement.label.returnTime')" prop="returnTime" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.returnTime">{{ parseDateTime(scope.row.returnTime, "dateTime") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.syncStatus')" prop="syncStatus">
            <template #default="scope">
              <span v-if="scope.row.syncStatus == 1">{{ $t('common.success') }}</span>
              <span v-else-if="scope.row.syncStatus == 2">{{ $t('common.fail') }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="$t('common.handle')" width="100">
            <template #default="scope">
<!--              v-hasPerm="['oms:afterSaleAudit:edit']"-->
              <el-button
                v-if="scope.row.orderReturnStatus==1 && scope.row.auditUserId == userId"
                type="primary"
                link
                @click="afterSaleHandel(scope.row.orderReturnCode)"
              >
                {{$t('afterSaleManagement.button.edit')}}
              </el-button>
              <el-button
                v-hasPerm="['oms:afterSaleAudit:detail']"
                type="primary"
                v-if="scope.row.orderReturnStatus!=1 || (scope.row.orderReturnStatus==1 && scope.row.auditUserId != userId)"
                link
                @click="afterSaleDetail(scope.row.orderReturnCode)"
              >
                {{$t('afterSaleManagement.button.detailBtn')}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.limit"
            @pagination="handleQuery"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup lang="ts">
    import {useRouter} from "vue-router";
    import afterSaleManagementApi,{AfterSaleManagementPageV0,QueryAfterSaleManagementDto} from "@/modules/oms/api/afterSaleManagement";
    import moment from "moment";
    import { changeDateRange,parseDateTime,convertToTimestamp } from "@/core/utils/index.js";
    defineOptions({
        name: "AfterSaleAudit",
        inheritAttrs: false,
    });
    const router = useRouter();
    const { t } = useI18n();
    const userId = localStorage.getItem("userId");
    const queryFormRef = ref(null);
    const queryParams = reactive<QueryAfterSaleManagementDto>({
        orderReturnStatus:'',
        dateType:1,
        // dateRange: [moment().subtract('days', 29).format('YYYY-MM-DD')+' 00:00:00',moment(new Date()).format('YYYY-MM-DD')+' 23:59:59'],
        dateRange: [],
        page: 1,
        limit: 20,
    });
    const statusList = ref([
      {
        key:1,
        value:t('common.success'),
      },
      {
        key:2,
        value:t('common.fail'),
      },
    ]);
    const afterSaleTableRef = ref(null);
    const multipleSelection = ref([]);
    const defaultTime: [Date, Date] = [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59),
    ];
    const loading = ref(false);
    const total = ref(0);
    const dateTypeList = ref([
        {
            key: 1,
            value: t('afterSaleManagement.dateTypeList.createDate')
        },
        {
            key: 2,
            value:t('afterSaleManagement.dateTypeList.handlerDate')
        }
    ]);

    const tabs = ref([
        {
            key: '',
            value: t('afterSaleManagement.statusList.all'),
            active: true
        },
        {
            key: 1,
            value: t('afterSaleManagement.statusList.toBeTreated'),
            active: false
        },
        {
          key: 2,
          value: t('afterSaleManagement.statusList.agreed'),
          active: false
        },
        {
            key: 3,
            value: t('afterSaleManagement.statusList.declined'),
            active: false
        },
    ]);
    const changePanel = (data) => {
        tabs.value.map(item => item.active = false);
        data.active =true;
        queryParams.orderReturnStatus = data.key;
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
        afterSaleTableRef.value.clearSelection()
        multipleSelection.value = []
    }
    function handleSelectionChange(val) {
      multipleSelection.value = val;
    }

    const afterSaleAuditList = ref<AfterSaleManagementPageV0[]>();

    //查询列表
    function handleQuery() {
        loading.value = true;
        let params = {
            ...queryParams,
        }
        params.queryType=queryParams.dateType
        if(queryParams.dateRange && queryParams.dateRange.length>0){
            params.startTime=convertToTimestamp(queryParams.dateRange[0])
            params.endTime=convertToTimestamp(queryParams.dateRange[1])
        }
        delete params.dateType
        delete params.dateRange
        afterSaleManagementApi.getPageList(params).then((data)=>{
            afterSaleAuditList.value = data.records;
            total.value = parseInt(data.total);
        }).finally(() => {
            loading.value = false;
        });
    }
    //重置
    function handleResetQuery() {
        queryFormRef.value.resetFields();
        queryParams.dateType=1
        queryParams.page = 1;
        queryParams.limit = 20;
        handleQuery();
    }
    //手动同步
    function handleSync() {
      if(multipleSelection.value.length === 0){
        return  ElMessage.error(t('afterSaleManagement.message.chooseData'));
      }
      if(multipleSelection.value.length > 20){
        return  ElMessage.error(t('afterSaleManagement.message.chooseData20'));
      }
      let disabledSyncData = []
      multipleSelection.value.forEach(selection=>{
        if(selection.returnType == 3 || selection.orderReturnStatus != 2 || selection.syncStatus == 1){
          disabledSyncData.push(selection.orderReturnCode)
        }
      })
      if(disabledSyncData.length > 0){
        return ElMessage.error(t('afterSaleManagement.message.syncData') + disabledSyncData.join('、') + t('afterSaleManagement.message.syncTips'))
      }
      let idList = []
      multipleSelection.value.forEach(selection=>{
        idList.push(selection.id)
      })
      loading.value = true;
      afterSaleManagementApi.sync(idList).then((res)=>{
        ElMessage.success(t('afterSaleManagement.message.syncSuccess'));
        queryParams.page = 1;
        handleQuery();
        afterSaleTableRef.value.clearSelection()
        multipleSelection.value = []
      }).catch(()=>{
        afterSaleTableRef.value.clearSelection()
        multipleSelection.value = []
      }).finally(() => {
        loading.value = false;
      });
    }
    /** 时间转换 */
    function handleChangeDateRange(val: any) {
        queryParams.dateRange = changeDateRange(val);
    }
    function afterSaleHandel(orderReturnCode) {
        router.push({
            path:"/oms/afterSale/afterSaleAudit/afterSaleDetail",
            query:{orderReturnCode:orderReturnCode,type:'edit',title:t('afterSaleManagement.button.edit')}
        })
    }
    function afterSaleDetail(orderReturnCode){
        router.push({
            path:"/oms/afterSale/afterSaleAudit/afterSaleDetail",
            query:{orderReturnCode:orderReturnCode,type:'detail',title:t('afterSaleManagement.button.afterSaleDetail')}
        })
    }
    function toDetail(val,code){
        if(val == 1){
            router.push({
                path:"/oms/afterSale/afterSaleAudit/afterSaleDetail",
                query:{orderReturnCode:code,type:'detail',title:t('afterSaleManagement.button.afterSaleDetail')}
            })
        }else if(val == 2){
            router.push({
                path: "/oms/order/orderDetail",
                query: {id:code,type:'detail',title:t("omsOrder.button.orderDetail")}
            });
        }
    }

    onActivated(() => {
        handleQuery();
    });
</script>
<style lang="scss" scoped>
  :deep(.el-button--primary.el-button--default.is-link) {
    color: #762adb;
  }
  :deep(.el-button--danger.el-button--default.is-link) {
    color: #c00c1d;
  }
  .afterSaleAudit{
    height: 100%;
    display: flex;
    flex-direction: column;

    .search-card {
      flex-shrink: 0;
    }
    .content-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :deep(.el-card__body) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .el-table {
        flex: 1;
        overflow: auto;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
    .panelContent {
      display: flex;
      border-bottom: 1px solid #F2F3F4;
      width: 100%;
      margin-bottom: 16px;
      .panelItem {
        font-size: 14px;
        color: #151719;
        padding: 10px 39px;
        cursor: pointer;
        &.active {
          color: var(--el-color-primary);
          border-bottom: 2px solid var(--el-color-primary);
        }
      }
    }
  }

</style>

