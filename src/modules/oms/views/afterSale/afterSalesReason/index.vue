<template>
  <div class="app-container">
    <div class="afterSalesReason">
      <el-card shadow="never" class="table-container">
        <template #header>
          <el-button v-hasPerm="['oms:afterSalesReason:batchDelete']"  type="danger" @click="batchDeleteReason">
            {{$t('afterSalesReason.button.batchDelete')}}
          </el-button>
          <el-button v-hasPerm="['oms:afterSalesReason:add']"  type="primary" @click="addReason()">
            {{$t('afterSalesReason.button.add')}}
          </el-button>
        </template>

        <el-table
          v-loading="loading"
          :data="afterSalesReasonList"
          highlight-current-row
          stripe
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <Empty/>
          </template>
          <el-table-column type="selection" width="60" align="center"/>
          <el-table-column :label="$t('afterSalesReason.label.afterSalesReason')" prop="reasonName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('afterSalesReason.label.status')" prop="enableStatus" min-width="120">
            <template #default="scope">
              <el-switch
                :active-text="$t('common.activeBtn')"
                :inactive-text="$t('common.deactivateBtn')"
                inline-prompt
                v-model="scope.row.enableStatus"
                :active-value="1"
                :inactive-value="0"
                v-hasPerm="['oms:afterSalesReason:updateStatus']"
                @change="
                handleStatusChange(scope.row.enableStatus, scope.row.id, scope.row)
              "
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('afterSalesReason.label.updateTime')" prop="updateTime" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.updateTime">{{ parseDateTime(scope.row.updateTime, "dateTime") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.handle')" width="300">
            <template #default="scope">
              <el-button
                v-hasPerm="['oms:afterSalesReason:edit']"
                type="primary"
                link
                @click="editReason(scope.row)"
              >
                {{$t('common.edit')}}
              </el-button>
              <el-button
                v-hasPerm="['oms:afterSalesReason:delete']"
                type="danger"
                link
                @click="handleDelete(scope.row.id)"
              >
                {{$t('common.delete')}}
              </el-button>
              <el-button
                v-hasPerm="['oms:afterSalesReason:operationLog']"
                type="primary"
                link
                @click="operationLog(scope.row.id)"
              >
                {{$t('afterSalesReason.button.operationLog')}}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-drawer v-model="showDialog" :title="handleType === 'add'?$t('afterSalesReason.title.addReason'):$t('afterSalesReason.title.editReason')" :close-on-click-modal="false" size="850px" @close="close">
        <el-form :model="reasonForm" :rules="reasonFormRules" ref="reasonFormRef" label-position="top">
          <el-form-item
            :label="$t('afterSalesReason.label.afterSalesReason')"
            prop="reasonName"
          >
            <el-input
              type="text"
              :placeholder="$t('common.placeholder.inputTips')"
              v-model="reasonForm.reasonName"
              :maxlength="30"
              clearable
            />
          </el-form-item>
          <el-form-item
            :label="$t('afterSalesReason.label.status')"
            prop="enableStatus"
          >
            <el-radio-group v-model="reasonForm.enableStatus">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCloseHandler()">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="onSaveHandler"
          >
            {{ $t("common.confirm") }}
          </el-button>
        </span>
        </template>
      </el-drawer>
      <el-drawer v-model="logDialog" title="操作记录" :close-on-click-modal="false" size="850px" @close="close">
        <el-table
          :data="logList"
          highlight-current-row
          stripe
        >
          <el-table-column :label="$t('afterSalesReason.label.createTime')" prop="createTime" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.createTime">{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('afterSalesReason.label.operationName')" prop="operationName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('afterSalesReason.label.createUserName')" prop="createUserName" show-overflow-tooltip></el-table-column>
        </el-table>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeLog()">{{ $t("afterSalesReason.button.close") }}</el-button>
          </span>
        </template>
      </el-drawer>
    </div>
  </div>
</template>
<script setup lang="ts">
    defineOptions({
        name: "AfterSalesReason",
        inheritAttrs: false,
    });
    import {useRouter} from "vue-router";
    import { FormRules } from "element-plus";
    import afterSalesReasonApi,{ReasonListVO,LogListVO} from "@/modules/oms/api/afterSalesReason"
    import { parseDateTime } from "@/core/utils/index.js";
    const { proxy } = getCurrentInstance();
    const router = useRouter();
    const { t } = useI18n();
    const loading = ref(false);
    const dialogLoading = ref(false);
    const showDialog = ref(false);
    const logDialog = ref(false);
    const handleType = ref("add");
    const reasonFormRef = ref(ElForm);
    const reasonForm = reactive({
        id:'',
        reasonName:'',
        enableStatus:'',
    });
    const multipleSelection = ref([]);
    const reasonFormRules = reactive<FormRules>({
        reasonName:[
            {
                required:true,
                message: proxy.$t("afterSalesReason.rules.reasonNameRules"),
                trigger: "blur",
            }
        ],
        enableStatus:[
            {
                required:true,
                message: proxy.$t("afterSalesReason.rules.statusRules"),
            }
        ]
    });
    const afterSalesReasonList = ref<ReasonListVO[]>();
    const logList = ref<LogListVO>();
    //查询售后原因列表
    function handleQuery(){
        loading.value = true
        afterSalesReasonApi.getReasonList().then((data)=>{
            afterSalesReasonList.value = data;
        }).finally(() => {
            loading.value = false;
        });
    }
    //删除售后原因
    function handleDelete(id?: string) {
        ElMessageBox.confirm(t('afterSalesReason.message.deleteTips'), t('common.tipTitle'), {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: "warning",
        }).then(
            () => {
                loading.value = true;
                let data = [id]
                afterSalesReasonApi.deleteBatch(data)
                    .then(() => {
                        ElMessage.success(t('afterSalesReason.message.deleteSuccess'));
                        handleQuery();
                    })
                    .finally(() => {loading.value = false});
            },
            () => {
                ElMessage.info(t('afterSalesReason.message.deleteCancel'));
            }
        );
    }
    //新增售后原因
    function addReason(){
        handleType.value = 'add'
        showDialog.value = true
        reasonForm.enableStatus = 1
        reasonFormRef.value.clearValidate()
    }
    //编辑售后原因
    function editReason(row){
        handleType.value = 'edit'
        reasonForm.id = row.id
        reasonForm.reasonName = row.reasonName
        reasonForm.enableStatus = row.enableStatus
        showDialog.value = true
    }
    function onSaveHandler(){
        reasonFormRef.value.validate((valid) => {
            if (!valid) return;
            dialogLoading.value = true
            let params = {
                reasonName: reasonForm.reasonName,
                enableStatus: reasonForm.enableStatus
            }
            if (handleType.value == 'edit') {
                params.id = reasonForm.id
            }
            afterSalesReasonApi.addReason(params).then((data) => {
                if (handleType.value == 'add') {
                    ElMessage.success(t('afterSalesReason.message.addSuccess'));
                } else {
                    ElMessage.success(t('afterSalesReason.message.editSuccess'));
                }
                onCloseHandler()
                handleQuery()
            }).finally(() => {
                dialogLoading.value = false
            })
        })
    }
    function onCloseHandler() {
        showDialog.value = false
        handleType.value = ''
        reasonForm.id = ''
        reasonForm.reasonName = ''
        reasonForm.enableStatus = ''
        reasonFormRef.value.clearValidate()
    }
    //操作记录
    function operationLog(id?: string) {
      let params = {
          id:id
      }
      afterSalesReasonApi.getLogByBusinessId(params).then((data)=>{
          logList.value = data
          logDialog.value = true
      }).finally(() => {

      });
    }
    function closeLog() {
        logDialog.value = false
        logList.value = []
    }
    //启用/禁用
    function handleStatusChange(enableStatus?: number,id?: number,row?: any){
        let params = {
            id: id,
            enableStatus: enableStatus,
            reasonName: row.reasonName
        };
        if (row) {
            row.enableStatus = row.enableStatus === 0 ? 1 : 0; //保持switch点击前的状态
        }
        let message =
            enableStatus == 1
                ? t("afterSalesReason.message.enableSuccessTips")
                : t("afterSalesReason.message.deactivateSuccessTips");
        if (enableStatus == 0) {
            ElMessageBox.confirm(
                t("afterSalesReason.message.statusChangeTips"),
                t("common.tipTitle"),
                {
                    confirmButtonText: t("common.confirm"),
                    cancelButtonText: t("common.cancel"),
                    type: "warning",
                }
            ).then(
                () => {
                    statusChangeOpt(params, message);
                },
                () => {
                    ElMessage.info(t("afterSalesReason.message.optCancel"));
                }
            );
        } else {
            statusChangeOpt(params, message);
        }
    }
    function statusChangeOpt(params, message) {
      afterSalesReasonApi.enableStatus(params).then((data)=>{
          ElMessage.success(message);
          handleQuery()
      })
    }
    //批量删除
    function batchDeleteReason(){
      if(multipleSelection.value.length == 0){
          return ElMessage.info(t('afterSalesReason.message.selectData'));
      }
      let ids = []
      multipleSelection.value.forEach(select=>{
          ids.push(select.id)
      })
      ElMessageBox.confirm(t('afterSalesReason.message.deleteTips'), t('common.tipTitle'), {
          confirmButtonText: t('common.confirm'),
          cancelButtonText: t('common.cancel'),
          type: "warning",
      }).then(() => {
          loading.value = true;
          afterSalesReasonApi.deleteBatch(ids).then(() => {
              ElMessage.success(t('afterSalesReason.message.deleteSuccess'));
              handleQuery();
          }).finally(() => {loading.value = false});
      },
      () => {
                ElMessage.info(t('afterSalesReason.message.deleteCancel'));
            }
      );
    }
    function handleSelectionChange(val){
        multipleSelection.value = val
    }
    onActivated(() => {
        handleQuery();
    });
</script>
<style lang="scss" scoped>
  .afterSalesReason{

  }
</style>
<style lang="scss">
  .afterSalesReason{

  }
</style>
