<template>
  <div class="app-container">
    <div class="addAfterSales">
      <div class="page-title">
        <div @click="handleClose()" class="cursor-pointer mr8px"> <el-icon><Back /></el-icon></div>
        <div>
          <span>{{ $t("afterSaleManagement.button.afterSaleApply") }}</span>
        </div>
      </div>
      <div class="page-content">
        <el-form :model="form" ref="formRef" label-width="82px" label-position="right">
          <div class="title-lable">
            <div class="title-content">
              {{ $t("afterSaleManagement.label.orderInformation") }}
            </div>
          </div>
          <div :style="showDetails?'border-bottom:1px solid #E5E7F3' : ''">
            <el-row>
              <el-col :span="6">
                <el-form-item
                  :label="$t('afterSaleManagement.label.orderCode')"
                  prop="orderCode"
                  :rules="[{ required: true, message: t('afterSaleManagement.rules.orderCode'), trigger:  ['change', 'blur'] }]"
                >
                  <el-input
                    v-model="form.orderCode"
                    :placeholder="$t('common.placeholder.inputTips')"
                    clearable
                    :maxlength="50"
                    class="!w-[256px]"
                    @change="queryOrderBatchCodeByOrderCode"
                    :disabled="showDetails"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :label="$t('afterSaleManagement.label.deliveryBatch')"
                  prop="orderBatchCode"
                  :rules="[{ required: true, message: t('afterSaleManagement.rules.orderBatchCode'), trigger:  ['change', 'blur'] }]"
                >
                  <el-select v-model="form.orderBatchCode" clearable :placeholder="$t('afterSaleManagement.placeholder.selectBatchCode')" class="!w-[256px]" :disabled="showDetails">
                    <el-option v-for="(item,index) in orderBatchCodeList" :key="index" :value="item" :label="item">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4" v-if="showDetails">
                <el-form-item
                  :label="$t('afterSaleManagement.label.customer')"
                  prop="customerCode"
                  >
                  <span>{{form.customerName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="4" v-if="showDetails">
                <el-form-item
                  :label="$t('afterSaleManagement.label.warehouse')"
                  prop="warehouseName"
                >
                  <span>{{form.warehouseName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label-width="20">
                  <el-button type="primary" @click="handleQuery" :disabled="showDetails">
                    {{$t('afterSaleManagement.button.confirm')}}
                  </el-button>
                  <el-button @click="handleResetQuery">
                    {{$t('common.reset')}}
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div v-if="showDetails">
            <div class="title-lable">
              <div class="title-content">
                {{ $t("afterSaleManagement.label.returnType") }}
              </div>
            </div>
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('afterSaleManagement.label.returnType')" prop="returnType" :rules="[{required: true, message: t('afterSaleManagement.rules.returnType'), trigger:  ['change', 'blur']}]">
                  <el-select v-model="form.returnType" clearable :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]" @change="setAmount">
                    <el-option v-for="item in typeList" :key="item.key" :value="item.key" :label="item.value"   />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="form.returnType != 2 && form.returnType != 1">
                <el-form-item :label="$t('afterSaleManagement.label.approveUser')" prop="approveUser" :rules="[{required: true, message: t('afterSaleManagement.rules.approveUser'), trigger:  ['change', 'blur']}]">
                  <el-select v-model="form.approveUser" :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]" @change="setName" filterable clearable>
                    <el-option v-for="item in personList" :key="item.userId" :value="item.userId" :label="item.nickName"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="form.returnType == 2 || form.returnType == 1" :span="12">
                <!-- 退货地址 -->
                <el-form-item
                  :label="$t('afterSaleManagement.label.returnAddress')"
                  class="address_style"
                >
                  <el-form-item prop="countryId" label-width="0" :rules="[{required:true,message:t('afterSaleManagement.rules.countryId')}]">
                    <!-- 国家 -->
                    <el-select
                      v-model="form.countryId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      clearable
                      @change="countryChange"
                      class="!w-[120px]"
                    >
                      <el-option
                        v-for="(item, index) in countryList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item prop="provinceId" label-width="0" :rules="[{required:true,message:t('afterSaleManagement.rules.provinceId')}]">
                    <!-- 省 -->
                    <el-select
                      :disabled="!form.countryId"
                      v-model="form.provinceId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleProvinceChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in provinceList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item
                    prop="cityId"
                    label-width="0"
                    v-if="showCityInput"
                    :rules="[{required:true,message:t('afterSaleManagement.rules.cityId')}]"
                  >
                    <!-- 市 -->
                    <el-select
                      v-model="form.cityId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleCityChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in cityList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item
                    prop="districtId"
                    label-width="0"
                    v-if="showDistrictInput"
                    :rules="[{required:true,message:t('afterSaleManagement.rules.districtId')}]"
                  >
                    <!-- 区 -->
                    <el-select
                      v-model="form.districtId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      @change="handleDistrictChange"
                      class="!w-[120px]"
                      clearable
                    >
                      <el-option
                        v-for="(item, index) in districtList"
                        :key="index"
                        :label="item.shortName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    prop="address"
                    label-width="0"
                    :rules="[{required:true,message:t('afterSaleManagement.rules.address')}]"
                  >
                    <el-input
                      v-model="form.address"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="100"
                      clearable
                      class="!w-[330px]"
                    />
                  </el-form-item>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="form.returnType == 2 || form.returnType == 1">
                <el-form-item :label="$t('afterSaleManagement.label.returnMethod')" prop="returnMethodId" :rules="[{required: true, message: t('afterSaleManagement.rules.returnMethod'), trigger:  ['change', 'blur']}]">
                  <el-select v-model="form.returnMethodId" @change="getMethodName" clearable :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]">
                    <el-option v-for="item in returnMethodList" :key="item.key" :value="item.key" :label="item.value"   />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="form.returnType == 2 || form.returnType == 1">

              <el-col :span="12">
                <el-form-item :label="$t('afterSaleManagement.label.operationReturnTime')" class="address_style">
                  <el-form-item label-width="0" prop="returnDate" :rules="[{required:true,message:t('afterSaleManagement.rules.returnDate')}]">
                    <el-date-picker
                      v-model="form.returnDate"
                      type="date"
                      :placeholder="$t('afterSaleManagement.placeholder.pleaseChooseDate')">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label-width="0" prop="returnTime" :rules="[{required:true,message:t('afterSaleManagement.rules.returnTime')}]">
                    <el-time-picker
                      v-model="form.returnTime"
                      format="HH:mm:ss"
                      value-format="HH:mm:ss"
                      :placeholder="$t('afterSaleManagement.placeholder.chooseTime')">
                    </el-time-picker>
                  </el-form-item>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('afterSaleManagement.label.approveUser')" prop="approveUser" :rules="[{required: true, message: t('afterSaleManagement.rules.approveUser'), trigger:  ['change', 'blur']}]">
                  <el-select v-model="form.approveUser" :placeholder="$t('common.placeholder.selectTips')" class="!w-[256px]" filterable @change="setName" clearable>
                    <el-option v-for="item in personList" :key="item.userId" :value="item.userId" :label="item.nickName"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="title-lable" >
              <div class="flex_style">
                <div class="title-content">
                  {{ $t("afterSaleManagement.label.refundGoods") }}
                </div>
                <div class="button-add cursor-pointer" @click="addProduct()">
                  {{$t('afterSaleManagement.button.addRefundGoods')}}
                </div>
              </div>
            </div>
            <div>
              <el-table
                :data="form.details"
                highlight-current-row
                stripe
              >
                <el-table-column :label="$t('afterSaleManagement.label.productInformation')" min-width="230">
                  <template #default="scope">
                    <div class="product-div">
                      <div class="picture">
                        <img :src="scope.row.productImg" alt="">
                      </div>
                      <div class="product">
                        <div>
                          <span class="product-key">{{$t('afterSaleManagement.label.productCode')}}：</span>
                          <span class="product-value">{{scope.row.productCode}}</span>
                        </div>
                        <div class="product-name">{{scope.row.productName}}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('afterSaleManagement.label.productSpec')" prop="productSpecs" min-width="100" show-overflow-tooltip/>
                <el-table-column :label="$t('afterSaleManagement.label.quantityShipped')" prop="productQty" min-width="100" show-overflow-tooltip/>
                <el-table-column :label="$t('afterSaleManagement.label.totalAmount')" prop="totalSaleAmount" align="right" min-width="120" show-overflow-tooltip/>
                <el-table-column :label="'*'+$t('afterSaleManagement.label.returnQuantity')" prop="returnQuantity" min-width="260">
                  <template #default="scope">
                    <el-form-item class="mt15px" label-width="0px" :prop="'details.'+scope.$index +'.returnQuantity'" :rules="[{required:true,message: t('afterSaleManagement.rules.returnQuantityNull'),trigger: ['blur']}, { required: true, validator: validateReturnQuantity, trigger: ['blur','change'] },{pattern:
                                      /^([1-9]\d{0,7}(?:.\d{1,2})?|0.(?:[1-9]\d?|\d[1-9]))$/,message:t('afterSaleManagement.rules.returnQuantity'),trigger: ['blur','change'],}]">
                      <el-input v-model="scope.row.returnQuantity" :placeholder="$t('common.placeholder.inputTips')" @change="setQuantity" clearable></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="'*'+$t('afterSaleManagement.label.applyReturnAmount')" prop="returnAmount" min-width="260">
                  <template #default="scope">
                    <el-form-item class="mt15px" label-width="0px" :prop="'details.'+scope.$index +'.returnAmount'" :rules="[{required:true,message: t('afterSaleManagement.rules.returnAmountNull'),trigger: ['blur']},{ required: true, validator: validateReturnAmount, trigger: ['blur','change'] },{pattern:
                                      /^(0|[1-9]\d{0,7})(.\d{1,2})?$/,message:t('afterSaleManagement.rules.returnAmount'),trigger: ['blur','change'],}]">
                      <el-input v-model="scope.row.returnAmount" :placeholder="$t('common.placeholder.inputTips')" clearable @change="setTotalAmount" >
                        <template #prefix>
                          <span v-if="scope.row.currencyCode == 'CNY'">￥</span>
                          <span v-else>$</span>
                        </template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('common.handle')" width="160">
                  <template #default="scope">
                    <el-button
                      type="danger"
                      size="small"
                      link
                      @click="handleDelete(scope.$index)"
                    >
                      {{$t('common.delete')}}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div  class="table-title" v-if="form.details && form.details.length>0">
                <div style="text-align: right;background: #F4F6FA;height: 50px;line-height: 50px;padding-right: 16px">
                  <span class="mr16px">{{t('afterSaleManagement.label.productCount')}}：
                    <span style="font-size: 18px; color: #C00C1D ">{{form.refundQty}}</span>
                  </span>
                  <span>{{t('afterSaleManagement.label.totalMoney')}}：
                        <span  style="font-size: 18px; color: #C00C1D ">
                          <span v-if="form.details[0].currencyCode == 'CNY'">￥</span>
                          <span v-else>$</span>
                          {{form.returnTotalAmount}}
                        </span>
                  </span>
                </div>
              </div>
            </div>
            <div class="title-lable">
              <div class="title-content">
                {{ $t("afterSaleManagement.label.afterSalesInformation") }}
              </div>
            </div>
            <div style="position: relative;margin-bottom: 100px;">
              <el-row>
                <el-col :span="8">
                  <el-form-item :label="$t('afterSaleManagement.label.returnReasonName')" prop="returnReasonId" :rules="[{required: true, message: t('afterSaleManagement.rules.returnReasonId'), trigger:  ['change', 'blur']}]">
                    <el-select
                      v-model="form.returnReasonId"
                      :placeholder="$t('common.placeholder.selectTips')"
                      clearable
                      class="!w-[256px]"
                      @change="selectName"
                    >
                      <el-option v-for="item in returnReasonList" :key="item.id" :label="item.reasonName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('afterSaleManagement.label.refundAmount')" prop="returnAmount" :rules="[{required:true, message:t('afterSaleManagement.rules.refundAmount'),trigger: ['change', 'blur']},{pattern:
                                      /^(0|[1-9]\d{0,7})(.\d{1,2})?$/,message:t('afterSaleManagement.rules.refundAmountFormat'),trigger: ['blur','change'],}]">
                    <el-input v-model="form.returnAmount" :placeholder="$t('afterSaleManagement.placeholder.automaticCalculation')" class="!w-[256px]" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('afterSaleManagement.label.returnReasonRemark')" prop="returnReasonRemark" :rules="[{ required: true, message: t('afterSaleManagement.rules.returnReasonRemark'), trigger:  ['change', 'blur'] }]">
                    <el-input
                      :rows="10"
                      type="textarea"
                      show-word-limit
                      v-model="form.returnReasonRemark"
                      :placeholder="$t('common.placeholder.inputTips')"
                      maxlength="200"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <div style="position: absolute;top: 52px;">
                <el-form-item
                  :label="$t('afterSaleManagement.label.returnAttachmentUrls')"
                  prop="returnAttachmentUrls"
                >
                  <upload-multiple
                    :tips="`图片比例1:1`"
                    :isPrivate="`public-read`"
                    :modelValue="form.imagesUrls"
                    @update:model-value="onChangeMultiple"
                    ref="detailPicsRef"
                    :limit="6"
                    :formRef="formUpdateRef"
                    class="modify-multipleUpload"
                    name="detailPic">
                    <template #default="{ file }">
                      点击上传
                    </template>
                  </upload-multiple>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.reback") }}</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit()">{{ $t("afterSaleManagement.button.submit") }}</el-button>
      </div>
      <AddProduct
        ref="addProductRef"
        v-model:visible="dialog.visible"
        :title="dialog.title"
        @onSubmit="onSubmit"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
    import afterSaleManagementApi, {AddDataForm} from "@/modules/oms/api/afterSaleManagement";
    import afterSalesReasonApi from "@/modules/oms/api/afterSalesReason";
    import AddProduct from "./components/addProduct.vue";
    import {useRoute,useRouter} from "vue-router";
    import {useTagsViewStore} from "@/core/store";
    import CommonAPI from "@/modules/wms/api/common";
    import { parseDateTime,parseTime } from "@/core/utils/index.js";
    import {querySalesPersonUser} from "@/modules/oms/api/contract";

    defineOptions({
        name: "AddAfterSales",
        inheritAttrs: false,
    });
    const route = useRoute();
    const router = useRouter();
    const fromPage = route.query.fromPage
    const tagsViewStore = useTagsViewStore()
    const { t } = useI18n();
    const submitLoading = ref(false)
    const showDetails = ref(false)
    const formRef = ref(ElForm);
    const form = reactive<AddDataForm>({
        details:[]
    });
    const orderBatchCodeList  = ref([])
    const detailList  = ref([])
    const returnReasonList  = ref([])
    const formUpdateRef = ref(null);
    const dialog = reactive({
        title: "",
        visible: false,
    });
    const addProductRef = ref();
    const typeList = ref([
      {
        key:3,
        value:t('afterSaleManagement.typeList.refund'),
      },
      {
        key:2,
        value:t('afterSaleManagement.typeList.returnAndRefund'),
      },
      {
        key:1,
        value:t('afterSaleManagement.typeList.returnGoods'),
      },
    ]);
    const personList = ref([]);
    const returnMethodList = ref([]);
    const countryList = ref([]);
    const provinceList = ref([]);
    const cityList = ref([]);
    const districtList = ref([]);
    const showCityInput = ref(false);
    const showDistrictInput = ref(false);
    function queryPersonList() {
      querySalesPersonUser().then((res)=>{
        personList.value = res
      }).finally(()=>{

      })
    }
    function setName() {
      if(form.approveUser && form.approveUser != null && form.approveUser != '' && form.approveUser != undefined){
          let data: any = personList.value.find(
            (item: any) => item.userId === form.approveUser
          );
          if (data) {
            form.auditUserName = data.nickName ? data.nickName : "";
          } else {
            form.auditUserName = "";
          }
      }else{
        form.auditUserName = ''
      }
    }
    /** 获取国家列表 */
    function queryAllCountry() {
      const queryParams = {
        pid: "0",
      };
      CommonAPI.getAreaList(queryParams)
        .then((data: any) => {
          countryList.value = data;
        })
        .finally(() => {});
    }
    function getAreaApi(val: any, tab?: any) {
      let params = {
        pid: val,
      };
      CommonAPI.getAreaList(params)
        .then((data: any) => {
          if (tab == "province") {
            provinceList.value = data;
          } else if (tab == "city") {
            if (data.length > 0) {
              cityList.value = data;
              showCityInput.value = true;
            } else {
              showCityInput.value = false;
            }
          } else {
            if (data.length > 0) {
              showDistrictInput.value = true;
              districtList.value = data;
            } else {
              showDistrictInput.value = false;
            }
          }
        })
        .finally(() => {});
    }
    function setAmount() {
      if(form.returnType == 1 && form.details.length > 0){
        form.details.forEach(list=>{
          list.returnAmount = '0'
        })
        setTotalAmount()
      }
    }
    function countryChange(val: any) {
      form.provinceId = "";
      form.provinceName = "";
      provinceList.value = [];
      form.cityId = "";
      form.cityName = "";
      cityList.value = [];
      form.districtId = "";
      form.districtName = "";
      districtList.value = [];
      showCityInput.value = false;
      showDistrictInput.value = false;
      if (form.countryId) {
        let data: any = countryList.value.find(
          (item: any) => item.id === form.countryId
        );
        if (data) {
          form.countryName = data.shortName ? data.shortName : "";
        } else {
          form.countryName = "";
        }
        getAreaApi(form.countryId, "province");
      }
    }
    function handleProvinceChange(val: any) {
      form.cityId = "";
      form.cityName = "";
      cityList.value = [];
      form.districtId = "";
      form.districtName = "";
      districtList.value = [];
      showCityInput.value = false;
      showDistrictInput.value = false;
      let data: any = provinceList.value.find(
        (item: any) => item.id === form.provinceId
      );
      if (data) {
        form.provinceName = data.shortName ? data.shortName : "";
      } else {
        form.provinceName = "";
      }
      if (val) {
        getAreaApi(val, "city");
      }
    }

    function handleCityChange(val: any) {
      form.districtId = "";
      form.districtName = "";
      districtList.value = [];
      showDistrictInput.value = false;
      let data: any = cityList.value.find(
        (item: any) => item.id === form.cityId
      );
      if (data) {
        form.cityName = data.shortName ? data.shortName : "";
      } else {
        form.cityName = "";
      }
      if (val) {
        getAreaApi(val, "district");
      }
    }

    function handleDistrictChange(val: any) {
      let data: any = districtList.value.find(
        (item: any) => item.id === form.districtId
      );
      if (data) {
        form.districtName = data.shortName ? data.shortName : "";
      } else {
        form.districtName = "";
      }
    }
    async function handleClose() {
        await tagsViewStore.delView(route);
        router.go(-1);
    };
    function getEnableList() {
        afterSalesReasonApi.getEnableList().then((data)=>{
            returnReasonList.value = data
        })
    }
    function selectName(){
        if(form.returnReasonId){
            returnReasonList.value.forEach((list)=>{
                if(list.id == form.returnReasonId){
                    form.returnReasonName = list.reasonName
                }
            })
        }else {
            form.returnReasonName = ''
        }
    }
    function validateReturnQuantity(rule, value, callback) {
        let index = rule.field?.split('.')[1]
        let productQty = form.details[index].productQty?form.details[index].productQty:0
        if(value && productQty){
            if (value > parseFloat(productQty)) {
                callback(new Error(t('afterSaleManagement.message.returnQuantityTips')));
            } else {
                callback();
            }
        }
    }
    function validateReturnAmount(rule, value, callback) {
        let index = rule.field?.split('.')[1]
        let saleAmount = form.details[index].totalSaleAmount?form.details[index].totalSaleAmount:0
        if(value && saleAmount){
            if (value > parseFloat(saleAmount)) {
                callback(new Error(t('afterSaleManagement.message.returnAmountTips')));
            } else {
                callback();
            }
        }
    }
    function setQuantity() {
      let quantity = '0'
      form.details.forEach(item=>{
        if(item.returnQuantity){
          quantity = (parseFloat(quantity)+parseFloat(item.returnQuantity)).toFixed(2)
        }
      })
      form.refundQty = quantity
    }
    function setTotalAmount() {
        let totalAmount = '0'
        form.details.forEach(item=>{
            if(item.returnAmount){
                totalAmount = (parseFloat(totalAmount)+parseFloat(item.returnAmount)).toFixed(2)
            }
        })
        form.returnTotalAmount = totalAmount
        form.returnAmount = totalAmount
    }
    function handleDelete(index){
        form.details.splice(index, 1);
        if(form.details.length > 0){
            setTotalAmount()
        }else {
            form.returnTotalAmount = ''
            form.returnAmount = ''
        }
    }
    function queryOrderBatchCodeByOrderCode(){
        if(form.orderCode && form.orderCode != null && form.orderCode != '' && form.orderCode != undefined){
            let params = {
                orderCode:form.orderCode.replace(/\s+/g, "")
            }
            afterSaleManagementApi.queryOrderBatchCodeByOrderCode(params).then((data)=>{
                orderBatchCodeList.value = data
            })
        }
    }
    function onChangeMultiple(val) {
        form.returnAttachmentUrls=val?val:''
        if(form.returnAttachmentUrls && form.returnAttachmentUrls.length>0){
            formRef.value.clearValidate('returnAttachmentUrls') //清除图片校验文字
        }
        console.info('==form.returnAttachmentUrls=='+form)
    }
    /** 添加商品 */
    async function addProduct() {
        formRef.value?.clearValidate();
        console.log("detailList.value====",detailList.value)
        dialog.title = t('afterSaleManagement.title.addProduct');
        addProductRef.value.setFormData({productAllList:detailList.value,queryParams:{} });
        dialog.visible = true;
    }
    function handleQuery(){
        formRef.value.validate((valid) => {
            if (!valid) return;
            let params = {
                orderCode:form.orderCode.replace(/\s+/g, ""),
                orderBatchCode:form.orderBatchCode,
            }
            afterSaleManagementApi.queryDetailsByOrderCodeAndOrderBatchCode(params).then((data) => {
                form.customerCode = data.customerCode
                form.customerName = data.customerName
                form.warehouseCode = data.warehouseCode
                form.warehouseName = data.warehouseName
                form.countryId = data.countryId
                form.countryName = data.countryName
                form.provinceId = data.provinceId
                form.provinceName = data.provinceName
                form.cityId = data.cityId
                form.cityName = data.cityName
                form.districtId = data.districtId
                form.districtName = data.districtName
                form.address = data.address
                if (form.countryId) {
                  getAreaApi(form.countryId, "province");
                }
                if (form.provinceId) {
                  getAreaApi(form.provinceId, "city");
                }
                if (form.cityId) {
                  getAreaApi(form.cityId);
                }
                if(data.detailList && data.detailList.length > 0){
                    let arr = []
                    data.detailList.forEach(list=>{
                        let obj = {
                            id:list.id,
                            productCode:list.productCode,
                            productImg:list.productImg,
                            productName:list.productName,
                            productSpecs:list.productSpecs,
                            productQty:list.productQty,
                            totalSaleAmount:list.totalSaleAmount,
                            currencyCode:list.currencyCode
                        }
                        arr.push(obj)
                    })
                    detailList.value = arr
                    showDetails.value = true
                }else {
                    detailList.value = []
                    showDetails.value = false
                }
            }).finally(() => {

            });
        })
    }
    function handleResetQuery(){
      showDetails.value = false;
      formRef.value.clearValidate();
      formRef.value.resetFields();
      form.details = []
      orderBatchCodeList.value = []
    }
    function handleSubmit(){
        if(form.details && form.details.length == 0){
            return  ElMessage.error(t('afterSaleManagement.message.refundProductTips'));
        }
        formRef.value.validate((valid) => {
          console.log("valid====",valid)
            if (!valid) return;
            submitLoading.value=true
            let details = []
            let arr = []
            form.details.forEach(item=>{
                let obj = {
                    productCode:item.productCode
                }
                arr.push(obj)
            })
            const uniqueItems = new Map();
            for(let i = 0;i<arr.length;i++){
                const key = JSON.stringify(arr[i]); // 将对象转换为字符串
                if (!uniqueItems.has(key)) {
                    uniqueItems.set(key, arr[i]); // 如果尚未存在，则添加到Map中
                }else {
                    submitLoading.value=false
                    return  ElMessage.error(t('afterSaleManagement.message.sameProduct'));
                }
            }
            form.details.forEach(item=>{
              let obj = {
                  orderBatchDetailId:item.id,
                  returnAmount:Number(item.returnAmount),
                  returnQuantity:Number(item.returnQuantity)
              }
                details.push(obj)
            })
            form.returnAttachmentUrls = JSON.stringify(form.returnAttachmentUrls)
            let params = {
                details:details,
                orderCode:form.orderCode.replace(/\s+/g, ""),
                orderBatchCode:form.orderBatchCode,
                returnReasonId:form.returnReasonId,
                returnReasonName:form.returnReasonName,
                returnReasonRemark:form.returnReasonRemark,
                returnAttachmentUrls:form.returnAttachmentUrls,
                customerCode:form.customerCode,
                customerName:form.customerName,
                warehouseCode:form.warehouseCode,
                warehouseName:form.warehouseName,
                returnType:form.returnType,
                auditUserId:form.approveUser,
                auditUserName:form.auditUserName,
                returnAmount:form.returnAmount
            }
            if(form.returnType == 2 || form.returnType == 1){
              params.countryId = form.countryId
              params.countryName = form.countryName
              params.provinceId = form.provinceId
              params.provinceName = form.provinceName
              params.cityId = form.cityId
              params.cityName = form.cityName
              params.districtId = form.districtId
              params.districtName = form.districtName
              params.address = form.address
              params.returnMethodId = form.returnMethodId
              params.returnMethodName = form.returnMethodName
              if(form.returnDate && form.returnTime){
                let date = parseDateTime(form.returnDate,'date') + ' ' + form.returnTime
                params.operationReturnTime = new Date(date).getTime()

              }
            }
            console.log("params===",params)
            afterSaleManagementApi.addOrderReturn(params).then((data) => {
              ElMessage.success(t('afterSaleManagement.message.addSuccess'));
              handleClose()
            }).finally(() => {
              submitLoading.value = false;
            });
        })
    }
    function onSubmit(data) {
        if(data){
          let arr = JSON.parse(JSON.stringify(data.concat(form.details)));
          let uniqueArr =[...new Map(arr.map(item => [item.productCode, item])).values()];
          form.details=uniqueArr;
          if(form.returnType == 1){
            form.details.forEach(item=>{
              item.returnAmount = '0'
            })
            setTotalAmount()
          }
          console.log("===form.details==="+form.details);
        }
    }
    function getMethodName() {
      if(form.returnMethodId){
        let data: any = returnMethodList.value.find(
          (item: any) => item.key == form.returnMethodId
        );
        if (data) {
          form.returnMethodName = data.value ? data.value : "";
        } else {
          form.returnMethodName = "";
        }
      }else {
        form.returnMethodName = ''
      }
    }
    function queryReturnMethodList(){
      returnMethodList.value = []
      let data = {
        page:1,
        limit:100,
        enableStatus:1
      }
      afterSaleManagementApi.queryReturnMethodList(data).then((res)=>{
        if(res.records && res.records.length > 0){
          res.records.forEach(item=>{
            let obj = {
              key:item.id,
              value:item.deliveryMethodsCategoryVO.deliveryMethodsCategoryName + '/' + item.methodName,
              methodName:item.methodName
            }
            returnMethodList.value.push(obj)
          })
        }else {
          returnMethodList.value = []
        }
      })
    }
    onMounted(() => {
        getEnableList()
        queryAllCountry()
        queryReturnMethodList()
        queryPersonList()
        if(fromPage != 1){
          form.orderCode = route.query.orderCode
          orderBatchCodeList.value = [route.query.orderBatchCode]
          form.orderBatchCode = route.query.orderBatchCode
          handleQuery()
        }
    })
</script>
<style scoped lang="scss">
  .addAfterSales {
    background: #FFFFFF;
    border-radius: 4px;
    .page-content {
      padding: 0px 30px 24px 30px;
      .item_content {
        border-bottom: 1px solid #e5e7f3 !important;
      }
      .select_goods:hover {
        color: var(--el-color-primary);
      }
      .title-lable {
        padding: 24px 0px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        &::before {
          content: " ";
          display: inline-block;
          width: 4px;
          height: 16px;
          background: var(--el-color-primary);
          border-radius: 2px;
          margin-right: 12px;
        }
      }
      .flex_style {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
      }
      .button-add{
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-weight: 600;
        font-size: 14px;
        color: var(--el-color-primary)
      }
    }
    .address_style {
      :deep(.el-form-item__label::before) {
        color: var(--el-color-danger);
        content: "*";
        margin-right: 4px;
      }
    }
  }
</style>
