import OrderAPI from "@/modules/oms/api/order";
import { ElMessage } from "element-plus";

export const useDevilery = () => {
  const { t } = useI18n();
  const createDeliveryPlan = async (data: {}) => {
    // 创建交货计划
    try {
      await OrderAPI.createDeliverySchedule(data);
      return true;
    } catch (error) {
      return false;
    }
  };

  const updateDeliveryPlan = async (data: {}) => {
    // 更新交货计划
    try {
      await OrderAPI.adjustDeliverySchedule(data);
      return true;
    } catch (error) {
      return false;
    }
  };

  const getSchedulePlanInfo = async (params: {}) => {
    // 获取交货计划详情
    try {
      return await OrderAPI.getDeliveryScheduleInfo(params);
    } catch (error) {
      return null;
    }
  };

  const getOrderDetail = async (data: { id: string; orderCode: string }) => {
    // 获取订单详情
    try {
      return await OrderAPI.queryOrderDetail(data);
    } catch (error) {
      return null;
    }
  };

  return {
    createDeliveryPlan,
    getSchedulePlanInfo,
    getOrderDetail,
    updateDeliveryPlan,
  };
};
