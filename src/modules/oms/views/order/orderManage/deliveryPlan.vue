<template>
  <div class="app-container">
    <div class="orderDetail" v-loading="loading">
      <div>
        <!--    顶部单号信息    -->
        <div class="page-title">
          <div class="purchase-title">
            <div @click="handleClose()" class="cursor-pointer mr8px">
              <el-icon>
                <Back />
              </el-icon>
            </div>
            <div>{{ t("omsOrder.label.orderCode") }}：{{ orderCode }}</div>
            <!-- <div v-if="orderDetail.orderStatus !== 0" class="ml6px cursor-pointer mr16px"
              style="color: var(&#45;&#45;el-color-primary)" @click="copyText">
              {{ t("omsOrder.button.copy") }}
            </div>-->
            <div>
              <CopyButton :text="orderCode" :showCopyText="true"></CopyButton>
            </div>
          </div>
          <div class="purchase">
            <span :class="[
              'purchase-status',
              `purchase-status-color${orderDetail.orderStatus}`,
            ]" v-if="orderDetail.orderStatus !== undefined">
              {{
                t(
                  `omsOrder.orderStatusList.${["draft", "check", "ready", "send", "finash",
                    "close"][orderDetail.orderStatus]}`
                )
              }}
            </span>
          </div>
        </div>
      </div>
      <!--      期望送达时间-->
      <div class="grad-row" style="position: absolute; top: 15px; right: 30px">
        <span class="el-form-item__label">
          {{ $t("omsOrder.label.orderCreateTime") }}
          <span class="el-form-item__content">
            &nbsp;{{
              moment(orderDetail.orderCreateTime).format("YYYY-MM-DD hh:mm:ss")
            }}
          </span>
        </span>
        <span class="el-form-item__label">
          {{ $t("omsOrder.label.planDeliveryDate") }}
          <span class="el-form-item__content">
            &nbsp;{{
              formatExpectedDeliveryTime(
                orderDetail.expectedReceivedTimeStar,
                orderDetail.expectedReceivedTimeEnd
              )
            }}
          </span>
        </span>
      </div>
      <div class="page-content">
        <el-form :model="form" ref="fromRef" label-width="96px" label-position="right">
          <!--        客户信息          -->
          <div class="title-lable">
            <div class="title-line"></div>
            <div class="title-content">
              {{ $t("omsOrder.label.customerInformation") }}
            </div>
            <div class="ml10px" v-hasPermEye="['oms:order:orderManage:eye']">
              <el-icon v-if="
                !orderDetail.isNameDecrypted ||
                !orderDetail.isMobileDecrypted ||
                (orderDetail.deliveryAddress.includes('*') &&
                  !orderDetail.isAddressDecrypted)
              " @click="handleViewInfo()" v-hasPermEye="['pms:requirements:eye']" color="#762ADB " size="16">
                <View />
              </el-icon>
            </div>
          </div>
          <div class="grad-row">
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('omsOrder.label.customerNameCopy')">
                  {{ orderDetail.customerName }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('omsOrder.label.receiveName')">
                  {{ orderDetail.contactPerson }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('omsOrder.label.receiveMobile')">
                  {{ orderDetail.contactAreaCode }}-{{
                    orderDetail.contactMobile
                  }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('omsOrder.label.deliveryAddressCopy')">
                  <span v-if="orderDetail.address" style="word-break: break-all">
                    {{ orderDetail.deliveryAddress }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item :label="$t('omsOrder.label.remarkCopy')">
                  <span v-if="orderDetail.remark" style="word-break: break-all">
                    {{ orderDetail.remark }}
                  </span>
                  <span v-else>-</span>
                </el-form-item>
              </el-col>
              <el-col :span="18" class="file-div">
                <el-form-item :label="$t('omsOrder.label.orderAttachmentFilesInformation')">
                  <upload-multiple
                    v-if="orderDetail.orderAttachmentFiles && orderDetail.orderAttachmentFiles.length > 0"
                    :isDelete="false" :showUploadBtn="false" :isShowTip="false" :listType="`text`" :tips="''"
                    :fileSize="20" :fileType="['jpg', 'jpeg', 'png', 'pdf', 'zip', 'rar']" :isPrivate="`public-read`"
                    :modelValue="orderDetail.orderAttachmentFiles" ref="detailPicsRef" :limit="1"
                    class="modify-multipleUpload" name="detailPic">
                    <template #default="{ file }">点击上传</template>
                  </upload-multiple>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div>
            <div class="title-lable" style="justify-content: space-between">
              <div class="flex-center-start">
                <div class="title-line"></div>
                <div class="title-content">
                  {{ $t("omsOrder.label.deliveryPlanInformation") }}
                </div>
              </div>
            </div>
            <div class="delivery-plan-table">
              <!-- 交货计划 -->
              <el-table :data="mergeData" border style="width: 100%" v-loading="loading" max-height="800"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }" :row-key="(row) => row.id">
                <!-- 商品信息列 -->
                <el-table-column fixed label="商品/交货日期" width="200">
                  <template #default="scope">
                    <div class="product-info">
                      <div class="product-img">
                        <el-image :src="scope.row.product?.productImg" fit="cover" style="width: 60px; height: 60px"
                          :preview-src-list="[scope.row.product?.productImg]" />
                      </div>
                      <div class="product-detail">
                        <div class="product-name">
                          {{ scope.row.product.productName }}
                        </div>
                        <div class="product-spec">
                          规格: {{ scope.row.product.productSpecs }}
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="数量单位" width="150">
                  <template #default="scope">
                    {{ scope.row.product.totalProductQty }}
                    {{ scope.row.product.productUnitName }}
                  </template>
                </el-table-column>

                <!-- 动态生成日期列，取第一条数据的日期列进行匹配 -->
                <template v-for="(dateItem, dateIndex) in deliveryDates" :key="dateIndex">
                  <el-table-column v-if="dateItem.dateStr" :label="formatDateLabel(dateItem.date)"
                    :prop="dateItem.dateStr" :width="150" class="date-column">
                    <template #header>
                      <div class="date-header">
                        <div>{{ formatDateLabel(dateItem.date) }}</div>
                        <div class="time-range">
                          <el-time-picker v-model="dateItem.timeRange" is-range size="small" range-separator="-"
                            start-placeholder="开始" end-placeholder="结束" format="HH:mm" time-format="HH:mm"
                            :arrow-control="false" :minute-step="60"  :disabledMinutes="disabledMinutes"
                            :disabled="dateItem.editFlag == 1" @change="handleTimeRangeChange(dateItem, dateIndex)" />
                        </div>
                      </div>
                    </template>
                    <template #default="scope">
                      <div class="quantity-input-wrapper" :class="{
                        'can-edit': canEdit(scope.row.batchId, scope.row.id),
                        disabled: !canInput(scope.row, dateItem.dateStr),
                      }">
                        <el-input v-model="scope.row[dateItem.dateStr].quantity"
                          :disabled="!canInput(scope.row, dateItem.dateStr)" @input="
                            handleQuantityInput(
                              scope.row,
                              dateItem.dateStr,
                              $event
                            )
                            " @focus="
                              handleQuantityFocus(scope.row, dateItem.dateStr)
                              " class="quantity-input"
                          :placeholder="!canInput(scope.row, dateItem.dateStr) ? '禁止输入' : (scope.row[dateItem.dateStr].placeholder || '点击输入')" />
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </div>
          </div>
        </el-form>
      </div>
      <div class="page-footer">
        <el-button @click="handleClose">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" @click="handlecCheckFinash">
          {{ $t("omsOrder.button.confirmDeliveryPlan") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "OrderDetail",
  inheritAttrs: false,
});

import moment from "moment";
import { useRoute, useRouter } from "vue-router";
import { useTagsViewStore } from "@/core/store";
import { onMounted, ref, reactive } from "vue";
import { ElMessage, ElMessageBox, SCOPE } from "element-plus";
import { useI18n } from "vue-i18n";
import OrderAPI, { OrderFrom } from "@/modules/oms/api/order";
import { useDevilery } from "./composables/devilery";
import { AdjustDeliveryScheduleDateInfoVO } from "@oms/types/orderScheduleInfo";
import { Decimal } from 'decimal.js';
const route = useRoute();
const router = useRouter();
const tagsViewStore = useTagsViewStore();
const { t } = useI18n();
const fromRef = ref();
const loading = ref(false);
const id = route.query.id;
const type = route.query.type;
const orderCode = route.query.orderCode;
const scheduleInfo = ref<any[]>([]);
const orderDetail = ref<OrderFrom>({});
const mergeData = ref<any[]>([]);
const form = reactive<OrderFrom>({
  orderDetailList: [],
  deliveryScheduleVOList: [],
  logList: [],
});

const {
  createDeliveryPlan,
  getSchedulePlanInfo,
  getOrderDetail,
  updateDeliveryPlan,
} = useDevilery();

async function handleClose() {
  await tagsViewStore.delView(route);
  router.go(-1);
}

const handleViewInfo = async () => {
  try {
    const data = await OrderAPI.querySrcInfo({ id: id });
    orderDetail.value.contactPerson = data.name;
    orderDetail.value.contactMobile = data.mobile;
    orderDetail.value.deliveryAddress = data.address;
    orderDetail.value.isNameDecrypted = true;
    orderDetail.value.isMobileDecrypted = true;
    orderDetail.value.isAddressDecrypted = true;
  } catch (e) { }
};

// 处理所有商品的并集列表
const productList = ref<any[]>([]);
const deliveryDates = ref<any[]>([]);

// 格式化日期显示
const formatDateLabel = (timestamp: number) => {
  if (!timestamp) return "";
  return moment(timestamp).format("M-D");
};

// 处理时间范围变更
const handleTimeRangeChange = (dateItem: any, dateIndex: number) => {
  if (
    dateItem.timeRange &&
    Array.isArray(dateItem.timeRange) &&
    dateItem.timeRange.length === 2
  ) {
    const startTime = dateItem.timeRange[0];
    const endTime = dateItem.timeRange[1];

    if (startTime && endTime) {
      // 使用moment获取小时和设置分钟为0
      const startMoment = moment(startTime).minutes(0).seconds(0).milliseconds(0);
      const endMoment = moment(endTime).minutes(0).seconds(0).milliseconds(0);

      // 更新日期时间格式字符串，用于显示
      dateItem.startTime = startMoment.format("HH:00");
      dateItem.endTime = endMoment.format("HH:00");

      // 创建完整的日期时间对象
      const baseMoment = moment(dateItem.date);

      const startDate = moment(baseMoment)
        .hours(startMoment.hours())
        .minutes(0)
        .seconds(0)
        .milliseconds(0);
      dateItem.planDateStart = startDate.valueOf();

      const endDate = moment(baseMoment)
        .hours(endMoment.hours())
        .minutes(0)
        .seconds(0)
        .milliseconds(0);
      dateItem.planDateEnd = endDate.valueOf();

      console.log(`时间范围更新: ${dateItem.startTime}-${dateItem.endTime}`);
    }
  } else if (!dateItem.timeRange) {
    dateItem.startTime = "";
    dateItem.endTime = "";
    dateItem.planDateStart = "";
    dateItem.planDateEnd = "";
  }
};

// 判断是否可编辑
const canEdit = (batchId: string, productId: string) => {
  if (!batchId) return true; // 如果没有批次ID（新建的日期列），默认可编辑

  // 获取当前批次
  const currentBatch = scheduleInfo.value.find(
    (item: any) => item.orderBatchId === batchId
  );

  // 注意：editFlag=0表示可编辑，1表示不可编辑
  return !currentBatch || currentBatch.editFlag === 0;
};

// 判断当前列是否可输入
const canInput = (row: any, dateStr: string) => {
  // 如果当前列不可编辑，直接返回false
  if (!canEdit(row.batchId, row.id)) {
    return false;
  }

  // 检查当前日期列的editFlag，如果editFlag=1则表示不可编辑
  if (row[dateStr]?.editFlag === 1) {
    return false;
  }

  const totalQty = Number(row.product.totalProductQty);
  const currentQty = Number(row[dateStr]?.quantity) || 0;

  // 如果当前列已经有值，允许编辑该值
  if (currentQty > 0) {
    return true;
  }

  // 计算其他列的总和（不包括当前列）
  const otherColumnsSum = Object.keys(row)
    .filter((key) => key !== "product" && key !== dateStr)
    .reduce((sum, key) => {
      const qty = Number(row[key]?.quantity) || 0;
      return sum + qty;
    }, 0);

  // 如果其他列的总和已经达到或超过totalProductQty，则当前列不可输入
  return otherColumnsSum < totalQty;
};

// 处理输入框数量变化
const handleQuantityInput = (row: any, dateStr: string, value: string) => {
  // 如果输入为空,允许清空输入框
  if (!value) {
    row[dateStr].quantity = "";
    return;
  }

  // 只允许输入数字
  let numValue = value.replace(/[^\d.]/g, "");

  // 只保留第一个小数点，去除多余的小数点
  numValue = numValue.replace(/\.{2,}/g, "."); // 连续小数点变一个
  numValue = numValue.replace(/^(\d*\.\d*?)\..*$/, "$1"); // 只保留第一个小数点

  // 小数点前去除多余的0（保留0.开头的情况）
  numValue = numValue.replace(/^0+(\d)/, "$1");
  numValue = numValue.replace(/^0+(\.)/, "0$1");

  // 特殊情况处理：只输入了小数点或0.等
  if (numValue === ".") numValue = "";
  if (
    numValue.startsWith("0") &&
    !numValue.startsWith("0.") &&
    numValue.length > 1
  )
    numValue = numValue.replace(/^0+/, "");

  // 限制只能有两位小数
  numValue = numValue.replace(/^(\d+)(\.\d{0,2}).*$/, "$1$2");

  const totalQty = new Decimal(row.product.totalProductQty);

  // 转换为Decimal对象进行精确计算
  const inputNum = numValue ? new Decimal(numValue) : new Decimal(0);

  // 计算其他列的总和
  const otherColumnsSum = Object.keys(row)
    .filter((key) => key !== "product" && key !== dateStr)
    .reduce((sum, key) => {
      const qty = row[key]?.quantity ? new Decimal(row[key].quantity) : new Decimal(0);
      return sum.plus(qty);
    }, new Decimal(0));

  // 验证总和是否超过totalProductQty
  if (inputNum.plus(otherColumnsSum).greaterThan(totalQty)) {
    ElMessage.warning(`输入数量不能超过总数量 ${totalQty}`);
    row[dateStr].quantity = totalQty.minus(otherColumnsSum).toString();
  } else {
    row[dateStr].quantity = numValue;
  }

  console.log(
    "数量输入:",
    row.product.productName,
    "日期:",
    dateStr,
    "新数量:",
    row[dateStr].quantity
  );
};

// 处理输入框获得焦点
const handleQuantityFocus = (row: any, dateStr: string) => {
  // 如果当前列不可输入，不设置placeholder
  if (!canInput(row, dateStr)) {
    return;
  }

  const totalQty = new Decimal(row.product.totalProductQty);

  // 计算其他列的总和
  const otherColumnsSum = Object.keys(row)
    .filter((key) => key !== "product" && key !== dateStr)
    .reduce((sum, key) => {
      const qty = row[key]?.quantity ? new Decimal(row[key].quantity) : new Decimal(0);
      return sum.plus(qty);
    }, new Decimal(0));

  // 计算剩余可输入数量
  const remainingQty = totalQty.minus(otherColumnsSum);

  // 更新当前输入框的placeholder为剩余数量
  row[dateStr].placeholder = `可输入数量: ${remainingQty.toString()}`;

  // 如果当前输入框为空，自动填入剩余数量
  /* if (!row[dateStr].quantity && remainingQty > 0) {
    row[dateStr].quantity = remainingQty;
  } */
};

// 格式化期望送达时间，显示日期+时间范围
const formatExpectedDeliveryTime = (
  startTime: string | Date,
  endTime?: string | Date
) => {
  if (!startTime) return "";

  try {
    // 使用moment处理日期
    const startMoment = moment(startTime);

    // 格式化日期部分（年-月-日）
    const dateStr = startMoment.format("YYYY-MM-DD");

    // 如果没有结束时间，只显示开始时间的完整日期和时间
    if (!endTime) {
      return startMoment.format("YYYY-MM-DD HH:mm:ss");
    }

    // 有结束时间，格式化时间范围（时:分:秒-时:分:秒）
    const endMoment = moment(endTime);

    // 返回日期和时间范围
    return `${dateStr} ${startMoment.format("HH:mm:ss")}-${endMoment.format("HH:mm:ss")}`;
  } catch (e) {
    console.error("日期格式化错误:", e);
    return String(startTime); // 发生错误时返回原始字符串，确保是字符串类型
  }
};

// 校验交货计划数据
const validateDeliveryPlan = () => {
  const validationErrors = [];

  // 创建一个映射来记录每个日期的时间范围状态
  const dateTimeMap: Record<string, { hasTimeRange: boolean, dateItem: any }> = {};
  deliveryDates.value.forEach(dateItem => {
    dateTimeMap[dateItem.dateStr] = {
      hasTimeRange: !!(dateItem.startTime && dateItem.endTime && dateItem.planDateStart && dateItem.planDateEnd),
      dateItem: dateItem
    };
  });

  mergeData.value.forEach((row) => {
    const totalQty = Number(row.product.totalProductQty);
    let sumQty = 0;
    const datesWithQuantity: string[] = [];

    // 计算所有日期列的数量总和
    Object.keys(row).forEach((key) => {
      if (key !== "product" && row[key]?.quantity) {
        const qty = Number(row[key].quantity);
        if (qty > 0) {
          sumQty += qty;
          datesWithQuantity.push(key);
        }
      }
    });

    // 检查有数量的日期是否都设置了时间范围
    for (const dateStr of datesWithQuantity) {
      if (dateTimeMap[dateStr] && !dateTimeMap[dateStr].hasTimeRange) {
        validationErrors.push({
          product: row.product,
          error: `存在交货日期${dateStr}没有设置时间范围，请设置交货时间范围`,
        });
        break; // 每个产品只报告一次错误即可
      }
    }

    // 如果数量不匹配，添加到错误列表  数量不足，交货数量还差 x斤
    if (sumQty !== totalQty) {
      validationErrors.push({
        product: row.product,
        error:
          sumQty < totalQty
            ? `数量不足，交货数量还差${(totalQty - sumQty).toFixed(2)}${row.product.productUnitName}`
            : `数量超出，超出${sumQty - totalQty}${row.product.productUnitName}`,
      });
    }
  });

  return validationErrors;
};

// 显示校验错误信息
const showValidationErrors = (errors: any[]) => {
  const errorMessage = errors
    .map((error) => {
      return `
        <div style="display: flex; align-items: center; margin-bottom: 10px;">
          <img src="${error.product.productImg}" style="width: 40px; height: 40px; margin-right: 10px;" />
          <div>
            <div>${error.product.productName}</div>
            <div>规格: ${error.product.productSpecs}</div>
            <div style="color: red;">${error.error}</div>
          </div>
        </div>
      `;
    })
    .join("");

  ElMessageBox.alert(
    `<div style="max-height: 400px; overflow-y: auto;">${errorMessage}</div>`,
    "交货计划校验失败",
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: "确定",
      type: "error",
    }
  );
};

// 构建更新交货计划的数据
const buildUpdateSubmitData = () => {
  const submitData = {
    orderId: id,
    orderCode: orderCode,
    adjustDeliveryScheduleDateDTOS: deliveryDates.value.map((dateItem) => {
      // 如果没有时间范围，则跳过这个日期
      /*   if (!dateItem.startTime || !dateItem.endTime || !dateItem.planDateStart || !dateItem.planDateEnd) {
          return {
            planDate: moment(`${dateItem.dateStr} 00:00:00`).valueOf(),
            adjustDeliveryScheduleDateDetailDTOS: [],
          };
        } */
      // 从mergeData中过滤出当前日期下有数量的商品
      const productsForThisDate = mergeData.value
        .filter((row) => {
          const dateData = row[dateItem.dateStr];
          return dateData && dateData.quantity > 0;
        })
        .map((row) => ({
          productCode: row.product.productCode,
          productName: row.product.productName,
          productSpecs: row.product.productSpecs,
          productImg: row.product.productImg,
          currencyCode: row.product.currencyCode,
          currencyName: row.product.currencyName,
          saleAmount: row.product.saleAmount,
          productUnitName: row.product.productUnitName,
          productUnitId: row.product.productUnitId,
          productQty: row[dateItem.dateStr].quantity,
          totalProductQty: row.product.totalProductQty,
          id: row.product.id,
        }));

      return {
        planDate: moment(`${dateItem.dateStr} 00:00:00`).valueOf(),
        planDateStart: moment(
          `${dateItem.dateStr} ${dateItem.startTime}`
        ).valueOf(),
        planDateEnd: moment(
          `${dateItem.dateStr} ${dateItem.endTime}`
        ).valueOf(),
        orderBatchId: dateItem.orderBatchId,
        editFlag: dateItem.editFlag,
        adjustDeliveryScheduleDateDetailDTOS: productsForThisDate,
      };
    }),
  };

  for (let item of submitData.adjustDeliveryScheduleDateDTOS) {
    item.adjustDeliveryScheduleDateDetailDTOS =
      item.adjustDeliveryScheduleDateDetailDTOS.filter(
        (detail) => detail.productQty > 0
      );

    if (item.adjustDeliveryScheduleDateDetailDTOS.length === 0) {
      delete item.planDateStart;
      delete item.planDateEnd;
    }
  }

  if (submitData.adjustDeliveryScheduleDateDTOS.length === 0) {
    ElMessage.warning(t("omsOrder.message.devileryEmpty"));
    return null;
  }

  return submitData;
};

// 构建创建交货计划的数据
const buildCreateSubmitData = () => {
  const submitData = {
    orderId: id,
    orderCode: orderCode,
    createDeliveryScheduleDateDTOS: deliveryDates.value.map((dateItem) => {
      // 如果没有时间范围，则跳过这个日期
      if (!dateItem.startTime || !dateItem.endTime || !dateItem.planDateStart || !dateItem.planDateEnd) {
        return {
          planDate: moment(`${dateItem.dateStr} 00:00:00`).valueOf(),
          createDeliveryScheduleDateDetailDTOS: [],
        };
      }

      // 从mergeData中过滤出当前日期下有数量的商品
      const productsForThisDate = mergeData.value.map((row) => ({
        productCode: row.product.productCode,
        productName: row.product.productName,
        productSpecs: row.product.productSpecs,
        productImg: row.product.productImg,
        currencyCode: row.product.currencyCode,
        currencyName: row.product.currencyName,
        saleAmount: row.product.saleAmount,
        productUnitName: row.product.productUnitName,
        productUnitId: row.product.productUnitId,
        productQty: row[dateItem.dateStr].quantity,
        totalProductQty: row.product.totalProductQty,
      }));

      return {
        planDate: moment(`${dateItem.dateStr} 00:00:00`).valueOf(),
        planDateStart: moment(
          `${dateItem.dateStr} ${dateItem.startTime}`
        ).valueOf(),
        planDateEnd: moment(
          `${dateItem.dateStr} ${dateItem.endTime}`
        ).valueOf(),
        orderBatchId: dateItem.orderBatchId,
        editFlag: dateItem.editFlag,
        createDeliveryScheduleDateDetailDTOS: productsForThisDate,
      };
    }),
  };

  // 根据createDeliveryScheduleDateDetailDTOS对数据过滤，如果createDeliveryScheduleDateDetailDTOS中商品productQty为空进行过滤
  // 如果createDeliveryScheduleDateDetailDTOS为空数组或空，删除planDateStart，planDateEnd

  for (let item of submitData.createDeliveryScheduleDateDTOS) {
    item.createDeliveryScheduleDateDetailDTOS =
      item.createDeliveryScheduleDateDetailDTOS.filter(
        (detail) => detail.productQty > 0
      );

    if (item.createDeliveryScheduleDateDetailDTOS.length === 0) {
      delete item.planDateStart;
      delete item.planDateEnd;
      // delete item.createDeliveryScheduleDateDetailDTOS;
    }
  }

  if (submitData.createDeliveryScheduleDateDTOS.length === 0) {
    ElMessage.warning(t("omsOrder.message.devileryEmpty"));
    return null;
  }

  return submitData;
};

// 提交交货计划
const handlecCheckFinash = async () => {
  // 校验所有商品的数量
  const validationErrors = validateDeliveryPlan();

  // 如果有错误，显示错误信息
  if (validationErrors.length > 0) {
    showValidationErrors(validationErrors);
    return;
  }

  // 添加确认弹窗
  ElMessageBox.confirm(
    t("omsOrder.confirm.deliveryPlanSubmit"),
    t("omsOrder.confirm.title"),
    {
      confirmButtonText: t("omsOrder.confirm.confirmText"),
      cancelButtonText: t("omsOrder.confirm.cancelText"),
      type: "warning",
    }
  )
    .then(async () => {
      try {
        loading.value = true;
        let result;
        let submitData;

        if (type === "update") {
          submitData = buildUpdateSubmitData();
          if (!submitData) return;
          result = await updateDeliveryPlan(submitData);
        } else {
          submitData = buildCreateSubmitData();
          if (!submitData) return;
          result = await createDeliveryPlan(submitData);
        }

        if (result) {
          if (type === "update") {
            ElMessage.success(t("omsOrder.message.devileryUpdateSuccess"));
          } else {
            ElMessage.success(t("omsOrder.message.devilerySuccess"));
          }
          handleClose();
        }
      } catch (error) {
        ElMessage.error(t("omsOrder.message.devileryFail"));
      } finally {
        loading.value = false;
      }
    })
    .catch(() => {
      // 用户取消操作
      ElMessage.info(t("omsOrder.message.deliveryCanceled"));
    });
};

// 生成从T+1到期望收货日期的日期列-创建交货计划使用
const generateDateColumns = (date: string) => {
  try {
    // Get tomorrow's date (T+1)
    const tomorrow = moment().add(1, "days").startOf("day");
    const dateColumns = [];

    while (tomorrow.isBefore(moment(date))) {
      // Set start time to current hour
      const startTime = moment();
      // Set end time to current hour + 1 hour
      const endTime = moment().add(1, "hour");

      dateColumns.push({
        planDate: tomorrow.format("YYYY-MM-DD"),
        // planDateStart: startTime.format("HH:mm:ss"),
        planDateStart: null,
        // planDateEnd: endTime.format("HH:mm:ss"),
        planDateEnd: null,
      });
      tomorrow.add(1, "days");
    }

    return dateColumns;
  }
  catch (error) {
    console.error("generateDateColumns: Invalid input date");
    return [];
  }

};

// 调整交货计划生成日期列
const generateDateColumnsByScheduleData = (scheduleData: any[]) => {
  const dateColumns = [];
  // 获取当前时间并设置为整点
  // const currentTime = moment().minutes(0).seconds(0).milliseconds(0);
  // const startTime = currentTime.format("HH:00:00");
  // const endTime = currentTime.add(1, "hour").format("HH:00:00");

  for (let item of scheduleData) {
    dateColumns.push({
      planDate: item.planDate,
      planDateStart:
        item.planDateStart ?
          moment(
            `${moment(item.planDateStart)}`
          ).minutes(0).seconds(0).milliseconds(0).valueOf() : '',
      planDateEnd:
        item.planDateEnd ?
          moment(
            `${moment(item.planDateEnd)}`
          ).minutes(0).seconds(0).milliseconds(0).valueOf() : '',
      orderBatchId: item.orderBatchId,
      editFlag: item.editFlag,
    });
  }
  return dateColumns;
};
// 格式化日期为YYYY-MM-DD
/*const formatDate = (date: Date | moment.Moment | string | number) => {
  return moment(date).format("YYYY-MM-DD");
};*/

// 处理交货计划数据
/*const tranferProcessScheduleData = (data: any[]) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.error("tranferProcessScheduleData: Invalid input data");
    return null;
  }

  // 直接返回data数组，因为数据结构已经符合要求
  return data.map((item) => ({
    orderBatchId: item.orderBatchId,
    planDate: item.planDate,
    planDateStart: item.planDateStart,
    planDateEnd: item.planDateEnd,
    editFlag: item.editFlag,
    adjustDeliveryScheduleDateDetailInfoVOList:
      item.adjustDeliveryScheduleDateDetailInfoVOList,
  }));
};*/

// 处理详情数据-修改交货计划
const processOrderDetailDataForUpdate = (
  dataProducts: [],
  dateColumns: [],
  scheduleData: []
) => {
  if (
    !dataProducts ||
    !Array.isArray(dataProducts) ||
    !dateColumns ||
    !Array.isArray(dateColumns)
  ) {
    console.error("processOrderDetailData: Invalid input dataProducts");
    return;
  }

  // 处理日期列数据
  deliveryDates.value = dateColumns.map((item) => {
    const dateStr = moment(item.planDate).format("YYYY-MM-DD");
    // 确保时间格式为整点
    const startTime = item.planDateStart ? moment(item.planDateStart).minutes(0).format("HH:00") : '';
    const endTime = item.planDateEnd ? moment(item.planDateEnd).minutes(0).format("HH:00") : '';
    return {
      dateStr,
      date: item.planDate,
      startTime,
      endTime,
      planDateStart: item.planDateStart,
      planDateEnd: item.planDateEnd,
      timeRange: [
        moment(`${dateStr} ${startTime}`).minutes(0).valueOf(),
        moment(`${dateStr} ${endTime}`).minutes(0).valueOf(),
      ],
      orderBatchId: item.orderBatchId,
      editFlag: item.editFlag,
    };
  });
  // console.log("deliveryDates.value------------", deliveryDates.value);

  // 处理商品数据
  productList.value = dataProducts;
  mergeData.value = dataProducts.map((product) => {
    // 创建基础数据结构
    const baseData = {
      product: product,
      ...dateColumns.reduce((acc, dateColumn) => {
        const dateStr = moment(dateColumn.planDate).format("YYYY-MM-DD");
        acc[dateStr] = {
          planDate: dateColumn.planDate,
          planDateStart: dateColumn.planDateStart,
          planDateEnd: dateColumn.planDateEnd,
          quantity: null, // 初始化为null
          editFlag: dateColumn.editFlag
        };
        return acc;
      }, {}),
    };

    // 根据scheduleData设置quantity
    scheduleData.forEach((scheduleItem) => {
      const dateStr = moment(scheduleItem.planDate).format("YYYY-MM-DD");
      if (
        scheduleItem.adjustDeliveryScheduleDateDetailInfoVOList &&
        scheduleItem.adjustDeliveryScheduleDateDetailInfoVOList.length > 0
      ) {
        const productDetail =
          scheduleItem.adjustDeliveryScheduleDateDetailInfoVOList.find(
            (detail) => detail.productCode === product.productCode
          );
        if (productDetail) {
          baseData[dateStr].quantity = productDetail.productQty;
        }
      }
    });

    return baseData;
  });

  // console.log("mergeData------------", mergeData.value);
};

// 处理详情数据-创建交货计划
const processOrderDetailDataForCreate = (data: [], dateColumns: []) => {
  /*
    // 订单商品
   [
     {
       "orgCode": null,
       "createTime": 1744178775000,
       "updateTime": 1744178775000,
       "createUser": "1894947841447854085",
       "updateUser": "1894947841447854085",
       "id": "1909850324331028482",
       "orderId": "1909850290445246466",
       "orderCode": "CO250409A000002",
       "productCode": "G25000000000000015",
       "firstCategoryId": "1894949878956990465",
       "secondCategoryId": "1894949965946855425",
       "thirdCategoryId": "1894950027791867906",
       "firstCategoryName": "蔬菜",
       "secondCategoryName": "茄瓜",
       "thirdCategoryName": "茄子",
       "productUnitName": "斤",
       "productUnitId": "1894949647372689409",
       "productName": "长茄28",
       "productSpecs": "6件/斤",
       "imagesUrls": "https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.com/yt-oxms-read-uat/image/739099e8144f4bf2afb8e349a990f64b.jpg",
       "currencyCode": "CNY",
       "currencyName": "人民币",
       "productQty": "1.00",
       "saleAmount": "60.00",
       "saleAmountRadio": "10",
       "totalSaleAmount": "60.00",
       "isDeleted": 0,
       "tenantId": "T0052",
       "remark": null
     },
   ]
  */
  /*[ // 日期列
     {
       planDate: "2025-04-10",
       planDateEnd: "16:15:13",
       planDateStart: "15:15:13"
     },
     ...
   ] */
  // 1、取出订单详情中的所有商品数据-data
  // 2、计算当前日期T+1到期望送达时间的日期列-dateColumns
  // 3、将日期列和商品数据进行关联，第一列为商品对象，第二列为商品计划商品数量productQty，第三列开始为日期列
  // 4、日期列中需要的关键字段，planDate：计划交货日期，planDateStart：计划开始时间，planDateEnd：计划结束时间，editFlag：0->正常；1->不可编辑 返回1 置灰
  // 5、table所需的Data = [{商品，数量,日期列: {planDate: 计划交货日期,planDateStart: 计划开始时间,planDateEnd: 计划结束时间,editFlag: 0->正常；1->不可编辑 返回1 置灰}}]
  // 6、将交货计划详情返回的数据进行结构转换，使其符合table所需的Data，方便后续两部分的数据合并

  // 检查输入参数
  if (
    !data ||
    !Array.isArray(data) ||
    !dateColumns ||
    !Array.isArray(dateColumns)
  ) {
    console.error("processOrderDetailData: Invalid input data");
    return;
  }

  // 处理日期列数据 - 转换为表格所需格式并存入deliveryDates
  const processedDateColumns = dateColumns?.map((dateItem, index) => {
    // 从字符串创建日期对象
    const baseDate = moment(`${dateItem?.planDate}`);
    const dateTimestamp = baseDate.valueOf();

    // 创建时间选择器使用的时间范围数组
    /* const startTimeObj = new Date();
    const [startHours, startMinutes] = dateItem?.planDateStart.split(":");
    startTimeObj.setHours(Number(startHours), Number(startMinutes), 0, 0);

    const endTimeObj = new Date();
    const [endHours, endMinutes] = dateItem?.planDateEnd.split(":");
    endTimeObj.setHours(Number(endHours), Number(endMinutes), 0, 0); */
    // 构建日期列对象
    return {
      batchId: "", // 默认空批次ID，可被后续scheduleData更新
      date: dateTimestamp,
      dateStr: dateItem?.planDate,
      startTime: dateItem?.planDateStart?.substr(0, 5),
      endTime: dateItem?.planDateEnd?.substr(0, 5),
      planDateStart: moment(
        `${dateItem?.planDate} ${dateItem?.planDateStart}`
      ).valueOf(),
      planDateEnd: moment(
        `${dateItem?.planDate} ${dateItem?.planDateEnd}`
      ).valueOf(),
      // timeRange: [startTimeObj, endTimeObj],
      timeRange: [],
      // editFlag: 0, // 默认可编辑 (0=可编辑)
      productQty: null,
    };
  });
  // 处理商品数据 - 为每个商品创建包含所有日期列的数据结构
  const processedProducts = data.map((product) => {
    // 创建时间选择器使用的时间范围数组
    /* if(!!dateColumns || dateColumns?.length > 0){
      const startTimeObj = new Date();
      const [startHours, startMinutes] = dateColumns[0]?.planDateStart.split(":");
      startTimeObj.setHours(Number(startHours), Number(startMinutes), 0, 0);

      const endTimeObj = new Date();
      const [endHours, endMinutes] = dateColumns[0]?.planDateEnd.split(":");
      endTimeObj.setHours(Number(endHours), Number(endMinutes), 0, 0);
    } */


    return {
      id: product.id,
      productCode: product.productCode,
      productName: product.productName,
      productSpecs: product.productSpecs,
      productImg: product.imagesUrls,
      currencyCode: product.currencyCode,
      currencyName: product.currencyName,
      saleAmount: product.saleAmount,
      productUnitName: product.productUnitName,
      productUnitId: product.productUnitId,
      totalProductQty: product.productQty,
      // timeRange: [startTimeObj, endTimeObj],
      editFlag: 0, // 默认可编辑 (0=可编辑)
    };
  });

  // 更新视图使用的ref数据
  deliveryDates.value = processedDateColumns;
  productList.value = processedProducts;
  mergeData.value = processedProducts.map((product) => {
    return {
      product: product,
      ...processedDateColumns.reduce((acc, dateColumn, index) => {
        acc[`${dateColumn?.dateStr}`] = {
          ...dateColumn,
          quantity: null,
          editFlag: dateColumn?.editFlag || 0 // 确保有editFlag，默认为0表示可编辑
        };
        return acc;
      }, {}),
    };
  });
};

// 初始化创建交货计划
const handleInitCreate = async () => {
  loading.value = true;
  try {
    orderDetail.value = await getOrderDetail({ id: id, orderCode: orderCode });
    if (!orderDetail.value.orderAttachmentFiles || orderDetail.value.orderAttachmentFiles === "null") {
      orderDetail.value.orderAttachmentFiles = [];
    }
    orderDetail.value.deliveryAddress =
      orderDetail.value.countryName +
      orderDetail.value.provinceName +
      orderDetail.value.cityName +
      orderDetail.value.districtName +
      orderDetail.value.address;
    // 创建日期列数据
    const dateColumns = generateDateColumns(
      orderDetail.value?.expectedReceivedTimeStar ?? []
    );
    processOrderDetailDataForCreate(
      orderDetail.value?.orderDetailList ?? [],
      dateColumns ?? []
    );
  } catch (error) {
    console.error("handleInitCreate: Error fetching data", error);
    ElMessage.error(t("omsOrder.message.loadFail"));
  } finally {
    loading.value = false;
  }
};

// 初始化更新交货计划
const handleInitUpdate = async () => {
  loading.value = true;
  try {
    // 使用Promise.all并行获取交货计划数据和订单详情
    const [scheduleData, orderData] = await Promise.all([
      getSchedulePlanInfo({ orderId: id }),
      getOrderDetail({ id: id, orderCode: orderCode }),
    ]);

    // scheduleData[2].editFlag = 1;
    scheduleInfo.value = scheduleData;
    orderDetail.value = orderData;
    orderDetail.value.deliveryAddress =
      orderDetail.value.countryName +
      orderDetail.value.provinceName +
      orderDetail.value.cityName +
      orderDetail.value.districtName +
      orderDetail.value.address;
    // 获取交货计划详情中的商品并集
    const productList = scheduleData.reduce((acc, item) => {
      if (item.adjustDeliveryScheduleDateDetailInfoVOList && item.adjustDeliveryScheduleDateDetailInfoVOList.length > 0) {
        item.adjustDeliveryScheduleDateDetailInfoVOList.forEach(product => {
          // 检查是否已存在相同productCode的产品
          const existingProductIndex = acc.findIndex(p => p.productCode === product.productCode);
          if (existingProductIndex === -1) {
            // 如果不存在，则添加到累加器中
            acc.push(product);
          }
        });
      }
      return acc;
    }, []);
    // 处理数据
    const dateColumns = generateDateColumnsByScheduleData(
      scheduleInfo.value ?? []
    );
    processOrderDetailDataForUpdate(
      productList ?? [],
      dateColumns ?? [],
      scheduleData ?? []
    );
  } catch (error) {
    ElMessage.error(t("omsOrder.message.loadFail"));
  } finally {
    loading.value = false;
  }
};

const initData = async (type: string) => {
  if (type === "create") {
    handleInitCreate();
  } else if (type === "update") {
    handleInitUpdate();
  }
};

onMounted(async () => {
  initData(type);
});

// 禁用分钟选项，只允许选择整点时间
const disabledMinutes = () => {
  // 返回0-59之间除了0以外的所有分钟
  return Array.from({ length: 59 }, (_, i) => i + 1);
};
</script>
<style scoped lang="scss">
:deep(.el-table__cell) {
  padding: 0 !important;
}

:deep(.el-table__header-wrapper) {
  background-color: #F4F6FA;
}

:deep(.el-table__header-wrapper thead) {
  height: 62px;
}

.orderDetail {
  background: #ffffff;
  border-radius: 4px;

  .page-content {
    .button-add {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: var(--el-color-primary);
    }

    .table-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 50px;
      background: #f4f6fa;
      box-shadow:
        inset 1px 1px 0px 0px #e5e7f3,
        inset -1px -1px 0px 0px #e5e7f3;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #52585f;
      font-style: normal;
      padding: 15px 12px;
    }

    // 交货计划表格样式
    .delivery-plan-table {

      // 表格固定列和滚动设置
      ::v-deep .el-table__fixed,
      ::v-deep .el-table__fixed-right {
        height: auto !important;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
      }

      ::v-deep .el-table__body-wrapper {
        overflow-x: auto !important;
      }

      ::v-deep .el-table--scrollable-x .el-table__body-wrapper {
        overflow-x: auto;
      }

      ::v-deep .el-table--scrollable-y .el-table__body-wrapper {
        overflow-y: auto;
      }

      ::v-deep .el-table__fixed-header-wrapper {
        z-index: 100;
      }

      .product-info {
        display: flex;
        padding: 10px;
        align-items: center;

        .product-img {
          margin-right: 10px;
        }

        .product-detail {
          text-align: left;

          .product-name {
            font-weight: bold;
            margin-bottom: 5px;
          }

          .product-spec,
          .product-price,
          .product-unit {
            font-size: 12px;
            color: #666;
            margin-bottom: 3px;
          }
        }
      }

      .date-header {
        display: flex;
        flex-direction: column;
        align-items: center;

        .time-range {
          display: flex;
          align-items: center;
          margin-top: 5px;
          margin-bottom: 10px;

          ::v-deep .el-input__inner {
            padding: 0 5px;
            font-size: 12px;
          }

          ::v-deep .el-range-editor {
            width: 120px;
          }

          ::v-deep .el-range-separator {
            padding: 0;
          }
        }
      }

      .quantity-input-wrapper {
        width: 100%;
        height: 100%;
        padding: 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        min-height: 40px;
        border: 1px solid transparent;
        transition: all 0.2s;

        &.can-edit {
          cursor: pointer;

          &:hover {
            border: 1px solid #f56c6c;
            background-color: rgba(245, 108, 108, 0.05);
          }
        }

        &.disabled {
          background-color: #f5f7fa;
          cursor: not-allowed;

          ::v-deep .el-input__wrapper {
            background-color: #f5f7fa !important;
            box-shadow: none !important;
          }

          ::v-deep .el-input__inner {
            color: #999;
            cursor: not-allowed;
          }
        }

        .quantity-display {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: #606266;
        }

        .quantity-input {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;

          ::v-deep .el-input__wrapper {
            padding: 0 8px;
            box-shadow: none !important;
            border-radius: 0;
            background-color: #fff;
          }

          ::v-deep .el-input__inner {
            height: 100%;
            min-height: 40px;
            text-align: center;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.orderDetail {
  .file-div {
    .el-form-item__content {
      display: block;
      margin-top: -38px;
    }

    .el-upload-list {
      margin: 5px;
    }

    .el-upload-list__item .is-success:hover {
      .el-upload-list__item-status-label {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }

      .el-icon {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }

      .el-icon--close-tip {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
      }
    }

    .el-upload-list__item-status-label {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }

    .el-icon {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }

    .el-icon--close-tip {
      width: 0px !important;
      height: 0px !important;
      display: none !important;
    }
  }
}
</style>
