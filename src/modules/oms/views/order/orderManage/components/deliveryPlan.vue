<template>
    <el-dialog v-model="props.visible" :title="props.title" :close-on-click-modal="false" width="500px" @close="close">
        <div>
            <el-form ref="deliverPlanFromRef" :model="deliverPlanFrom" label-position="top">
                    <el-form-item :label="$t('omsOrder.label.deliveryPlan')" prop="deliveryPlan">
                        <el-radio-group v-model="deliverPlanFrom.deliveryPlanType">
                            <div v-for="(item,index) in deliveryPlanList" :key="index" class="mr-10px">
                               <el-radio :value="item.key">{{item.value}}</el-radio>
                            </div>
                        </el-radio-group>
                    </el-form-item>
            </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close()">{{ $t("common.cancel") }}</el-button>
            <el-button type="primary" @click="submitForm(deliverPlanFrom.deliveryPlanType)">{{ $t("common.confirm") }}</el-button>
          </span>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
    import OrderAPI, { CreateDeliverySchedule} from "@/modules/oms/api/order";
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "关闭",
        },
    });
    const emit = defineEmits(["update:visible","onSubmit"]);
    const { t } = useI18n();
    const  deliverPlanFromRef= ref()
    const  deliveryPlanList= ref([
        {
            key: 1,
            value: t('omsOrder.deliveryPlanList.uniformDelivery')
        },
        {
            key: 2,
            value:t('omsOrder.deliveryPlanList.batchesdelivery')
        },
    ])
    const deliverPlanFrom = reactive<CreateDeliverySchedule>({
        deliveryPlanType:1
    });

    function close() {
        emit("update:visible", false);
        reset();
    }

    function reset() {
        deliverPlanFromRef.value.resetFields();
    }

    function submitForm(type: Number) {
       /* if(type === 1) { // 统一送达

        }
        else if(type === 2){ // 分次送达

        }*/
        close()
        emit("onSubmit",type);
    }

    defineExpose({
    });
</script>

<style scoped lang="scss">
    .supplier-div{
        width: calc(100% - 150px);
    }
    .mr-10px{
      margin-right: 10px;
    }
</style>
<style lang="scss">
</style>
