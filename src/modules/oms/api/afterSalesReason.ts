import request from "@/core/utils/request";
const AFTERSALESREASON_BASE_URL = '/supply-oms/orderReturnReason'
const OPERATIONLOG_BASE_URL = '/supply-oms/operationLog'
class afterSalesReasonApi {
  //售后原因列表
  static getReasonList(){
    return request<any, ReasonListVO[]>({
      url: `${AFTERSALESREASON_BASE_URL}/list`,
      method: "get",
    });
  }
  //新增售后原因
  static addReason(data: addReasonDto){
    return request({
      url: `${AFTERSALESREASON_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }
  //批量删除
  static deleteBatch(data:[]){
    return request({
      url: `${AFTERSALESREASON_BASE_URL}/deleteBatch`,
      method: "post",
      data: data,
    });
  }
  //启用/禁用
  static enableStatus(data: addReasonDto){
    return request({
      url: `${AFTERSALESREASON_BASE_URL}/enableStatus`,
      method: "post",
      data: data,
    });
  }
  //获取启用的售后原因列表
  static getEnableList(){
    return request<any, ReasonListVO[]>({
      url: `${AFTERSALESREASON_BASE_URL}/getEnableList`,
      method: "get",
    });
  }
  //操作记录查询
  static getLogByBusinessId(data: { id?:string }){
    return request<any, LogListVO[]>({
      url: `${OPERATIONLOG_BASE_URL}/getByBusinessId/${data.id}`,
      method: "get",
    });
  }
}
export default afterSalesReasonApi
//售后原因列表实体
export interface ReasonListVO{
  /** id */
  id?: string;
  /** 状态 */
  enableStatus?: number;
  /** 售后原因 */
  reasonName?: string;
  /** 更新时间 */
  updateTime?: string;
}
//新增售后原因实体
export interface addReasonDto {
  /** id */
  id?: string;
  /** 状态 */
  enableStatus?: number;
  /** 售后原因 */
  reasonName?: string;
}
//操作记录实体
export interface LogListVO {
  /** 操作时间  */
  createTime?: string
  /** 操作类型 */
  operationName?: string
  /** 操作人 */
  createUserName?: string
}
