import request from "@/core/utils/request";
import {LogListVO} from "@/modules/oms/api/afterSalesReason";
const AFTERSALEMANAGEMENT_BASE_URL = '/supply-oms/orderReturn'
const ORDERBATCH_BASE_URL = '/supply-oms/orderBatch'
const OPERATIONLOG_BASE_URL = '/supply-oms/operationLog'
class afterSaleManagementApi{
  /** 查询售后管理列表 */
  static getPageList(queryParams?: QueryAfterSaleManagementDto){
    return request<any, PageResult<AfterSaleManagementPageV0[]>>({
      url: `${AFTERSALEMANAGEMENT_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }
  /** 解密 */
  static querySrcInfo(data: {id?: string}){
    return request({
      url: `${AFTERSALEMANAGEMENT_BASE_URL}/desensitization/${data.id}`,
      method: "get",
    });
  }
  /** 统计状态数量 */
  static queryCount(){
    return request({
      url: `${AFTERSALEMANAGEMENT_BASE_URL}/count`,
      method: 'get'
    })
  }
  /** 新增售后 */
  static addOrderReturn(data ?: AddDataForm){
    return request({
      url: `${AFTERSALEMANAGEMENT_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }
  /** 处理售后 */
  static editOrderReturn(data ?: EditForm){
    return request({
      url: `${AFTERSALEMANAGEMENT_BASE_URL}/process`,
      method: "post",
      data: data,
    });
  }
  /** 售后详情 */
  static orderReturnDetail(data: {orderReturnCode?: string}){
    return request({
      url: `${AFTERSALEMANAGEMENT_BASE_URL}/detail/${data.orderReturnCode}`,
      method: "get",
    });
  }
  /** 根据订购单号查询该订购单已完成批次单号 */
  static queryOrderBatchCodeByOrderCode(data: {orderCode?: string}){
    return request({
      url: `${ORDERBATCH_BASE_URL}/byOrderCode/list/${data.orderCode}`,
      method: "get",
    });
  }
  /** 根据订购单号及批次号查询详情 */
  static queryDetailsByOrderCodeAndOrderBatchCode(data: {orderCode?: string,orderBatchCode?: string}){
    return request({
      url: `${ORDERBATCH_BASE_URL}/getInfo/${data.orderCode}/${data.orderBatchCode}`,
      method: "get",
    });
  }
  static getLogByBusinessCode(data: {orderReturnCode?: string}){
    return request<any, LogListVO[]>({
      url: `${OPERATIONLOG_BASE_URL}/getByBusinessCode/${data.orderReturnCode}`,
      method: "get",
    });
  }
  /** 获取退货方式下拉  /deliveryMethods/queryPageList */
  static queryReturnMethodList(data?: any){
    return request({
      url: `/supply-biz-common/deliveryMethods/queryPageList`,
      method: "post",
      data: data
    })
  }

  /** 手动同步 */
  static sync(data?: any){
    return request({
      url: `${AFTERSALEMANAGEMENT_BASE_URL}/sync`,
      method: "post",
      data: data
    })
  }
}
export default afterSaleManagementApi
/** 售后管理列表查询请求实体 */
export interface QueryAfterSaleManagementDto {
  /** 售后单号 */
  orderReturnCode?: string
  /** 订购单号 */
  orderCode?: string
  /** 批次单号 */
  orderBatchCode?: string
  /** 同步状态 */
  syncStatus?: number
}
/** 售后管理列表实体 */
export interface AfterSaleManagementPageV0 extends PageQuery{
  /** 售后单号 */
  orderReturnCode?: string
  /** 订购单号 */
  orderCode?: string
  /** 批次单号 */
  orderBatchCode?: string
  /** 状态 */
  orderReturnStatus?: number
  /** 商品 */
  productNames?: string
  /** 客户 */
  customerName?: string
  /** 售后原因 */
  returnReasonName?: string
  /** 售后金额 */
  returnAmount?: number
  /** 申请时间 */
  createTime?: string
  /** 处理时间 */
  returnTime?: string
}
/** 售后申请请求实体 */
export interface AddDataForm {
  /** 订单编码 */
  orderCode?: string
  /** 批次编码 */
  orderBatchCode?: string
  /** 客户编码 */
  customerCode?: string
  /** 客户名称 */
  customerName?: string
  /** 配货仓库 */
  warehouseCode?: string
  warehouseName?: string
  /** 售后类型 */
  returnType?: number
  /** 退货地址国家 */
  countryId?: string
  countryName?: string
  /** 退货地址省 */
  provinceId?: string
  provinceName?: string
  /** 退货地址市 */
  cityId?: string
  cityName?: string
  /** 退货地址区 */
  districtId?: string
  districtName?: string
  /** 退货地址详细地址 */
  address?: string
  /** 退货方式 */
  returnMethodId?: number
  returnMethodName?: string
  /** 退货时间  returnType */
  returnDate?: string
  returnTime?: string
  /** 售后原因id */
  returnReasonId?: number
  /** 售后原因 */
  returnReasonName?: string
  /** 售后备注 */
  returnReasonRemark?: string
  /** 售后附件信息 */
  returnAttachmentUrls?: string
  /** 审核人ID */
  approveUser?: string
  /** 审核人名称 */
  auditUserName?: string
}
/** 售后详情实体 */
export interface AfterSaleDetailDto {
  /** ID */
  id?: string
  /** 售后单号 */
  orderReturnCode?: string
  /** 订购单号 */
  orderCode?: string
  /** 客户 */
  customerName?: string
  /** 申请人 */
  createUserName?: string
  /** 联系电话 */
  contactMobile?: string
  /** 联系电话区域代码 */
  contactAreaCode?: string
  /** 申请时间 */
  createTime?: string
  /** 交付批次 */
  orderBatchCode?: string
  /** 退款商品 */
  orderReturnDetailList?: any
  /** 售后状态 */
  orderReturnStatus?: number
  /** 售后金额 */
  returnAmount?: number
  /** 退款商品数量合计 */
  refundQty?: number
  /** 售后备注 */
  returnRemark?: string
  /** 退款原因 */
  returnReasonName?: string
  /** 退款说明 */
  returnReasonRemark?: string
  /** 售后图片 */
  returnAttachmentUrls?: string
  /** 售后时间 */
  returnTime?: string
  /** 币种 */
  currencyCode?: string
}
/** 售后处理请求实体 */
export interface EditForm {
  /** 售后单号 */
  orderReturnCode?: string
  /** 处理意见 */
  processRemark?: string
  /** 处理类型 */
  processType?: number
}
