/**
 * AdjustDeliveryScheduleDateInfoVO
 */
export interface AdjustDeliveryScheduleDateInfoVO {
  /**
   * 订单批次明细 如果没有会返回空集合
   */
  adjustDeliveryScheduleDateDetailInfoVOList?: AdjustDeliveryScheduleDateDetailInfoVO[];
  /**
   * 编辑状态：0->正常；1->不可编辑 返回1 置灰
   */
  editFlag?: number;
  /**
   * 订单批次主键id
   */
  orderBatchId?: number;
  /**
   * 计划交货日期
   */
  planDate?: string;
  /**
   * 计划结束时间
   */
  planDateEnd?: string;
  /**
   * 计划开始时间
   */
  planDateStart?: string;
  [property: string]: any;
}

/**
 * AdjustDeliveryScheduleDateDetailInfoVO
 */
export interface AdjustDeliveryScheduleDateDetailInfoVO {
  /**
   * 币种编码
   */
  currencyCode?: string;
  /**
   * 币种名称
   */
  currencyName?: string;
  /**
   * 订单批次明细主键ID
   */
  id?: number;
  /**
   * 商品编码
   */
  productCode?: string;
  /**
   * 商品图片
   */
  productImg?: string;
  /**
   * 商品名称
   */
  productName?: string;
  /**
   * 计划商品数量
   */
  productQty?: number;
  /**
   * 规格
   */
  productSpecs?: string;
  /**
   * 商品采购单位id，外键关联（t_product_unit.id）
   */
  productUnitId?: number;
  /**
   * 商品采购单位名称，外键关联（t_product_unit.name）
   */
  productUnitName?: string;
  /**
   * 销售价
   */
  saleAmount?: number;
  /**
   * 总计划商品数量
   * 总计划商品数量 订单明细中的商品订购总量
   */
  totalProductQty?: number;
  [property: string]: any;
}
