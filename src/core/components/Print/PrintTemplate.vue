<template>
  <div class="print-template">
    <!-- 打印按钮区域 -->
    <!-- <div class="print-controls" v-if="!isPrinting">
      <el-button type="primary" @click="print">
        <i class="el-icon-printer"></i>
        打印123
      </el-button>
    </div> -->
    <div ref="printArea" class="print-content">
      <slot></slot>
    </div>

    <!-- 打印内容区域 -->
    <div :class="{ 'is-printing': isPrinting }">
      <iframe
        ref="printIframe"
        style="width: 0; height: 0; border: 0; position: absolute"
      ></iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: "PrintTemplate",
  data() {
    return {
      isPrinting: false,
    };
  },
  methods: {
    print() {
      this.isPrinting = true;

      // 使用nextTick确保DOM已更新
      this.$nextTick(() => {
        try {
          // 获取打印区域内容
          const printContent = this.$refs.printArea.innerHTML;

          // 获取所有样式表
          const styles = this.getStyles();

          // 获取iframe引用
          const iframe = this.$refs.printIframe;

          // 获取iframe的document对象
          const iframeDoc =
            iframe.contentDocument ||
            (iframe.contentWindow && iframe.contentWindow.document);

          if (!iframeDoc) {
            console.error("无法获取iframe文档");
            this.isPrinting = false;
            return;
          }

          // 写入打印内容到iframe
          iframeDoc.open();

          // HTML头部
          iframeDoc.write("<!DOCTYPE html>");
          iframeDoc.write("<html>");
          iframeDoc.write("<head>");
          iframeDoc.write("<title>打印</title>");

          // 样式
          iframeDoc.write("<style>");
          iframeDoc.write(styles);
          iframeDoc.write(`
            /* 确保打印内容可见 */
            body {
              margin: 0;
              padding: 0;
            }
            
            .print-content {
              display: block !important;
            }
            
            @media print {
              @page {
                margin: 0;
                size: auto;
              }
              
              body {
                margin: 0 !important;
                padding: 0 !important;
              }
            }
          `);
          // 结束style标签
          iframeDoc.write("<" + "/style>");

          // 结束head标签
          iframeDoc.write("<" + "/head>");

          // HTML主体
          iframeDoc.write("<body>");
          iframeDoc.write(
            `<div class="print-only" style="width: 100vw; height: 100vh;">${printContent}</div>`
          );

          // 打印脚本
          const scriptContent = `
            window.onload = function() {
              setTimeout(function() {
                window.focus();
                window.print();
              }, 300);
            };
          `;

          // 创建script标签
          iframeDoc.write("<script>");
          iframeDoc.write(scriptContent);
          // 结束script标签
          iframeDoc.write("<" + "/script>");

          // 结束body和html标签
          iframeDoc.write("<" + "/body>");
          iframeDoc.write("<" + "/html>");

          iframeDoc.close();

          // 设置iframe加载完成后的回调
          iframe.onload = () => {
            // 打印完成后重置状态
            setTimeout(() => {
              this.isPrinting = false;
            }, 500);
          };
        } catch (error) {
          console.error("打印过程中发生错误:", error);
          this.isPrinting = false;
        }
      });
    },

    // 获取样式的辅助方法
    getStyles() {
      try {
        return Array.from(document.styleSheets)
          .map((styleSheet) => {
            try {
              return Array.from(styleSheet.cssRules)
                .filter((rule) => {
                  // 包含所有打印相关的样式
                  return (
                    rule.cssText.includes("@media print") ||
                    rule.cssText.includes(".print-")
                  );
                })
                .map((rule) => rule.cssText)
                .join("\n");
            } catch (e) {
              // 跨域样式表可能会抛出安全错误
              return "";
            }
          })
          .filter(Boolean)
          .join("\n");
      } catch (e) {
        console.error("获取样式时发生错误:", e);
        return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.is-printing {
  width: 100%;
  margin: 0;
  padding: 0;
}
.print-content {
  display: block;
}
@media print {
  .print-template {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
  }
  .print-controls {
    display: none !important;
  }

  .is-printing {
    position: absolute;
    left: 0;
    top: 0;
  }

  .print-content {
    display: none !important;
  }
}
</style>
