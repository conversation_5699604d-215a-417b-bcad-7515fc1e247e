<template>
    <!--修改密码弹窗 -->
    <el-dialog
            v-model="props.dialogVisible"
            :title="$t('updatePassword.title.updatePasswordTitle')"
            :close-on-click-modal="false"
            width="500px"
            @close="handleCloseUpdatePassDialog"
            :show-close="showCloseButton"
    >

        <el-form ref="updatePassFormRef"
                 :model="updatePassData"
                 :rules="updatePassRules"
                 label-width="82px"
                 class="update-password"
        >
            <el-form-item :label="$t('updatePassword.label.oldPassword')" prop="oldPassword">
                <el-input v-model="updatePassData.oldPassword" :placeholder="$t('updatePassword.rules.oldPassword')" type="password" show-password clearable/>
            </el-form-item>
            <el-form-item :label="$t('updatePassword.label.newPassword')" prop="newPassword">
                <el-input v-model="updatePassData.newPassword" :placeholder="$t('updatePassword.rules.newPassword')" type="password" show-password clearable/>
            </el-form-item>
            <el-form-item :label="$t('updatePassword.label.confirmPassword')" prop="confirmPassword">
                <el-input v-model="updatePassData.confirmPassword" :placeholder="$t('updatePassword.rules.confirmPassword')" type="password" show-password clearable/>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleUpdatePassDataSubmit" :loading="loading">{{$t('common.confirm')}}</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from "element-plus";
import { reactive, ref, watch, watchEffect } from "vue";
import UpdatePasswordAPI, { updatePasswordData } from "@/core/api/updatePassword";
import {useTagsViewStore, useUserStore} from "@/core/store";
import {useRoute, useRouter} from "vue-router";
import { UPDATEPASSWORD_FLAG_KEY } from "@/core/enums/CacheEnum";

const { t } = useI18n();
const userStore = useUserStore();
const tagsViewStore = useTagsViewStore();
const router = useRouter();
const route = useRoute();

// 定义接收的属性
const props = defineProps({
    dialogVisible: {
        type: Boolean,
        default: false,
    },
});
// const emit = defineEmits(["update:dialogVisible", "onSubmit"]);
const emit = defineEmits(["onSubmit"]);

// const showCloseButton = true
const showCloseButton = localStorage.getItem(UPDATEPASSWORD_FLAG_KEY)==1?false:true
// 按钮 loading 状态
const loading = ref(false);
// 修改密码表单ref
const updatePassFormRef = ref<FormInstance>();
// 修改密码表单
const updatePassData = reactive({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
});
// 修改密码表单校验规则
const updatePassRules = reactive({
    oldPassword: [
        {required: true, message:t('updatePassword.rules.oldPassword'), trigger: "blur"}
    ],
    newPassword: [
        {required: true, message: t('updatePassword.label.newPassword'), trigger: "blur"},
        { min: 8, max: 20, message: "长度在8到20个字符", trigger: "blur" },
        { pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Za-z])(?=\S*[!@$%^&_=.])\S*$/, message: t('updatePassword.rules.passwordFomart'), trigger: 'change' },
    ],
    confirmPassword: [
        {required: true, message:t('updatePassword.rules.confirmPassword'), trigger: "blur"},
        { min: 8, max: 20, message: "长度在8到20个字符", trigger: "blur" },
        { pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Za-z])(?=\S*[!@$%^&_=.])\S*$/, message: t('updatePassword.rules.passwordFomart'), trigger: 'change' },
        { required: true, validator: validateConfirmPassword, trigger: 'change' }
    ],
});

function validateConfirmPassword(rule, value, callback) {
  if (value !== updatePassData.newPassword) {
    callback(new Error(t('updatePassword.message.passwordNoSameTips')));
  } else {
    callback();
  }
}


/** 关闭修改密码弹窗 */
function handleCloseUpdatePassDialog() {
    updatePassFormRef.value.resetFields();
    updatePassFormRef.value.clearValidate();
    // emit("update:dialogVisible", false);
    emit("onSubmit", false);
}

/** 提交修改密码表单 */
function handleUpdatePassDataSubmit() {
    updatePassFormRef.value.validate((valid: any) => {
        if (valid) {
            loading.value = true;
            UpdatePasswordAPI.updatePassword(updatePassData)
                .then(() => {
                    ElMessage.success(t('updatePassword.message.updateSucess'));
                    handleCloseUpdatePassDialog();
                    setTimeout(function () {
                        // tagsViewStore.delAllViews();
                        // useUserStore().resetToken()
                        userStore
                            .logout()
                            .then(() => {
                                tagsViewStore.delAllViews();
                            })
                            .then(() => {
                                router.push(`/login?redirect=${route.fullPath}`);
                            });
                    }, 1000);
                })
                .finally(() => {
                    loading.value = false
                    localStorage.removeItem(UPDATEPASSWORD_FLAG_KEY)
                });
        }
    });
}



</script>
