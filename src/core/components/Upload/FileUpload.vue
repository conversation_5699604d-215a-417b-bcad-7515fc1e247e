<!-- 文件上传组件 -->
<!-- <template>
  <div>
    <el-upload
      v-model:file-list="fileList"
      :class="props.showUploadBtn ? 'show-upload-btn' : 'hide-upload-btn'"
      :style="props.style"
      multiple
      :headers="props.headers"
      :data="props.data"
      :name="props.name"
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :on-progress="handleProgress"
      :on-success="handleSuccessFile"
      :on-error="handleError"
      :action="props.action"
      :accept="props.accept"
      :limit="props.limit"
    >
      <el-button
        type="primary"
        v-if="props.showUploadBtn"
        :disabled="fileList.length >= props.limit"
      >
        {{ props.uploadBtnText }}
      </el-button>
      <template #tip v-if="props.showTip">
        <div class="el-upload__tip">
          {{ props.tip }}
        </div>
      </template>
      <template #file="{ file }">
        <div class="el-upload-list__item-info">
          <a class="el-upload-list__item-name" @click="downloadFile(file)">
            <el-icon><Document /></el-icon>
            <span class="el-upload-list__item-file-name">{{ file.name }}</span>
            <span
              class="el-icon--close"
              v-if="props.showDelBtn"
              @click.stop="handleRemove(file)"
            >
              <el-icon><Close /></el-icon>
            </span>
          </a>
        </div>
      </template>
    </el-upload>
    <el-progress
      :style="{
        display: showUploadPercent ? 'inline-flex' : 'none',
        width: '100%',
      }"
      :percentage="uploadPercent"
      :color="customColorMethod"
      v-if="showUploadPercent"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  UploadRawFile,
  UploadUserFile,
  UploadFile,
  UploadProgressEvent,
  UploadFiles,
} from "element-plus";
import { TOKEN_KEY } from "@/core/enums/CacheEnum";
import FileAPI from "@/core/api/file";
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { ResultEnum } from "@/core/enums/ResultEnum";
const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  /**
   * 文件集合
   */
  modelValue: {
    type: Array<UploadUserFile>,
    default: () => [],
  },
  /**
   * 上传地址
   */
  action: {
    type: String,
    default: FileAPI.uploadUrl,
  },
  /**
   * 文件上传数量限制
   */
  limit: {
    type: Number,
    default: 10,
  },
  /**
   * 是否显示删除按钮
   */
  showDelBtn: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示上传按钮
   */
  showUploadBtn: {
    type: Boolean,
    default: true,
  },
  /**
   * 单个文件上传大小限制(单位byte)
   */
  uploadMaxSize: {
    type: Number,
    default: 2 * 1024 * 1024,
  },
  /**
   * 上传文件类型
   */
  accept: {
    type: String,
    default: "*",
  },
  /**
   * 上传按钮文本
   */
  uploadBtnText: {
    type: String,
    default: "上传文件",
  },
  /**
   * 是否展示提示信息
   */
  showTip: {
    type: Boolean,
    default: false,
  },
  /**
   * 提示信息内容
   */
  tip: {
    type: String,
    default: "",
  },
  /**
   * 请求头
   */
  headers: {
    type: Object,
    default: () => {
      return {
        Authorization: localStorage.getItem(TOKEN_KEY),
      };
    },
  },
  /**
   * 请求携带的额外参数
   */
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  /**
   * 上传文件的参数名
   */
  name: {
    type: String,
    default: "file",
  },
  /**
   * 样式
   */
  style: {
    type: Object,
    default: () => {
      return {
        width: "300px",
      };
    },
  },
});

const fileList = ref([] as UploadUserFile[]);
const valFileList = ref([] as UploadUserFile[]);
const showUploadPercent = ref(false);
const uploadPercent = ref(0);

watch(
  () => props.modelValue,
  (newVal: UploadUserFile[]) => {
    const filePaths = fileList.value.map((file) => file.url);
    const fileNames = fileList.value.map((file) => file.name);
    // 监听modelValue文件集合值未变化时，跳过赋值
    if (
      filePaths.length > 0 &&
      filePaths.length === newVal.length &&
      filePaths.every((x) => newVal.some((y) => y.url === x)) &&
      newVal.every((y) => filePaths.some((x) => x === y.url)) &&
      fileNames.every((x) => newVal.some((y) => y.name === x)) &&
      newVal.every((y) => fileNames.some((x) => x === y.name))
    ) {
      return;
    }

    if (newVal.length <= 0) {
      fileList.value = [];
      return;
    }

    fileList.value = newVal.map((file) => {
      return { name: file.name, url: file.url } as UploadFile;
    });

    valFileList.value = newVal.map((file) => {
      return { name: file.name, url: file.url } as UploadFile;
    });
  },
  { immediate: true }
);

/**
 * 限制用户上传文件的大小
 */
function handleBeforeUpload(file: UploadRawFile) {
  if (file.size > props.uploadMaxSize) {
    ElMessage.warning(
      "上传文件不能大于" + Math.trunc(props.uploadMaxSize / 1024 / 1024) + "M"
    );
    return false;
  }
  uploadPercent.value = 0;
  showUploadPercent.value = true;
  return true;
}

const handleSuccessFile = (response: any, file: UploadFile) => {
  showUploadPercent.value = false;
  uploadPercent.value = 0;
  if (response.code === ResultEnum.SUCCESS) {
    ElMessage.success("上传成功");
    valFileList.value.push({
      name: file.name,
      url: response.data.url,
    });
    emit("update:modelValue", valFileList.value);
    return;
  } else {
    ElMessage.error(response.msg || "上传失败");
  }
};

const handleError = (error: any) => {
  showUploadPercent.value = false;
  uploadPercent.value = 0;
  ElMessage.error("上传失败");
};

const customColorMethod = (percentage: number) => {
  if (percentage < 30) {
    return "#909399";
  }
  if (percentage < 70) {
    return "#375ee8";
  }
  return "#67c23a";
};

const handleProgress = (event: UploadProgressEvent) => {
  uploadPercent.value = event.percent;
};

/**
 * 删除文件
 */
function handleRemove(removeFile: UploadUserFile) {
  const filePath = removeFile.url;
  if (filePath) {
    FileAPI.deleteByPath(filePath).then(() => {
      // 删除成功回调
      valFileList.value = valFileList.value.filter(
        (file) => file.url !== filePath
      );
      emit("update:modelValue", valFileList.value);
    });
  }
}

/**
 * 下载文件
 */
function downloadFile(file: UploadUserFile) {
  const filePath = file.url;
  if (filePath) {
    FileAPI.downloadFile(filePath, file.name);
  }
}
</script>
<style lang="scss" scoped>
.el-upload-list__item .el-icon--close {
  position: absolute;
  top: 50%;
  right: 5px;
  color: var(--el-text-color-regular);
  cursor: pointer;
  opacity: 0.75;
  transition: opacity var(--el-transition-duration);
  transform: translateY(-50%);
}

:deep(.el-upload-list) {
  margin: 0;
}

:deep(.el-upload-list__item) {
  margin: 0;
}

.show-upload-btn {
  :deep(.el-upload) {
    display: inline-flex;
  }
}

.hide-upload-btn {
  :deep(.el-upload) {
    display: none;
  }
}
</style> -->
<template>
  <div class="upload-file" id="uploadWrap">
    <el-upload
      multiple
      :http-request="uploadFile"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      class="upload-file-uploader"
      ref="fileUpload"
      v-if="fileList.length < limit"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip && fileList.length < limit">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul" v-if="fileList.length > 0">
      <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :underline="false" target="_blank" @click="handleOpen(file.url)" style="min-width: 120px;">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger" v-if="!isDisabled">删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import commonUpload from "../../utils/commonUpload";
import FileAPI from "@/core/api/file";
const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 是否为敏感文件 ‘’ 为否  ‘PRIVATE’ 为是
  isPrivate: {
    type:String,
    default:''
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "xls", "ppt", "txt", "pdf"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  formRef: {
    type: Object,
    default: () => {
    }
  },
  name: {
    type: String,
    default: ''
  },
  isDisabled:{
     type: Boolean,
     default: false
  },
  folder:{
    type: String,
    default: 'omsFile'
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(() => props.modelValue, val => {
  if (val) {
    let temp = 1;
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : props.modelValue.split(',');
    console.log(list);
    // 然后将数组转为对象数组
    fileList.value = list.map(item => {
      if (typeof item === "string") {
        item = { name: item, url: item };
      }
      item.uid = item.uid || new Date().getTime() + temp++;
      return item;
    });
  } else {
    fileList.value = [];
    return [];
  }
},{ deep: true, immediate: true });

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt.toLowerCase()) >= 0;
    if (!isTypeOk) {
      ElMessage.error(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  return true;
}

// 文件个数超出
function handleExceed() {
  ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
function handleUploadError(err) {
  ElMessage.error("上传文件失败");
}

/**
 * 自定义文件上传
 *
 * @param options
 */
async function uploadFile(options){
  // proxy.$modal.loading("正在上传文件，请稍候...");
  await commonUpload(options.file,props.folder,props.isPrivate).then(res => {
    console.log(res);
    if(res.res.status === 200){
      ElMessage.success("上传成功");
      number.value++;
      uploadList.value.push({ name: res.name, url: res.url });
      uploadedSuccessfully();
      props.formRef.validateField(props.name)
    }else{
      ElMessage.error(res.msg);
        number.value--;
        // proxy.$refs.fileUpload.handleRemove(file);
        props.formRef.validateField(props.name);
        uploadedSuccessfully();
    }
  }).finally(() => {
    // proxy.$modal.closeLoading();
  })

}
// 上传成功回调
function handleUploadSuccess(res, file) {
  console.log("上传成功============")
  // if (res.code === 200) {
  //   uploadList.value.push({ name: res.fileName, url: res.fileName });
  //   uploadedSuccessfully();
  // } else {
  //   number.value--;
  //   proxy.$modal.closeLoading();
  //   ElMessage.error(res.msg);
  //   proxy.$refs.fileUpload.handleRemove(file);
  //   props.formRef.validateField(props.name);
  //   uploadedSuccessfully();
  // }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1);
  emit("update:modelValue", listToString(fileList.value));
}
function handleOpen(url){
  if(props.isPrivate === 'PRIVATE'){
    FileAPI.getSignedUrl({filePath:url})
      .then((res) => {
        if(res.code === 0){
          window.open(res.data.data)
        }
     })
  }else{
    window.open(url)
  }
  
}
// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    console.log(fileList.value)
    emit("update:modelValue", listToString(fileList.value));
    // proxy.$modal.closeLoading();
  }
}

// 获取文件名称
function getFileName(name) {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return decodeURIComponent(name.slice(name.lastIndexOf("/") + 1));
  } else {
    return decodeURIComponent(name);
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (list[i].url) {
      strs += list[i].url + separator;
    }
  }
  return strs != '' ? strs.substr(0, strs.length - 1) : '';
}
</script>

<style scoped lang="scss">
#uploadWrap{
  :deep(.upload-file-uploader) {
    margin-bottom: 5px;
  }
  :deep(.upload-file-list .el-upload-list__item) {
    //border: 1px solid #e4e7ed;
    //line-height: 2;
    margin-bottom: 10px;
    position: relative;
    text-decoration: underline;
  }
  :deep(.upload-file-list .ele-upload-list__item-content) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
  :deep(.ele-upload-list__item-content-action .el-link) {
    margin-right: 10px;
  }
  :deep(.ele-upload-list__item-content-action){
    margin-left: 10px;
  }
}

</style>
