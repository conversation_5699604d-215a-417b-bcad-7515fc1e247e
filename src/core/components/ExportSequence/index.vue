<template>
    <el-drawer v-model="props.dialogVisible" :title="$t('common.exportSequence')" :close-on-click-modal="false" width="500px" @close="handleClose">
        <div class="mb10px" style="font-size: 14px;color: #90979E">{{$t('exportSequence.message.tips')}}</div>
        <el-table
                v-loading="loading"
                :data="exportSequenceTable"
                highlight-current-row
                stripe
        >
            <el-table-column type="index" :label="$t('common.sort')" width="60"  align="center"/>
            <el-table-column :label="$t('exportSequence.label.fileName')" prop="fileName" show-overflow-tooltip></el-table-column>
            <el-table-column :label="$t('exportSequence.label.date')" prop="date" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ parseDateTime(scope.row.date, "dateTime") }}</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" :label="$t('common.handle')" width="80">
                <template #default="scope">
                    <el-button v-if="scope.row.status===2" type="primary" link @click="selectPurchaseOrder(scope.row.id)">
                        {{$t('common.tryAgain')}}
                    </el-button>
                    <el-button v-if="scope.row.status!==2" type="primary" link @click="downLoad(scope.row)">
                        {{$t('common.downLoad')}}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
<!--        <template #footer>-->
<!--          <span class="dialog-footer">-->
<!--            <el-button @click="handleClose()">{{ $t("common.cancel") }}</el-button>-->
<!--          </span>-->
<!--        </template>-->
    </el-drawer>
</template>

<script setup lang="ts">
import ExportSequenceAPI, { updatePasswordData } from "@/core/api/exportSequence";
import FileAPI from '@/core/api/file';
import {parseDateTime} from "@/core/utils/index.js";

const { t } = useI18n();

// 定义接收的属性
const props = defineProps({
    dialogVisible: {
        type: Boolean,
        default: false,
    },
    path:{
        type: String,
        default: '',
    }
});
const emit = defineEmits(["update:dialogVisible"]);
// const emit = defineEmits(["onSubmit"]);
const exportSequenceTable = ref([])
const loading = ref(false);

/** 关闭弹窗 */
function handleClose() {
    emit("update:dialogVisible", false);
    // emit("onSubmit", false);
}

/** 修改下载状态为已下载 */
function downLoad(row) {
    /*let params = {
        path:props.path,
        queryId:row.queryId
    }
    ExportSequenceAPI.exportSequenceUpdate(params)
        .then(() => {
             FileAPI.downloadFile(row.filePath, row.fileName+'.xlsx');
            exportSequenceListPage()
        })*/
    FileAPI.downloadFile(row.filePath, row.fileName+'.xlsx');
}

function exportSequenceListPage(){
    let params = {
       path:props.path
    }
    ExportSequenceAPI.exportSequenceListPage(params)
        .then((res) => {
            exportSequenceTable.value=res.downLoadList
        })
}

defineExpose({
    exportSequenceListPage,
});

</script>
