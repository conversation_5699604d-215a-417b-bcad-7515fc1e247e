<template>
    <div :class="level === 1?'svg-div':''">
        <!-- 根据 icon 类型决定使用的不同类型的图标组件 -->
        <el-icon v-if="icon && icon.startsWith('el-icon')" class="sub-el-icon">
            <component :is="icon.replace('el-icon-', '')" />
        </el-icon>
        <svg-icon v-else-if="icon" :icon-class="icon" />
        <svg-icon v-else-if="level === 1" icon-class="menu" class="mr-8px"/>
        <!--  <span v-else class="mr-8px dot"></span>-->
        <span v-else class="mr-8px"></span>
    </div>
  <!-- 菜单标题 -->
  <span v-if="title" :class="level === 1?'ml-24px':''">{{ translateRouteTitle(title) }}</span>
</template>

<script setup lang="ts">
import { translateRouteTitle } from "@/core/utils/i18n";

defineProps({
  icon: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
  level: {
    type: Number,
    default: 1,
  },
});
</script>

<style lang="scss" scoped>
.sub-el-icon {
  width: 14px !important;
  margin-right: 0 !important;
  color: currentcolor;
}
.dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  background: #ffffff;
  border-radius: 50%;
}

.hideSidebar {
  .el-sub-menu,
  .el-menu-item {
    .svg-icon,
    .sub-el-icon {
    }
  }
}

.svg-div{
    position: relative;
    left:-8px;
    .svg-icon{
        width: 16px !important;
        height: 16px !important;
        position: absolute !important;
        left:16px;
        top:50%;
        transform: translate(-50%, -50%);
        fill:#90979E;
    }

}


</style>
