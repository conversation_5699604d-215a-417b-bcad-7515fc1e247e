<template>
  <div class="navbar-container">
    <!-- pms, oms, wms, tms 系统切换 -->
    <div class="flex h-full">
      <div
        v-for="item in permissionStore.matchedSystems"
        :key="item.value"
        :class="{ active: userStore.systemId === item.value }"
        @click="handleSystemChange(item.value)"
        class="system-item-wrapper flex-center"
      >
        <div class="system-item">
            {{ item.name }}
        </div>
        <div class="tab-bg-change-image"  v-if="userStore.systemId === item.value">
            <img src="@/core/assets/images/tab-bg-change.png"  />
        </div>
      </div>
    </div>

    <!-- 导航栏面包屑 -->
    <!-- <div class="flex">
      <hamburger
        :is-active="appStore.sidebar.opened"
        @toggle-click="toggleSideBar"
      />
      <breadcrumb />
    </div> -->
    <!-- 导航栏右侧 -->
    <NavbarAction />
  </div>
</template>

<script setup lang="ts">
import {
  useAppStore,
  useUserStore,
  useTagsViewStore,
  usePermissionStore,
} from "@/core/store";
// import { getSystemRoutes } from "@/core/utils";
import {
  SYSTEM_ID_KEY
} from "@/core/enums/CacheEnum";
const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();
const permissionStore = usePermissionStore();
/* const systemList = ref([
  {
    name: "采购PMS",
    value: "pms",
  },
  {
    name: "销售OMS",
    value: "oms",
  },
  {
    name: "仓储WMS",
    value: "wms",
  },
  {
    name: "运输TMS",
    value: "tms",
  },
]); */
function toggleSideBar() {
  appStore.toggleSidebar();
}

const handleSystemChange = async (sid: string) => {
  const tagsViewStore = useTagsViewStore();
  const lastSystemId = userStore.systemId;
  userStore.setSystemId(sid);
  // systemId 保存在本地
  localStorage.setItem(SYSTEM_ID_KEY, sid);
  permissionStore.setSystemId(sid);
  // await getSystemRoutes(sid);

  // 清除所有缓存的视图
  // tagsViewStore.delAllViews();
  // 恢复指定系统的tagsview状态
  tagsViewStore.restoreSystemState(sid, lastSystemId);
  /* try {
    await router.replace(`/${sid}/dashboard`);
  } catch (error) {
    console.error("路由跳转失败:", error);
  } */
};
</script>

<style lang="scss" scoped>
.navbar-container {
  @apply flex-x-between;

  height: $navbar-height;
  background: var(--el-bg-header-color);
  box-sizing: border-box;
  padding-top: 7px;
  padding-left: 100px;
}

.system-item-wrapper {
  position: relative;
  cursor: pointer;
  font-weight: 500;
  &.active {
    box-sizing: border-box;
    height: 43px;
    /*background: #FFFFFF;*/
    /*border-radius: 16px 16px 0px 0px;*/
      .tab-bg-change-image{
          position: absolute;
          z-index: 1;
          top:0px;
          img{
              width: 102px;
              height: 43px;
          }
      }
    .system-item {
      color: var(--el-color-primary) ;
      position: relative;
      z-index:2;
      font-weight: 600;
     /* &::after {
        // 底部高亮line
        content: "";
        display: inline-block;
        width: 10px;
        height: 2px;
        background:var(--el-color-primary);
        border-radius: 2px;
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
      }*/
    }
    /*  &::after {
      position: absolute;
      bottom: 0;
      left: 50%;
      content: "";
      border: 12px solid transparent;
      border-bottom-color: #fff;
      transform: translateX(-50%);
    } */
  }
}

.system-item {
  padding: 0 30px;
  line-height: $navbar-height;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 22px;
  text-align: right;
  font-style: normal;
  word-break: keep-all;
}
</style>
