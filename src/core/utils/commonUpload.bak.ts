import OSS from "ali-oss";
import FileAPI from "@/core/api/file";
// import { Message } from 'element-plus';

let credentials = null; // STS凭证
let ossClient = null; // oss客户端实例
let folder = ""; // 文件夹
let expiration = ""; // 过期时间
let bucket = ""; // bucket名称
let OPCODE = "";
const region = "oss-cn-shanghai"; // oss服务区域名称

// 获取STS Token
function getCredential(optCode) {
  const params = {
    opCode: optCode,
  };
  return FileAPI.getOssStsToken(params)
    .then((res) => {
      // if (res.code === 0) {
      credentials = res;
      folder = res.bucketName;
      bucket = res.bucketName;
      expiration = res.expirationTime;
      initOSSClient();
      /* } else {
      // Message.error(res.message);
    }*/
    })
    .catch((err) => {
      // Message.error(err);
    });
}

// 创建OSS Client
function initOSSClient() {
  const { accessKeyId, secretAccessKey, securityToken } = credentials;
  ossClient = new OSS({
    accessKeyId: accessKeyId,
    accessKeySecret: secretAccessKey,
    stsToken: securityToken,
    secure: true,
    bucket,
    region,
  });
}

// 创建uuid随机数
function uuid() {
  let temp_url = URL.createObjectURL(new Blob());
  let uuid = temp_url.toString();
  URL.revokeObjectURL(temp_url);
  return uuid.substring(uuid.lastIndexOf("/") + 1);
}

// 普通上传
/*
  type为folder的子文件夹，主要为了便于设置不同文件类型的失效时间，取值为image, apk, doc, excel
*/
async function commonUpload(file, type = "image", opCode = "default") {
  if (!ossClient || OPCODE !== opCode) {
    await getCredential(opCode);
  }
  OPCODE = opCode;
  const nowTime = new Date().getTime();
  const expirationTime = expiration * 1000;
  if (nowTime > expirationTime) {
    ossClient && ossClient.cancel();
    await getCredential(opCode);
  }
  // const extensionName = file.name.substring(file.name.lastIndexOf('.'));
  // const fileName = uuid() + extensionName;
  const fileName = file.name;
  console.info(ossClient);
  const response = await ossClient.put(`/${folder}/${type}/${fileName}`, file);
  if (response) {
    const ossRes = await FileAPI.previewFile([
      { bucket: folder, fileName: response.name },
    ]);
    // if (ossRes.code === 0) {
    if (ossRes.urls && ossRes.urls.length > 0) {
      response.url_origin = response.url;
      response.url = ossRes.urls[0].url;
      response.fileName = ossRes.urls[0].fileName;
    }
  }
  return response;
}

export default commonUpload;
