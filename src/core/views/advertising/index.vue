<template>
  <div class="app-container">
    <!-- 顶部锚点 -->
    <div class="image-container">
      <img src="@/core/assets/images/advertising.png" class="img_style" />
      <div @click="toCustomerSteward" class="start-button"></div>
      <div @click="scrollToTop" class="toTop" v-show="showBackToTop"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
const showBackToTop = ref(false);
function toCustomerSteward() {
  window.open("https://erp.yto.net.cn/login", "_blank");
}

function scrollToTop() {
  // 方法1：直接使用原生JS滚动
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}

function handleScroll() {
  // 当滚动超过 200px 时显示按钮，否则隐藏
  showBackToTop.value = window.scrollY > 200;
}

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped lang="scss">
/* 全局启用平滑滚动 */
html {
  scroll-behavior: smooth;
}
.app-container {
  position: relative;
  width: 100%;
}

.image-container {
  position: relative;
  display: inline-block;
}

.img_style {
  width: 100%;
  display: block;
}

.start-button {
  position: absolute;
  top: 6%; /* 调整到按钮的垂直位置 */
  left: 18%; /* 调整到按钮的水平位置 */
  transform: translate(-50%, -50%);
  width: 50%; /* 按钮宽度 */
  height: 5%; /* 按钮高度 */
  cursor: pointer;
  /* 可选：添加半透明背景，方便调试 */
  // background: rgba(255, 0, 0, 0.3);
  // z-index: 9999;
}
.toTop {
  position: fixed;
  right: 30px;
  bottom: 38px;
  width: 96px;
  height: 96px;
  background: url("@/core/assets/images/top.png") no-repeat 50%;
  background-size: contain;
  cursor: pointer;
  z-index: 9999;
  transition: opacity 0.3s ease; /* 添加淡入淡出效果 */
}
</style>
