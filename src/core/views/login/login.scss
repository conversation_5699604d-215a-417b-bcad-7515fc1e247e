.loginPage {
  background: #000;
  height: 100%;
  width: 100%;

  .login {
    display: flex;
    position: relative;
    width: 100%;
    height: 100%;

    &-bd {
      padding: 0 40px;
    }
  }

  .login-left {
    width: 600px;
    height: 100%;
    vertical-align: top;
    position: relative;
    z-index: 10;
    object-fit: cover;
    min-width: 0;
  }

  .login-container {
    flex: 1;
    position: relative;
    z-index: 10;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #F8FBFC;
    flex-shrink: 0;

    .login-bd {
      width: 400px;
      padding: 0px;
    }

    &__hd {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 40px;
    }

    &__logo {
      width: 64px;
      height: 64px;
      padding-top: 5px;
      margin-right: 10px;
    }

    &__title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 32px;
      color: #252829;
      line-height: 28px;
      text-align: center;
      font-style: normal;
      margin: 0px;
    }

    .modify-tabs {
      margin-bottom: 20px;
    }
  }
}

@media screen and (max-width: 1540px) {
  /*当屏幕尺寸小于600px时，应用下面的CSS样式*/

}

:deep(.login-form) {
  position: relative;
  padding: 0 0 0;
  margin: 0 auto;
  overflow: hidden;

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-input__wrapper {
    padding: 14px 16px;
  }

  .el-form-item.is-error .el-input__wrapper {
    // box-shadow: none;
  }

  .el-form-item__error {
    padding-top: 0;
  }

  .el-form-item__content {
    line-height: initial;
  }

  .el-input__clear {
    line-height: 22px;
  }

  .el-input__inner {
    vertical-align: top;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #151719;
    height: 22px;
    // border: none;
    padding: 0 !important;
    border-radius: 0;

    &::placeholder {
      color: #CCCFD5;
    }

    &:-webkit-autofill {
      box-shadow: 0 0 0 1000px white inset !important;
    }
  }

  .countDown {
    height: 22px;
    display: inline-block;
    min-width: 80px;
    font-weight: 600;
    font-size: 16px !important;
    line-height: 22px;
    border: 0;
    // border-bottom: 1px solid #EAEAEA;
    // padding-bottom: 17px !important;
    text-align: center;
    border-radius: 0;
    box-sizing: border-box;
    color: var(--el-color-primary) !important;
    margin-left: 0;

    &.is-wait {
      color: #90979F !important;
      min-width: 100px;
      font-weight: 400;
    }
  }

  .submit-button {
    width: 100% !important;
    height: 48px;
    background: var(--el-color-primary) !important;
    border-color: var(--el-color-primary) !important;
    border-radius: 2px;
    font-weight: 500;
    font-size: 16px !important;
    color: #FFFFFF;
  }
}


.forgot {
  cursor: pointer;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #51585F;
  line-height: 20px;
}
