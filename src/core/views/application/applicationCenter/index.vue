<!-- 应用中心 -->
<template>
  <div class="app-container">
    <div class="card_container">
      <el-card class="card_one">
        <div>
          <img
            src="@/core/assets/images/customer_steward.png"
            class="img_style"
          />
        </div>
        <div class="card_title">
          {{ $t("applicationCenter.cardTitle.customerStewardTitle") }}
        </div>
        <div class="card_content">
          {{ $t("applicationCenter.message.customerStewardMsg") }}
        </div>
        <div class="card_btn" @click="toAdvertising">
          {{ $t("applicationCenter.button.learnMore") }}
          <el-icon><Right /></el-icon>
        </div>
      </el-card>
      <el-card class="card_two">
        <div>
          <img src="@/core/assets/images/wms.png" class="img_style" />
        </div>
        <div class="card_title">
          {{ $t("applicationCenter.cardTitle.WMSTitle") }}
        </div>
        <div class="card_content">
          {{ $t("applicationCenter.message.WMSMsg") }}
        </div>
        <div class="card_btn" @click="toWMS">
          {{ $t("applicationCenter.button.learnMore") }}
          <el-icon><Right /></el-icon>
        </div>
      </el-card>
      <el-card class="card_three">
        <div>
          <img src="@/core/assets/images/jy_platform.png" class="img_style" />
        </div>
        <div class="card_title">
          {{ $t("applicationCenter.cardTitle.JYTitle") }}
        </div>
        <div class="card_content">
          {{ $t("applicationCenter.message.JYMsg") }}
        </div>
        <div class="card_btn" @click="toTransportationMgr">
          {{ $t("applicationCenter.button.learnMore") }}
          <el-icon><Right /></el-icon>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ApplicationCenter",
  inheritAttrs: false,
});
const { t } = useI18n();
const router = useRouter();

function toWMS() {
  window.open("https://ywmskpc.yto.net.cn/home?token=token", "_blank");
}

function toAdvertising() {
  const route = router.resolve({ path: "/advertising/index" });
  window.open(route.href, "_blank");
  
  // window.open("/advertising/index", "_blank");
}

function toTransportationMgr() {
  const route = router.resolve({ path: "/transportationMgr/index" });
  window.open(route.href, "_blank");
}

/* function toJYPlatform() {
  const route = router.resolve({ path: "/jyPlatform/index" });
   window.open(route.href, "_blank");
} */

onMounted(() => {});
</script>
<style scoped lang="scss">
.app-container {
  background: #fff;
  height: 100%;
}
.card_container {
  display: flex;
  padding: 30px;
  font-family:
    PingFangSC,
    PingFang SC;
  .el-card {
    width: 20%;
    min-height: 224px;
    margin-right: 22px;
    border-radius: 8px !important;
  }
  .card_one {
    background: url("@/core/assets/images/card_3.png") no-repeat;
    background-size: 100% 100%;
  }
  .card_two {
    background: url("@/core/assets/images/card_4.png") no-repeat;
    background-size: 100% 100%;
  }
  .card_three {
    background: url("@/core/assets/images/card_jy.png") no-repeat;
    background-size: 100% 100%;
  }
  .img_style {
    width: 64px;
    height: 64px;
  }
  .card_title {
    font-weight: 500;
    font-size: 16px;
    color: #151719;
    line-height: 22px;
    padding: 6px 0px;
  }
  .card_content {
    font-weight: 400;
    font-size: 12px;
    color: #90979e;
    line-height: 18px;
    height: 58px; /* Fixed height for 3 lines of text */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* Limit to 3 lines */
    -webkit-box-orient: vertical;
  }
  .card_btn {
    width: 106px;
    height: 32px;
    background: #f8f1ff;
    border-radius: 4px;
    border: 1px solid #d9bdff;
    font-weight: 500;
    font-size: 14px;
    color: #762adb;
    line-height: 32px;
    text-align: center;
    margin-top: 10px;
    cursor: pointer;
  }
}
</style>
