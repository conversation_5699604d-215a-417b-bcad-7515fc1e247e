<!-- 账号管理 -->
<template>
  <div class="app-container">
    <!-- 用户列表 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item
          :label="$t('accountManagement.label.name')"
          prop="nickName"
        >
          <el-input
            v-model="queryParams.nickName"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            style="width: 210px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item
          :label="$t('accountManagement.label.phone')"
          prop="mobile"
        >
          <el-input
            v-model="queryParams.mobile"
            :placeholder="$t('common.placeholder.inputTips')"
            clearable
            style="width: 210px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item
          :label="$t('accountManagement.label.status')"
          prop="status"
        >
          <el-select
            v-model="queryParams.status"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
            class="!w-[210px]"
          >
            <el-option
              v-for="item in userStatusList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleQuery"
            v-hasPerm="['pms:account:search']"
          >
            {{ $t("common.search") }}
          </el-button>
          <el-button
            @click="handleResetQuery"
            v-hasPerm="['pms:account:reset']"
          >
            {{ $t("common.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="flex-x-between">
          <div>
            <el-button
              type="primary"
              @click="openAdd()"
              v-hasPerm="['pms:account:add']"
            >
              {{ $t("accountManagement.button.addAccunt") }}
            </el-button>
          </div>
        </div>
      </template>

      <el-table v-loading="loading" :data="tableData" stripe>
        <template #empty>
          <Empty />
        </template>
        <el-table-column
          :label="$t('accountManagement.label.account')"
          prop="userName"
        />
        <el-table-column
          prop="nickName"
          :label="$t('accountManagement.label.name')"
        >
          <template #default="scope">
            {{ scope.row.nickName }}
            <!-- <EncryptPhone :nameType="true" :name="scope.row.nickName" /> -->
          </template>
        </el-table-column>
        <!-- <el-table-column
          :label="$t('accountManagement.label.name')"
          prop="nickName"
        /> -->
        <el-table-column
          prop="mobile"
          :label="$t('accountManagement.label.phone')"
          min-width="90"
        >
          <template #default="scope">
            <span class="encryptBox">
              {{ scope.row.countryAreaCode }}
              <span v-if="scope.row.mobile.length <= 4">
                {{ scope.row.mobile }}
              </span>
              <span v-else>
                {{ scope.row.mobile }}
                <el-icon
                  v-if="scope.row.mobile"
                  @click="
                    scope.row.mobilePhoneShow
                      ? getRealPhone(scope.row.userId, scope.$index)
                      : ''
                  "
                  class="encryptBox-icon"
                  color="#762ADB "
                  size="16"
                  v-hasPermEye="['pms:account:eye']"
                >
                  <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                </el-icon>
              </span>
              <!-- <EncryptPhone :phone="scope.row.mobile" /> -->
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="roles"
          :label="$t('accountManagement.label.roles')"
          show-overflow-tooltip
        />
        <el-table-column
          :label="$t('accountManagement.label.affiliatedDepartment')"
          prop="deptName"
        />

        <el-table-column :label="$t('accountManagement.label.status')">
          <template #default="scope">
            <el-switch
              :active-text="$t('common.activeBtn')"
              :inactive-text="$t('common.inactiveBtn')"
              inline-prompt
              style="
                --el-switch-on-color: #762ADB ;
                --el-switch-off-color: #cccfd5;
              "
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              :disabled="scope.row.userType == 'scm_main'"
              @change="changeUserStatus(scope.row)"
              v-hasPerm="['pms:account:updateStatus']"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          :label="$t('accountManagement.label.createTime')"
          min-width="90"
        >
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.createTime, "dateTime") }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.handle')" fixed="right" width="220">
          <template #default="scope">
            <!-- 编辑 -->
            <el-button
              type="primary"
              link
              size="small"
              @click="handleEdit(scope.row)"
              :disabled="scope.row.userType == 'scm_main'"
              v-hasPerm="['pms:account:edit']"
            >
              {{ $t("common.edit") }}
            </el-button>
            <el-button
              type="primary"
              @click="handleResetPassword(scope.row)"
              link
              v-hasPerm="['pms:account:resetPassword']"
            >
              {{ $t("accountManagement.button.resetPassword") }}
            </el-button>
            <!-- 删除 -->
            <el-button
              type="danger"
              link
              size="small"
              @click="handleDelete(scope.row)"
              :disabled="scope.row.userType == 'scm_main'"
              v-hasPerm="['pms:account:delete']"
            >
              {{ $t("common.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>
    <edit
      class="userEditDialog"
      v-model="dialog.visible"
      ref="cmtUser"
      :title="dialog.title"
      @submitted="submitUser"
    />
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n();
defineOptions({
  name: "AccountManagement",
  inheritAttrs: false,
});

import UserAPI, {
  UserForm,
  UserPageQuery,
  UserPageVO,
} from "@/core/api/accountManagement";
import { encryptPhone, parseDateTime } from "@/core/utils/index.js";
// import Edit from "./components/edit.vue";

const userStatusList = ref([
  {
    statusId: " ",
    statusName: t("common.statusEmun.all"),
  },
  {
    statusId: 0,
    statusName: t("common.statusEmun.disable"),
  },
  {
    statusId: 1,
    statusName: t("common.statusEmun.enable"),
  },
]);

const queryFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);
const tableData = ref<UserPageVO[]>();
/** 用户查询参数  */
const queryParams = reactive<UserPageQuery>({
  page: 1,
  limit: 20,
});

/**  用户弹窗对象  */
const dialog = reactive({
  visible: false,
  title: "",
});

/** 查询 */
function handleQuery() {
  loading.value = true;
  UserAPI.queryUserListPage(queryParams)
    .then((data: any) => {
      tableData.value = data.records.map((item: any, index: any) => {
        item.mobilePhoneShow = true;
        item.roles = item.baseRoleVOList
          ? item.baseRoleVOList.map((it: any) => it.roleName).join(",")
          : [];
        return { ...item };
      });
      total.value = parseInt(data.total);
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  queryParams.limit = 20;
  queryParams.nickName = "";
  queryParams.mobile = "";
  queryParams.status = "";
  handleQuery();
}

let cmtUser = ref();
// 新增角色
function openAdd() {
  cmtUser.value.setEditType("add");
  dialog.visible = true;
  dialog.title = t("accountManagement.title.addAccountTitle");
  cmtUser.value.getDeptList();
  cmtUser.value.queryAllRoleList();
  cmtUser.value.getAreaList();
}

// 编辑角色
function handleEdit(row: any) {
  cmtUser.value.setEditType("edit");
  row.account = row.userName;
  row.password = "********";
  cmtUser.value.setFormData(row);
  dialog.visible = true;
  dialog.title = t("accountManagement.title.editAccountTitle");
  cmtUser.value.getDeptList();
  cmtUser.value.queryAllRoleList();
  cmtUser.value.getAreaList();
}

function submitUser() {
  handleQuery();
}

// 重置密码
function handleResetPassword(row: any) {
  ElMessageBox.confirm(
    t("accountManagement.message.resetPasswordTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    let params = {
      userId: row.userId,
    };
    UserAPI.resetPassword(params).then((res: any) => {
      ElMessage.success(t("accountManagement.message.resertPasswordSucess"));
      handleQuery();
      // if (res && res.code === 0) {
      //   ElMessage.success(t("accountManagement.message.resertPasswordSucess"));
      //   handleQuery();
      // } else {
      //   ElMessage.success(t("accountManagement.message.resertPasswordFail"));
      // }
    });
  });
}

/** 删除用户 */
function handleDelete(row: any) {
  if (row.status == 1) {
    return ElMessage.error(t("accountManagement.message.deleteNotTips"));
  }
  ElMessageBox.confirm(
    t("accountManagement.message.deleteTips"),
    t("common.tipTitle"),
    {
      confirmButtonText: t("common.confirm"),
      cancelButtonText: t("common.cancel"),
      type: "warning",
    }
  ).then(
    function () {
      loading.value = true;
      let params = {
        userId: row.userId,
        status: 2,
      };
      UserAPI.updateUserStatus(params)
        .then(() => {
          ElMessage.success(t("accountManagement.message.deleteSucess"));
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    function () {
      ElMessage.info(t("accountManagement.message.deleteCancel"));
    }
  );
}

// 修改角色状态
function changeUserStatus(row: any) {
  let flag = row.status;
  row.status = row.status === 0 ? 1 : 0; //保持switch点击前的状态
  let params = {
    userId: row.userId,
    status: flag,
  };
  if (flag === 0) {
    ElMessageBox.confirm(
      t("accountManagement.message.disableTips"),
      t("common.tipTitle"),
      {
        confirmButtonText: t("common.confirm"),
        cancelButtonText: t("common.cancel"),
        type: "warning",
      }
    ).then(
      () => {
        UserAPI.updateUserStatus(params).then((res: any) => {
          ElMessage.success(t("accountManagement.message.disableSucess"));
          handleQuery();
        });
      },
      () => {
        ElMessage.info(t("accountManagement.message.deleteCancel"));
      }
    );
  } else {
    UserAPI.updateUserStatus(params).then((res: any) => {
      ElMessage.success(t("accountManagement.message.enableSucess"));
      handleQuery();
    });
  }
}

function getRealPhone(id: any, index: any) {
  UserAPI.queryRealPhone({ userId: id })
    .then((data: any) => {
      tableData.value[index].mobile = data.mobile;
      tableData.value[index].mobilePhoneShow = false;
    })
    .finally(() => {});
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.encryptBox {
  // display: inline-flex;
  // justify-content: space-between;
  // align-items: center;
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  // align-self: flex-start;
  vertical-align: text-top;
}
</style>
