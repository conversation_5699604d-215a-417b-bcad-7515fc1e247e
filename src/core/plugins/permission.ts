import {
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteRecordRaw,
} from "vue-router";

import NProgress from "@/core/utils/nprogress";
import { TOKEN_KEY } from "@/core/enums/CacheEnum";
import router from "@/core/router";
import { usePermissionStore, useUserStore } from "@/core/store";

export function setupPermission() {
  // 白名单路由
  const whiteList = ["/login"];

  router.beforeEach(async (to, from, next) => {
    NProgress.start();
    const hasToken = localStorage.getItem(TOKEN_KEY);
    console.log("[权限] Token状态:", hasToken ? "已获取" : "未获取");

    if (hasToken) {
      console.log("[权限] 用户已登录，正在访问:", to.path);
      if (to.path === "/login") {
        console.log("[权限] 用户已登录，从登录页重定向到首页");
        next({ path: "/" });
        NProgress.done();
      } else {
        const userStore = useUserStore();
        const permissionStore = usePermissionStore();
        const hasRoles = permissionStore.hasRoles;
        console.log("[权限] 是否已有角色信息:", hasRoles ? "是" : "否");

        if (hasRoles) {
          if (to.matched.length === 0) {
            console.log(
              "[权限] 未找到匹配路由，重定向到:",
              from.name || "/404"
            );
            next(from.name ? { name: from.name } : "/404");
          } else {
            const title =
              (to.params.title as string) || (to.query.title as string);
            if (title) {
              console.log("[权限] 设置路由标题:", title);
              to.meta.title = title;
            }
            next();
          }
        } else {
          console.log("[权限] 无角色信息，正在生成动态路由");
          const permissionStore = usePermissionStore();
          try {
            await userStore.getUserInfo();
            const dynamicRoutes = await permissionStore.generateRoutes();
            console.log(
              "[权限] 动态路由生成完成，共生成",
              dynamicRoutes.length,
              "个路由"
            );

            dynamicRoutes.forEach((route: RouteRecordRaw) =>
              router.addRoute(route)
            );

            next({ ...to, replace: true });
          } catch (error) {
            console.error("[权限] 路由生成过程出错:", error);
            await userStore.resetToken();
            redirectToLogin(to, next);
            NProgress.done();
          }
        }
      }
    } else {
      console.log("[权限] 用户未登录，当前访问路径:", to.path);
      if (whiteList.includes(to.path)) {
        console.log("[权限] 当前路径在白名单中，允许访问");
        next();
      } else {
        console.log("[权限] 当前路径不在白名单中，重定向到登录页");
        redirectToLogin(to, next);
        NProgress.done();
      }
    }
  });

  router.afterEach(() => {
    NProgress.done();
  });
}

/** 重定向到登录页 */
function redirectToLogin(
  to: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const params = new URLSearchParams(to.query as Record<string, string>);
  const queryString = params.toString();
  const redirect = queryString ? `${to.path}?${queryString}` : to.path;
  next(`/login?redirect=${encodeURIComponent(redirect)}`);
}

/** 判断是否有权限 */
export function hasAuth(
  value: string | string[],
  type: "button" | "role" = "button"
) {
  const { roles, perms } = useUserStore().user;

  // 超级管理员 拥有所有权限
  if (type === "button" && roles.includes("ROOT")) {
    return true;
  }
  // return true;
  const auths = type === "button" ? perms : roles;
  if (type === "button") {
    return auths.includes("ACTION_" + value);
  } else {
    return typeof value === "string"
      ? auths.includes(value)
      : value.some((perm) => auths.includes(perm));
  }
}
