import request from "@/core/utils/request";

// const ROLE_BASE_URL = "/api/v1/roles";
const ROLE_BASE_URL = "/supply-base/role";

class RoleAPI {
  /** 获取角色分页数据 */
  static getPage(queryParams?: RolePageQuery) {
    return request<any, PageResult<RolePageVO[]>>({
      url: `${ROLE_BASE_URL}/page`,
      method: "get",
    });
  }


  /** 获取角色分页数据 */
  static getRolePage(queryParams?: RolePageQuery) {
    return request<any, PageResult<RolePageVO[]>>({
      url: `${ROLE_BASE_URL}/page`,
      method: "post",
      data: queryParams,
    });
  }

  /** 获取角色下拉数据源 */
  static getOptions() {
    return request<any, OptionType[]>({
      url: `${ROLE_BASE_URL}/options`,
      method: "get",
    });
  }

  /**
   * 获取角权限
   */
  static authorityInfo(data) {
    return request({
      url: `${ROLE_BASE_URL}/authorityInfo`,
      method: "post",
      data: data,
    });
  }

  /**
   * 分配菜单权限
   *
   * @param roleId 角色ID
   * @param data 菜单ID集合
   */
  static updateRoleMenus(roleId: number, data: number[]) {
    return request({
      url: `${ROLE_BASE_URL}/${roleId}/menus`,
      method: "put",
      data: data,
    });
  }

  /**
   * 获取角色表单数据
   *
   * @param id 角色ID
   * @returns 角色表单数据
   */
  static getFormData(id: number) {
    return request<any, RoleForm>({
      url: `${ROLE_BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /** 添加角色 */
  static addRole(data: RoleForm) {
    return request({
      url: `${ROLE_BASE_URL}/add`,
      method: "post",
      data: data,
    });
  }

  /**
   * 更新角色
   *
   * @param id 角色ID
   * @param data 角色表单数据
   */
  static updateRole(data: RoleForm) {
    return request({
      url: `${ROLE_BASE_URL}/update`,
      method: "post",
      data: data,
    });
  }

  /**
   * 删除角色
   *
   */
  static delete(data:{roleId?:string}) {
    return request({
      url: `${ROLE_BASE_URL}/delete`,
      method: "get",
      params: data,
    });
  }

  /**
   * 修改角色状态角色
   *
   */
  static updateEnableStatus(data:{roleId?:string,status?:number}) {
    return request({
      url: `${ROLE_BASE_URL}/updateEnableStatus`,
      method: "post",
      data: data,
    });
  }
}

export default RoleAPI;

/** 角色分页查询参数 */
export interface RolePageQuery extends PageQuery {
  /** 角色名称 */
  roleName?: string;
  /** 状态 */
  status?: number;
}

/** 角色分页对象 */
export interface RolePageVO {
  /** 角色ID */
  roleId?: string;
  /** 角色名称 */
  roleName?: string;
  /** 账号数 */
  countNumber?: number;
  /** 角色描述 */
  roleDesc?: string;
  /** 角色状态 */
  status?: number;
  /** 创建时间 */
  createTime?: Date;
}

/** 角色表单对象 */
export interface RoleForm {
  /** 角色ID */
  roleId?: string;
  /** 角色名称 */
  roleName?: string;
  /** 角色描述 */
  roleDesc?: string,
  /** 平台类型(platform: 运营平台:supply：供应链平台; pms_app:供应商app) */
  platformType?: string,
  /** 权限设置 */
  roleSysList?: authorityVO[],
}

/** 角色表单对象 */
export interface authorityVO {
    /** 权限编码数组 */
    authorityIds?:[];
    /** 平台 */
    systemType?: string;
}
