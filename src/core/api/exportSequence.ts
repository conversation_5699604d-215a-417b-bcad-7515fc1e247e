import request from "@/core/utils/request";

const EXPORT_BASE_URL = "/supply-base/export";

class ExportSequenceAPI {
    // 查询用户下载历史
    static exportSequenceListPage(params: { path?:string }) {
        return request<any,ExportSequenceVO>({
            url: `${EXPORT_BASE_URL}/page`,
            method: "get",
            params: params,
        });
    }
    // 修改下载状态为已下载
    static exportSequenceUpdate(params: { path?:string,queryId?:string}) {
        return request({
            url: `${EXPORT_BASE_URL}/update`,
            method: "get",
            params: params,
        });
    }

}

export default ExportSequenceAPI;

/** 用户下载历史对象 */
export interface ExportSequenceVO {
    /** 等待下载数量 */
    waitDownLoadCount?:	number,
    /** 导出列表数据 */
    downLoadList?:DownLoadList[]
}
/** 导出列表数据对象 */
export interface DownLoadList {
    /** 导出时间 */
    date?:	number,
    /** 文件名 */
    fileName?:	string,
    /** excel文件路径 */
    filePath?:	string,
    /** queryId */
    queryId?:	number,
    /** 导出状态(0：导出中，1：成功，2：失败，3：已下载) */
    status?:	number,
}

